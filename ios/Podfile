# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'Taleem' do
  config = use_native_modules!
  use_frameworks! :linkage => :static

  $RNFirebaseAsStaticFramework = true


  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'TaleemTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
      
      bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
        def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
          framework_path = File.join(Dir.pwd, framework_relative_path)
          command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
          puts "Stripping bitcode: #{command}"
          system(command)
        end

      framework_paths = [
        "Pods/AgoraRtm_iOS/AgoraRtmKit.xcframework/ios-arm64_armv7/AgoraRtmKit.framework/AgoraRtmKit",
      ]

      framework_paths.each do |framework_relative_path|
        strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
      end
    
   # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
   react_native_post_install(
     installer,
     config[:reactNativePath],
     :mac_catalyst_enabled => false,
     # :ccache_enabled => true
   )
 end
end
