2024-12-20 06:30:35 +0000  Initial pipeline context: <IDEDistributionProcessingPipelineContext: 0x2ad419ca0; archive(resolved)="<IDEArchive: 0x60006c73ca80>", distributionTask(resolved)="2", distributionDestination(resolved)="1", distributionMethod(resolved)="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team(resolved)="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	Chain (15, self inclusive):
	<IDEDistributionProcessingPipelineContext: 0x2ad419ca0; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionProcessingPipelineContext: 0x29b21d890; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2e84bac80; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2b76adfe0; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2e8a83970; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2f4a435a0; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2f4a1ec60; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2e872bc00; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x29ce179e0; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x60000788d000; teamID='7KJ68PDGJG', teamName='WAQTI L.L.C FZ', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x29b25f660; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="(null)">
	<IDEDistributionContext: 0x2b7292730; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="(null)">
	<IDEDistributionContext: 0x2b77bdba0; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="(null)">
	<IDEDistributionContext: 0x2e872b4f0; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x6000ae169ea0>", team="(null)">
	<IDEDistributionContext: 0x2b7b78610; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="(null)", team="(null)">
	<IDEDistributionContext: 0x2e8a9caa0; archive = "<IDEArchive: 0x60006c73ca80>", distributionMethod="(null)", team="(null)">
</IDEDistributionProcessingPipelineContext: 0x2ad419ca0>
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionCreateDestRootStep
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionCopyItemStep
2024-12-20 06:30:35 +0000  Running /usr/bin/ditto '-V' '/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app' '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app'
2024-12-20 06:30:35 +0000  >>> Copying /Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app 
2024-12-20 06:30:35 +0000  copying file ./_CodeSignature/CodeResources ... 
2024-12-20 06:30:35 +0000  146679 bytes for ./_CodeSignature/CodeResources
2024-12-20 06:30:35 +0000  copying file ./Fontisto.ttf ... 
2024-12-20 06:30:35 +0000  313528 bytes for ./Fontisto.ttf
copying file ./Octicons.ttf ... 
2024-12-20 06:30:35 +0000  49404 bytes for ./Octicons.ttf
copying file ./Feather.ttf ... 
2024-12-20 06:30:35 +0000  56228 bytes for ./Feather.ttf
copying file ./Manrope-Semibold_wght.ttf ... 
2024-12-20 06:30:35 +0000  164936 bytes for ./Manrope-Semibold_wght.ttf
2024-12-20 06:30:35 +0000  copying file ./fonnts.com-ProximaSansMedium.ttf ... 
2024-12-20 06:30:35 +0000  50564 bytes for ./fonnts.com-ProximaSansMedium.ttf
copying file ./FontAwesome6_Regular.ttf ... 
2024-12-20 06:30:35 +0000  63348 bytes for ./FontAwesome6_Regular.ttf
2024-12-20 06:30:35 +0000  copying file ./Entypo.ttf ... 
2024-12-20 06:30:35 +0000  66200 bytes for ./Entypo.ttf
2024-12-20 06:30:35 +0000  copying file ./LottiePrivacyInfo.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  599 bytes for ./LottiePrivacyInfo.bundle/PrivacyInfo.xcprivacy
copying file ./LottiePrivacyInfo.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  782 bytes for ./LottiePrivacyInfo.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./Manrope-VariableFont_wght.ttf ... 
2024-12-20 06:30:35 +0000  164936 bytes for ./Manrope-VariableFont_wght.ttf
2024-12-20 06:30:35 +0000  copying file ./Manrope-Medium.ttf ... 
2024-12-20 06:30:35 +0000  137768 bytes for ./Manrope-Medium.ttf
2024-12-20 06:30:35 +0000  copying file ./Petrona-Bold.ttf ... 
2024-12-20 06:30:35 +0000  218708 bytes for ./Petrona-Bold.ttf
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/de.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1131 bytes for ./QBImagePicker.bundle/de.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/zh-Hans.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1119 bytes for ./QBImagePicker.bundle/zh-Hans.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/ja.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1214 bytes for ./QBImagePicker.bundle/ja.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/en.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1064 bytes for ./QBImagePicker.bundle/en.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/uk.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1398 bytes for ./QBImagePicker.bundle/uk.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib/runtime.nib ... 
2024-12-20 06:30:35 +0000  10065 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib/runtime.nib
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib/objects-12.3+.nib ... 
2024-12-20 06:30:35 +0000  10292 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib/objects-12.3+.nib
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsViewController.nib/runtime.nib ... 
2024-12-20 06:30:35 +0000  1800 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsViewController.nib/runtime.nib
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsViewController.nib/objects-12.3+.nib ... 
2024-12-20 06:30:35 +0000  1800 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsViewController.nib/objects-12.3+.nib
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAssetsViewController.nib/runtime.nib ... 
2024-12-20 06:30:35 +0000  1393 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAssetsViewController.nib/runtime.nib
copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAssetsViewController.nib/objects-12.3+.nib ... 
2024-12-20 06:30:35 +0000  1393 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAssetsViewController.nib/objects-12.3+.nib
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib/runtime.nib ... 
2024-12-20 06:30:35 +0000  7037 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib/runtime.nib
copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib/objects-12.3+.nib ... 
2024-12-20 06:30:35 +0000  7037 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib/objects-12.3+.nib
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsNavigationController.nib/runtime.nib ... 
2024-12-20 06:30:35 +0000  2631 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsNavigationController.nib/runtime.nib
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsNavigationController.nib/objects-12.3+.nib ... 
2024-12-20 06:30:35 +0000  2631 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsNavigationController.nib/objects-12.3+.nib
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/QBImagePicker.storyboardc/Info.plist ... 
2024-12-20 06:30:35 +0000  289 bytes for ./QBImagePicker.bundle/QBImagePicker.storyboardc/Info.plist
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/nb.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1076 bytes for ./QBImagePicker.bundle/nb.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/es.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1113 bytes for ./QBImagePicker.bundle/es.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/da.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1015 bytes for ./QBImagePicker.bundle/da.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/it.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1088 bytes for ./QBImagePicker.bundle/it.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/sv.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1082 bytes for ./QBImagePicker.bundle/sv.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/ko.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1139 bytes for ./QBImagePicker.bundle/ko.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/zh-Hant.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1119 bytes for ./QBImagePicker.bundle/zh-Hant.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/tr.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1104 bytes for ./QBImagePicker.bundle/tr.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/pl.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1119 bytes for ./QBImagePicker.bundle/pl.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/vi.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1143 bytes for ./QBImagePicker.bundle/vi.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/ru.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1433 bytes for ./QBImagePicker.bundle/ru.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/fr.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1153 bytes for ./QBImagePicker.bundle/fr.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/fi.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1002 bytes for ./QBImagePicker.bundle/fi.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/nl.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1017 bytes for ./QBImagePicker.bundle/nl.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/pt.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1112 bytes for ./QBImagePicker.bundle/pt.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/ro.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:35 +0000  1127 bytes for ./QBImagePicker.bundle/ro.lproj/QBImagePicker.strings
2024-12-20 06:30:35 +0000  copying file ./QBImagePicker.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  772 bytes for ./QBImagePicker.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./FontAwesome5_Brands.ttf ... 
2024-12-20 06:30:35 +0000  134040 bytes for ./FontAwesome5_Brands.ttf
2024-12-20 06:30:35 +0000  copying file ./MaterialCommunityIcons.ttf ... 
2024-12-20 06:30:35 +0000  1147844 bytes for ./MaterialCommunityIcons.ttf
2024-12-20 06:30:35 +0000  copying file ./FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  373 bytes for ./FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:35 +0000  copying file ./FBLPromises_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  785 bytes for ./FBLPromises_Privacy.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./fonnts.com-Proxima_Nova_Condensed_Bold.ttf ... 
2024-12-20 06:30:35 +0000  51444 bytes for ./fonnts.com-Proxima_Nova_Condensed_Bold.ttf
copying file ./Manrope-SemiBold.ttf ... 
2024-12-20 06:30:35 +0000  138360 bytes for ./Manrope-SemiBold.ttf
2024-12-20 06:30:35 +0000  copying file ./Petrona-Regular.ttf ... 
2024-12-20 06:30:35 +0000  124820 bytes for ./Petrona-Regular.ttf
2024-12-20 06:30:35 +0000  copying file ./<EMAIL> ... 
2024-12-20 06:30:35 +0000  13658 bytes for ./<EMAIL>
copying file ./AntDesign.ttf ... 
2024-12-20 06:30:35 +0000  70344 bytes for ./AntDesign.ttf
2024-12-20 06:30:35 +0000  copying file ./Lottie_React_Native_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  373 bytes for ./Lottie_React_Native_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Lottie_React_Native_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  802 bytes for ./Lottie_React_Native_Privacy.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./Foundation.ttf ... 
2024-12-20 06:30:35 +0000  56976 bytes for ./Foundation.ttf
2024-12-20 06:30:35 +0000  copying file ./Ionicons.ttf ... 
2024-12-20 06:30:35 +0000  442604 bytes for ./Ionicons.ttf
2024-12-20 06:30:35 +0000  copying file ./FontAwesome5_Solid.ttf ... 
2024-12-20 06:30:35 +0000  202744 bytes for ./FontAwesome5_Solid.ttf
2024-12-20 06:30:35 +0000  copying file ./FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  855 bytes for ./FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:35 +0000  copying file ./FirebaseCoreInternal_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  806 bytes for ./FirebaseCoreInternal_Privacy.bundle/Info.plist
copying file ./FontAwesome5_Regular.ttf ... 
2024-12-20 06:30:35 +0000  33736 bytes for ./FontAwesome5_Regular.ttf
2024-12-20 06:30:35 +0000  copying file ./RNDeviceInfoPrivacyInfo.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  1159 bytes for ./RNDeviceInfoPrivacyInfo.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:35 +0000  copying file ./RNDeviceInfoPrivacyInfo.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  794 bytes for ./RNDeviceInfoPrivacyInfo.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./GTMAppAuth_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  373 bytes for ./GTMAppAuth_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./GTMAppAuth_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  784 bytes for ./GTMAppAuth_Privacy.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./GoogleService-Info.plist ... 
2024-12-20 06:30:35 +0000  756 bytes for ./GoogleService-Info.plist
copying file ./fonnts.com-Proxima_Nova_Light.otf ... 
2024-12-20 06:30:35 +0000  62968 bytes for ./fonnts.com-Proxima_Nova_Light.otf
copying file ./FontAwesome.ttf ... 
2024-12-20 06:30:35 +0000  165548 bytes for ./FontAwesome.ttf
2024-12-20 06:30:35 +0000  copying file ./GTMSessionFetcher_Core_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  598 bytes for ./GTMSessionFetcher_Core_Privacy.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:35 +0000  copying file ./GTMSessionFetcher_Core_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  808 bytes for ./GTMSessionFetcher_Core_Privacy.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./RNImageCropPickerPrivacyInfo.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  905 bytes for ./RNImageCropPickerPrivacyInfo.bundle/PrivacyInfo.xcprivacy
copying file ./RNImageCropPickerPrivacyInfo.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  804 bytes for ./RNImageCropPickerPrivacyInfo.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/de.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1703 bytes for ./GoogleSignIn.bundle/de.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/he.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1780 bytes for ./GoogleSignIn.bundle/he.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/ar.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1759 bytes for ./GoogleSignIn.bundle/ar.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/el.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  2103 bytes for ./GoogleSignIn.bundle/el.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/ja.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1711 bytes for ./GoogleSignIn.bundle/ja.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/en.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1547 bytes for ./GoogleSignIn.bundle/en.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/uk.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1864 bytes for ./GoogleSignIn.bundle/uk.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/es_MX.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1607 bytes for ./GoogleSignIn.bundle/es_MX.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/zh_CN.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1489 bytes for ./GoogleSignIn.bundle/zh_CN.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/nb.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1593 bytes for ./GoogleSignIn.bundle/nb.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/es.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1678 bytes for ./GoogleSignIn.bundle/es.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/pt_BR.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1608 bytes for ./GoogleSignIn.bundle/pt_BR.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/da.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1611 bytes for ./GoogleSignIn.bundle/da.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/it.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1570 bytes for ./GoogleSignIn.bundle/it.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/sk.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1665 bytes for ./GoogleSignIn.bundle/sk.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/pt_PT.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1646 bytes for ./GoogleSignIn.bundle/pt_PT.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1773 bytes for ./GoogleSignIn.bundle/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/ms.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1565 bytes for ./GoogleSignIn.bundle/ms.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/sv.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1579 bytes for ./GoogleSignIn.bundle/sv.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/cs.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1615 bytes for ./GoogleSignIn.bundle/cs.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1166 bytes for ./GoogleSignIn.bundle/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/ko.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1634 bytes for ./GoogleSignIn.bundle/ko.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/hu.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1663 bytes for ./GoogleSignIn.bundle/hu.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/tr.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1596 bytes for ./GoogleSignIn.bundle/tr.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/pl.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1578 bytes for ./GoogleSignIn.bundle/pl.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/zh_TW.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1525 bytes for ./GoogleSignIn.bundle/zh_TW.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/en_GB.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1547 bytes for ./GoogleSignIn.bundle/en_GB.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/vi.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1717 bytes for ./GoogleSignIn.bundle/vi.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  3756 bytes for ./GoogleSignIn.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/ru.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1954 bytes for ./GoogleSignIn.bundle/ru.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/fr_CA.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1657 bytes for ./GoogleSignIn.bundle/fr_CA.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/fr.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1676 bytes for ./GoogleSignIn.bundle/fr.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/fi.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1640 bytes for ./GoogleSignIn.bundle/fi.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/id.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1553 bytes for ./GoogleSignIn.bundle/id.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/nl.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1606 bytes for ./GoogleSignIn.bundle/nl.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/th.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  2133 bytes for ./GoogleSignIn.bundle/th.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/pt.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1608 bytes for ./GoogleSignIn.bundle/pt.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/google.png ... 
2024-12-20 06:30:35 +0000  643 bytes for ./GoogleSignIn.bundle/google.png
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/Roboto-Bold.ttf ... 
2024-12-20 06:30:35 +0000  127744 bytes for ./GoogleSignIn.bundle/Roboto-Bold.ttf
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/ro.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1598 bytes for ./GoogleSignIn.bundle/ro.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  770 bytes for ./GoogleSignIn.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/hr.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1621 bytes for ./GoogleSignIn.bundle/hr.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/hi.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  2286 bytes for ./GoogleSignIn.bundle/hi.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./GoogleSignIn.bundle/ca.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:35 +0000  1669 bytes for ./GoogleSignIn.bundle/ca.lproj/GoogleSignIn.strings
2024-12-20 06:30:35 +0000  copying file ./Zocial.ttf ... 
2024-12-20 06:30:35 +0000  25788 bytes for ./Zocial.ttf
2024-12-20 06:30:35 +0000  copying file ./RNPermissionsPrivacyInfo.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  598 bytes for ./RNPermissionsPrivacyInfo.bundle/PrivacyInfo.xcprivacy
copying file ./RNPermissionsPrivacyInfo.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  796 bytes for ./RNPermissionsPrivacyInfo.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./Assets.car ... 
2024-12-20 06:30:35 +0000  302264 bytes for ./Assets.car
copying file ./EvilIcons.ttf ... 
2024-12-20 06:30:35 +0000  13456 bytes for ./EvilIcons.ttf
2024-12-20 06:30:35 +0000  copying file ./AppIcon76x76@2x~ipad.png ... 
2024-12-20 06:30:35 +0000  17266 bytes for ./AppIcon76x76@2x~ipad.png
2024-12-20 06:30:35 +0000  copying file ./AppAuthCore_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  391 bytes for ./AppAuthCore_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./AppAuthCore_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  785 bytes for ./AppAuthCore_Privacy.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  1289 bytes for ./GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:35 +0000  copying file ./GoogleUtilities_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  794 bytes for ./GoogleUtilities_Privacy.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./main.jsbundle ... 
2024-12-20 06:30:35 +0000  5449581 bytes for ./main.jsbundle
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/de.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/de.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1312 bytes for ./RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/he.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/he.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1444 bytes for ./RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin
copying file ./RCTI18nStrings.bundle/ar.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/ar.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1500 bytes for ./RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/el.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/el.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1600 bytes for ./RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1296 bytes for ./RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/ja.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/ja.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1436 bytes for ./RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/en.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/en.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1296 bytes for ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/uk.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/uk.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1564 bytes for ./RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/nb.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/nb.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1296 bytes for ./RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1268 bytes for ./RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin
copying file ./RCTI18nStrings.bundle/es.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/es.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1364 bytes for ./RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin
copying file ./RCTI18nStrings.bundle/da.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/da.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1300 bytes for ./RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/it.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/it.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1388 bytes for ./RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/sk.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/sk.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1356 bytes for ./RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1368 bytes for ./RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/ms.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/ms.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1292 bytes for ./RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/sv.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/sv.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1300 bytes for ./RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/cs.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/cs.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1368 bytes for ./RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/ko.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/ko.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1332 bytes for ./RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1308 bytes for ./RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/hu.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/hu.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1348 bytes for ./RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/tr.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/tr.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1360 bytes for ./RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/pl.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/pl.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1340 bytes for ./RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/vi.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/vi.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1352 bytes for ./RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/ru.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/ru.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1576 bytes for ./RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/fr.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/fr.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1360 bytes for ./RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/fi.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/fi.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1340 bytes for ./RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/id.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/id.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1300 bytes for ./RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/nl.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/nl.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1324 bytes for ./RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/th.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/th.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1784 bytes for ./RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin
copying file ./RCTI18nStrings.bundle/pt.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/pt.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1356 bytes for ./RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/zu.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/zu.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  2076 bytes for ./RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1372 bytes for ./RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/ro.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/ro.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1344 bytes for ./RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  775 bytes for ./RCTI18nStrings.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/hr.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/hr.lproj/Localizable.strings
2024-12-20 06:30:35 +0000  copying file ./RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1352 bytes for ./RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin
copying file ./RCTI18nStrings.bundle/hi.lproj/Localizable.strings ... 
2024-12-20 06:30:35 +0000  64 bytes for ./RCTI18nStrings.bundle/hi.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:35 +0000  1640 bytes for ./RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin
2024-12-20 06:30:35 +0000  copying file ./FontAwesome6_Solid.ttf ... 
2024-12-20 06:30:35 +0000  394668 bytes for ./FontAwesome6_Solid.ttf
2024-12-20 06:30:35 +0000  copying file ./PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  1283 bytes for ./PrivacyInfo.xcprivacy
2024-12-20 06:30:35 +0000  copying file ./Manrope-Regular.ttf ... 
2024-12-20 06:30:35 +0000  137148 bytes for ./Manrope-Regular.ttf
2024-12-20 06:30:35 +0000  copying file ./LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib ... 
2024-12-20 06:30:35 +0000  3813 bytes for ./LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib
2024-12-20 06:30:35 +0000  copying file ./LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib ... 
2024-12-20 06:30:35 +0000  924 bytes for ./LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib
2024-12-20 06:30:35 +0000  copying file ./LaunchScreen.storyboardc/Info.plist ... 
2024-12-20 06:30:35 +0000  258 bytes for ./LaunchScreen.storyboardc/Info.plist
2024-12-20 06:30:35 +0000  copying file ./Petrona-Medium.ttf ... 
2024-12-20 06:30:35 +0000  218708 bytes for ./Petrona-Medium.ttf
2024-12-20 06:30:35 +0000  copying file ./Waqti ... 
2024-12-20 06:30:35 +0000  9717024 bytes for ./Waqti
copying file ./Petrona-SemiBold.ttf ... 
2024-12-20 06:30:35 +0000  126576 bytes for ./Petrona-SemiBold.ttf
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/de.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  189 bytes for ./TOCropViewControllerBundle.bundle/de.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/ar.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  207 bytes for ./TOCropViewControllerBundle.bundle/ar.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/zh-Hans.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  172 bytes for ./TOCropViewControllerBundle.bundle/zh-Hans.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/ja.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  210 bytes for ./TOCropViewControllerBundle.bundle/ja.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/fa.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  210 bytes for ./TOCropViewControllerBundle.bundle/fa.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/en.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  168 bytes for ./TOCropViewControllerBundle.bundle/en.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/uk.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  231 bytes for ./TOCropViewControllerBundle.bundle/uk.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/es.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  179 bytes for ./TOCropViewControllerBundle.bundle/es.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/it.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  181 bytes for ./TOCropViewControllerBundle.bundle/it.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/sk.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  176 bytes for ./TOCropViewControllerBundle.bundle/sk.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/ms.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  173 bytes for ./TOCropViewControllerBundle.bundle/ms.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/cs.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  173 bytes for ./TOCropViewControllerBundle.bundle/cs.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/ko.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  187 bytes for ./TOCropViewControllerBundle.bundle/ko.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/Base.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  168 bytes for ./TOCropViewControllerBundle.bundle/Base.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/zh-Hant.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  175 bytes for ./TOCropViewControllerBundle.bundle/zh-Hant.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/fa-IR.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  210 bytes for ./TOCropViewControllerBundle.bundle/fa-IR.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/hu.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  194 bytes for ./TOCropViewControllerBundle.bundle/hu.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/da-DK.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  174 bytes for ./TOCropViewControllerBundle.bundle/da-DK.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/tr.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  183 bytes for ./TOCropViewControllerBundle.bundle/tr.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/pl.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  171 bytes for ./TOCropViewControllerBundle.bundle/pl.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/pt-BR.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  181 bytes for ./TOCropViewControllerBundle.bundle/pt-BR.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/vi.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  178 bytes for ./TOCropViewControllerBundle.bundle/vi.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  373 bytes for ./TOCropViewControllerBundle.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/ru.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  237 bytes for ./TOCropViewControllerBundle.bundle/ru.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/fr.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  193 bytes for ./TOCropViewControllerBundle.bundle/fr.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/fi.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  179 bytes for ./TOCropViewControllerBundle.bundle/fi.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/id.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  178 bytes for ./TOCropViewControllerBundle.bundle/id.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/nl.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  178 bytes for ./TOCropViewControllerBundle.bundle/nl.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/pt.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  181 bytes for ./TOCropViewControllerBundle.bundle/pt.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/ro.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  181 bytes for ./TOCropViewControllerBundle.bundle/ro.lproj/TOCropViewControllerLocalizable.strings
copying file ./TOCropViewControllerBundle.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  800 bytes for ./TOCropViewControllerBundle.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./TOCropViewControllerBundle.bundle/ca.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:35 +0000  177 bytes for ./TOCropViewControllerBundle.bundle/ca.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:35 +0000  copying file ./Frameworks/libswift_Concurrency.dylib ... 
2024-12-20 06:30:35 +0000  557600 bytes for ./Frameworks/libswift_Concurrency.dylib
2024-12-20 06:30:35 +0000  copying file ./Frameworks/hermes.framework/_CodeSignature/CodeResources ... 
2024-12-20 06:30:35 +0000  1798 bytes for ./Frameworks/hermes.framework/_CodeSignature/CodeResources
2024-12-20 06:30:35 +0000  copying file ./Frameworks/hermes.framework/hermes ... 
2024-12-20 06:30:35 +0000  4444800 bytes for ./Frameworks/hermes.framework/hermes
copying file ./Frameworks/hermes.framework/Info.plist ... 
2024-12-20 06:30:35 +0000  815 bytes for ./Frameworks/hermes.framework/Info.plist
2024-12-20 06:30:35 +0000  copying file ./SimpleLineIcons.ttf ... 
2024-12-20 06:30:35 +0000  54056 bytes for ./SimpleLineIcons.ttf
2024-12-20 06:30:35 +0000  copying file ./fonnts.com-ProximaSansRegular.ttf ... 
2024-12-20 06:30:35 +0000  50788 bytes for ./fonnts.com-ProximaSansRegular.ttf
2024-12-20 06:30:35 +0000  copying file ./FontAwesome6_Brands.ttf ... 
2024-12-20 06:30:35 +0000  189684 bytes for ./FontAwesome6_Brands.ttf
copying file ./embedded.mobileprovision ... 
2024-12-20 06:30:35 +0000  16932 bytes for ./embedded.mobileprovision
2024-12-20 06:30:35 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2024-12-20 06:30:35 +0000  761 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2024-12-20 06:30:35 +0000  405 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
copying file ./assets/node_modules/@react-navigation/elements/src/assets/back-icon.png ... 
2024-12-20 06:30:35 +0000  290 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/back-icon.png
2024-12-20 06:30:35 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png ... 
2024-12-20 06:30:35 +0000  913 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1372 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1038 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/info.png ... 
2024-12-20 06:30:35 +0000  453 bytes for ./assets/src/assets/img/info.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  748 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1159 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1227 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/add.png ... 
2024-12-20 06:30:35 +0000  1927 bytes for ./assets/src/assets/img/add.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/angle-right.png ... 
2024-12-20 06:30:35 +0000  137 bytes for ./assets/src/assets/img/angle-right.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/whitelock.png ... 
2024-12-20 06:30:35 +0000  253 bytes for ./assets/src/assets/img/whitelock.png
copying file ./assets/src/assets/img/Plus.png ... 
2024-12-20 06:30:35 +0000  229 bytes for ./assets/src/assets/img/Plus.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  854 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/liveClock.png ... 
2024-12-20 06:30:35 +0000  2838 bytes for ./assets/src/assets/img/liveClock.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/FavoriteGroup.png ... 
2024-12-20 06:30:35 +0000  742 bytes for ./assets/src/assets/img/FavoriteGroup.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Group279340.png ... 
2024-12-20 06:30:35 +0000  430 bytes for ./assets/src/assets/img/Group279340.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  234 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  762 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  692 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  3337 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Tick.png ... 
2024-12-20 06:30:35 +0000  1531 bytes for ./assets/src/assets/img/Tick.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Icon.png ... 
2024-12-20 06:30:35 +0000  515 bytes for ./assets/src/assets/img/Icon.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/newBackground.png ... 
2024-12-20 06:30:35 +0000  6330945 bytes for ./assets/src/assets/img/newBackground.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/eyemid.png ... 
2024-12-20 06:30:35 +0000  711 bytes for ./assets/src/assets/img/eyemid.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/deliver.png ... 
2024-12-20 06:30:35 +0000  819 bytes for ./assets/src/assets/img/deliver.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  526 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/sucess.png ... 
2024-12-20 06:30:35 +0000  2105 bytes for ./assets/src/assets/img/sucess.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/filledLayer.png ... 
2024-12-20 06:30:35 +0000  451 bytes for ./assets/src/assets/img/filledLayer.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/flag.png ... 
2024-12-20 06:30:35 +0000  351 bytes for ./assets/src/assets/img/flag.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Group81.png ... 
2024-12-20 06:30:35 +0000  481 bytes for ./assets/src/assets/img/Group81.png
copying file ./assets/src/assets/img/deleteImg.png ... 
2024-12-20 06:30:35 +0000  3118 bytes for ./assets/src/assets/img/deleteImg.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/filledFav.png ... 
2024-12-20 06:30:35 +0000  502 bytes for ./assets/src/assets/img/filledFav.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  562 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/discover.png ... 
2024-12-20 06:30:35 +0000  436 bytes for ./assets/src/assets/img/discover.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  802 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/backArrowNew.png ... 
2024-12-20 06:30:35 +0000  276 bytes for ./assets/src/assets/img/backArrowNew.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/MaskGroup.png ... 
2024-12-20 06:30:35 +0000  111836 bytes for ./assets/src/assets/img/MaskGroup.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/favoriteIcon.png ... 
2024-12-20 06:30:35 +0000  424 bytes for ./assets/src/assets/img/favoriteIcon.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  698 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  5314 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1053 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  963 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/xClose.png ... 
2024-12-20 06:30:35 +0000  341 bytes for ./assets/src/assets/img/xClose.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  307 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/stopwatch.png ... 
2024-12-20 06:30:35 +0000  414 bytes for ./assets/src/assets/img/stopwatch.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Group279420.png ... 
2024-12-20 06:30:35 +0000  430 bytes for ./assets/src/assets/img/Group279420.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1073 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/noun-brand-6645268.png ... 
2024-12-20 06:30:35 +0000  473 bytes for ./assets/src/assets/img/noun-brand-6645268.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/dobleClock.png ... 
2024-12-20 06:30:35 +0000  1463 bytes for ./assets/src/assets/img/dobleClock.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/plusGreen.png ... 
2024-12-20 06:30:35 +0000  223 bytes for ./assets/src/assets/img/plusGreen.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  833 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1701 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/lock.png ... 
2024-12-20 06:30:35 +0000  430 bytes for ./assets/src/assets/img/lock.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1091 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  949 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  724 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  733 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1228 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1993 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/party-horn.png ... 
2024-12-20 06:30:35 +0000  539 bytes for ./assets/src/assets/img/party-horn.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  712 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/recent.png ... 
2024-12-20 06:30:35 +0000  426 bytes for ./assets/src/assets/img/recent.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/myorder.png ... 
1129 bytes for ./assets/src/assets/img/myorder.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  607 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/buySell.png ... 
2024-12-20 06:30:35 +0000  761 bytes for ./assets/src/assets/img/buySell.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  634 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/file.png ... 
2024-12-20 06:30:35 +0000  492 bytes for ./assets/src/assets/img/file.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1947 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/angle-down.png ... 
2024-12-20 06:30:35 +0000  141 bytes for ./assets/src/assets/img/angle-down.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  3903 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1069 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  299 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/FavouriteActive.png ... 
2024-12-20 06:30:35 +0000  389 bytes for ./assets/src/assets/img/FavouriteActive.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  843 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/file1.png ... 
2024-12-20 06:30:35 +0000  370 bytes for ./assets/src/assets/img/file1.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Favourite.png ... 
2024-12-20 06:30:35 +0000  400 bytes for ./assets/src/assets/img/Favourite.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/home.png ... 
2024-12-20 06:30:35 +0000  297 bytes for ./assets/src/assets/img/home.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/midSizearrow.png ... 
2024-12-20 06:30:35 +0000  229 bytes for ./assets/src/assets/img/midSizearrow.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/checkBoxFalse.png ... 
2024-12-20 06:30:35 +0000  152 bytes for ./assets/src/assets/img/checkBoxFalse.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/user.png ... 
2024-12-20 06:30:35 +0000  383 bytes for ./assets/src/assets/img/user.png
copying file ./assets/src/assets/img/filterIc.png ... 
2024-12-20 06:30:35 +0000  520 bytes for ./assets/src/assets/img/filterIc.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/writeedit.png ... 
2024-12-20 06:30:35 +0000  791 bytes for ./assets/src/assets/img/writeedit.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1174 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  745 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  219 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/cart-shopping-fast.png ... 
2024-12-20 06:30:35 +0000  337 bytes for ./assets/src/assets/img/cart-shopping-fast.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  6156 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/Path.png ... 
2024-12-20 06:30:35 +0000  403 bytes for ./assets/src/assets/img/Path.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1354 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/sorted.png ... 
2024-12-20 06:30:35 +0000  312 bytes for ./assets/src/assets/img/sorted.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  915 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  873 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/payment.png ... 
2024-12-20 06:30:35 +0000  914 bytes for ./assets/src/assets/img/payment.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/list.png ... 
2024-12-20 06:30:35 +0000  556 bytes for ./assets/src/assets/img/list.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  487 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/whiteAngleDown.png ... 
2024-12-20 06:30:35 +0000  146 bytes for ./assets/src/assets/img/whiteAngleDown.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  847 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/nounLocation.png ... 
2024-12-20 06:30:35 +0000  326 bytes for ./assets/src/assets/img/nounLocation.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1396 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  490 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/search.png ... 
2024-12-20 06:30:35 +0000  400 bytes for ./assets/src/assets/img/search.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1666 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Minus.png ... 
2024-12-20 06:30:35 +0000  212 bytes for ./assets/src/assets/img/Minus.png
copying file ./assets/src/assets/img/DeleveryCar.png ... 
2024-12-20 06:30:35 +0000  722 bytes for ./assets/src/assets/img/DeleveryCar.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/midwatch.png ... 
2024-12-20 06:30:35 +0000  1577 bytes for ./assets/src/assets/img/midwatch.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/apple.png ... 
2024-12-20 06:30:35 +0000  388 bytes for ./assets/src/assets/img/apple.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  821 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1172 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Layer.png ... 
2024-12-20 06:30:35 +0000  368 bytes for ./assets/src/assets/img/Layer.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  365069 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/menu.png ... 
2024-12-20 06:30:35 +0000  158 bytes for ./assets/src/assets/img/menu.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/filledShoppingBag.png ... 
2024-12-20 06:30:35 +0000  432 bytes for ./assets/src/assets/img/filledShoppingBag.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  584 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  624 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Component.png ... 
2024-12-20 06:30:35 +0000  443 bytes for ./assets/src/assets/img/Component.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/shareimg.png ... 
2024-12-20 06:30:35 +0000  2925 bytes for ./assets/src/assets/img/shareimg.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/enter.png ... 
2024-12-20 06:30:35 +0000  280 bytes for ./assets/src/assets/img/enter.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/arrows-repeat.png ... 
2024-12-20 06:30:35 +0000  337 bytes for ./assets/src/assets/img/arrows-repeat.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/window.png ... 
2024-12-20 06:30:35 +0000  3920 bytes for ./assets/src/assets/img/window.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  753 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  773 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/Notifications.png ... 
2024-12-20 06:30:35 +0000  427 bytes for ./assets/src/assets/img/Notifications.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Group279893.png ... 
2024-12-20 06:30:35 +0000  108863 bytes for ./assets/src/assets/img/Group279893.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1103 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  579 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/bidSucces.png ... 
2024-12-20 06:30:35 +0000  1354 bytes for ./assets/src/assets/img/bidSucces.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/cartorder.png ... 
2024-12-20 06:30:35 +0000  1316 bytes for ./assets/src/assets/img/cartorder.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Rectangle8524.png ... 
2024-12-20 06:30:35 +0000  6705 bytes for ./assets/src/assets/img/Rectangle8524.png
copying file ./assets/src/assets/img/WAQTI.png ... 
2024-12-20 06:30:35 +0000  43754 bytes for ./assets/src/assets/img/WAQTI.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/notify.png ... 
2024-12-20 06:30:35 +0000  1733 bytes for ./assets/src/assets/img/notify.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/comment.png ... 
2024-12-20 06:30:35 +0000  415 bytes for ./assets/src/assets/img/comment.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Rectangle8525.png ... 
2024-12-20 06:30:35 +0000  7954 bytes for ./assets/src/assets/img/Rectangle8525.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  456 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  847 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/openEye.png ... 
2024-12-20 06:30:35 +0000  605 bytes for ./assets/src/assets/img/openEye.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  720532 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/sort.png ... 
2024-12-20 06:30:35 +0000  265 bytes for ./assets/src/assets/img/sort.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/watch1.png ... 
2024-12-20 06:30:35 +0000  118425 bytes for ./assets/src/assets/img/watch1.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1249 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  806 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/Path150459.png ... 
2024-12-20 06:30:35 +0000  258 bytes for ./assets/src/assets/img/Path150459.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/profile.png ... 
2024-12-20 06:30:35 +0000  392 bytes for ./assets/src/assets/img/profile.png
copying file ./assets/src/assets/img/eye.png ... 
2024-12-20 06:30:35 +0000  769 bytes for ./assets/src/assets/img/eye.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/phone-rotary.png ... 
2024-12-20 06:30:35 +0000  591 bytes for ./assets/src/assets/img/phone-rotary.png
copying file ./assets/src/assets/img/user_a.png ... 
2024-12-20 06:30:35 +0000  545 bytes for ./assets/src/assets/img/user_a.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/location.png ... 
2024-12-20 06:30:35 +0000  635 bytes for ./assets/src/assets/img/location.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/Rectangle8526.png ... 
2024-12-20 06:30:35 +0000  6174 bytes for ./assets/src/assets/img/Rectangle8526.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1032 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/delete.png ... 
2024-12-20 06:30:35 +0000  484 bytes for ./assets/src/assets/img/delete.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/frame.png ... 
2024-12-20 06:30:35 +0000  475 bytes for ./assets/src/assets/img/frame.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/unchecked.png ... 
2024-12-20 06:30:35 +0000  371 bytes for ./assets/src/assets/img/unchecked.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  654 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  822 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  618 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  412 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  845 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/envelope-dot.png ... 
2024-12-20 06:30:35 +0000  432 bytes for ./assets/src/assets/img/envelope-dot.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/activityicon.png ... 
2024-12-20 06:30:35 +0000  1992 bytes for ./assets/src/assets/img/activityicon.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1474 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1162 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  5904 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/startblack.png ... 
2024-12-20 06:30:35 +0000  884 bytes for ./assets/src/assets/img/startblack.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/favuroit.png ... 
2024-12-20 06:30:35 +0000  516 bytes for ./assets/src/assets/img/favuroit.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/sendIcon.png ... 
2024-12-20 06:30:35 +0000  503 bytes for ./assets/src/assets/img/sendIcon.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  748 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  750 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/filterScale.png ... 
2024-12-20 06:30:35 +0000  391 bytes for ./assets/src/assets/img/filterScale.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/MessageIcon.png ... 
2024-12-20 06:30:35 +0000  593 bytes for ./assets/src/assets/img/MessageIcon.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  926 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/waqtiLogo.png ... 
2024-12-20 06:30:35 +0000  5377 bytes for ./assets/src/assets/img/waqtiLogo.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/kkk.png ... 
2024-12-20 06:30:35 +0000  108980 bytes for ./assets/src/assets/img/kkk.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/editAuction.png ... 
2024-12-20 06:30:35 +0000  3055 bytes for ./assets/src/assets/img/editAuction.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1028 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/Union.png ... 
2024-12-20 06:30:35 +0000  1355 bytes for ./assets/src/assets/img/Union.png
copying file ./assets/src/assets/img/google.png ... 
2024-12-20 06:30:35 +0000  740 bytes for ./assets/src/assets/img/google.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/midRating.png ... 
2024-12-20 06:30:35 +0000  1717 bytes for ./assets/src/assets/img/midRating.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/addImage.png ... 
2024-12-20 06:30:35 +0000  558 bytes for ./assets/src/assets/img/addImage.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/shoppingBag.png ... 
2024-12-20 06:30:35 +0000  384 bytes for ./assets/src/assets/img/shoppingBag.png
copying file ./assets/src/assets/img/LocationICon.png ... 
2024-12-20 06:30:35 +0000  634 bytes for ./assets/src/assets/img/LocationICon.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/phone-call.png ... 
2024-12-20 06:30:35 +0000  384 bytes for ./assets/src/assets/img/phone-call.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  664 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/newCal.png ... 
2024-12-20 06:30:35 +0000  355 bytes for ./assets/src/assets/img/newCal.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1410 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/box.png ... 
2024-12-20 06:30:35 +0000  836 bytes for ./assets/src/assets/img/box.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/newuser.png ... 
2024-12-20 06:30:35 +0000  2563 bytes for ./assets/src/assets/img/newuser.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/message.png ... 
2024-12-20 06:30:35 +0000  1644 bytes for ./assets/src/assets/img/message.png
copying file ./assets/src/assets/img/arrow-small-left.png ... 
2024-12-20 06:30:35 +0000  197 bytes for ./assets/src/assets/img/arrow-small-left.png
copying file ./assets/src/assets/img/auction.png ... 
2024-12-20 06:30:35 +0000  284 bytes for ./assets/src/assets/img/auction.png
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  990 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/equals-symbol.png ... 
2024-12-20 06:30:35 +0000  2069 bytes for ./assets/src/assets/img/equals-symbol.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/back.png ... 
2024-12-20 06:30:35 +0000  1459 bytes for ./assets/src/assets/img/back.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1144 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/check-circle.png ... 
2024-12-20 06:30:35 +0000  330 bytes for ./assets/src/assets/img/check-circle.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/popular.png ... 
2024-12-20 06:30:35 +0000  400 bytes for ./assets/src/assets/img/popular.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  3738 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1598 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/bookmarkNew.png ... 
2024-12-20 06:30:35 +0000  305 bytes for ./assets/src/assets/img/bookmarkNew.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/roundfilter.png ... 
2024-12-20 06:30:35 +0000  1637 bytes for ./assets/src/assets/img/roundfilter.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  942 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  318 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1254 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/checkBoxTrue.png ... 
2024-12-20 06:30:35 +0000  277 bytes for ./assets/src/assets/img/checkBoxTrue.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  928 bytes for ./assets/src/assets/img/<EMAIL>
copying file ./assets/src/assets/img/Welcome.png ... 
2024-12-20 06:30:35 +0000  11979207 bytes for ./assets/src/assets/img/Welcome.png
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1218 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:35 +0000  1011 bytes for ./assets/src/assets/img/<EMAIL>
2024-12-20 06:30:35 +0000  copying file ./Manrope-Bold.ttf ... 
2024-12-20 06:30:35 +0000  139100 bytes for ./Manrope-Bold.ttf
2024-12-20 06:30:35 +0000  copying file ./fonnts.com-ProximaSansBlack.ttf ... 
2024-12-20 06:30:35 +0000  47416 bytes for ./fonnts.com-ProximaSansBlack.ttf
2024-12-20 06:30:35 +0000  copying file ./Info.plist ... 
2024-12-20 06:30:35 +0000  5131 bytes for ./Info.plist
2024-12-20 06:30:35 +0000  copying file ./fonnts.com-ProximaNova-Semibold.ttf ... 
2024-12-20 06:30:35 +0000  90132 bytes for ./fonnts.com-ProximaNova-Semibold.ttf
2024-12-20 06:30:35 +0000  copying file ./RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:35 +0000  512 bytes for ./RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:35 +0000  copying file ./RNCAsyncStorage_resources.bundle/Info.plist ... 
2024-12-20 06:30:35 +0000  798 bytes for ./RNCAsyncStorage_resources.bundle/Info.plist
2024-12-20 06:30:35 +0000  copying file ./PkgInfo ... 
2024-12-20 06:30:35 +0000  8 bytes for ./PkgInfo
copying file ./MaterialIcons.ttf ... 
2024-12-20 06:30:35 +0000  356840 bytes for ./MaterialIcons.ttf
2024-12-20 06:30:35 +0000  /usr/bin/ditto exited with 0
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionEmbedProfileStep
2024-12-20 06:30:35 +0000  Skipping profile for item: <IDEDistributionItem: 0x60006c903600; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x60004caa01c0:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c3dada0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000299dfb40; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='(null)', teamID='7KJ68PDGJG', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x600090642640:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'>
2024-12-20 06:30:35 +0000  Skipping profile for item: <IDEDistributionItem: 0x60006c902160; bundleID='com.apple.dt.runtime.swiftConcurrency', path='<DVTFilePath:0x600070cdcd80:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/libswift_Concurrency.dylib'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c30a580; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000299ef180; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='(null)', teamID='7KJ68PDGJG', identifier='com.apple.dt.runtime.swiftConcurrency', executablePath='<DVTFilePath:0x600070cdcd80:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/libswift_Concurrency.dylib'>', hardenedRuntime='0'>'>
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionInfoPlistStep
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionInfoPlistStep because it said so
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionInfoPlistStep because it said so
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionInfoPlistStep because it said so
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionAppThinningPlistStep
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionAppThinningPlistStep because it said so
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionCompileBitcodeStep
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionCompileBitcodeStep because it said so
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionCodeSlimmingStep
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionCopyBCSymbolMapsStep
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionCopyBCSymbolMapsStep because it said so
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionCopyBCSymbolMapsStep because it said so
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionCopyBCSymbolMapsStep because it said so
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionSymbolsStep
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionSymbolsStep because it said so
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionSymbolsStep because it said so
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionSymbolsStep because it said so
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionAppThinningStep
2024-12-20 06:30:35 +0000  Skipping step: IDEDistributionAppThinningStep because it said so
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionArchThinningStep
2024-12-20 06:30:35 +0000  Running /Users/<USER>/Downloads/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/hermes.framework/hermes' '-verify_arch' 'arm64e'
2024-12-20 06:30:35 +0000  /Users/<USER>/Downloads/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2024-12-20 06:30:35 +0000  Skipping architecture thinning for item "hermes" because arch "arm64e" wasn't found
2024-12-20 06:30:35 +0000  Running /Users/<USER>/Downloads/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/libswift_Concurrency.dylib' '-verify_arch' 'arm64e'
2024-12-20 06:30:35 +0000  /Users/<USER>/Downloads/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2024-12-20 06:30:35 +0000  Skipping architecture thinning for item "libswift_Concurrency.dylib" because arch "arm64e" wasn't found
2024-12-20 06:30:35 +0000  Running /Users/<USER>/Downloads/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Waqti' '-verify_arch' 'arm64e'
2024-12-20 06:30:35 +0000  /Users/<USER>/Downloads/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2024-12-20 06:30:35 +0000  Skipping architecture thinning for item "Waqti" because arch "arm64e" wasn't found
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionODRStep
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionStripXattrsStep
2024-12-20 06:30:35 +0000  Skipping stripping extended attributes because the codesign step will strip them.
2024-12-20 06:30:35 +0000  Skipping stripping extended attributes because the codesign step will strip them.
2024-12-20 06:30:35 +0000  Skipping stripping extended attributes because the codesign step will strip them.
2024-12-20 06:30:35 +0000  Processing step: IDEDistributionCodesignStep
2024-12-20 06:30:35 +0000  Entitlements for <IDEDistributionItem: 0x60006c903600; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x60004caa01c0:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c3dada0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000299e5f80; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='(null)', teamID='7KJ68PDGJG', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x600090642640:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'>: {
}
2024-12-20 06:30:35 +0000  Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2024-12-20 06:30:35 +0000  Entitlements for <IDEDistributionItem: 0x60006c903600; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x60004caa01c0:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c3dada0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x60000d961bc0; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='(null)', teamID='7KJ68PDGJG', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x600090642640:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'> are: {
}
2024-12-20 06:30:35 +0000  Writing entitlements for <IDEDistributionItem: 0x60006c903600; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x60004caa01c0:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c3dada0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x60000d960cc0; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='(null)', teamID='7KJ68PDGJG', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x600090642640:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'> to: /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/entitlements~~~w7W50M
2024-12-20 06:30:35 +0000  Running /usr/bin/codesign '-vvv' '--force' '--sign' 'B8CB00C4C96DB8428B2C501B62EF0318CC01A071' '--entitlements' '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/entitlements~~~w7W50M' '--generate-entitlement-der' '--preserve-metadata=identifier,flags,runtime' '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/hermes.framework'
2024-12-20 06:30:35 +0000  /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/hermes.framework: replacing existing signature
2024-12-20 06:30:35 +0000  /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/hermes.framework: signed bundle with Mach-O thin (arm64) [dev.hermesengine.iphoneos]
2024-12-20 06:30:35 +0000  /usr/bin/codesign exited with 0
2024-12-20 06:30:35 +0000  Entitlements for <IDEDistributionItem: 0x60006c902160; bundleID='com.apple.dt.runtime.swiftConcurrency', path='<DVTFilePath:0x600070cdcd80:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/libswift_Concurrency.dylib'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c30a580; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000299ef180; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='(null)', teamID='7KJ68PDGJG', identifier='com.apple.dt.runtime.swiftConcurrency', executablePath='<DVTFilePath:0x600070cdcd80:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/libswift_Concurrency.dylib'>', hardenedRuntime='0'>'>: {
}
2024-12-20 06:30:35 +0000  Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2024-12-20 06:30:35 +0000  Entitlements for <IDEDistributionItem: 0x60006c902160; bundleID='com.apple.dt.runtime.swiftConcurrency', path='<DVTFilePath:0x600070cdcd80:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/libswift_Concurrency.dylib'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c30a580; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x60000d961bc0; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='(null)', teamID='7KJ68PDGJG', identifier='com.apple.dt.runtime.swiftConcurrency', executablePath='<DVTFilePath:0x600070cdcd80:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/libswift_Concurrency.dylib'>', hardenedRuntime='0'>'> are: {
}
2024-12-20 06:30:35 +0000  Writing entitlements for <IDEDistributionItem: 0x60006c902160; bundleID='com.apple.dt.runtime.swiftConcurrency', path='<DVTFilePath:0x600070cdcd80:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/libswift_Concurrency.dylib'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c30a580; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000299e5f80; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='(null)', teamID='7KJ68PDGJG', identifier='com.apple.dt.runtime.swiftConcurrency', executablePath='<DVTFilePath:0x600070cdcd80:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Frameworks/libswift_Concurrency.dylib'>', hardenedRuntime='0'>'> to: /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/entitlements~~~hfmdt8
2024-12-20 06:30:35 +0000  Running /usr/bin/codesign '-vvv' '--force' '--sign' 'B8CB00C4C96DB8428B2C501B62EF0318CC01A071' '--entitlements' '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/entitlements~~~hfmdt8' '--generate-entitlement-der' '--preserve-metadata=identifier,flags,runtime' '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/libswift_Concurrency.dylib'
2024-12-20 06:30:35 +0000  /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/libswift_Concurrency.dylib: replacing existing signature
2024-12-20 06:30:35 +0000  /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/libswift_Concurrency.dylib: signed Mach-O thin (arm64) [com.apple.dt.runtime.swiftConcurrency]
2024-12-20 06:30:35 +0000  /usr/bin/codesign exited with 0
2024-12-20 06:30:35 +0000  Entitlements for <IDEDistributionItem: 0x60006c975560; bundleID='com.waqtiApp', path='<DVTFilePath:0x60004caee140:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c3080a0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000299e6340; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='{
    "application-identifier" = "7KJ68PDGJG.com.waqtiApp";
    "aps-environment" = development;
    "com.apple.developer.applesignin" =     (
        Default
    );
    "com.apple.developer.team-identifier" = 7KJ68PDGJG;
    "get-task-allow" = 1;
}', teamID='7KJ68PDGJG', identifier='com.waqtiApp', executablePath='<DVTFilePath:0x60006c91f420:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Waqti'>', hardenedRuntime='0'>'>: {
    "application-identifier" = "7KJ68PDGJG.com.waqtiApp";
    "aps-environment" = development;
    "com.apple.developer.applesignin" =     (
        Default
    );
    "com.apple.developer.team-identifier" = 7KJ68PDGJG;
    "get-task-allow" = 1;
}
2024-12-20 06:30:36 +0000  Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2024-12-20 06:30:36 +0000  Entitlements for <IDEDistributionItem: 0x60006c975560; bundleID='com.waqtiApp', path='<DVTFilePath:0x60004caee140:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c3080a0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000096e70c0; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='{
    "application-identifier" = "7KJ68PDGJG.com.waqtiApp";
    "aps-environment" = development;
    "com.apple.developer.applesignin" =     (
        Default
    );
    "com.apple.developer.team-identifier" = 7KJ68PDGJG;
    "get-task-allow" = 1;
}', teamID='7KJ68PDGJG', identifier='com.waqtiApp', executablePath='<DVTFilePath:0x60006c91f420:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Waqti'>', hardenedRuntime='0'>'> are: {
    "application-identifier" = "7KJ68PDGJG.com.waqtiApp";
    "aps-environment" = development;
    "com.apple.developer.applesignin" =     (
        Default
    );
    "com.apple.developer.team-identifier" = 7KJ68PDGJG;
    "get-task-allow" = 1;
}
2024-12-20 06:30:36 +0000  Writing entitlements for <IDEDistributionItem: 0x60006c975560; bundleID='com.waqtiApp', path='<DVTFilePath:0x60004caee140:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60008c3080a0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000299dfb40; name='Apple Development: Mohammed Abdulla Almulla (X3WMZQBAB8)', hash='B8CB00C4C96DB8428B2C501B62EF0318CC01A071', serialNumber='5C3508915ACF124AEE3A4E79CCC8D909', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2024-10-10 12:23:51 +0000''>', entitlements='{
    "application-identifier" = "7KJ68PDGJG.com.waqtiApp";
    "aps-environment" = development;
    "com.apple.developer.applesignin" =     (
        Default
    );
    "com.apple.developer.team-identifier" = 7KJ68PDGJG;
    "get-task-allow" = 1;
}', teamID='7KJ68PDGJG', identifier='com.waqtiApp', executablePath='<DVTFilePath:0x60006c91f420:'/Users/<USER>/Library/Developer/Xcode/Archives/2024-12-20/Waqti 20-12-24, 11.59.xcarchive/Products/Applications/Waqti.app/Waqti'>', hardenedRuntime='0'>'> to: /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/entitlements~~~KOSL2t
2024-12-20 06:30:36 +0000  Running /usr/bin/codesign '-vvv' '--force' '--sign' 'B8CB00C4C96DB8428B2C501B62EF0318CC01A071' '--entitlements' '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/entitlements~~~KOSL2t' '--generate-entitlement-der' '--preserve-metadata=identifier,flags,runtime' '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app'
2024-12-20 06:30:36 +0000  /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app: replacing existing signature
2024-12-20 06:30:36 +0000  /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app: signed app bundle with Mach-O thin (arm64) [com.waqtiApp]
2024-12-20 06:30:36 +0000  /usr/bin/codesign exited with 0
2024-12-20 06:30:36 +0000  Processing step: IDEDistributionZipODRItemStep
2024-12-20 06:30:36 +0000  Skipping step: IDEDistributionZipODRItemStep because it said so
2024-12-20 06:30:36 +0000  Skipping step: IDEDistributionZipODRItemStep because it said so
2024-12-20 06:30:36 +0000  Skipping step: IDEDistributionZipODRItemStep because it said so
2024-12-20 06:30:36 +0000  Processing step: IDEDistributionCreateIPAStep
2024-12-20 06:30:36 +0000  Running /usr/bin/ditto '-V' '-c' '-k' '--norsrc' '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root' '/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Packages/Waqti.ipa'
2024-12-20 06:30:36 +0000  >>> Copying /var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root 
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/_CodeSignature/CodeResources ... 
2024-12-20 06:30:36 +0000  146679 bytes for ./Payload/Waqti.app/_CodeSignature/CodeResources
copying file ./Payload/Waqti.app/Fontisto.ttf ... 
2024-12-20 06:30:36 +0000  313528 bytes for ./Payload/Waqti.app/Fontisto.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/Octicons.ttf ... 
2024-12-20 06:30:36 +0000  49404 bytes for ./Payload/Waqti.app/Octicons.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/Feather.ttf ... 
2024-12-20 06:30:36 +0000  56228 bytes for ./Payload/Waqti.app/Feather.ttf
copying file ./Payload/Waqti.app/Manrope-Semibold_wght.ttf ... 
2024-12-20 06:30:36 +0000  164936 bytes for ./Payload/Waqti.app/Manrope-Semibold_wght.ttf
copying file ./Payload/Waqti.app/fonnts.com-ProximaSansMedium.ttf ... 
2024-12-20 06:30:36 +0000  50564 bytes for ./Payload/Waqti.app/fonnts.com-ProximaSansMedium.ttf
copying file ./Payload/Waqti.app/FontAwesome6_Regular.ttf ... 
2024-12-20 06:30:36 +0000  63348 bytes for ./Payload/Waqti.app/FontAwesome6_Regular.ttf
copying file ./Payload/Waqti.app/Entypo.ttf ... 
2024-12-20 06:30:36 +0000  66200 bytes for ./Payload/Waqti.app/Entypo.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/LottiePrivacyInfo.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:36 +0000  599 bytes for ./Payload/Waqti.app/LottiePrivacyInfo.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/Waqti.app/LottiePrivacyInfo.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  782 bytes for ./Payload/Waqti.app/LottiePrivacyInfo.bundle/Info.plist
copying file ./Payload/Waqti.app/Manrope-VariableFont_wght.ttf ... 
2024-12-20 06:30:36 +0000  164936 bytes for ./Payload/Waqti.app/Manrope-VariableFont_wght.ttf
copying file ./Payload/Waqti.app/Manrope-Medium.ttf ... 
2024-12-20 06:30:36 +0000  137768 bytes for ./Payload/Waqti.app/Manrope-Medium.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/Petrona-Bold.ttf ... 
2024-12-20 06:30:36 +0000  218708 bytes for ./Payload/Waqti.app/Petrona-Bold.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/de.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1131 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/de.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/zh-Hans.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1119 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/zh-Hans.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/ja.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1214 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/ja.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/en.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1064 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/en.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/uk.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1398 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/uk.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib/runtime.nib ... 
2024-12-20 06:30:36 +0000  10065 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib/runtime.nib
copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib/objects-12.3+.nib ... 
2024-12-20 06:30:36 +0000  10292 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib/objects-12.3+.nib
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsViewController.nib/runtime.nib ... 
2024-12-20 06:30:36 +0000  1800 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsViewController.nib/runtime.nib
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsViewController.nib/objects-12.3+.nib ... 
2024-12-20 06:30:36 +0000  1800 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsViewController.nib/objects-12.3+.nib
copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAssetsViewController.nib/runtime.nib ... 
2024-12-20 06:30:36 +0000  1393 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAssetsViewController.nib/runtime.nib
copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAssetsViewController.nib/objects-12.3+.nib ... 
2024-12-20 06:30:36 +0000  1393 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAssetsViewController.nib/objects-12.3+.nib
copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib/runtime.nib ... 
2024-12-20 06:30:36 +0000  7037 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib/runtime.nib
copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib/objects-12.3+.nib ... 
2024-12-20 06:30:36 +0000  7037 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib/objects-12.3+.nib
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsNavigationController.nib/runtime.nib ... 
2024-12-20 06:30:36 +0000  2631 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsNavigationController.nib/runtime.nib
copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsNavigationController.nib/objects-12.3+.nib ... 
2024-12-20 06:30:36 +0000  2631 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/QBAlbumsNavigationController.nib/objects-12.3+.nib
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/Info.plist ... 
2024-12-20 06:30:36 +0000  289 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/QBImagePicker.storyboardc/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/nb.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1076 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/nb.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/es.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1113 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/es.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/da.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1015 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/da.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/it.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1088 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/it.lproj/QBImagePicker.strings
copying file ./Payload/Waqti.app/QBImagePicker.bundle/sv.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1082 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/sv.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/ko.lproj/QBImagePicker.strings ... 
1139 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/ko.lproj/QBImagePicker.strings
copying file ./Payload/Waqti.app/QBImagePicker.bundle/zh-Hant.lproj/QBImagePicker.strings ... 
1119 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/zh-Hant.lproj/QBImagePicker.strings
copying file ./Payload/Waqti.app/QBImagePicker.bundle/tr.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1104 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/tr.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/pl.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1119 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/pl.lproj/QBImagePicker.strings
copying file ./Payload/Waqti.app/QBImagePicker.bundle/vi.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1143 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/vi.lproj/QBImagePicker.strings
copying file ./Payload/Waqti.app/QBImagePicker.bundle/ru.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1433 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/ru.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/fr.lproj/QBImagePicker.strings ... 
1153 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/fr.lproj/QBImagePicker.strings
copying file ./Payload/Waqti.app/QBImagePicker.bundle/fi.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1002 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/fi.lproj/QBImagePicker.strings
copying file ./Payload/Waqti.app/QBImagePicker.bundle/nl.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1017 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/nl.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/pt.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1112 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/pt.lproj/QBImagePicker.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/QBImagePicker.bundle/ro.lproj/QBImagePicker.strings ... 
2024-12-20 06:30:36 +0000  1127 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/ro.lproj/QBImagePicker.strings
copying file ./Payload/Waqti.app/QBImagePicker.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  772 bytes for ./Payload/Waqti.app/QBImagePicker.bundle/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/FontAwesome5_Brands.ttf ... 
2024-12-20 06:30:36 +0000  134040 bytes for ./Payload/Waqti.app/FontAwesome5_Brands.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/MaterialCommunityIcons.ttf ... 
2024-12-20 06:30:36 +0000  1147844 bytes for ./Payload/Waqti.app/MaterialCommunityIcons.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:36 +0000  373 bytes for ./Payload/Waqti.app/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/FBLPromises_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  785 bytes for ./Payload/Waqti.app/FBLPromises_Privacy.bundle/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/fonnts.com-Proxima_Nova_Condensed_Bold.ttf ... 
2024-12-20 06:30:36 +0000  51444 bytes for ./Payload/Waqti.app/fonnts.com-Proxima_Nova_Condensed_Bold.ttf
copying file ./Payload/Waqti.app/Manrope-SemiBold.ttf ... 
2024-12-20 06:30:36 +0000  138360 bytes for ./Payload/Waqti.app/Manrope-SemiBold.ttf
copying file ./Payload/Waqti.app/Petrona-Regular.ttf ... 
2024-12-20 06:30:36 +0000  124820 bytes for ./Payload/Waqti.app/Petrona-Regular.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/<EMAIL> ... 
2024-12-20 06:30:36 +0000  13658 bytes for ./Payload/Waqti.app/<EMAIL>
copying file ./Payload/Waqti.app/AntDesign.ttf ... 
2024-12-20 06:30:36 +0000  70344 bytes for ./Payload/Waqti.app/AntDesign.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/Lottie_React_Native_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:36 +0000  373 bytes for ./Payload/Waqti.app/Lottie_React_Native_Privacy.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/Lottie_React_Native_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  802 bytes for ./Payload/Waqti.app/Lottie_React_Native_Privacy.bundle/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/Foundation.ttf ... 
2024-12-20 06:30:36 +0000  56976 bytes for ./Payload/Waqti.app/Foundation.ttf
copying file ./Payload/Waqti.app/Ionicons.ttf ... 
2024-12-20 06:30:36 +0000  442604 bytes for ./Payload/Waqti.app/Ionicons.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/FontAwesome5_Solid.ttf ... 
2024-12-20 06:30:36 +0000  202744 bytes for ./Payload/Waqti.app/FontAwesome5_Solid.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:36 +0000  855 bytes for ./Payload/Waqti.app/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/FirebaseCoreInternal_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  806 bytes for ./Payload/Waqti.app/FirebaseCoreInternal_Privacy.bundle/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/FontAwesome5_Regular.ttf ... 
2024-12-20 06:30:36 +0000  33736 bytes for ./Payload/Waqti.app/FontAwesome5_Regular.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/RNDeviceInfoPrivacyInfo.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:36 +0000  1159 bytes for ./Payload/Waqti.app/RNDeviceInfoPrivacyInfo.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/RNDeviceInfoPrivacyInfo.bundle/Info.plist ... 
794 bytes for ./Payload/Waqti.app/RNDeviceInfoPrivacyInfo.bundle/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GTMAppAuth_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:36 +0000  373 bytes for ./Payload/Waqti.app/GTMAppAuth_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/Waqti.app/GTMAppAuth_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  784 bytes for ./Payload/Waqti.app/GTMAppAuth_Privacy.bundle/Info.plist
copying file ./Payload/Waqti.app/GoogleService-Info.plist ... 
2024-12-20 06:30:36 +0000  756 bytes for ./Payload/Waqti.app/GoogleService-Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/fonnts.com-Proxima_Nova_Light.otf ... 
2024-12-20 06:30:36 +0000  62968 bytes for ./Payload/Waqti.app/fonnts.com-Proxima_Nova_Light.otf
copying file ./Payload/Waqti.app/FontAwesome.ttf ... 
2024-12-20 06:30:36 +0000  165548 bytes for ./Payload/Waqti.app/FontAwesome.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GTMSessionFetcher_Core_Privacy.bundle/PrivacyInfo.xcprivacy ... 
598 bytes for ./Payload/Waqti.app/GTMSessionFetcher_Core_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/Waqti.app/GTMSessionFetcher_Core_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  808 bytes for ./Payload/Waqti.app/GTMSessionFetcher_Core_Privacy.bundle/Info.plist
copying file ./Payload/Waqti.app/RNImageCropPickerPrivacyInfo.bundle/PrivacyInfo.xcprivacy ... 
905 bytes for ./Payload/Waqti.app/RNImageCropPickerPrivacyInfo.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/Waqti.app/RNImageCropPickerPrivacyInfo.bundle/Info.plist ... 
804 bytes for ./Payload/Waqti.app/RNImageCropPickerPrivacyInfo.bundle/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/de.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1703 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/de.lproj/GoogleSignIn.strings
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/he.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1780 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/he.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/ar.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1759 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/ar.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/el.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  2103 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/el.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/ja.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1711 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/ja.lproj/GoogleSignIn.strings
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/en.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1547 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/en.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/uk.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1864 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/uk.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/es_MX.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1607 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/es_MX.lproj/GoogleSignIn.strings
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/zh_CN.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1489 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/zh_CN.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/nb.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1593 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/nb.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/es.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1678 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/es.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/pt_BR.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1608 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/pt_BR.lproj/GoogleSignIn.strings
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/da.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1611 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/da.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/it.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1570 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/it.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/sk.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1665 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/sk.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/pt_PT.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1646 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/pt_PT.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/<EMAIL> ... 
2024-12-20 06:30:36 +0000  1773 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/<EMAIL>
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/ms.lproj/GoogleSignIn.strings ... 
1565 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/ms.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/sv.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1579 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/sv.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/cs.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1615 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/cs.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/<EMAIL> ... 
2024-12-20 06:30:36 +0000  1166 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/<EMAIL>
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/ko.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1634 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/ko.lproj/GoogleSignIn.strings
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/hu.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1663 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/hu.lproj/GoogleSignIn.strings
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/tr.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1596 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/tr.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/pl.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1578 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/pl.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/zh_TW.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1525 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/zh_TW.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/en_GB.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1547 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/en_GB.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/vi.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1717 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/vi.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:36 +0000  3756 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/ru.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1954 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/ru.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/fr_CA.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1657 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/fr_CA.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/fr.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1676 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/fr.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/fi.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1640 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/fi.lproj/GoogleSignIn.strings
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/id.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1553 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/id.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/nl.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1606 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/nl.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/th.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  2133 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/th.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/pt.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1608 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/pt.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/google.png ... 
2024-12-20 06:30:36 +0000  643 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/google.png
copying file ./Payload/Waqti.app/GoogleSignIn.bundle/Roboto-Bold.ttf ... 
2024-12-20 06:30:36 +0000  127744 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/Roboto-Bold.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/ro.lproj/GoogleSignIn.strings ... 
1598 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/ro.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  770 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/hr.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  1621 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/hr.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/hi.lproj/GoogleSignIn.strings ... 
2024-12-20 06:30:36 +0000  2286 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/hi.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleSignIn.bundle/ca.lproj/GoogleSignIn.strings ... 
1669 bytes for ./Payload/Waqti.app/GoogleSignIn.bundle/ca.lproj/GoogleSignIn.strings
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/Zocial.ttf ... 
2024-12-20 06:30:36 +0000  25788 bytes for ./Payload/Waqti.app/Zocial.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/RNPermissionsPrivacyInfo.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:36 +0000  598 bytes for ./Payload/Waqti.app/RNPermissionsPrivacyInfo.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/RNPermissionsPrivacyInfo.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  796 bytes for ./Payload/Waqti.app/RNPermissionsPrivacyInfo.bundle/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/Assets.car ... 
2024-12-20 06:30:36 +0000  302264 bytes for ./Payload/Waqti.app/Assets.car
copying file ./Payload/Waqti.app/EvilIcons.ttf ... 
2024-12-20 06:30:36 +0000  13456 bytes for ./Payload/Waqti.app/EvilIcons.ttf
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/AppIcon76x76@2x~ipad.png ... 
2024-12-20 06:30:36 +0000  17266 bytes for ./Payload/Waqti.app/AppIcon76x76@2x~ipad.png
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/AppAuthCore_Privacy.bundle/PrivacyInfo.xcprivacy ... 
391 bytes for ./Payload/Waqti.app/AppAuthCore_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/Waqti.app/AppAuthCore_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  785 bytes for ./Payload/Waqti.app/AppAuthCore_Privacy.bundle/Info.plist
copying file ./Payload/Waqti.app/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy ... 
1289 bytes for ./Payload/Waqti.app/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/GoogleUtilities_Privacy.bundle/Info.plist ... 
2024-12-20 06:30:36 +0000  794 bytes for ./Payload/Waqti.app/GoogleUtilities_Privacy.bundle/Info.plist
2024-12-20 06:30:36 +0000  copying file ./Payload/Waqti.app/main.jsbundle ... 
2024-12-20 06:30:37 +0000  5449581 bytes for ./Payload/Waqti.app/main.jsbundle
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/de.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/de.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1312 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/he.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/he.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1444 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ar.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ar.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1500 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/el.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/el.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1600 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings ... 
64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1296 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ja.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ja.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1436 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/en.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/en.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1296 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/uk.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/uk.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1564 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/nb.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/nb.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1296 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings ... 
64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1268 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/es.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/es.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1364 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/da.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/da.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1300 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/it.lproj/Localizable.strings ... 
64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/it.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1388 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/sk.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/sk.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1356 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1368 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ms.lproj/Localizable.strings ... 
64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ms.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1292 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/sv.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/sv.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1300 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/cs.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/cs.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1368 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ko.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ko.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1332 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1308 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/hu.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/hu.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1348 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/tr.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/tr.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1360 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/pl.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/pl.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1340 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/vi.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/vi.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1352 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ru.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ru.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1576 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/fr.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/fr.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1360 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/fi.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/fi.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1340 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/id.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/id.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1300 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/nl.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/nl.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1324 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/th.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/th.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1784 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/pt.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/pt.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1356 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/zu.lproj/Localizable.strings ... 
64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/zu.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  2076 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1372 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ro.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ro.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1344 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/Info.plist ... 
2024-12-20 06:30:37 +0000  775 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/Info.plist
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/hr.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/hr.lproj/Localizable.strings
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1352 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/hi.lproj/Localizable.strings ... 
2024-12-20 06:30:37 +0000  64 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/hi.lproj/Localizable.strings
copying file ./Payload/Waqti.app/RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin ... 
2024-12-20 06:30:37 +0000  1640 bytes for ./Payload/Waqti.app/RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin
copying file ./Payload/Waqti.app/FontAwesome6_Solid.ttf ... 
2024-12-20 06:30:37 +0000  394668 bytes for ./Payload/Waqti.app/FontAwesome6_Solid.ttf
copying file ./Payload/Waqti.app/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:37 +0000  1283 bytes for ./Payload/Waqti.app/PrivacyInfo.xcprivacy
copying file ./Payload/Waqti.app/Manrope-Regular.ttf ... 
2024-12-20 06:30:37 +0000  137148 bytes for ./Payload/Waqti.app/Manrope-Regular.ttf
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib ... 
2024-12-20 06:30:37 +0000  3813 bytes for ./Payload/Waqti.app/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib ... 
2024-12-20 06:30:37 +0000  924 bytes for ./Payload/Waqti.app/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib
2024-12-20 06:30:37 +0000  copying file ./Payload/Waqti.app/LaunchScreen.storyboardc/Info.plist ... 
2024-12-20 06:30:37 +0000  258 bytes for ./Payload/Waqti.app/LaunchScreen.storyboardc/Info.plist
copying file ./Payload/Waqti.app/Petrona-Medium.ttf ... 
2024-12-20 06:30:37 +0000  218708 bytes for ./Payload/Waqti.app/Petrona-Medium.ttf
copying file ./Payload/Waqti.app/Waqti ... 
2024-12-20 06:30:38 +0000  9717024 bytes for ./Payload/Waqti.app/Waqti
copying file ./Payload/Waqti.app/Petrona-SemiBold.ttf ... 
2024-12-20 06:30:38 +0000  126576 bytes for ./Payload/Waqti.app/Petrona-SemiBold.ttf
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/de.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  189 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/de.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ar.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  207 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ar.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/zh-Hans.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  172 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/zh-Hans.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ja.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  210 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ja.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/fa.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  210 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/fa.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/en.lproj/TOCropViewControllerLocalizable.strings ... 
168 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/en.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/uk.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  231 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/uk.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/es.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  179 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/es.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/it.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  181 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/it.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/sk.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  176 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/sk.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ms.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  173 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ms.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/cs.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  173 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/cs.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ko.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  187 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ko.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/Base.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  168 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/Base.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/zh-Hant.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  175 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/zh-Hant.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/fa-IR.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  210 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/fa-IR.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/hu.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  194 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/hu.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/da-DK.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  174 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/da-DK.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/tr.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  183 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/tr.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/pl.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  171 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/pl.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/pt-BR.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  181 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/pt-BR.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/vi.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  178 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/vi.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:38 +0000  373 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/PrivacyInfo.xcprivacy
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ru.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  237 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ru.lproj/TOCropViewControllerLocalizable.strings
copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/fr.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  193 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/fr.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/fi.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  179 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/fi.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/id.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  178 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/id.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/nl.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  178 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/nl.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/pt.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  181 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/pt.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ro.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  181 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ro.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/Info.plist ... 
2024-12-20 06:30:38 +0000  800 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/Info.plist
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ca.lproj/TOCropViewControllerLocalizable.strings ... 
2024-12-20 06:30:38 +0000  177 bytes for ./Payload/Waqti.app/TOCropViewControllerBundle.bundle/ca.lproj/TOCropViewControllerLocalizable.strings
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/Frameworks/libswift_Concurrency.dylib ... 
2024-12-20 06:30:38 +0000  557952 bytes for ./Payload/Waqti.app/Frameworks/libswift_Concurrency.dylib
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/Frameworks/hermes.framework/_CodeSignature/CodeResources ... 
2024-12-20 06:30:38 +0000  1798 bytes for ./Payload/Waqti.app/Frameworks/hermes.framework/_CodeSignature/CodeResources
copying file ./Payload/Waqti.app/Frameworks/hermes.framework/hermes ... 
2024-12-20 06:30:38 +0000  4445056 bytes for ./Payload/Waqti.app/Frameworks/hermes.framework/hermes
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/Frameworks/hermes.framework/Info.plist ... 
2024-12-20 06:30:38 +0000  815 bytes for ./Payload/Waqti.app/Frameworks/hermes.framework/Info.plist
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/SimpleLineIcons.ttf ... 
2024-12-20 06:30:38 +0000  54056 bytes for ./Payload/Waqti.app/SimpleLineIcons.ttf
copying file ./Payload/Waqti.app/fonnts.com-ProximaSansRegular.ttf ... 
2024-12-20 06:30:38 +0000  50788 bytes for ./Payload/Waqti.app/fonnts.com-ProximaSansRegular.ttf
copying file ./Payload/Waqti.app/FontAwesome6_Brands.ttf ... 
2024-12-20 06:30:38 +0000  189684 bytes for ./Payload/Waqti.app/FontAwesome6_Brands.ttf
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/embedded.mobileprovision ... 
2024-12-20 06:30:38 +0000  16932 bytes for ./Payload/Waqti.app/embedded.mobileprovision
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2024-12-20 06:30:38 +0000  761 bytes for ./Payload/Waqti.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
copying file ./Payload/Waqti.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2024-12-20 06:30:38 +0000  405 bytes for ./Payload/Waqti.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon.png ... 
2024-12-20 06:30:38 +0000  290 bytes for ./Payload/Waqti.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon.png
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png ... 
2024-12-20 06:30:38 +0000  913 bytes for ./Payload/Waqti.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:38 +0000  1372 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:38 +0000  1038 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/info.png ... 
2024-12-20 06:30:38 +0000  453 bytes for ./Payload/Waqti.app/assets/src/assets/img/info.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
748 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:38 +0000  1159 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:38 +0000  1227 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/add.png ... 
2024-12-20 06:30:38 +0000  1927 bytes for ./Payload/Waqti.app/assets/src/assets/img/add.png
copying file ./Payload/Waqti.app/assets/src/assets/img/angle-right.png ... 
2024-12-20 06:30:38 +0000  137 bytes for ./Payload/Waqti.app/assets/src/assets/img/angle-right.png
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/whitelock.png ... 
2024-12-20 06:30:38 +0000  253 bytes for ./Payload/Waqti.app/assets/src/assets/img/whitelock.png
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Plus.png ... 
2024-12-20 06:30:38 +0000  229 bytes for ./Payload/Waqti.app/assets/src/assets/img/Plus.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:38 +0000  854 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/liveClock.png ... 
2024-12-20 06:30:38 +0000  2838 bytes for ./Payload/Waqti.app/assets/src/assets/img/liveClock.png
copying file ./Payload/Waqti.app/assets/src/assets/img/FavoriteGroup.png ... 
2024-12-20 06:30:38 +0000  742 bytes for ./Payload/Waqti.app/assets/src/assets/img/FavoriteGroup.png
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Group279340.png ... 
2024-12-20 06:30:38 +0000  430 bytes for ./Payload/Waqti.app/assets/src/assets/img/Group279340.png
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
234 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:38 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:38 +0000  762 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:38 +0000  692 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:38 +0000  3337 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/Tick.png ... 
2024-12-20 06:30:38 +0000  1531 bytes for ./Payload/Waqti.app/assets/src/assets/img/Tick.png
copying file ./Payload/Waqti.app/assets/src/assets/img/Icon.png ... 
2024-12-20 06:30:38 +0000  515 bytes for ./Payload/Waqti.app/assets/src/assets/img/Icon.png
copying file ./Payload/Waqti.app/assets/src/assets/img/newBackground.png ... 
2024-12-20 06:30:39 +0000  6330945 bytes for ./Payload/Waqti.app/assets/src/assets/img/newBackground.png
copying file ./Payload/Waqti.app/assets/src/assets/img/eyemid.png ... 
2024-12-20 06:30:39 +0000  711 bytes for ./Payload/Waqti.app/assets/src/assets/img/eyemid.png
copying file ./Payload/Waqti.app/assets/src/assets/img/deliver.png ... 
819 bytes for ./Payload/Waqti.app/assets/src/assets/img/deliver.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  526 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/sucess.png ... 
2024-12-20 06:30:39 +0000  2105 bytes for ./Payload/Waqti.app/assets/src/assets/img/sucess.png
copying file ./Payload/Waqti.app/assets/src/assets/img/filledLayer.png ... 
2024-12-20 06:30:39 +0000  451 bytes for ./Payload/Waqti.app/assets/src/assets/img/filledLayer.png
copying file ./Payload/Waqti.app/assets/src/assets/img/flag.png ... 
351 bytes for ./Payload/Waqti.app/assets/src/assets/img/flag.png
copying file ./Payload/Waqti.app/assets/src/assets/img/Group81.png ... 
2024-12-20 06:30:39 +0000  481 bytes for ./Payload/Waqti.app/assets/src/assets/img/Group81.png
copying file ./Payload/Waqti.app/assets/src/assets/img/deleteImg.png ... 
2024-12-20 06:30:39 +0000  3118 bytes for ./Payload/Waqti.app/assets/src/assets/img/deleteImg.png
copying file ./Payload/Waqti.app/assets/src/assets/img/filledFav.png ... 
2024-12-20 06:30:39 +0000  502 bytes for ./Payload/Waqti.app/assets/src/assets/img/filledFav.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  562 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/discover.png ... 
2024-12-20 06:30:39 +0000  436 bytes for ./Payload/Waqti.app/assets/src/assets/img/discover.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  802 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/backArrowNew.png ... 
2024-12-20 06:30:39 +0000  276 bytes for ./Payload/Waqti.app/assets/src/assets/img/backArrowNew.png
copying file ./Payload/Waqti.app/assets/src/assets/img/MaskGroup.png ... 
2024-12-20 06:30:39 +0000  111836 bytes for ./Payload/Waqti.app/assets/src/assets/img/MaskGroup.png
copying file ./Payload/Waqti.app/assets/src/assets/img/favoriteIcon.png ... 
2024-12-20 06:30:39 +0000  424 bytes for ./Payload/Waqti.app/assets/src/assets/img/favoriteIcon.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  698 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  5314 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1053 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  963 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/xClose.png ... 
2024-12-20 06:30:39 +0000  341 bytes for ./Payload/Waqti.app/assets/src/assets/img/xClose.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  307 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/stopwatch.png ... 
2024-12-20 06:30:39 +0000  414 bytes for ./Payload/Waqti.app/assets/src/assets/img/stopwatch.png
copying file ./Payload/Waqti.app/assets/src/assets/img/Group279420.png ... 
2024-12-20 06:30:39 +0000  430 bytes for ./Payload/Waqti.app/assets/src/assets/img/Group279420.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1073 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/noun-brand-6645268.png ... 
2024-12-20 06:30:39 +0000  473 bytes for ./Payload/Waqti.app/assets/src/assets/img/noun-brand-6645268.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/dobleClock.png ... 
2024-12-20 06:30:39 +0000  1463 bytes for ./Payload/Waqti.app/assets/src/assets/img/dobleClock.png
copying file ./Payload/Waqti.app/assets/src/assets/img/plusGreen.png ... 
2024-12-20 06:30:39 +0000  223 bytes for ./Payload/Waqti.app/assets/src/assets/img/plusGreen.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  833 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1701 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/lock.png ... 
2024-12-20 06:30:39 +0000  430 bytes for ./Payload/Waqti.app/assets/src/assets/img/lock.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1091 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  949 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  724 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  733 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1228 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1993 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/party-horn.png ... 
2024-12-20 06:30:39 +0000  539 bytes for ./Payload/Waqti.app/assets/src/assets/img/party-horn.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  712 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/recent.png ... 
2024-12-20 06:30:39 +0000  426 bytes for ./Payload/Waqti.app/assets/src/assets/img/recent.png
copying file ./Payload/Waqti.app/assets/src/assets/img/myorder.png ... 
2024-12-20 06:30:39 +0000  1129 bytes for ./Payload/Waqti.app/assets/src/assets/img/myorder.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  607 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/buySell.png ... 
2024-12-20 06:30:39 +0000  761 bytes for ./Payload/Waqti.app/assets/src/assets/img/buySell.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  634 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/file.png ... 
2024-12-20 06:30:39 +0000  492 bytes for ./Payload/Waqti.app/assets/src/assets/img/file.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1947 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/angle-down.png ... 
2024-12-20 06:30:39 +0000  141 bytes for ./Payload/Waqti.app/assets/src/assets/img/angle-down.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  3903 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1069 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  299 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/FavouriteActive.png ... 
2024-12-20 06:30:39 +0000  389 bytes for ./Payload/Waqti.app/assets/src/assets/img/FavouriteActive.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  843 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/file1.png ... 
2024-12-20 06:30:39 +0000  370 bytes for ./Payload/Waqti.app/assets/src/assets/img/file1.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Favourite.png ... 
2024-12-20 06:30:39 +0000  400 bytes for ./Payload/Waqti.app/assets/src/assets/img/Favourite.png
copying file ./Payload/Waqti.app/assets/src/assets/img/home.png ... 
2024-12-20 06:30:39 +0000  297 bytes for ./Payload/Waqti.app/assets/src/assets/img/home.png
copying file ./Payload/Waqti.app/assets/src/assets/img/midSizearrow.png ... 
2024-12-20 06:30:39 +0000  229 bytes for ./Payload/Waqti.app/assets/src/assets/img/midSizearrow.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/checkBoxFalse.png ... 
152 bytes for ./Payload/Waqti.app/assets/src/assets/img/checkBoxFalse.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/user.png ... 
2024-12-20 06:30:39 +0000  383 bytes for ./Payload/Waqti.app/assets/src/assets/img/user.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/filterIc.png ... 
2024-12-20 06:30:39 +0000  520 bytes for ./Payload/Waqti.app/assets/src/assets/img/filterIc.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/writeedit.png ... 
2024-12-20 06:30:39 +0000  791 bytes for ./Payload/Waqti.app/assets/src/assets/img/writeedit.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1174 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  745 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  219 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/cart-shopping-fast.png ... 
2024-12-20 06:30:39 +0000  337 bytes for ./Payload/Waqti.app/assets/src/assets/img/cart-shopping-fast.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  6156 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Path.png ... 
403 bytes for ./Payload/Waqti.app/assets/src/assets/img/Path.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1354 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/sorted.png ... 
312 bytes for ./Payload/Waqti.app/assets/src/assets/img/sorted.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
915 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  873 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/payment.png ... 
2024-12-20 06:30:39 +0000  914 bytes for ./Payload/Waqti.app/assets/src/assets/img/payment.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/list.png ... 
2024-12-20 06:30:39 +0000  556 bytes for ./Payload/Waqti.app/assets/src/assets/img/list.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  487 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/whiteAngleDown.png ... 
2024-12-20 06:30:39 +0000  146 bytes for ./Payload/Waqti.app/assets/src/assets/img/whiteAngleDown.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  847 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/nounLocation.png ... 
2024-12-20 06:30:39 +0000  326 bytes for ./Payload/Waqti.app/assets/src/assets/img/nounLocation.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1396 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  490 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/search.png ... 
400 bytes for ./Payload/Waqti.app/assets/src/assets/img/search.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1666 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Minus.png ... 
2024-12-20 06:30:39 +0000  212 bytes for ./Payload/Waqti.app/assets/src/assets/img/Minus.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/DeleveryCar.png ... 
2024-12-20 06:30:39 +0000  722 bytes for ./Payload/Waqti.app/assets/src/assets/img/DeleveryCar.png
copying file ./Payload/Waqti.app/assets/src/assets/img/midwatch.png ... 
2024-12-20 06:30:39 +0000  1577 bytes for ./Payload/Waqti.app/assets/src/assets/img/midwatch.png
copying file ./Payload/Waqti.app/assets/src/assets/img/apple.png ... 
2024-12-20 06:30:39 +0000  388 bytes for ./Payload/Waqti.app/assets/src/assets/img/apple.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  821 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1172 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/Layer.png ... 
2024-12-20 06:30:39 +0000  368 bytes for ./Payload/Waqti.app/assets/src/assets/img/Layer.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  365069 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/menu.png ... 
2024-12-20 06:30:39 +0000  158 bytes for ./Payload/Waqti.app/assets/src/assets/img/menu.png
copying file ./Payload/Waqti.app/assets/src/assets/img/filledShoppingBag.png ... 
2024-12-20 06:30:39 +0000  432 bytes for ./Payload/Waqti.app/assets/src/assets/img/filledShoppingBag.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  584 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  624 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Component.png ... 
2024-12-20 06:30:39 +0000  443 bytes for ./Payload/Waqti.app/assets/src/assets/img/Component.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/shareimg.png ... 
2024-12-20 06:30:39 +0000  2925 bytes for ./Payload/Waqti.app/assets/src/assets/img/shareimg.png
copying file ./Payload/Waqti.app/assets/src/assets/img/enter.png ... 
2024-12-20 06:30:39 +0000  280 bytes for ./Payload/Waqti.app/assets/src/assets/img/enter.png
copying file ./Payload/Waqti.app/assets/src/assets/img/arrows-repeat.png ... 
2024-12-20 06:30:39 +0000  337 bytes for ./Payload/Waqti.app/assets/src/assets/img/arrows-repeat.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/window.png ... 
2024-12-20 06:30:39 +0000  3920 bytes for ./Payload/Waqti.app/assets/src/assets/img/window.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  753 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  773 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Notifications.png ... 
2024-12-20 06:30:39 +0000  427 bytes for ./Payload/Waqti.app/assets/src/assets/img/Notifications.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Group279893.png ... 
2024-12-20 06:30:39 +0000  108863 bytes for ./Payload/Waqti.app/assets/src/assets/img/Group279893.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1103 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  579 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/bidSucces.png ... 
2024-12-20 06:30:39 +0000  1354 bytes for ./Payload/Waqti.app/assets/src/assets/img/bidSucces.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/cartorder.png ... 
2024-12-20 06:30:39 +0000  1316 bytes for ./Payload/Waqti.app/assets/src/assets/img/cartorder.png
copying file ./Payload/Waqti.app/assets/src/assets/img/Rectangle8524.png ... 
2024-12-20 06:30:39 +0000  6705 bytes for ./Payload/Waqti.app/assets/src/assets/img/Rectangle8524.png
copying file ./Payload/Waqti.app/assets/src/assets/img/WAQTI.png ... 
2024-12-20 06:30:39 +0000  43754 bytes for ./Payload/Waqti.app/assets/src/assets/img/WAQTI.png
copying file ./Payload/Waqti.app/assets/src/assets/img/notify.png ... 
2024-12-20 06:30:39 +0000  1733 bytes for ./Payload/Waqti.app/assets/src/assets/img/notify.png
copying file ./Payload/Waqti.app/assets/src/assets/img/comment.png ... 
2024-12-20 06:30:39 +0000  415 bytes for ./Payload/Waqti.app/assets/src/assets/img/comment.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Rectangle8525.png ... 
2024-12-20 06:30:39 +0000  7954 bytes for ./Payload/Waqti.app/assets/src/assets/img/Rectangle8525.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  456 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  847 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/openEye.png ... 
2024-12-20 06:30:39 +0000  605 bytes for ./Payload/Waqti.app/assets/src/assets/img/openEye.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  720532 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/sort.png ... 
2024-12-20 06:30:39 +0000  265 bytes for ./Payload/Waqti.app/assets/src/assets/img/sort.png
copying file ./Payload/Waqti.app/assets/src/assets/img/watch1.png ... 
2024-12-20 06:30:39 +0000  118425 bytes for ./Payload/Waqti.app/assets/src/assets/img/watch1.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1249 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  806 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/Path150459.png ... 
2024-12-20 06:30:39 +0000  258 bytes for ./Payload/Waqti.app/assets/src/assets/img/Path150459.png
copying file ./Payload/Waqti.app/assets/src/assets/img/profile.png ... 
392 bytes for ./Payload/Waqti.app/assets/src/assets/img/profile.png
copying file ./Payload/Waqti.app/assets/src/assets/img/eye.png ... 
2024-12-20 06:30:39 +0000  769 bytes for ./Payload/Waqti.app/assets/src/assets/img/eye.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/phone-rotary.png ... 
2024-12-20 06:30:39 +0000  591 bytes for ./Payload/Waqti.app/assets/src/assets/img/phone-rotary.png
copying file ./Payload/Waqti.app/assets/src/assets/img/user_a.png ... 
2024-12-20 06:30:39 +0000  545 bytes for ./Payload/Waqti.app/assets/src/assets/img/user_a.png
copying file ./Payload/Waqti.app/assets/src/assets/img/location.png ... 
2024-12-20 06:30:39 +0000  635 bytes for ./Payload/Waqti.app/assets/src/assets/img/location.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/Rectangle8526.png ... 
2024-12-20 06:30:39 +0000  6174 bytes for ./Payload/Waqti.app/assets/src/assets/img/Rectangle8526.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1032 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/delete.png ... 
2024-12-20 06:30:39 +0000  484 bytes for ./Payload/Waqti.app/assets/src/assets/img/delete.png
copying file ./Payload/Waqti.app/assets/src/assets/img/frame.png ... 
2024-12-20 06:30:39 +0000  475 bytes for ./Payload/Waqti.app/assets/src/assets/img/frame.png
copying file ./Payload/Waqti.app/assets/src/assets/img/unchecked.png ... 
371 bytes for ./Payload/Waqti.app/assets/src/assets/img/unchecked.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  654 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  822 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
618 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
412 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  845 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/envelope-dot.png ... 
2024-12-20 06:30:39 +0000  432 bytes for ./Payload/Waqti.app/assets/src/assets/img/envelope-dot.png
copying file ./Payload/Waqti.app/assets/src/assets/img/activityicon.png ... 
2024-12-20 06:30:39 +0000  1992 bytes for ./Payload/Waqti.app/assets/src/assets/img/activityicon.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1474 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1162 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  5904 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/startblack.png ... 
2024-12-20 06:30:39 +0000  884 bytes for ./Payload/Waqti.app/assets/src/assets/img/startblack.png
copying file ./Payload/Waqti.app/assets/src/assets/img/favuroit.png ... 
2024-12-20 06:30:39 +0000  516 bytes for ./Payload/Waqti.app/assets/src/assets/img/favuroit.png
copying file ./Payload/Waqti.app/assets/src/assets/img/sendIcon.png ... 
2024-12-20 06:30:39 +0000  503 bytes for ./Payload/Waqti.app/assets/src/assets/img/sendIcon.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  748 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  750 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/filterScale.png ... 
2024-12-20 06:30:39 +0000  391 bytes for ./Payload/Waqti.app/assets/src/assets/img/filterScale.png
copying file ./Payload/Waqti.app/assets/src/assets/img/MessageIcon.png ... 
2024-12-20 06:30:39 +0000  593 bytes for ./Payload/Waqti.app/assets/src/assets/img/MessageIcon.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  926 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/waqtiLogo.png ... 
2024-12-20 06:30:39 +0000  5377 bytes for ./Payload/Waqti.app/assets/src/assets/img/waqtiLogo.png
copying file ./Payload/Waqti.app/assets/src/assets/img/kkk.png ... 
2024-12-20 06:30:39 +0000  108980 bytes for ./Payload/Waqti.app/assets/src/assets/img/kkk.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/editAuction.png ... 
2024-12-20 06:30:39 +0000  3055 bytes for ./Payload/Waqti.app/assets/src/assets/img/editAuction.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1028 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/Union.png ... 
2024-12-20 06:30:39 +0000  1355 bytes for ./Payload/Waqti.app/assets/src/assets/img/Union.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/google.png ... 
2024-12-20 06:30:39 +0000  740 bytes for ./Payload/Waqti.app/assets/src/assets/img/google.png
copying file ./Payload/Waqti.app/assets/src/assets/img/midRating.png ... 
2024-12-20 06:30:39 +0000  1717 bytes for ./Payload/Waqti.app/assets/src/assets/img/midRating.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/addImage.png ... 
2024-12-20 06:30:39 +0000  558 bytes for ./Payload/Waqti.app/assets/src/assets/img/addImage.png
copying file ./Payload/Waqti.app/assets/src/assets/img/shoppingBag.png ... 
2024-12-20 06:30:39 +0000  384 bytes for ./Payload/Waqti.app/assets/src/assets/img/shoppingBag.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/LocationICon.png ... 
2024-12-20 06:30:39 +0000  634 bytes for ./Payload/Waqti.app/assets/src/assets/img/LocationICon.png
copying file ./Payload/Waqti.app/assets/src/assets/img/phone-call.png ... 
2024-12-20 06:30:39 +0000  384 bytes for ./Payload/Waqti.app/assets/src/assets/img/phone-call.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  664 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/newCal.png ... 
2024-12-20 06:30:39 +0000  355 bytes for ./Payload/Waqti.app/assets/src/assets/img/newCal.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1410 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/box.png ... 
2024-12-20 06:30:39 +0000  836 bytes for ./Payload/Waqti.app/assets/src/assets/img/box.png
copying file ./Payload/Waqti.app/assets/src/assets/img/newuser.png ... 
2024-12-20 06:30:39 +0000  2563 bytes for ./Payload/Waqti.app/assets/src/assets/img/newuser.png
copying file ./Payload/Waqti.app/assets/src/assets/img/message.png ... 
2024-12-20 06:30:39 +0000  1644 bytes for ./Payload/Waqti.app/assets/src/assets/img/message.png
copying file ./Payload/Waqti.app/assets/src/assets/img/arrow-small-left.png ... 
2024-12-20 06:30:39 +0000  197 bytes for ./Payload/Waqti.app/assets/src/assets/img/arrow-small-left.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/auction.png ... 
2024-12-20 06:30:39 +0000  284 bytes for ./Payload/Waqti.app/assets/src/assets/img/auction.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  990 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/equals-symbol.png ... 
2024-12-20 06:30:39 +0000  2069 bytes for ./Payload/Waqti.app/assets/src/assets/img/equals-symbol.png
copying file ./Payload/Waqti.app/assets/src/assets/img/back.png ... 
2024-12-20 06:30:39 +0000  1459 bytes for ./Payload/Waqti.app/assets/src/assets/img/back.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1144 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/check-circle.png ... 
2024-12-20 06:30:39 +0000  330 bytes for ./Payload/Waqti.app/assets/src/assets/img/check-circle.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/popular.png ... 
2024-12-20 06:30:39 +0000  400 bytes for ./Payload/Waqti.app/assets/src/assets/img/popular.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  3738 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1598 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/bookmarkNew.png ... 
2024-12-20 06:30:39 +0000  305 bytes for ./Payload/Waqti.app/assets/src/assets/img/bookmarkNew.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/roundfilter.png ... 
2024-12-20 06:30:39 +0000  1637 bytes for ./Payload/Waqti.app/assets/src/assets/img/roundfilter.png
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  942 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  318 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1254 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/checkBoxTrue.png ... 
2024-12-20 06:30:39 +0000  277 bytes for ./Payload/Waqti.app/assets/src/assets/img/checkBoxTrue.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  928 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/Welcome.png ... 
2024-12-20 06:30:39 +0000  11979207 bytes for ./Payload/Waqti.app/assets/src/assets/img/Welcome.png
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1218 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
copying file ./Payload/Waqti.app/assets/src/assets/img/<EMAIL> ... 
2024-12-20 06:30:39 +0000  1011 bytes for ./Payload/Waqti.app/assets/src/assets/img/<EMAIL>
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/Manrope-Bold.ttf ... 
2024-12-20 06:30:39 +0000  139100 bytes for ./Payload/Waqti.app/Manrope-Bold.ttf
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/fonnts.com-ProximaSansBlack.ttf ... 
2024-12-20 06:30:39 +0000  47416 bytes for ./Payload/Waqti.app/fonnts.com-ProximaSansBlack.ttf
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/Info.plist ... 
2024-12-20 06:30:39 +0000  5131 bytes for ./Payload/Waqti.app/Info.plist
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/fonnts.com-ProximaNova-Semibold.ttf ... 
2024-12-20 06:30:39 +0000  90132 bytes for ./Payload/Waqti.app/fonnts.com-ProximaNova-Semibold.ttf
2024-12-20 06:30:39 +0000  copying file ./Payload/Waqti.app/RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy ... 
2024-12-20 06:30:39 +0000  512 bytes for ./Payload/Waqti.app/RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/Waqti.app/RNCAsyncStorage_resources.bundle/Info.plist ... 
2024-12-20 06:30:39 +0000  798 bytes for ./Payload/Waqti.app/RNCAsyncStorage_resources.bundle/Info.plist
copying file ./Payload/Waqti.app/PkgInfo ... 
2024-12-20 06:30:39 +0000  8 bytes for ./Payload/Waqti.app/PkgInfo
copying file ./Payload/Waqti.app/MaterialIcons.ttf ... 
2024-12-20 06:30:39 +0000  356840 bytes for ./Payload/Waqti.app/MaterialIcons.ttf
2024-12-20 06:30:39 +0000  /usr/bin/ditto exited with 0
2024-12-20 06:30:39 +0000  Processing step: IDEDistributionAppStoreInformationStep
2024-12-20 06:30:39 +0000  Skipping step: IDEDistributionAppStoreInformationStep because it said so
2024-12-20 06:30:39 +0000  Processing step: IDEDistributionGenerateProcessedDistributionItems
2024-12-20 06:30:40 +0000  IDEDistributionItem init <DVTFilePath:0x600070d08f80:'/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/libswift_Concurrency.dylib'>
2024-12-20 06:30:40 +0000  [OPTIONAL] Can't have a profile, not a bundle at <DVTFilePath:0x600070d08f80:'/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/libswift_Concurrency.dylib'>
2024-12-20 06:30:40 +0000  IDEDistributionItem init <DVTFilePath:0x6000508643f0:'/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app/Frameworks/hermes.framework'>
2024-12-20 06:30:40 +0000  IDEDistributionItem init <DVTFilePath:0x6000508040e0:'/var/folders/zp/k1qpllgx5lxbgs724wr1bxgh0000gn/T/XcodeDistPipeline.~~~4iJx6W/Root/Payload/Waqti.app'>
2024-12-20 06:30:40 +0000  Processing step: IDEDistributionCreateManifestStep
