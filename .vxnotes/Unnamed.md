This is very imp note for this proj.
// If scheduling at similar time, apply to all dates within range
const startDate = new Date(packageStartDate);
console.log('🚀 ~ SelectAvailableSlot ~ startDate:', startDate);
const endDate = new Date(startDate);
endDate.setDate(startDate.getDate() + total_days - 1);
console.log('🚀 ~ SelectAvailableSlot ~ endDate:', endDate);
// Update slots for all dates in range
for (
let date = new Date(startDate);
date.setDate(date.getDate() + 1) <= endDate;
date.setDate(date.getDate() + 1)
) {
const dateKey = date.toISOString().split('T')[0];
console.log('🚀 ~ SelectAvailableSlot ~ dateKey:', dateKey);

          // For the current date, use the updated slots
          // if (dateKey === selectedDate.toISOString().split('T')[0]) {
          if (dateKey === selectedDate) {
            console.log('date key is equals to selectedDate');
            dispatch(
              updateGreySlots({
                date: dateKey,
                slots: updatedSlots,
              }),
            );

            // dispatch(
            //   updateSelectedSlots({
            //     date: dateKey,
            //     slots: updatedSlots,
            //   }),
            // );
          } else {
            // For other dates, only update if it's the same slot ID
            const existingSlots = selectedSlots[dateKey] || [];
            const newSlots = isAlreadySelected
              ? existingSlots.filter(
                  existingSlot => existingSlot.id !== slot.id,
                )
              : [...existingSlots, slot];
            console.log('🚀 ~ SelectAvailableSlot ~ newSlots:', newSlots);
            dispatch(
              updateGreySlots({
                date: dateKey,
                slots: newSlots,
              }),
            );
            // dispatch(
            //   updateSelectedSlots({
            //     date: dateKey,
            //     slots: newSlots,
            //   }),
            // );
          }
        }

//13 mARCH CREATE OPEN SESSION

INSIDE BODY TAG

   <View>
          <Text style={[styles.dateTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
            {t('date')}
          </Text>
        </View>
        <CustomDatePicker
          initialDate={startDate}
          onDateChange={handleDateSelect}
          datePickerProps={{minimumDate: startDate}}
          isRTL={isRTL}
        />
        <CustomDatePicker
          minimumDate={startDate}
          initialDate={endDate}
          onDateChange={date => setEndDate(date)}
          isRTL={isRTL}
        />
        <View>
          <Text style={[styles.dateTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
            {t('addYourTime')}
          </Text>
          <View
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
              marginVertical: wp(3),
            }}>
            <View style={{width: '80%'}}>
              {onlineAvailablity?.length > 0 ? (
                onlineAvailablity.map((item, index) => {
                  return (
                    <View
                      onPress={() => setShowAddSlotModal(true)}
                      style={{
                        flexDirection: isRTL ? 'row-reverse' : 'row',
                        alignItems: 'center',
                        marginVertical: hp(1),
                        borderWidth: 1,
                        borderColor: colors.offGrey,
                        borderRadius: 10,
                        padding: fp(1.8),
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: Fonts.medium,
                          }}>{`${convertTo12HourFormat(
                          item?.start_time,
                        )} - ${convertTo12HourFormat(item?.end_time)}`}</Text>
                      </View>
                      <TouchableOpacity onPress={() => handleRemoveSlot(index)}>
                        <Image
                          source={icons.cross}
                          style={{height: fp(1.4), width: fp(1.4)}}
                        />
                      </TouchableOpacity>
                      {/* <View style={{marginLeft: wp(3)}}>
                        {renderStatusBadge(item?.recurrence_type)}
                      </View> */}
                    </View>
                  );
                })
              ) : (
                <Text style={{textAlign: 'center', color: colors.grey}}>
                  {t('noData')}
                </Text>
              )}
            </View>

            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => setShowAddSlotModal(true)}
              style={styles.addBtn}>
              <Image
                source={icons.plusIcon}
                style={{height: wp(10), width: wp(10)}}
              />
            </TouchableOpacity>
          </View>
          <View>
            {classtypeData?.data?.rows?.map(item => {
              return (
                <CustomCheckbox
                  key={item?.id}
                  label={item?.name}
                  isSelected={sessionSelect === item?.id} // Compare with item.id for selection
                  onSelect={() => setSessionSelect(item?.id)}
                />
              );
            })}
            {/* <CustomCheckbox
                    key={item?.id}
                    label={item?.name}
                    isSelected={selectedFilters[selectedCategory] === item?.id} // Compare with item.id for selection
                    onSelect={() => handleSelect(selectedCategory, item?.id)}
                  /> */}
          </View>
          <View>
            <Text
              style={[styles.dateTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
              {t('selectRateCard')}
            </Text>
            <CustomDropDown
              lable={''}
              data={rateCardData || []}
              backgroundColor={colors.white}
              defaultValue={rateCard}
              height={fp(6)}
              borderWidth={1}
              borderColor={colors.themeColor}
              onSelect={selected => setRateCard(selected)}
            />
          </View>
        </View>

//removed setTimeOut conditions in verify otp screen
// if (
// response?.data?.user_type == '3' && // tutor
// response?.data?.tutorStatus == '0' && //profile filling is pending
// response?.data?.tutorProfileCompletionStep == '0' && //profile setup at 0
// response.data.action == 'login'
// ) {
// console.log(
// 'tutor login tutorstatus',
// response?.data?.tutorStatus,
// );
// refRBSheet.current.close();
// navigation.navigate('CompleteYourProfilePageOne');
// dispatch(setAuthData(userData));
// } else if (
// response?.data?.user_type == '3' && //tutor
// response?.data?.tutorStatus == '0' && //profile filling is pending
// response?.data?.tutorProfileCompletionStep == '1' && //profile setup at 0
// response.data.action == 'login'
// ) {
// console.log(
// 'tutor login tutorstatus',
// response?.data?.tutorStatus,
// );
// refRBSheet.current.close();
// navigation.navigate('CompleteYourProfilePageTwo');
// dispatch(setAuthData(userData));
// } else if (
// response?.data?.user_type == '3' && //tutor
// response?.data?.tutorStatus == '3' && //profile filling is submitted but not approved pending
// response.data.action == 'login'
// ) {
// console.log(
// 'tutor login tutorstatus',
// response?.data?.tutorStatus,
// );
// // navigation.navigate('TutorApprovalPending');
// refRBSheet.current.close();
// dispatch(setAuthData(userData));
// dispatch(setIsLoggedIn(true));
// } else if (
// response?.data?.user_type == '3' && //tutor
// response?.data?.tutorStatus == '2' && //profile filling is submitted but rejected
// response.data.action == 'login'
// ) {
// showToast(
// 'error',
// 'Your profile has been rejected, Please contact customer support.',
// );
// console.log('login userType 3');
// dispatch(setAuthData(userData));
// refRBSheet.current.close();
// dispatch(setIsLoggedIn(true));
// }

          // else
