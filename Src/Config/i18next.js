// import i18next from 'i18next';
// import {initReactI18next} from 'react-i18next';
// import AsyncStorage from '@react-native-async-storage/async-storage';
// import en from './Locales/en.json';
// import ar from './Locales/ar.json';
// import {SELECTED_LANG} from '../Utils/storageKeys';

// export const languageResources = {
//   en: {translation: en},
//   ar: {translation: ar},
// };

// export const initializeI18n = async () => {
//   const savedLocale = await AsyncStorage.getItem(SELECTED_LANG); // Retrieve saved locale
//   console.log('🚀 ~ initializeI18n ~ savedLocale:', savedLocale);
//   const defaultLocale = savedLocale || 'en'; // Fallback to 'en' if not found

//   await i18next.use(initReactI18next).init({
//     compatibilityJSON: 'v2',
//     lng: defaultLocale,
//     fallbackLng: 'en',
//     resources: languageResources,
//     interpolation: {
//       escapeValue: false, // React already escapes output
//     },
//   });
// };

// initializeI18n();

// export default i18next;

import React, {useEffect} from 'react';
import i18next from 'i18next';
import {initReactI18next} from 'react-i18next';
import {useSelector} from 'react-redux';
import en from './Locales/en.json';
import ar from './Locales/ar.json';

export const languageResources = {
  en: {translation: en},
  ar: {translation: ar},
};

export const I18nInitializer = ({children}) => {
  const {appLocale} = useSelector(state => state?.auth);
  console.log('🚀 ~ I18nInitializer ~ appLocale:', typeof appLocale);

  useEffect(() => {
    const initializeI18n = async () => {
      const defaultLocale = appLocale || 'en';

      await i18next.use(initReactI18next).init({
        compatibilityJSON: 'v2',
        lng: defaultLocale,
        fallbackLng: 'en',
        resources: languageResources,
        interpolation: {
          escapeValue: false,
        },
      });
    };

    initializeI18n();
  }, [appLocale]);

  return children;
};
export default i18next;
