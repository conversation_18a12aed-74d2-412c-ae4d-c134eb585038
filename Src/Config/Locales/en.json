{"home_Header_Txt": "Welcome to Taleem", "subWelcomHeader": "The Power of Mentorship", "student_Txt": "I'm a Student", "parent_Txt": "I'm a Parent", "tutor_Txt": "I'm a <PERSON>tor", "login_Txt": "Log In", "subTitle_Txt": "Let's continue your learning journey together.", "mobile_Txt": "Mobile Number", "enter_number_Txt": "Enter your mobile number", "enter_full_name": "Enter full name", "commingsoon_Txt": "Coming Soon", "loginemail_Txt": "Login with <PERSON><PERSON>", "continueGoogle_Txt": "Continue with Google", "continueApple_Txt": "Continue with Apple", "donotaccount_Txt": "Don't have an account?", "signUp_Txt": "Sign Up", "get_Started": "Get Started", "or_Txt": "Or", "missing_mobile_number": "Please enter your mobile number.", "invalid_input_Txt": "Invalid Input", "enter_mobile_number": "Enter a valid 10-digit mobile number.", "fullName_required": "Full Name is required.", "phoneNumber_required": "Mobile Number is required.", "invalid_phoneNumber": "Please enter a valid mobile number.", "invalid_email": "Email ID not valid.", "gender_required": "Please select a gender.", "termsOfService": "Terms of Services", "error_terms_conditions": "Please accept the Terms and Conditions.", "error_privacy_policy": "Please accept the Privacy Policy.", "welcomeText": "Welcome to Taleem Education App!", "termsContent1": "These terms and conditions outline the rules and  regulations for the use of Taleem Education  App's mobile application.", "termsContent2": "By accessing this mobile application, we assume you accept these terms and conditions in full. Do not continue to use Taleem Education App's mobile application if you do not accept all of the terms and conditions stated on this page.", "termsContent3": "The following terminology applies to these Terms and Conditions, Privacy Statement and Disclaimer Notice and any or all Agreements: \"Client\", \"You\" and \"Your\" refers to you, the person accessing this mobile application and accepting the Company’s terms and conditions.", "privacyPolicy": "Privacy Policy", "privacyContent1": "Your privacy is important to us. This Privacy Policy explains how we collect, use, and protect your personal information.", "privacyContent2": "We take privacy seriously and will ensure all your personal data is handled securely. We take privacy seriously and will ensure all your personal data is handled securely.", "privacyContent3": "The following terminology applies to these Terms and Conditions, Privacy Statement and Disclaimer Notice and any or all Agreements: \"Client\", \"You\" and \"Your\" refers to you, the person accessing this mobile application and accepting the Company’s terms and conditions.", "createAccount": "Create your account", "continueLearning": "Let’s continue your learning journey together", "fullName": "Full Name*", "placeholder_fullName": "Your Full Name", "phoneNumber": "Phone Number*", "placeholder_phoneNumber": "Mobile Number", "email": "Email ID (Optional)", "placeholder_email": "Enter email", "agreeTerms": "By signing up you agree with", "our": "our", "registration_success": "User registered succesfully", "signUp": "Sign Up", "alreadyHaveAccount": "Already have an account?", "logIn": "Log In", "cancel": "Cancel", "iAgree": "I Agree", "selectgender": "Select your gender", "male": "Male", "female": "Female", "enternumber": "Enter your phone number", "enter_otp": "Enter OTP", "otp_sent_message": "We’ve sent an OTP to your registered mobile number.", "invalid_input": "Invalid Input. Please enter a numeric value.", "missing_otp": "Please fill in all the OTP fields.", "invalid_otp": "Please enter a valid 4-digit OTP.", "invalid_name": "Please enter a valid name", "didnt_receive": "Didn’t Receive?", "resend": "Resend", "verify_otp": "Verify OTP", "otp_successful": "OTP Verification Successfull!", "error_message": "Error: ", "wait_for_resend": "Please wait before resending.", "otp_resent_successfully": "OTP resent successfully.", "hello_student": "Hello Student!", "add_grade_details": "Add your grade details to recommend you a better tutor.", "hello_naseem": "Hello Naseem", "your_details": "Your Details", "quick_actions": "Quick Actions", "academic": "Academic", "recreational": "Recreational", "courses": "Courses", "missing_details": "Please complete your details", "notifications_unavailable": "Notifications are unavailable", "curriculum": "Curriculum", "class": "Class", "grade": "Grade", "add_details": "Add Details", "secondary": "Secondary", "arabic": "Arabic", "10th": "10th", "myclass": "My Class", "home": "Home", "wallet": "Wallet", "choose_curriculum": "Choose your Curriculum", "curriculum_subtitle": "From Primary to University, <PERSON><PERSON>d Tutoring for Every Stage", "select_curriculum": "Please select a curriculum before continuing.", "coming_soon": "Coming Soon", "continue": "Continue", "american": "American", "british": "British", "canadian": "Canadian", "choose_grade": "Choose your grade", "grade_subtitle": "From Primary to University, <PERSON><PERSON>d Tutoring for Every Stage", "select_grade": "Please select a grade before continuing.", "other_grade": "Please specify your other grade.", "select_class": "Select Class", "primary": "Primary", "middle": "Middle", "university": "University", "class1": "Class 1", "class2": "Class 2", "class3": "Class 3", "class4": "Class 4", "class5": "Class 5", "class6": "Class 6", "primary_class1": "Class 1", "primary_class2": "Class 2", "primary_class3": "Class 3", "middle_class1": "Middle Class 1", "middle_class2": "Middle Class 2", "secondary_class1": "Secondary Class 1", "secondary_class2": "Secondary Class 2", "university_class1": "University Class 1", "university_class2": "University Class 2", "university_class3": "University Class 3", "tutorsTitle": "Tutors ({{count}})", "searchPlaceholder": "Search here...", "search": "Search", "resentSearch": "Resent Search", "locationLabel": "Qatar", "biology": "Biology Teacher", "math": "Math Teacher", "physics": "Physics Teacher", "chemistry": "Chemistry Teacher", "english": "English", "history": "History Teacher", "geography": "Geography Teacher", "french": "French Teacher", "spanish": "Spanish Teacher", "art": "Art Teacher", "computerScience": "Computer Science Teacher", "music": "Music Teacher", "AlbertLee": "<PERSON>", "AnnaSmith": "<PERSON>", "JamesBrown": "<PERSON>", "LindaJohnson": "<PERSON>", "MichaelWilliams": "<PERSON>", "SarahMiller": "<PERSON>", "DavidWilson": "<PERSON>", "JessicaTaylor": "<PERSON>", "RobertMoore": "<PERSON>", "EmilyAnderson": "<PERSON>", "DanielThomas": "<PERSON>", "SophiaJackson": "<PERSON>", "currencyPerHour": "QAR/hr", "about": "About", "reviews": "Reviews", "package": "Package", "bio": "Bio", "teaching_philosophy": "Teaching Philosophy: {{name}} believes in making math accessible and enjoyable for all students. She uses a variety of teaching methods to cater to different learning styles and encourages students.", "grades": "Grades", "expertise_in": "Expertise In", "experience": "Experience", "years": "years", "year": "year", "availability_for": "Availability for", "online": "Online", "face_to_face": "Face to Face", "group_sessions": "Group Sessions", "open_session": "Public Session", "book_tutor": "Book Tutor", "name": "Loading..", "rating": "4.0", "location": "Doha, Qatar", "mathematics": "Mathematics", "algebra": "Algebra", "calculus": "Calculus", "trigonometry": "Trigonometry", "probability": "Probability", "equation": "Equation", "top_tutors": "Top Tutors", "notifications": "Notifications", "manage_profile": "Manage Profile", "terms_of_service": "Terms of Service", "faqs": "FAQs", "rating_feedbacks": "Rating & Feedbacks", "contact_us": "Contact Us", "change_language": "Change Language", "logout": "Logout", "logged_in_as_student": "Logged in as Student", "logging_out": "Logging out...", "daniela_name": "<PERSON><PERSON>", "daniela_email": "N/A", "view_profile": "View Profile", "filters_title": "Filters and Sort", "clear_all": "Clear All", "back": "← Filters", "language": "Language", "subject": "Subject", "price": "Price", "rating_en": "Rating", "gender": "Gender", "apply": "Apply", "free": "Free", "paid": "Paid", "zero_to_fifty": "0-50", "fifty_to_hundred": "50-100", "hundred_plus": "100+", "five_star": "Best", "four_star": "Good", "three_star": "Average", "two_star": "2 Star", "one_star": "1 Star", "other": "Other", "science": "Science", "englishL": "English", "filter": "Filter", "book_tutor_title": "Book your tutor", "select_class_type": "Select Class Type", "select_session": "Select Session", "individual": "Individual", "group": "Group", "select_package_date": "Select Package Starting Date", "select_date": "Select Date", "close": "Close", "select_availability": "Select Availability", "select_hours": "Select Hours", "instructions": "Instructions", "instructions_placeholder": "Add your instructions", "total_amount": "Total Amount", "currency": "QAR", "book_now": "Book Now", "available_soon": "This feature will be available in future updates.", "128": "128", "19": "19", "availablePackages": "Available Packages", "oneWeekPackage": "1 Week Package", "twoWeeksPackage": "2 Weeks Package", "monthlyPackage": "Monthly Package", "duration": "Duration", "instructionsHeading": "Instructions", "instructionsText": "Please select between 3 to 52 hours per week when booking your tutoring package.", "paymentGateway": "Payment Gateway Coming Soon", "SelectAvailableSlot": "Select Available Slot", "SelectStartTime": "Select Start Time", "SelectEndTime": "Select End Time", "tutorDetails": "<PERSON><PERSON>", "availableSlots": "Available Slots", "availableHours": "Available Hours", "scheduleAtSimilarTimes": "Schedule at similar times", "availableDatesAndHours": "Available dates and hours", "booked": "Booked", "save": "Save", "packageStartingDate": "Package Starting Date", "session": "Session", "time": "Time", "selectPaymentMethod": "Select payment method", "creditDebitCard": "Credit/Debit Card", "applePay": "Apple Pay", "googlePay": "Google Pay", "totalAmount": "Total Amount", "bookNow": "Book Now", "date": "Date", "oneHour": "1 Hours", "twoHour": "2 Hours", "120riyal": "120 qatariRiyals", "reviewText": "I really enjoyed Calculus 101. The instructor explained difficult concepts in a way that was easy to understand, and the course materials were very understand helpful.", "date_review": "12 Dec 2024", "dateOfBirth": "Date of Birth", "completeProfile": "Complete your profile", "qualifications": "Qualifications", "selectQualification": "Select your qualification", "occupation": "Occupation", "enterOccupation": "Enter your occupation", "errorOccupation": "Occupation is invalid", "enterExperience": "Enter your experience in years", "tutoringGrades": "Which grades do you provide tutoring for?", "writeYourOwn": "Write your own", "expertiseIn": "Expertise In", "selectExpertise": "Select your expertise", "skip": "<PERSON><PERSON>", "professional": "Professional", "uploadAcademicDocument": "Upload Academic Document", "uploadAddressProof": "Upload Address Proof (Optional)", "idPhotoAttachment": "ID Photo Attachment", "dropFilesToUpload": "or drop files to upload", "fileTypes": "PDF, JPG or PNG.", "browse": "Browse", "allSet": "You're All Set!", "welcomeMessage": "Welcome to the Taleem community. Let’s make learning extraordinary.", "needHelp": "Need Help?", "startJourney": "Start your Journey", "ageValid": "You must be at least 18 years old", "invalid_fullName": "Please enter valid name", "dob_required": "Date of birth is required.", "experience_valid": "Experience should be a 2-digit number.", "hello_tutor": "Hello <PERSON><PERSON>!", "price_for_online": "Price for Online", "packageAvailable": "Package Available", "price_for_face_to_face": "Price for Face to Face", "view": "View", "edit": "Edit", "add": "+ADD", "add_rate_card": "Add Rate Card", "complete_your_profile": "Complete your Profile", "profile_not_complete": "Your profile is not yet complete. Kindly provide your details to complete the process.", "get_started": "Get Started", "setup_your_card": "Setup your rate card and availability", "setup_now": "Setup Now", "my_booking": "My Booking", "rate_card": "Rate Card", "schedule": "Schedule", "earning": "Earning", "today_class": "Today Classes", "today": "Today", "algebra_for_primary": "Algebra for Primary Students (Grade - 4th to 6th)", "start": "Start", "all": "All", "less": "Less", "set_ratecard": "Setup your rate card and availability", "greeting_message": "Hello {{name}}!", "geometry_for_intermediate": "Geometry for Intermediate", "math_group_session": "Math Group Session", "date_time": "{{date}}, {{time}}", "students_enrolled": "{{count}} students enrolled", "my_rate_card": "My Rate Card", "doc": "Documents", "logged_out": "Logged out successfully", "logged_out_fail": "Failed to log out", "logged_in_as_tutor": "Logged in as <PERSON><PERSON>", "my_bookings": "My Bookings", "math_class": "Algebra for Primary Students (Grade - 6th)", "science_lab": "Science Lab", "yoga_session": "Yoga Session", "art_workshop": "Art Workshop", "web_dev_course": "Web Development Course", "data_science_course": "Data Science Course", "time_label": "Time : ", "timing": "9:00 AM - 10:30 AM", "timing1": "11:00 AM - 12:30 PM", "timing2": "1:00 PM - 2:30 PM", "session_label": "Session", "category_label": "Category", "package_start": "Package Starting Date  : ", "package_date": "08 Aug 2024", "language_label": "Language  : ", "week": "1 Week", "class_type": "Class Type", "schedule_date": "28 Aug 2024", "schedule_date1": "29 Aug 2024", "schedule_date2": "30 Aug 2024", "class_link": "Class Link: ", "link": "https//.www.zoom-meet/y66834", "payment_details": "Payment Details", "booking_id": "Booking ID", "payment_date_times": "08 Aug, 2024, 10:40 AM", "payment_mode": "Payment Mode", "debit_card": "Debit Card", "status": "Status", "success": "Success", "join_call": "Please join your class link before the class starts", "insert_link": "Insert Zoom Meeting Link", "class_link_placeholder": "Enter Class link", "meeting_detail": "Or Add Meeting Details", "meeting_id": "Meeting ID", "passcode": "Passcode", "student_details": "Student Details", "start_class": "Start Class", "selectCurriculum": "Select curriculum", "selectGrade": "Select Grade", "selectSubject": "Select Subject", "Maths": "Maths", "price_placeholder": "120 Qatari Riyal", "addSubject": "Add Subject ( Fee per hour )", "selectName": "Select Name", "sessionType": "Session Type", "packageType": "Package Type", "oneWeek": "1 Week", "twoWeek": "2 Week", "threeWeek": "3 Week", "courseTitle": "Course Title", "description": "Description", "courseDuration": "Course Duration", "selectCategoryCourse": "Select Category Course", "selectLevel": "Select Level", "courseFee": "Course Fee", "addOptional": "+ Add Optional", "maxStudent": "Max. Student No.", "minStudent": "Min. Student No.", "noData": "No data found!", "waitingForStudent": "Waiting for student to join", "classStarted": "Class Started Virtually", "addkey": "Add", "selectRecurrenceType": "Select Recurrence Type", "add_new_schedule": "Add New Schedule", "todayAvailability": "Today Availability", "student": "Student", "level": "Level", "type": "Type", "price_f2f": "Price (Face-to-Face)", "price_online": "Price (Online)", "select_subject": "Select Subject", "add_optional": "Add Optional", "optional_subject": "Optional Subject", "enter_optional_subject": "Enter Optional Subject", "min_student": "Minimum Students", "max_student": "Maximum Students", "package_type": "Package Type", "please_fill_all_fields": "Please Fill All Fields.", "min_and_max_students_must_be_numeric": "Min and Max Students must be numeric.", "min_students_must_be_less_than_max_students": "Min Students must be less than Max Students.", "please_select_at_least_one_session_type": "Please select at least one session type.", "please_provide_online_price": "Please provide Online price.", "please_provide_face_to_face_price": "Please provide Face-to-Face price.", "academic_rate_card_added_successfully": "Academic Rate Card Added Successfully.", "error_saving_rate_card": "Error Saving Rate Card.", "120_qar": "120 QAR", "please_select_expertise": "Please select expertise", "select_expertise": "Select expertise", "please_enter_valid_online_price": "Please enter a valid online price.", "please_enter_valid_face_to_face_price": "Please enter a valid Face-to-Face price.", "please_select_at_least_one_package": "Please select at least one package.", "recreational_rate_card_added_successfully": "Recreational Rate Card added successfully.", "something_went_wrong": "Something went wrong.", "please_enter_the_course_title": "Please enter the course title", "please_enter_the_description": "Please enter the description", "course_duration_must_be_a_numeric_value_up_to_5_digits": "Course duration must be a numeric value up to 5 digits", "please_select_a_course_category": "Please select a course category", "please_select_a_course_level": "Please select a course level", "please_select_at_least_one_session": "Please select at least one session", "please_enter_a_valid_price_for_online_sessions": "Please enter a valid price for online sessions", "please_enter_a_valid_price_for_face_to_face_sessions": "Please enter a valid price for face-to-face sessions", "course_added_successfully": "Course added successfully", "course_title": "Course Title", "enter_course_title": "Enter course title", "enter_description": "Enter description", "course_duration": "Course Duration", "enter_course_duration": "Enter course duration", "select_category": "Select Category", "select_level": "Select Level", "enter_online_price": "Enter online price", "enter_face_to_face_price": "Enter face-to-face price", "loading": "Loading...", "error_loading_data": "Error loading data.", "retry": "Retry", "edit_academic_rate_card": "Edit Academic Rate Card", "face_to_face_price": "Face to Face Price", "online_price": "Online Price", "delete": "Delete", "academic_rate_card_updated_successfully": "Academic Rate Card Updated Successfully.", "error_in_saving_rate_card": "Error In Saving Rate Card.", "confirm_delete": "Confirm Delete", "are_you_sure_you_want_to_delete_this_card": "Are you sure you want to delete this card?", "academic_rate_card_deleted_successfully": "Academic Rate Card Deleted Successfully.", "error_in_deleting_rate_card": "Error in deleting rate card", "expertise_is_required": "Expertise is required", "edit_recreational_rate_card": "Edit Recreational Rate Card", "enter_price": "Enter price", "at_least_one_session_type_is_required": "At least one session type is required", "online_price_is_required": "Online price is required", "face_to_face_price_is_required": "Face to face price is required", "at_least_one_package_is_required": "At least one package is required", "please_fill_in_all_required_fields": "Please fill in all required fields", "rate_card_updated_successfully": "Rate Card Updated Successfully", "error_updating_rate_card": "Error Updating Rate Card", "are_you_sure_you_want_to_delete_this_rate_card": "Are you sure you want to delete this rate card?", "rate_card_deleted_successfully": "Rate card deleted successfully", "error_deleting_rate_card": "Error deleting rate card", "Edit_Course": "Edit Course", "Course_Title": "Course Title", "Enter_course_title": "Enter course title", "Description": "Description", "Enter_description": "Enter description", "Course_Duration": "Course Duration", "Enter_course_duration": "Enter course duration", "Select_Category": "Select Category", "Select_Level": "Select Level", "Select_Session_Type": "Select Session Type", "Price_for": "Price for ", "Enter_price": "Enter price", "Save": "Save", "Delete": "Delete", "Confirm_Delete": "Confirm Delete", "Are_you_sure_you_want_to_delete_this_course": "Are you sure you want to delete this course?", "Course_Updated_Successfully": "Course Updated Successfully.", "Failed_To_Update_Course": "Failed To Update Course.", "Course_Deleted_Successfully": "Course Deleted Successfully.", "Failed_To_Delete_Course": "Failed To Delete Course.", "enterNationality": "Select nationality", "nationality": "Nationality", "MeetingPreference": "Meeting Preference", "select_students": "Select Students", "meeting_point": "Select Meeting Point", "saveProfile": "Save Profile", "enterName": "Enter Name", "mobileNo": "Mobile No.", "Email": "Email", "address": "Address", "editProfile": "Edit Profile", "meetingPreference": "Meeting Preference", "editQualificationsPreferences": "Edit Qualifications Preferences", "gradesTutoring": "Which grades do you provide tutoring for?", "totalYearsOfExperience": "Total Years of Experience", "enterYourBio": "Enter your Bio", "update": "Update", "failedToUpdate": "Failed to update profile. Please try again.", "profileUpdated": "Profile updated successfully!", "errorLoadingProfile": "Error loading profile data.", "classMode": "Class Mode", "loadingPreferences": "Loading preferences...", "loadingMeetingPoints": "Loading meeting points...", "errorLoadingPreferences": "Failed to load preferences. Pull down to refresh.", "errorLoadingMeetingPoints": "Failed to load meeting points. Pull down to refresh.", "bookingPreferences": "Booking Preferences", "meetingPoints": "Meeting Points", "meetingPoint": "Meeting Point", "noMeetingPoints": "No meeting points available. Add a new one.", "allowStudentMeetingPoints": "Allow students to set their own meeting point", "addMeetingPoint": "Add New Meeting Point", "savePreferences": "Save Preferences", "saving": "Saving...", "preferencesUpdated": "Preferences updated successfully", "preferencesUpdateFailed": "Failed to update preferences. Please try again.", "editMeetingPoint": "Edit Meeting Point", "loadingMeetingPoint": "Loading meeting point...", "errorLoadingMeetingPoint": "Failed to load meeting point.", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this meeting point?", "deleteSuccess": "Meeting point deleted successfully!", "deleteError": "Failed to delete the meeting point. Please try again.", "locationMandatory": "Location is mandatory. Please select a valid location.", "saveChanges": "Save Changes", "saveSuccess": "Meeting point updated successfully!", "saveError": "Failed to update meeting point. Please try again.", "editYourMeetingPoint": "Edit Your Meeting Point", "failedToRetrieveLocationDetails": "Failed to retrieve location details. Please try again.", "setYourPreference": "Set Your Preference", "yourMeetingPoint": "Your Meeting Point", "enterAddress": "Enter address", "addNewMeetingPoint": "Add New Meeting Point", "EditMeetingPreference": "Edit meeting preference", "EditqualificationsPreference": "Edit qualifications and other preferences", "bookingPreferenceRadius": "Booking Preference Radius", "myProfile": "My Profile", "monthTitle": "{{month}} {{year}}", "noDataAvailable": "No data available!", "bookOpenSessions": "Book Open Sessions", "addNewSchedule": "Add New Schedule", "comingSoon": "Coming Soon!", "leftArrow": "Previous", "rightArrow": "Next", "availableTab": "Available", "confirmLogout": "Confirm <PERSON>ut", "logOutMessage": "Are you sure you want to log out?", "AddYourSchedule": "Add your Schedule", "classDetails": "Class Details", "joinClass": "Join Class", "rateYourTeacher": "Rate your Teacher", "rateYourTutor": "Rate your tutor", "chats": "Chats", "skipCash": "Debit or Credit Card", "currentBalance": "Current Balance", "refresh": "Refresh", "addMoney": "Add Money", "transactions": "Transactions", "saved_payment_method": "Saved Payment Method", "withdraw": "Withdraw", "addAccount": "Add Account", "transaction_history": "Transaction History", "enter_withdrawal_amount": "<PERSON><PERSON> Amount", "open_schedule": "Public Session", "addYourTime": "Add your time for availablity", "bookOpenSession": "Book Public Session", "price_for_open_session": "Price for Public Session", "select_payment_type": "Select Payment Type", "group_details": "Group Details", "student_name": "Student Name", "callNow": "Need Help? Call Now", "chatWithAdmin": "Chat With Admin", "riseConcern": "<PERSON><PERSON>n", "submit": "Submit", "savedPaymentMethods": "Saved Payment Method", "highestToLowest": "High to Low", "lowestToHighest": "Low to High", "concern": "Concern", "settings": "Settings", "aboutUs": "About Us", "deleteAccount": "Delete Account", "noLongerUsing": "No longer using the services/platform", "enterEmail": "Please enter email", "emailId": "Email ID", "yourMessage": "Your Message*", "placeHolderMessage": "Leave your message...", "manageKids": "Manage Kids", "admin": "Admin", "Open": "Open", "inProgress": "In progress", "closed": "Closed", "hereToHelp": "We’re Here to Help!", "ContactUsSubtitle": "If you need assistance or have any questions, please reach out to us using one of the options below", "adminSupportMsg": "For quick support, start a chat with our admin team right away", "callUs": "Call Us", "connectWithSupport": "Connect with our support team directly for immediate assistance", "emailForSupport": "Send us an email and our team will get back to you promptly", "emailUs": "Email Us", "call": "Call", "selectLanguage": "Select Language", "chooseLanguage": "Choose your preferred language here", "tutorMeetingPoint": "Tutor Meeting Point", "reschedule": "Reschedule", "meetingDetails": "Meeting Details", "cancelBooking": "Cancel Booking", "bookingCancel": "Booking Cancelled", "confirmCancel": "Confirm Cancel", "reason": "Reason", "typeHere": "Type here...", "remove": "Remove", "Add": "Add", "studentListAlert": "Choose at least 3 students and no more than 10 students from the list.", "fountBatter": "Found a better alternative", "privacyConcern": "Privacy Concerns", "tooManyEmails": "Too many emails/notification", "accountSecurity": "Account Security Concerns", "personalReason": "Personal reasons", "reasonRequired": "Reason is required", "messagePlaceHolder": "Write a message here", "tutorMeeting": "Tutor's Meeting Point", "suggestedMeeting": "Suggested Meeting Point", "useCurrentLocation": "Use Current Location", "chooseCategory": "Choose Category", "categoryError": "Error loading categories", "startTimeErrorMessage": "Start time should be less then end time", "selectDate": "Select Date", "noSchedules": "No Schedules", "areYouSure": "Are you sure?", "deleteSlotMsg": "You want to delete the full recurrence of the slot", "ok": "Ok", "addSlot": "Add Slot", "maximumStudentError": "Maximum student count should be more then minimum student count", "addPriceError": "please add price", "selectAvailableSlots": "Select Available Slots", "selectSlotError": "Please select slots to proceed.", "insufficientBalance": "Your wallet has not sufficient balance", "numberOfSessions": "No. of sessions", "payment": "Payment", "paymentFailed": "Payment Failed", "currencyAdded": "Currency added to wallet", "tuttorDataMissing": "Tutor data is missing.", "selectClassType": "Please select a class type.", "selectAvailabeSlots": "Please select available slots.", "calculatePrice": "Price calculated successfully!", "priceCalculationFailed": "Failed to calculate price. Please try again.", "classTypeNotAvailable": "This class type is not available for this tutor.", "sessionTypeNotAvailable": "This session type is not available for this tutor.", "addMeeting": "Add meeting point", "calculate_price": "Calculate Price", "notAvailableClass": "This class type is not available", "notAvailableSession": "This session is not available", "emptyPackageDate": "Please select package start date.", "emptyPackage": "Please select package.", "emptyClassType": "Please select class type.", "rescheduleBooking": "Reschedule Booking", "addMoreSlots": "Add more slots", "paymentStatus": "Payment Status", "bookingFailed": "Booking Failed !!", "lowWalletError": "Unable to complete booking: Your wallet balance is too low.", "addMoneyToWallet": "Add Money to Wallet", "payUsingCard": "Pay Using Card", "emptyMessageError": "Please write something to send", "searchCurriculum": "Search curriculum", "submitAcademicFailed": "Failed to submit academic details", "enterQualification": "Enter Qualification", "oldEmailNotMatched": "Old email does not match the new email.", "emailError": "Invalid new email address.", "otpSentSuccessfully": "OTP sent successfully to the new email.", "failedToSendOtp": "Failed to send OTP.", "emptyOtp": "OTP cannot be empty.", "otpInvalid": "Invalid OTP. Please try again.", "changeEmail": "Change Email", "enterOldEmail": "Enter Old Email", "enterNewEmail": "Enter New Email", "newEmailPlaceHolder": "Enter your new email", "sendOtp": "Send OTP", "mobileNumberError": "Please enter a valid 10-digit phone number.", "phoneMissmatch": "New phone number cannot be the same as the old phone number.", "otpSentOnNewPhone": "OTP sent successfully to your new phone number.", "phoneNumberChanged": "Phone number changed successfully!", "changeMobileNumber": "Change Mobile Number", "oldPhoneNumber": "Old Phone Number", "newPhoneNumber": "New Phone Number", "submitOtp": "Submit OTP", "accept": "Accept", "otpVarificationFailed": "OTP Verification failed", "bookingSuccessfull": "Booking Successful!", "viewBooking": "View Booking", "exploreTutor": "Explore Tutor", "addYourAddress": "Add your address", "profile": "Profile", "addressMaxLength": "Address should not exceed 70 characters.", "fillAllFields": "Please fill all required fields", "updateProfileError": "Error updating profile", "failedImageSelection": "Failed to select image", "invalidImageData": "Invalid image data", "imageUploadSuccess": "Image uploaded successfully", "failedImageupload": "Failed to upload image after multiple attempts", "unExpectedError": "An unexpected error occurred", "selectImageSource": "Select Image Source", "takePhoto": "Take Photo", "chooseFromGallery": "Choose from Gallery", "reasons": "Reasons", "slotAvailable": "This slot is currently unavailable.", "deleteSlot": "Delete Slot", "deleteSlotMessage": "Do you want to delete this slot or the entire recurrence?", "deleteAll": "Delete All", "editSlot": "Edit Slot", "mathsTutors": "Maths Tutors", "englishTutors": "English Tutors", "artTutor": "<PERSON>", "hourMessage": "Please select at most 1 hours.", "noSlots": " No Slots Found", "openWhiteBoard": "Open White Board", "addressSaved": "Address saved successfully!", "failedSaveAddress": "Failed to save address. Please try again.", "typeLocation": "Type a location...", "Zone": "Zone", "enterZone": "Enter Zone", "Street": "Street", "enterStreet": "Enter Street", "buildingNumber": "Building Number", "enterBuildingNumber": "Enter Building Number", "saveAddress": "Save Address", "specifyCancelReason": "Please specify reason for cancelling", "addressProof": "Address Proof", "noReviews": "No Reviews", "errorLoadingCourseDetails": "Error loading course details. Please try again later.", "courseDetails": "Course Details", "courseLanguage": "Course Language", "courseDescription": "Course Description", "noDescription": "No description available.", "danceTutor": "Dance Tutor in University", "noAddress": "No Address", "pleaseEnterAmount": "Please enter amount", "requestSuccess": "Request Successfully", "docUploadFailed": "Document upload failed after multiple attempts.", "unexpectedError": "An unexpected error occurred. Please try again.", "pleaseSelectDoc": "Please select required documents", "uploadFailed": "Failed to upload documents. Please try again.", "pleaseSelectSession": "Please select a session type.", "selectPackage": "Please select a package.", "sendForVerification": "Your request has been sent for verification", "pleaseAddReview": "Please add review", "add1Star": "Rate should be minimum 1 star", "classNotStarted": "Your class has not been started by tutor yet.", "contactPermissionMsg": "This app would like to view your contacts.", "acceptBareMortal": "Please accept bare mortal", "allowContactPermission": "Please allow the permission to access contacts.", "failedToPostContact": "Failed to post contacts. Please try again.", "typeYourMessage": "Type your message", "selectUserType": "Select User Type", "turnONOFF": "Turn on/turn off", "pleaseEnterMessage": "Please enter message", "emailRequired": "Email is required", "slotBookedAlready": "This Slot is already booked.", "youMustSelect": "Please select at least {{count}} students", "selectStudentError": "Please select no more than {{count}} students.", "validSelection": "Selection Valid", "validSelectionMsg": "Your selection is valid.", "clickHereToStart": "Click here to Start", "done": "Done", "document": "Document", "hello": "Hello", "selectOne": "Select One", "dialingError": "Dialing is not supported on this device", "linkEmailError": "Email client is not available on this device", "Split": "Split", "Full": "Full", "proffetionalSubtitle": "Have a great experience", "failedToLoadProfile": "Failed to load profile details", "repeatOn": "Repeat on", "recurenceType": "Recurrence Type", "uploadImage": "Upload Image", "noClasses": "No classes for today", "selectRateCard": "Select the Rate Card", "risedTicket": "Raised Ticket", "Join": "Join", "Review": "Review", "profileRejectedMsg": "Sorry, your profile has been rejected by the admin. You can update your details and resubmit your documents from your profile.", "profileUnderReview": "Your profile is under review. Schedule creation will be available after approval.", "Qatar": "Qatar", "loggedInAsParent": "Logged in as <PERSON>", "parent": "Parent", "tutor": "Tutor", "From": "From", "To": "To", "ratings": "Ratings", "distance": "Distance", "areYouSureAccountDelete": "Are you sure you want to delete your account?", "areYouSureAccountProfile": "Are you sure you want to delete your profile?", "deleteMsg": "This action is irreversible and will permanently delete your data. You lose your wallet as well.", "Select": "Select", "availableMeetingPoint": "Available meeting points", "mobile_number_min_length": "Mobile number must be {{minLength}} digits.", "information": "Information", "academic_document": "Academic Document", "address_proof": "Address Proof", "id_photo": "ID Photo", "other_document": "Other Document", "no_session_found": "No Session Found", "no_class_found": "No Class Found", "no_transaction_history": "No Transaction History", "no_chats_found": "No Chats Found", "no_raised_tickets": "No Raised Tickets", "no_booking_found": "No Booking Found", "no_rate_card_found": "No Rate Card Found", "other_expertise": "Other Expertise", "enter_other_expertise": "Enter Expertise", "add_expertise": "Add Expertise", "rate_per_hour": "Rate per hour", "optional_category": "Optional Category", "enter_category": "Enter Category", "create_open_session": "Create Public Session", "enter": "Enter", "account_holder_name": "Account Holder Name", "account_number": "Account Number", "bank_name": "Bank Name", "support": "Support", "create_open_schedule": "Create Open Session", "create": "Create", "swift_code": "Swift Code", "swiftCodeRequired": "SWIFT code is required", "invalidSwiftCode": "Invalid SWIFT code format", "bankNameRequired": "Bank name is required", "accountHolderNameRequired": "Account holder name is required", "ibanRequired": "IBAN is required", "invalidIban": "Invalid IBAN format", "addBankAccountError": "Failed to add bank account", "checkInputs": "Please check your inputs and try again", "addedAccounts": "Added Accounts", "select_end_enrollment_date": "Select End Date for Enrollment", "select_start_enrollment_date": "Select Start Date for Enrollment", "primaryAccountUpdated": "Primary Account Updated", "updatePrimaryAccountError": "Primary Account Update Failure", "message_tutor": "Message Tutor", "no_of_sessions": "No of sessions", "enrollment_ends_on": "Enrollment End Date", "enrollment_starts_on": "Enrollment Start Date", "sessionDate": "Session Date", "selectStudentType": "Select Student Type", "selectClass": "Select Class", "no_notification_found": "No notification found", "saved_payment_methods": "Saved payment methods", "no_payment_methods": "No payment methods.", "explore_campaigns": "Explore Campaigns", "invite": "Invite", "contacts": "Contacts", "members": "Members", "noContactsFound": "No Contacts Found", "noMembersFound": "No Members Found", "invited": "Invited", "invitationSentAlready": "Invitation <PERSON><PERSON> Already", "classType": "Class Type", "download_pdf": "Download PDF", "selectAtLeastOneSlot": "Select at least one slot.", "open": "Public", "you_dont_have_sufficient_balance_for_payout": "You don't have sufficient balance for payout.", "request_payout": "Request Payout", "noAddressesAvailable": "No address available", "optional": "Optional", "welcomeTooltip": "The Power of Mentorship", "academicTooltip": "Browse tutors for academic subjects across all grade levels and curriculums.", "recreationalTooltip": "Explore tutors for personal skills, creative hobbies, and general interest areas like music, art, and more.", "coursesTooltip": "Discover structured multi-hour courses created by tutors with detailed plans and objectives.", "myBookingTooltip": "View, accept, or reject session requests from students.", "rateCardTooltip": "Set your subjects, session types, and hourly pricing.", "scheduleTooltip": "Set your available hours and days for students to book sessions.", "earningBtnTooltip": "Track your session earnings and request withdrawals once sessions are done.", "rateCardHelper": "Select the type of rate card you want to create: Academic, Skills, Hobbies & Recreational, or Course.", "selectGradeHelper": "Choose the grade this rate card applies to. If you teach multiple grades, create separate rate cards.", "curriculamHelper": "Choose the curriculum relevant to this subject and grade. Create another rate card if you teach others.", "f2fHelperTxt": "Select the types of face-to-face sessions you want to offer for this rate card. Group and public sessions require student limits.", "onlineHelper": "Select the types of online sessions you want to offer for this rate card. Options can be combined with face-to-face types.", "selectSubAcademicHelper": "Choose the subject this rate card is for. To offer multiple subjects, create separate rate cards. If the subject is not available in the current list, please select 'Other' and type in the required subject name.", "packageHelperTxt": "Select the packages you'd like to offer for this rate card. Packages define duration and minimum bookings. and may include a discount set by the admin.", "priceF2fHelper": "Set the hourly rate per student for face-to-face sessions for this rate card.", "priceOnlineHelper": "Set the hourly rate per student for online sessions for this rate card.", "minStudentHelper": "Minimum number of students required for group or public sessions for this rate card.", "maxStudentHelper": "Maximum number of students allowed for group or public sessions for this rate card.", "individualHelperTxt": "One-on-one sessions between tutor and a single student.", "groupHelperTxt": "Private group sessions with students who book together (e.g., siblings, friends).", "publicHelperTxt": "Open group sessions created and scheduled by the tutor, which any student can join from the app.", "oneWeekPackageHelper": "A short-term package valid for one week. Students must book a minimum of 3 hours. No discount is applied.", "twoWeekPackageHelper": "A mid-range package valid for two weeks. Students must book a minimum of 4 hours. Discount is applied as per admin settings.", "threeWeeksPackageHelper": "A flexible package valid for three weeks. Students must book a minimum of 6 hours. Discount is applied as per admin settings.", "oneMonthPackageHelper": "A long-term package valid for one month. Students must book a minimum of 8 hours. Discount is applied as per admin settings.", "recreationalF2FHelpe": "Select which face-to-face session types you'd like to offer for this rate card (Individual, Group, Public Session).", "recreationalOnlineHelper": "Select which online session types you'd like to offer for this rate card (Individual, Group, Public Session).", "recreationalSubjectHelper": "Choose the subject this rate card is for. To offer multiple subjects, create separate rate cards. If the subject is not available in the current list, please select Other and type in the required subject name.", "packageTypeRecreationalHelper": "Select the applicable packages you'd like to offer for this rate card. Each package comes with a minimum required booking duration and may include a discount set by the admin.", "priceForF2FHelperRecreational": "Enter your hourly rate per student for face-to-face sessions.", "priceForOnlineRecreational": "Enter your hourly rate per student for online sessions.", "minStudentRecreational": "If offering Group or Public Sessions, set the minimum number of students allowed per session.", "maxStudentRecreational": "If offering Group or Public Sessions, set the maximum number of students allowed per session.", "courseTitleHelper": "Enter the title of the course you will be offering.", "discriptionHelper": "Add a brief description of the course.", "courseDureation": "Specify the total number of hours this course requires. Students can only complete the booking if their total selected slots match this duration exactly.", "selectCategory": "Choose the category under which your course falls. If the category is not listed, please select “Other” and enter it manually.", "coursesF2FHelper": "Select which face-to-face session types you'd like to offer for this course (Individual, Group, Public Session).", "coursesOnlineHelper": "Select which online session types you'd like to offer for this course (Individual, Group, Public Session).", "coursesPriceF2F": "Enter the hourly price per student for face-to-face sessions.", "coursesPriceOnline": "Enter the hourly price per student for online sessions.", "coursesMinStudentHelper": "f offering Group or Public Sessions, set the minimum number of students per session.", "coursesMaxStudentHelper": "If offering Group or Public Sessions, set the maximum number of students per session.", "selectDateHelper": "View and manage your availability by day. You can edit or cancel specific time slots. When deleting a slot, choose to remove it only for the selected date or for all recurring days.", "enrollmentStartDate": "Set when students can begin enrolling in this public session.", "enrolmentEndDate": "Set the final date by which students can enroll. Students can only join session(s) scheduled after this date.", "selectClassTypeHelperTxt": "Select whether this public session will be held online or face to face.", "reateCardHelperTxt": "Choose a rate card previously created with public session enabled. This will define the pricing and settings for the session(s).", "timeSlotHelperTxt": "Choose the time slots from your schedule to apply to this public session. Sessions will be generated based on selected hours.", "openSessionDiscriptionHelper": "Briefly describe what you will be teaching in the session(s). This will be visible to students.", "meetinPointHelperTxt": "Add the meeting point where the face-to-face session(s) will be held. This location will be shown to students.", "meetingPreferenceHelper": "Set the maximum distance you're willing to travel for face-to-face sessions. Students outside this range won't see your profile.", "newMeetingPoingHelperTxt": "Add specific locations where you’re available to meet students. These will be visible for face-to-face bookings.", "allowStudentMeetingHelperTxt": "If enabled, students can suggest their own meeting point when booking face-to-face sessions.", "addNewMeetingPointHelperTxt": "Click to add a new face-to-face meeting location to your preferences.", "setupPublicSesctionHelper": "Set up a public group class that students can join. You’ll need to select your preferred class type (Online or Face to Face) and choose available time slots from your schedule.", "addTimeSlotHelper": "Select the start and end time for your availability. The system will automatically divide it into 1-hour time slots. You can choose to repeat the same schedule across multiple days. You can also select whether this availability is for Online, Face-to-Face, or both. You may add more slots as necessary for different weekdays, or if your availability is divided throughout the day.", "riseYourConcern": "Raise Your Concern", "dateInstruction1": "Black color indicates available time slots for the day.", "dateInstruction2": "Grey color indicates that the slots are not available for that day.", "dateInstrunction3": "Yellow dot show that you have a booking on that day.", "choose_profile": "<PERSON>ose <PERSON>", "addProfile": " Add Profile", "rateCardDiscription": "*The rate card has been sent for approval to the admin. Once approved, it will be published.", "Pending": "Pending", "Approved": "Approved", "Rejected": "Rejected", "timeValidationError": "Please select start time and end time", "Bio": "Bio", "parant": "As parents", "selectNationality": "Select nationality", "provide_f2f_price": "Please enter price for face to face session", "provide_online_price": "Please enter price for online session", "your_profile_is_currently_under_review": "Your profile is currently under review.", "pay_now": "Pay Now", "pay_with_new_payment": "Pay with new payment", "OPEN_SESSION": "Open Session", "others": "Others", "raised_tickets": "Raised Tickets", "minimum": "Minimum", "maximum": "Maximum", "hour": "hour", "hours": "hours", "reject": "Reject", "accept_booking": "Accept Booking", "commission_applied": "Commission Applied", "version": "Version", "selectGradeFirst": "Please select a grade first.", "for": "for", "students": "Students", "please_select_subject": "Please select a subject.", "km": "km", "find_ideal_tutor_effortlessly": "Find your ideal tutor effortlessly—just use the filter!", "block_user": "Block User", "block_user_confirmation": "Are you sure you want to block this user?", "user_blocked_success": "User blocked successfully", "you_have_blocked_this_chat": "You have blocked this chat", "your_chat_is_blocked": "Your chat is blocked", "user_unblocked_success": "User unblocked successfully", "approval_pending": "Approval Pending"}