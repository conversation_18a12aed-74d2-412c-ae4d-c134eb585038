import { useTranslation } from 'react-i18next';
import {Icon} from '../Utils/Icon';


// export const optionsList = [
//   {
//     id: 1,
//     title: t('mySubPlan'),
//     code: 'myPlan',
//     icon: Icon.FreePlanCartIcon,
//     isDropDown: false,
//   },
//   {
//     id: 2,
//     title: 'My Diet Plan',
//     code: 'myDietPlan',
//     icon: Icon.restaurant,
//     isDropDown: false,
//   },
//   {
//     id: 3,
//     title: 'Subscriptions',
//     code: 'subscriptions',
//     icon: Icon.calendarClock,
//     isDropDown: false,
//   },

//   {
//     id: 4,
//     title: 'My Goals',
//     code: 'myGoals',
//     icon: Icon.goal,
//     isDropDown: true,
//   },

//   {
//     id: 5,
//     title: 'Achievements',
//     code: 'achievements',
//     icon: Icon.achievment,
//     isDropDown: true,
//   },

//   {
//     id: 6,
//     title: 'My To Do List',
//     code: 'myToDos',
//     icon: Icon.lists,
//     isDropDown: false,
//   },

//   {
//     id: 7,
//     title: 'Recipies',
//     code: 'recipies',
//     icon: Icon.recipes,
//     isDropDown: false,
//   },

//   {
//     id: 8,
//     title: 'Preferences',
//     code: 'preferences',
//     icon: Icon.preferences,
//     isDropDown: false,
//   },

//   {
//     id: 9,
//     title: 'Transactions',
//     code: 'transactions',
//     icon: Icon.transactions,
//     isDropDown: false,
//   },

//   {
//     id: 10,
//     title: 'Rewards',
//     code: 'rewards',
//     icon: Icon.reward,
//     isDropDown: false,
//   },

//   {
//     id: 11,
//     title: 'Change Language',
//     icon: Icon.translate,
//     isDropDown: true,
//   },
//   {
//     id: 12,
//     title: 'Setting',
//     code: 'setting',
//     icon: Icon.setting,
//     isDropDown: true,
//   },
// ];

export const recipeData = [
  {
    description: 'Delicious spaghetti with meat sauce',
    imagePath: Icon.rectangleImge,
    vegetarian: false,
    image: Icon.caloreBurn,
    burnedCalories: '340',
    parameter: 'kcal',
  },
  {
    description: 'Classic Caesar salad with chicken',
    imagePath: Icon.rectangleImge,
    vegetarian: false,
    image: Icon.caloreBurn,
    burnedCalories: '340',
    parameter: 'kcal',
  },
  {
    description: 'Freshly baked pizza with tomato and mozzarella',
    imagePath: Icon.rectangleImge,
    vegetarian: false,
    image: Icon.caloreBurn,
    burnedCalories: '340',
    parameter: 'kcal',
  },
  {
    description: 'Freshly baked pizza with tomato and mozzarella',
    imagePath: Icon.rectangleImge,
    vegetarian: false,
    image: Icon.caloreBurn,
    burnedCalories: '340',
    parameter: 'kcal',
  },
];

export const menuItems1 = [
  {
    description: 'Seitan Wheat Meat (Vegan Meat Substitute)',
    imagePath: Icon.rectangleImg1,
    vegetarian: false,
    image: Icon.caloreBurn,
    burnedCalories: '300',
    parameter: 'kcal',
  },
  {
    description: 'Seitan Wheat Meat (Vegan Meat Substitute)',
    imagePath: Icon.rectangleImg1,
    vegetarian: false,
    image: Icon.caloreBurn,
    burnedCalories: '300',
    parameter: 'kcal',
  },
  {
    description: 'Seitan Wheat Meat (Vegan Meat Substitute)',
    imagePath: Icon.rectangleImg1,
    vegetarian: false,
    image: Icon.caloreBurn,
    burnedCalories: '300',
    parameter: 'kcal',
  },
];

export const userSavedData = {
  data: [
    {
      scrName: 'DemographicDetail',
      isSkip: false,
      id: 1,
      scrData: {
        age: '',
        height: '',
        weight: '',
        dietaryPref: [],
      },
    },
    {
      scrName: 'MedicalDetail',
      isSkip: false,
      id: 3,
      scrData: {
        haveAnyAllergy: false,
        allergyData: [],
        anyMedicalIssue: false,
        illnessData: [],
      },
    },
    {
      scrName: 'MedicalDetailSecond',
      isSkip: false,
      id: 4,
      scrData: {
        exerciseTimePref: [],
        exerciseTime: '',
        lifeStyle: '',
      },
    },
    {
      scrName: 'Goal',
      isSkip: false,
      id: 5,
      scrData: {
        efficiencyLevel: '',
        comfortableExercise: '',
      },
    },
    {
      scrName: 'GoalSecond',
      isSkip: false,
      id: 6,
      scrData: {
        fitnessGoal: '',
        targetWeight: '',
        timeframe: '',
      },
    },
    {
      scrName: 'MealPreference',
      isSkip: false,
      id: 7,
      scrData: {
        mealPrefer: '',
        dietaryRestriction: [],
      },
    },
    {
      scrName: 'MealPreferenceSecond',
      isSkip: false,
      id: 8,
      scrData: {
        preferredCuisine: [],
        foodPreference: {
          isLike: false,
          like: [],
          isDislike: [],
        },
      },
    },
  ],
};

export const timeframeData = [
  {_id: '1', name: '7 days'},
  {_id: '2', name: '15 days'},
  {_id: '3', name: '28 days'},
  {_id: '4', name: '60 days'},
  {_id: '5', name: '90 days'},
];

export const exerciseTimeData = [
  {_id: '1', name: '10 Min'},
  {_id: '2', name: '15 Min'},
  {_id: '3', name: '20 Min'},
  {_id: '4', name: '30 Min'},
  {_id: '5', name: '1 Hour'},
  {_id: '6', name: '1:20 Hour'},
  {_id: '7', name: '1:40 Hour'},
  {_id: '8', name: '2:00 Hour'},
  {_id: '9', name: '2:20 Hour'},
  {_id: '10', name: '2:40 Hour'},
  {_id: '11', name: '3:00 Hour'},
];


export const MyGoalsData = [
  {
    goalTitle: 'Steps',
    value: '5500 / 10000',
    image: Icon.goalFoot,
  },
  {
    goalTitle: 'Water',
    value: '9 glass/day',
    image: Icon.water,
  },
  {
    goalTitle: 'Weight',
    value: '74 lbs',
    image: Icon.weightImg,
  },
  {
    goalTitle: 'Sleep',
    value: '7 hrs',
    image: Icon.sleep,
  },
];
