import {combineReducers, configureStore} from '@reduxjs/toolkit';
import {persistStore, persistReducer} from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import authReducer from '../Features/authSlice';
import apiSlice from '../Api/ApiSlice';
import tutorBookingSlice from '../Redux/Slices/Student/TutorBookingSlice'; // Import your new slice
import generalStudentSlice from '../Redux/Slices/Student/GeneralStudentSlice'; // Import your new slice
import slotSlice from '../Redux/Slices/Student/SlotSlice'; // Import your new slice
import rateCardSlice from '../Redux/Slices/Tutor/RateCardSlice'; // Import your new slice
import currentLocSlice from '../Redux/Slices/General/CurrentLocSlice'; // Import your new slice
import tutorSlotReducer from '../Redux/Slices/Tutor/TutorSlotSlice';
import selectedKidReducer from '../Redux/Slices/Parent/SelectedKidSlice';
import notificationReducer from '../Redux/Slices/NotiSlice/NotificationSlice';

// Persist configuration

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['auth'], // Only auth reducer will be persisted
  blacklist: [
    apiSlice.reducerPath,
    'tutorBookingSlice',
    'generalStudentSlice',
    'slotSlice',
    'rateCardSlice',
    'currentLocSlice',
    'selectedKidSlice',
  ], // Exclude apiSlice and tutorBookingSlice from persistence
};

// Combine reducers
const rootReducer = combineReducers({
  auth: authReducer,
  [apiSlice.reducerPath]: apiSlice.reducer,
  tutorBookingSlice: tutorBookingSlice,
  generalStudentSlice: generalStudentSlice,
  slotSlice: slotSlice,
  rateCardSlice: rateCardSlice,
  currentLocSlice: currentLocSlice,
  tutorSlot: tutorSlotReducer,
  selectedKidSlice: selectedKidReducer,
  notifications: notificationReducer,
  // Add the new slice here
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: [
          'tutorBookingSlice.packageStartDate',
          'tutorBookingSlice.selectedSlotDate',
          'slotSlice.currentDate',
        ], // Ignore this specific path
      },
    }).concat(apiSlice.middleware),
});

// Persistor
export const persistor = persistStore(store);
