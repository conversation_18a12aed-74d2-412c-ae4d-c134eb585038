import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize, SCREEN_HEIGHT} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

const styles = StyleSheet.create({
  buttonContainer: {
    paddingVertical: 10,
    marginLeft: wp(2),
  },
  topButton: {
    borderRadius: fp(2),
    paddingVertical: fp(0.6),
    paddingHorizontal: fp(1.8),
    marginHorizontal: fp(0.4),
    marginVertical: hp(0.6),
    elevation: 2,
  },
  btnTxt: {
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
  },
  contentContainer: {
    flex: 1,
    marginHorizontal: 20,
    marginTop: 10,
  },
  bottomView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    width: '80%',
    alignSelf: 'center',
    marginBottom: 10,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0)',
  },
  modalView: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    height: '30%',
    padding: 10,
  },
  modalTitle: {
    fontSize: fp(1.8),
    color: colors.black,
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
    marginRight: 8,
  },
  headingContainer: {
    marginVertical: 10,
    alignItems: 'center',
  },
  modalButton: {
    borderRadius: 15,
    paddingVertical: 4,
    paddingHorizontal: 12,
    marginHorizontal: 5,
    height: SCREEN_HEIGHT * 0.05,
    marginVertical: 4,
    justifyContent: 'center',
  },
  noData: {
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
    textAlign: 'center',
  },
});

export default styles;
