import React, {useState, useCallback} from 'react';
import {
  FlatList,
  Modal,
  Platform,
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import RateDetailsCard from '../../Components/RateDetailsCard';
import {PrimaryButton} from '../../Components/CustomButton';
import CourseCardTutor from '../../Components/CourseCardTutor';
import styles from './styles';

import {
  useGetAcademicRateCardQuery,
  useGetCourseRateCardQuery,
  useGetRecreationalRateCardQuery,
} from '../../Api/ApiSlice';

import {useFocusEffect} from '@react-navigation/native';
import {updateRateCardLength} from '../../Redux/Slices/Tutor/RateCardSlice';
import {useDispatch} from 'react-redux';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import {hp} from '../../Helper/ResponsiveDimensions';
import HelperTextComponent from '../../Components/HelperTipComp';
// import {SafeAreaView} from 'react-native-safe-area-context';

//
const transformAcademicData = item => {
  console.log('🚀 ~ item: academic', item);
  const onlineClass = item.tlm_tutor_class_types?.find(
    t => t.tlm_class_type.id === 1,
  );
  const faceToFaceClass = item.tlm_tutor_class_types?.find(
    t => t.tlm_class_type.id === 2,
  );
  const openSessionClass = item.tlm_tutor_class_types?.find(
    t => t.tlm_class_type.id === 3,
  );

  return {
    id: item.id,
    title:
      item?.tlm_subject?.name == 'Other' || item?.tlm_subject?.name == 'Others'
        ? item?.optional_subject
        : item?.tlm_subject?.name,
    curriculum: item.tlm_curriculum?.name,
    language: item.language,
    priceForOnline: onlineClass?.price,
    priceForFaceToFace: faceToFaceClass?.price,
    packageAvailable: item.tlm_tutor_rate_card_packages?.length > 0,

    // Commission and rate for online classes
    onlineCommissionPrice: onlineClass?.commission_price,
    onlineCommissionRate: onlineClass?.rate,

    // Commission and rate for face-to-face classes
    faceToFaceCommissionPrice: faceToFaceClass?.commission_price,
    faceToFaceCommissionRate: faceToFaceClass?.rate,
    openSessionCommissionPrice: openSessionClass?.commission_price,
    openSessionCommissionRate: openSessionClass?.rate,
    status: item?.status,
  };
};

const transformRecreationalData = item => {
  console.log('🚀 ~ item: recreational', item);
  const onlineClass = item.tlm_tutor_class_types?.find(
    t => t.tlm_class_type.value?.toLowerCase() === 'online',
  );
  const faceToFaceClass = item.tlm_tutor_class_types?.find(
    t => t.tlm_class_type.value === 'faceToFace',
  );
  const openSessionClass = item.tlm_tutor_class_types?.find(
    t => t.tlm_class_type.value?.toLowerCase() === 'opensession',
  );

  return {
    id: item.id,
    title:
      item?.tlm_expertise?.name == 'Other' ||
      item?.tlm_expertise?.name == 'Others'
        ? item?.other_expertise || 'Other'
        : item?.tlm_expertise?.name,
    language: '',
    priceForOnline: onlineClass?.price,
    priceForFaceToFace: faceToFaceClass?.price,
    packageAvailable:
      item.tlm_tutor_rate_card_packages?.length > 0 ? true : false,

    // Commission and rate for online classes
    onlineCommissionPrice: onlineClass?.commission_price,
    onlineCommissionRate: onlineClass?.rate,

    // Commission and rate for face-to-face classes
    faceToFaceCommissionPrice: faceToFaceClass?.commission_price,
    faceToFaceCommissionRate: faceToFaceClass?.rate,
    openSessionCommissionPrice: openSessionClass?.commission_price,
    openSessionCommissionRate: openSessionClass?.rate,
    status: item?.status,
  };
};

const RateCardScreen = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const dispatch = useDispatch();
  const [activeBtn, setActiveBtn] = useState(1);
  const [modalVisible, setModalVisible] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const isRTL = i18n.language === 'ar';
  const [openHelperText, setOpenHelperText] = useState(false);

  const {
    data: academicData,
    error,
    isLoading,
    refetch: refetchAcademic,
  } = useGetAcademicRateCardQuery();
  const {data: recreationalData, refetch: refetchRecreational} =
    useGetRecreationalRateCardQuery();
  console.log(
    '🚀 ~ RateCardScreen ~ recreationalData:',
    JSON.stringify(recreationalData),
  );

  const {data: courseData, refetch: refetchCourse} =
    useGetCourseRateCardQuery();

  const buttonData = [
    {id: 1, title: t('academic')},
    {id: 2, title: t('recreational')},
    {id: 3, title: t('courses')},
  ];

  useFocusEffect(
    useCallback(() => {
      console.log('RateCardScreen is focused');
      refetchAcademic();
      refetchRecreational();
      refetchCourse();
      return () => {
        console.log('RateCardScreen is unfocused');
      };
    }, [refetchAcademic, refetchRecreational, refetchCourse]),
  );

  const getActiveData = () => {
    if (activeBtn === 1) {
      const rawData = academicData?.data?.rows || [];
      console.log('acadmeic length', rawData?.length);

      return rawData.map(transformAcademicData);
    }
    if (activeBtn === 2) {
      const rawData = recreationalData?.data?.rows || [];
      updateRateCardLength(rawData?.length);
      return rawData.map(transformRecreationalData);
    }
    if (activeBtn === 3) {
      updateRateCardLength(courseData?.data?.rows?.length);
      return courseData?.data?.rows || [];
    }
    return [];
  };

  const renderItem = ({item}) => {
    console.log(
      'item: ---00-0--------------------------------',
      JSON.stringify(item),
    );

    if (activeBtn === 3) {
      // Extract online and face-to-face class details
      const onlineClass = item.tlm_tutor_class_types?.find(
        type => type.tlm_class_type?.value.toLowerCase() === 'online',
      );
      const f2fClass = item.tlm_tutor_class_types?.find(
        type => type.tlm_class_type.value === 'faceToFace',
      );
      const openSessionClass = item.tlm_tutor_class_types?.find(
        type => type.tlm_class_type.value.toLowerCase() === 'opensession',
      );

      // Prepare the required data
      const title = item.course_title || '';

      const language = item.language || t('English');
      const level = item.tlm_course_level?.name || '';
      const duration = item.course_duration
        ? `${item.course_duration} hours`
        : '';
      // const type = item.tlm_course_category?.name || '';
      const type = item?.tlm_course_category?.is_extra
        ? item?.other_category
        : item?.tlm_course_category?.name;
      const priceOnline = onlineClass?.price || '';
      const priceFaceToFace = f2fClass?.price || '';
      const priceOpenSession = openSessionClass?.price || '';

      const commission_price_online = onlineClass?.commission_price || '';
      const rate_online = onlineClass?.rate || '';

      const commission_price_facetoface = f2fClass?.commission_price || '';
      const rate_facetoface = f2fClass?.rate || '';
      const commission_price_openSession =
        openSessionClass?.commission_price || '';
      const rate_opensession = openSessionClass?.rate || '';

      // Render the CourseCardTutor component
      return (
        <CourseCardTutor
          item={item}
          title={title}
          language={language}
          level={level}
          duration={duration}
          type={type}
          priceOnline={priceOnline}
          priceFaceToFace={priceFaceToFace}
          priceOpenSession={priceOpenSession}
          commission_price_online={commission_price_online}
          rate_online={rate_online}
          commission_price_facetoface={commission_price_facetoface}
          rate_facetoface={rate_facetoface}
          rate_opensession={rate_opensession}
          commision_price_opensession={commission_price_openSession}
          onPressEdit={() =>
            navigation.navigate('EditCourses', {
              courseCardId: item.id,
              cummissionOnline: rate_online,
              cummissionF2F: rate_facetoface,
            })
          }
        />
      );
    } else if (activeBtn === 2) {
      return (
        <RateDetailsCard
          title={item.title}
          language={item.language || t('English')}
          curriculum={item.curriculum || 'N/A'}
          duration={item.duration || 'N/A'}
          onlinePrice={parseFloat(item.priceForOnline).toString() || 'N/A'}
          packageAvailable={item.packageAvailable || 'N/A'}
          priceForFaceToFace={
            parseFloat(item.priceForFaceToFace).toString() || 'N/A'
          }
          // New commission and rate properties
          onlineCommissionPrice={item?.onlineCommissionPrice}
          onlineCommissionRate={item.onlineCommissionRate}
          faceToFaceCommissionPrice={item.faceToFaceCommissionPrice}
          faceToFaceCommissionRate={item.faceToFaceCommissionRate}
          openSessionCommisionPrice={item?.openSessionCommissionPrice}
          status={item?.status}
          onPressEdit={() =>
            navigation.navigate('EditRecreational', {
              rateCardId: item.id,
              cummissionOnline: item.onlineCommissionRate,
              cummissionF2F: item?.faceToFaceCommissionRate,
            })
          }
        />
      );
    } else {
      return (
        <RateDetailsCard
          isAcademic={true}
          title={item?.title}
          language={item.language || t('English')}
          curriculum={item.curriculum || 'N/A'}
          duration={item.duration || 'N/A'}
          onlinePrice={parseFloat(item.priceForOnline).toString() || 'N/A'}
          packageAvailable={item.packageAvailable || 'N/A'}
          priceForFaceToFace={
            parseFloat(item.priceForFaceToFace).toString() || 'N/A'
          }
          onPressEdit={() =>
            navigation.navigate('EditAcademic', {
              rateCardId: item.id,
              cummissionOnline: item.onlineCommissionRate,
              cummissionF2F: item?.faceToFaceCommissionRate,
            })
          }
          onlineCommissionPrice={item?.onlineCommissionPrice}
          onlineCommissionRate={item.onlineCommissionRate}
          faceToFaceCommissionPrice={item.faceToFaceCommissionPrice}
          faceToFaceCommissionRate={item.faceToFaceCommissionRate}
          openSessionCommisionPrice={item?.openSessionCommissionPrice}
          status={item?.status}
          // openSessionCommisionRate={rate_opensession}
        />
      );
    }
  };

  const renderContent = () => {
    if (isLoading)
      return (
        <View style={styles.noDataContainer}>
          <ActivityIndicator size="large" color={colors.themeColor} />
          <Text style={styles.noData}>{t('loading')}</Text>
        </View>
      );
    if (error)
      return (
        <View style={styles.noDataContainer}>
          <Text style={styles.noData}>{t('error_loading_data')}</Text>
        </View>
      );
    const activeData = getActiveData();

    if (activeData.length === 0)
      return <Text style={styles.noData}>{t('no_rate_card_found')}</Text>;
    else {
      console.log('else conditon working');
      dispatch(updateRateCardLength(activeData.length));
    }
    return (
      <FlatList
        data={activeData}
        renderItem={renderItem}
        keyExtractor={item => item?.id?.toString()}
        refreshing={isRefreshing}
        onRefresh={handleRefresh}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  const handleRefresh = useCallback(async () => {
    try {
      setIsRefreshing(true);
      await Promise.all([
        refetchAcademic(),
        refetchRecreational(),
        refetchCourse(),
      ]);
      console.log('Data refreshed successfully');
    } catch (err) {
      console.error('Error refreshing data:', err);
    } finally {
      setIsRefreshing(false);
    }
  }, [refetchAcademic, refetchRecreational, refetchCourse]);

  const addNewRateCard = (id, name) => {
    setActiveBtn(id);
    setModalVisible(false);
    navigation.navigate('AddYourRateCard', {id, label: name});
  };

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('rate_card')} />

      {/* Top Tabs */}
      <View
        style={[
          styles.buttonContainer,
          {alignItems: isRTL ? 'flex-end' : 'flex-start'},
        ]}>
        <FlatList
          data={buttonData}
          horizontal
          contentContainerStyle={{flexDirection: isRTL ? 'row-reverse' : 'row'}}
          keyExtractor={item => item.id.toString()}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={() => setActiveBtn(item.id)}
              style={[
                applyShadowStyleIos(styles.topButton),
                {
                  backgroundColor:
                    activeBtn === item.id ? colors.themeColor : colors.white,
                },
              ]}>
              <Text
                style={[
                  styles.btnTxt,
                  {
                    color:
                      activeBtn === item.id ? colors.white : colors.greyLight,
                  },
                ]}>
                {item.title}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>

      {/* Content */}
      <View style={styles.contentContainer}>{renderContent()}</View>

      {/* Bottom Buttons */}
      <View style={styles.bottomView}>
        {/* <PrimaryButton
          title={t('filter')}
          style={{width: '45%', borderRadius: 30}}
          rightIcon={icons.filterIcon}
          textStyle={{marginLeft: 20}}
        /> */}
        <PrimaryButton
          title={t('add')}
          onPress={() => setModalVisible(!modalVisible)}
          style={{width: '90%', borderRadius: 30}}
        />
      </View>

      {/* Modal */}
      <Modal
        animationType="slide"
        transparent
        visible={modalVisible}
        onRequestClose={() => setModalVisible(!modalVisible)}>
        <TouchableWithoutFeedback onPress={() => setModalVisible(false)}>
          <View style={applyShadowStyleIos(styles.modalContainer)}>
            <View style={styles.modalView}>
              <View style={styles.headingContainer}>
                <View style={{flexDirection: 'row', alignItems: 'baseline'}}>
                  <Text style={styles.modalTitle}>{t('add_rate_card')}</Text>
                  <HelperTextComponent
                    helperText={t('rateCardHelper')}
                    setOpen={setOpenHelperText}
                    open={openHelperText}
                    borderColor={colors.black}
                    iconColor={colors.black}
                  />
                </View>
              </View>
              <FlatList
                data={buttonData}
                keyExtractor={item => item.id.toString()}
                renderItem={({item}) => (
                  <TouchableOpacity
                    onPress={() => addNewRateCard(item.id, item.title)}
                    style={[
                      applyShadowStyleIos(styles.modalButton),
                      {
                        backgroundColor:
                          activeBtn === item.id
                            ? colors.themeColor
                            : colors.white,
                      },
                    ]}>
                    <Text
                      style={[
                        styles.btnTxt,
                        {
                          textAlign: isRTL ? 'right' : 'left',
                          color:
                            activeBtn === item.id
                              ? colors.white
                              : colors.txtGrey1,
                        },
                      ]}>
                      {item.title}
                    </Text>
                  </TouchableOpacity>
                )}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </SafeAreaView>
  );
};

export default RateCardScreen;
