import React, {useState} from 'react';
import {
  ActivityIndicator,
  Image,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {StatusContainer} from '../../Components/StatusBar';
import FormHeader from '../../Components/FormHeader';
import FileUploadBox from '../../Components/FileUploadBox';
import colors from '../../Utils/colors';
import CustomButton from '../../Components/Custom_Components/customButton';
import DocumentPicker from 'react-native-document-picker';
import {useTranslation} from 'react-i18next';
import {
  useUpdateTutorProfileMutation,
  useUploadDocumentMutation,
  useUploadTutorDocumentsMutation,
} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';
import AppLoader from '../../Helper/AppLoader/AppLoader';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';
import icons from '../../Utils/icons';

const CompleteProfileTutorAfterSignUpTwo = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [uploadDocument, {isLoading: s3uploadLoading}] =
    useUploadDocumentMutation();

  const [uploadTutorDocuments, {isLoading: docLoading}] =
    useUploadTutorDocumentsMutation();

  const [updateTutorProfile, {isLoading}] = useUpdateTutorProfileMutation();

  const [academicDocuments, setAcademicDocuments] = useState([]);
  const [addressProofDocuments, setAddressProofDocuments] = useState([]);
  const [idPhotoDocuments, setIdPhotoDocuments] = useState([]);
  const [otherDocument, setOtherDocument] = useState([]);
  const [loading, setLoading] = useState(false);

  const haddleSkip = () => {
    navigation?.navigate('Home');
    // navigation.navigate('Home');
  };

  const handleFileUpload = async (docType, setDocuments) => {
    console.log('🚀 ~ handleFileUpload ~ docType:', docType);
    console.log('🚀 ~ handleFileUpload ~ setDocuments:', setDocuments);
    try {
      const result = await DocumentPicker.pick({
        type: [DocumentPicker.types.pdf, DocumentPicker.types.images],
      });

      console.log(`${docType} Document:`, JSON.stringify(result));

      const formData = new FormData();

      formData.append('file', {
        uri: result[0].uri,
        type: result[0].type,
        name: result[0].name,
      });
      console.log(
        '🚀 ~ handleFileUpload ~ formData:',
        JSON.stringify(formData),
      );
      setLoading(true);
      let success = false;
      let retryCount = 0;
      const maxRetries = 3;
      let response;

      while (!success && retryCount < maxRetries) {
        try {
          response = await uploadDocument(formData);
          console.log('🚀 ~ handleFileUpload ~ response:', response?.data);
          if (response?.data?.status) {
            success = true;
          } else {
            retryCount++;
            console.log(`Retrying upload... Attempt ${retryCount}`);
          }
        } catch (uploadError) {
          console.error('Upload attempt failed:', uploadError);
          retryCount++;
        }
      }

      setLoading(false);

      if (success) {
        setDocuments([
          {
            name: result[0].name,
            uri: response?.data?.data?.path,
            size: result[0].size,
            type: response?.data?.data?.contentType,
          },
        ]);
        showToast('success', response?.data?.message, 'bottom', isRTL);
      } else {
        showToast(
          'error',
          response?.data?.message || t('docUploadFailed'),
          'bottom',
          isRTL,
        );
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        console.log('User canceled the document picker');
      } else {
        console.error('Error uploading document:', err);
        showToast('error', t('unexpectedError'), 'bottom', isRTL);
      }
    }
  };

  const validateFile = () => {
    if (academicDocuments[0]?.uri && idPhotoDocuments[0]?.uri) {
      handleClick();
    } else {
      showToast('error', t('pleaseSelectDoc'), 'bottom', isRTL);
    }
  };

  const handleClick = async () => {
    console.log('🚀 ~ handleClick ~ handleClick:');

    try {
      console.log('documents', {
        academic_document: academicDocuments[0]?.uri || '',
        address_proof: addressProofDocuments[0]?.uri || '',
        id_photo: idPhotoDocuments[0]?.uri || null,
      });

      const response = await uploadTutorDocuments({
        academic_document: academicDocuments[0]?.uri || null,
        address_proof: addressProofDocuments[0]?.uri || null,
        id_photo: idPhotoDocuments[0]?.uri || null,
        other_document: otherDocument[0]?.uri || null,
      }).unwrap();

      console.log('response in upload file apiii', JSON.stringify(response));

      showToast('success', response?.message, 'bottom', isRTL);
      navigation.navigate('Home');

      // handleUploadTutorProfile();
    } catch (error) {
      console.log('🚀 ~ handleClick ~ error:', error);
      // console.error('Error uploading tutor documents:', error);
      showToast('error', t('uploadFailed'), 'bottom', isRTL);
    }
  };

  // const handleUploadTutorProfile = async () => {
  //   const profileData = {
  //     id_photo: idPhotoDocuments[0]?.uri || null,
  //     academic_document: academicDocuments[0]?.uri || null,
  //     address_proof: addressProofDocuments[0]?.uri || null,
  //     profile_completion_step: '2',
  //   };

  //   try {
  //     const response = await updateTutorProfile(profileData);
  //     console.log('Form submitted successfully', JSON.stringify(response));
  //     showToast('success', response?.data?.message);
  //     navigation.navigate('AllSetPage');
  //   } catch (error) {
  //     console.error('Error submitting form', error);
  //   }
  // };
  console.log('35462783980', academicDocuments);
  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={docLoading ? 'rgba(0,0,0,0.2)' : colors.white} />
      <FormHeader
        title={t('completeProfile')}
        titleStyle={{
          color: colors.black,
          fontFamily: Fonts.bold,
        }}
        showBackButton={true}
        showSkipButton={true}
        style={{backgroundColor: colors.white}}
        buttonStyle={{color: colors.black}}
        onSkip={haddleSkip}
      />
      <View style={styles.progressBar}>
        <View style={styles.progressBarFilled}></View>
        <View style={styles.progressBarUnfilled}></View>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}>
        <Modal
          animationType={'fade'}
          visible={docLoading || s3uploadLoading}
          transparent>
          <View style={styles.loadingContainer}>
            <View style={styles.loader}>
              <ActivityIndicator size={'large'} color={colors.themeColor} />
            </View>
          </View>
        </Modal>

        <View style={styles.uploadContainer}>
          <Text
            style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>{`${t(
            'uploadAcademicDocument',
          )}*`}</Text>
          <FileUploadBox
            loading={loading}
            onBrowsePress={() =>
              handleFileUpload('Academic Document', setAcademicDocuments)
            }
          />
          {academicDocuments.map((doc, index) => (
            <TouchableOpacity
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: 'center',
              }}
              onPress={() => navigation.navigate('DocViewer', {doc})}>
              <Text key={index} style={styles.uploadedDocument}>
                {doc.name}
              </Text>
              <View style={styles.view}>
                <Text style={styles.see}>{t('view')}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.uploadContainer}>
          <Text
            style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>{`${t(
            'uploadAddressProof',
          )}`}</Text>
          <FileUploadBox
            loading={loading}
            onBrowsePress={() =>
              handleFileUpload('Address Proof', setAddressProofDocuments)
            }
          />
          {addressProofDocuments.map((doc, index) => (
            <TouchableOpacity
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: 'center',
              }}
              onPress={() => navigation.navigate('DocViewer', {doc})}>
              <Text key={index} style={styles.uploadedDocument}>
                {doc.name}
              </Text>
              <View style={[styles.view]}>
                <Text style={styles.see}>{t('view')}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.uploadContainer}>
          <Text
            style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>{`${t(
            'idPhotoAttachment',
          )}*`}</Text>
          <FileUploadBox
            loading={loading}
            onBrowsePress={() =>
              handleFileUpload('ID Photo', setIdPhotoDocuments)
            }
          />
          {idPhotoDocuments.map((doc, index) => (
            <TouchableOpacity
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: 'center',
              }}
              onPress={() => navigation.navigate('DocViewer', {doc})}>
              <Text key={index} style={styles.uploadedDocument}>
                {doc.name}
              </Text>
              <View style={styles.view}>
                <Text style={styles.see}>{t('view')}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.uploadContainer}>
          <Text
            style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>{`${t(
            'other_document',
          )} (${t('optional')})`}</Text>
          <FileUploadBox
            loading={loading}
            onBrowsePress={() =>
              handleFileUpload('other_document', setOtherDocument)
            }
          />
          {otherDocument.map((doc, index) => (
            <TouchableOpacity
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: 'center',
              }}
              onPress={() => navigation.navigate('DocViewer', {doc})}>
              <Text key={index} style={styles.uploadedDocument}>
                {doc.name}
              </Text>
              <View style={styles.view}>
                <Text style={styles.see}>{t('view')}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <CustomButton
          style={styles.continueButton}
          title={t('continue')}
          onPress={validateFile}
          textStyle={styles.continueText}
        />
      </View>
    </SafeAreaView>
  );
};

export default CompleteProfileTutorAfterSignUpTwo;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  label: {
    fontSize: fp(1.6),
    color: colors.black,
    marginBottom: 8,
    fontFamily: Fonts.medium,
  },
  progressBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  uploadContainer: {
    marginHorizontal: 16,
    marginBottom: 20,
  },
  progressBarFilled: {
    flex: 1,
    height: 5,
    backgroundColor: colors.themeColor,
    borderRadius: 5,
    marginRight: 3,
  },
  progressBarUnfilled: {
    flex: 1,
    height: 5,
    backgroundColor: colors.themeColor,
    borderRadius: 5,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  continueButton: {
    backgroundColor: colors.themeColor,
    borderRadius: 8,
    alignItems: 'center',
    padding: 16,
    marginTop: 16,
    width: '100%',
  },
  buttonContainer: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: colors.white,
  },
  continueText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: Fonts.semiBold,
  },
  uploadedDocument: {
    fontSize: responsiveFontSize(14),
    color: colors.black,
    marginTop: 5,
    fontFamily: Fonts.medium,
    // lineHeight: hp(5),
    fontSize: fp(1.8),
    textDecorationLine: 'underline',
  },
  loadingContainer: {
    height: hp(120),
    width: wp(100),
    alignSelf: 'center',
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  loader: {
    // flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    // backgroundColor: colors.white,
    // shadowOffset: {
    //   width: 0,
    //   height: 3,
    // },
    // shadowOpacity: 0.29,
    // shadowRadius: 4.65,
    // elevation: 7,
  },
  see: {
    fontFamily: Fonts.regular,
    color: colors.themeColor,
    textDecorationLine: 'underline',
    fontSize: fp(1.8),
  },
  view: {
    marginLeft: wp(1.5),
    marginTop: hp(0.5),
  },
});

//951159951
