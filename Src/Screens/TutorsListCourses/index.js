import React, {useEffect, useState} from 'react';
import {
  Image,
  Pressable,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from 'react-native';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import {FilterButton} from '../../Components/FilterButton';
import CourseCard from '../../Components/CourseCard';
import styles from './styles';
import {useGetTutorsAllCoursesQuery} from '../../Api/ApiSlice';
import TutorListHeader from '../../Components/TutorListHeader';
import {Fonts} from '../../Utils/Fonts';
import {useDebounce} from 'use-debounce';
import {DUMMY_COURSE_IMG} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {fp, hp} from '../../Helper/ResponsiveDimensions';

const TutorsListCourses = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const {course_category_id} = route.params;
  const [data, setData] = useState([]);
  const isRTL = i18n.language === 'ar';
  const [searchQuery, setSearchQuery] = useState('');

  const {
    data: TutorsAllCoursesData,
    error,
    isLoading,
  } = useGetTutorsAllCoursesQuery({
    course_category_id,
    course_title: searchQuery,
  });

  useEffect(() => {
    console.log('API Data:', TutorsAllCoursesData);
    console.log('Loading Status:', isLoading);
    console.log('Error:', error);
  }, [TutorsAllCoursesData, error, isLoading]);

  const handleCourseClick = id => {
    navigation.navigate('TutorsDetailsCourses', {id});
  };
  useEffect(() => {
    setData(TutorsAllCoursesData?.data?.rows);
  }, [TutorsAllCoursesData, searchQuery]);
  const courses = TutorsAllCoursesData?.data?.rows || [];

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <TutorListHeader
        onBackPress={() => navigation.goBack()}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />
      <View style={styles.content}>
        <Text style={styles.title}>{`${t('courses')} (${
          data?.length ? data?.length : 0
        })`}</Text>
        {courses.length > 0 ? (
          <ScrollView
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}>
            {courses.map((course, index) => {
              const sessionType =
                course.sessions?.[0]?.tlm_sessions_type?.name || 'N/A';
              const price = course.sessions?.[0]?.price || course.price;
              const courseImageUrl = course?.image
                ? {uri: `${IMAGE_BASE_URL}${course?.image}`} // Updated image URL concatenation
                : {uri: DUMMY_COURSE_IMG};
              return (
                <CourseCard
                  key={course.id}
                  title={course.course_title}
                  level={course.tlm_course_level?.name}
                  type={sessionType}
                  duration={course.course_duration}
                  price={`${price} QAR/hr`}
                  rating={null}
                  icon={icons.teacherImage1}
                  onPress={() => handleCourseClick(course.id)}
                  imgUrl={courseImageUrl}
                />
              );
            })}
          </ScrollView>
        ) : (
          <Text
            style={{
              alignSelf: 'center',
              fontFamily: Fonts.medium,
              fontSize: fp(1.8),
              marginTop: hp(2),
            }}>
            No Courses Found
          </Text>
        )}

        {/* 
        <FilterButton
          onPress={() => navigation.navigate('FilterScreen')}
          title={t('filter')}
          textStyle={{
            fontSize: 16,
            color: colors.white,
            fontFamily: Fonts.regular,
          }}
        /> */}
      </View>
    </SafeAreaView>
  );
};

export default TutorsListCourses;
