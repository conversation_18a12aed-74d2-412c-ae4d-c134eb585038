import {I18nManager, StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 20,
    color: colors.black,
    fontFamily: Fonts.bold,
    marginVertical: 16,
  },
  countText: {
    fontSize: 16,
    color: colors.black,
    fontFamily: Fonts.medium,
    marginBottom: 8,
  },
  listContainer: {
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.themeColor,
    height: 50,
    width: '100%',
  },
  backButtonContainer: {
    width: '13%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButton: {
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    height: 18,
    width: 18,
    tintColor: colors.white,
  },
  searchContainer: {
    flex: 1,
  },
  searchBar: {
    flexDirection: 'row',
    width: '87%',
    height: '100%',
    paddingRight: 10,
    alignItems: 'center',
  },
  searchIcon: {
    width: 18,
    height: 18,
    marginRight: 8,
  },
  searchPlaceholder: {
    flex: 1,
    fontSize: 14,
    color: colors.searchGray,
    padding: 0,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.offWhite1,
    borderRadius: 20,
    height: 25,
    marginLeft: 10,
    paddingHorizontal: 8,
  },
  locationIcon: {
    width: 14,
    height: 14,
  },
  locationText: {
    marginHorizontal: 8,
    color: colors.black,
    fontSize: 12,
  },
  downArrowIcon: {
    width: 14,
    height: 14,
  },
});

export default styles;
