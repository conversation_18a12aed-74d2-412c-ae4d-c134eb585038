import React, {useState, useEffect, useRef} from 'react';
import {
  SafeAreaView,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  View,
  TextInput,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
} from 'react-native';
import MapView, {Marker} from 'react-native-maps';
import {Fonts} from '../../Utils/Fonts';
import {useRoute, useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import icons from '../../Utils/icons';
import {AppHeader} from '../../Components/Header';
import colors from '../../Utils/colors';
import {StatusContainer} from '../../Components/StatusBar';
import {PrimaryButton} from '../../Components/CustomButton';
import Geolocation from 'react-native-geolocation-service';

import {
  useGetMeetingPointsByIdQuery,
  useEditMeetingPointMutation,
  useDeleteMeetingPointMutation,
} from '../../Api/ApiSlice';
import {API_KEY_PERSONAL, IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {showToast} from '../../Components/ToastHelper';
import {wp, hp} from '../../Helper/ResponsiveDimensions';
import {requestLocationPermission} from '../../Helper/Location/LocationHelpers';

const EditMeetingPoint = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const route = useRoute();
  const navigation = useNavigation();
  const {id} = route.params;

  const [address, setAddress] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [deleteId, setDeleteId] = useState();
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [region, setRegion] = useState(null);
  const [markerPosition, setMarkerPosition] = useState(null);
  const [shouldFetchSuggestions, setShouldFetchSuggestions] = useState(false); // Added flag

  const inputRef = useRef(null);
  const mapRef = useRef(null);
  const scrollViewRef = useRef(null);

  const {
    data: meetingPointData,
    isLoading: isLoadingMeetingPoint,
    error: fetchError,
    refetch: refetchMeetingPoint,
  } = useGetMeetingPointsByIdQuery(id);

  const fullAddressForPopulatedTheAddressField =
    meetingPointData?.data?.full_address;

  const [editMeetingPoint] = useEditMeetingPointMutation();
  const [deleteMeetingPoint] = useDeleteMeetingPointMutation();

  useEffect(() => {
    setDeleteId(id);
    if (meetingPointData?.data) {
      setAddress(meetingPointData.data.full_address);
      const initialRegion = {
        latitude: parseFloat(meetingPointData.data.latitude),
        longitude: parseFloat(meetingPointData.data.longitude),
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
      setRegion(initialRegion);
      setMarkerPosition({
        latitude: parseFloat(meetingPointData.data.latitude),
        longitude: parseFloat(meetingPointData.data.longitude),
      });
      setSelectedLocation({
        lat: parseFloat(meetingPointData.data.latitude),
        lng: parseFloat(meetingPointData.data.longitude),
        city: meetingPointData.data.city,
        country: meetingPointData.data.country,
      });
    }
  }, [meetingPointData]);

  useEffect(() => {
    const initializeLocation = async () => {
      try {
        const hasPermission = await requestLocationPermission();
        if (!hasPermission) {
          console.log('Location permission denied');
          return;
        }

        Geolocation.getCurrentPosition(
          position => {
            const {latitude, longitude} = position.coords;
            if (!region) {
              const initialRegion = {
                latitude,
                longitude,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
              };
              setRegion(initialRegion);
              setMarkerPosition({latitude, longitude});
            }
          },
          error => {
            console.error('Geolocation error:', error);
            if (Platform.OS === 'ios') {
              Alert.alert(
                'Location Error',
                'Please enable location services in Settings to use this feature',
                [
                  {
                    text: 'Open Settings',
                    onPress: () => Linking.openSettings(),
                  },
                  {text: 'Cancel', style: 'cancel'},
                ],
              );
            }
          },
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 10000,
            distanceFilter: 10,
          },
        );
      } catch (error) {
        console.error('Location initialization error:', error);
      }
    };

    initializeLocation();
    return () => {
      Geolocation.stopObserving();
    };
  }, []);

  useEffect(() => {
    if (
      shouldFetchSuggestions &&
      address &&
      address !== meetingPointData?.data?.full_address
    ) {
      fetchSuggestions(address);
    } else {
      setSuggestions([]); // Clear suggestions when not fetching
    }
  }, [address, shouldFetchSuggestions, meetingPointData]);

  const fetchSuggestions = async query => {
    if (query.length <= 2) {
      setSuggestions([]);
      return;
    }
    setIsLoadingSuggestions(true);
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
          query,
        )}&key=${API_KEY_PERSONAL}&language=en`,
      );
      const data = await response.json();
      if (data.status === 'OK') {
        setSuggestions(data.predictions);
      } else {
        setSuggestions([]);
      }
    } catch (error) {
      console.error('Error fetching Google Places suggestions:', error);
      setSuggestions([]);
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const fetchPlaceDetails = async placeId => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${API_KEY_PERSONAL}`,
      );
      const data = await response.json();
      if (data.status === 'OK') {
        const {lat, lng} = data.result.geometry.location;
        const cityComponent =
          data.result.address_components.find(comp =>
            comp.types.includes('locality'),
          ) ||
          data.result.address_components.find(comp =>
            comp.types.includes('administrative_area_level_2'),
          );
        const countryComponent = data.result.address_components.find(comp =>
          comp.types.includes('country'),
        );
        const city = cityComponent ? cityComponent.long_name : '';
        const country = countryComponent ? countryComponent.long_name : '';

        const newRegion = {
          latitude: lat,
          longitude: lng,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        };
        setRegion(newRegion);
        setMarkerPosition({latitude: lat, longitude: lng});
        mapRef.current?.animateToRegion(newRegion, 1000);

        return {lat, lng, city, country};
      }
      return null;
    } catch (error) {
      console.error('Error fetching place details:', error);
      return null;
    }
  };

  const fetchAddressFromCoordinates = async (latitude, longitude) => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${API_KEY_PERSONAL}&language=en`,
      );
      const data = await response.json();
      if (data.status === 'OK' && data.results.length > 0) {
        const formattedAddress = data.results[0].formatted_address;
        setAddress(formattedAddress);
        const cityComponent =
          data.results[0].address_components.find(comp =>
            comp.types.includes('locality'),
          ) ||
          data.results[0].address_components.find(comp =>
            comp.types.includes('administrative_area_level_2'),
          );
        const countryComponent = data.results[0].address_components.find(comp =>
          comp.types.includes('country'),
        );
        const city = cityComponent ? cityComponent.long_name : 'NA';
        const country = countryComponent ? countryComponent.long_name : 'NA';
        setSelectedLocation({lat: latitude, lng: longitude, city, country});
        return formattedAddress;
      } else {
        showToast('error', t('failedToRetrieveAddress'), 'bottom', isRTL);
        return null;
      }
    } catch (error) {
      console.error('Error fetching address from coordinates:', error);
      showToast('error', t('failedToRetrieveAddress'), 'bottom', isRTL);
      return null;
    }
  };

  const handleSuggestionPress = async suggestion => {
    setAddress(suggestion.description);
    setSuggestions([]);
    setShouldFetchSuggestions(false); // Disable suggestions after selection
    const locationDetails = await fetchPlaceDetails(suggestion.place_id);
    if (locationDetails) {
      setSelectedLocation(locationDetails);
      scrollViewRef.current?.scrollTo({y: hp(60), animated: true});
    } else {
      showToast('error', t('failedToRetrieveLocationDetails'), 'bottom', isRTL);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      t('confirmDelete'),
      t('confirmDeleteMessage'),
      [
        {text: t('cancel'), style: 'cancel'},
        {
          text: t('delete'),
          style: 'destructive',
          onPress: handleDeleteMeetingPoint,
        },
      ],
      {cancelable: true},
    );
  };

  const handleDeleteMeetingPoint = async () => {
    try {
      await deleteMeetingPoint(deleteId).unwrap();
      showToast('success', t('deleteSuccess'), 'bottom', isRTL);
      navigation.goBack();
    } catch (error) {
      console.error('Error deleting meeting point:', error);
      showToast('error', t('deleteError'), 'bottom', isRTL);
    }
  };

  const handleSaveMeetingPoint = async () => {
    if (!selectedLocation || !address.trim()) {
      showToast('error', t('locationMandatory'), 'bottom', isRTL);
      return;
    }

    setIsSaving(true);
    const {lat, lng, city, country} = selectedLocation;

    const payload = {
      full_address: address,
      latitude: lat,
      longitude: lng,
      city: city === null ? 'NA' : city,
      country: country === null ? 'NA' : country,
    };

    try {
      await editMeetingPoint({id, payload}).unwrap();
      setIsSaving(false);
      showToast('success', t('saveSuccess'), 'bottom', isRTL);
      navigation.goBack();
    } catch (error) {
      setIsSaving(false);
      console.error('Error updating meeting point:', error);
      showToast('error', t('saveError'), 'bottom', isRTL);
    }
  };

  const handleDragEnd = async event => {
    const {latitude, longitude} = event.nativeEvent.coordinate;
    const newPosition = {latitude, longitude};
    setMarkerPosition(newPosition);
    setRegion({
      ...region,
      latitude,
      longitude,
    });
    mapRef.current?.animateToRegion(
      {
        latitude,
        longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      },
      1000,
    );
    setShouldFetchSuggestions(false); // Disable suggestions after dragging
    await fetchAddressFromCoordinates(latitude, longitude);
  };

  const renderSuggestion = ({item}) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}>
      <Text style={styles.suggestionText}>{item.description}</Text>
    </TouchableOpacity>
  );

  if (isLoadingMeetingPoint) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusContainer color="transparent" />
        <AppHeader
          isWhite={true}
          title={t('editMeetingPoint')}
          isBackBtn={true}
          backIcon={icons.backbtn}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.themeColor} />
          <Text style={styles.loadingText}>{t('loadingMeetingPoint')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (fetchError) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusContainer color="transparent" />
        <AppHeader
          isWhite={true}
          title={t('editMeetingPoint')}
          isBackBtn={true}
          backIcon={icons.backbtn}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{t('errorLoadingMeetingPoint')}</Text>
          <PrimaryButton title={t('retry')} onPress={refetchMeetingPoint} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.keyboardAvoidingView}
      behavior="padding"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.container}>
          <StatusContainer color={'transparent'} />
          <AppHeader
            backIcon={icons.backbtn}
            isBackBtn
            title={t('editMeetingPoint')}
            style={styles.transparentHeaderStyle}
            isHome={false}
            isWhite={true}
          />
          <ScrollView
            ref={scrollViewRef}
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled">
            <View style={styles.mapContainer}>
              <MapView
                ref={mapRef}
                style={styles.map}
                region={region}
                showsUserLocation>
                {markerPosition && (
                  <Marker
                    coordinate={markerPosition}
                    image={{
                      uri: `${IMAGE_BASE_URL}document/1734345316053-TaleemMapPin.png`,
                    }}
                    title={address || 'Selected Location'}
                    draggable
                    onDragEnd={handleDragEnd}
                  />
                )}
              </MapView>
            </View>
            <View style={styles.bottomContainer}>
              <Text style={styles.meetingPointTitle}>
                {t('editYourMeetingPoint')}
              </Text>
              <View style={styles.addressContainer}>
                <TextInput
                  ref={inputRef}
                  style={styles.largeTextInput}
                  placeholder={fullAddressForPopulatedTheAddressField}
                  placeholderTextColor={colors.txtGrey3}
                  value={address}
                  onChangeText={text => {
                    setAddress(text);
                    setShouldFetchSuggestions(true); // Enable suggestions when typing
                  }}
                  autoCorrect={false}
                  autoCapitalize="none"
                  onFocus={() => {
                    setShouldFetchSuggestions(true); // Enable suggestions on focus
                    scrollViewRef.current?.scrollTo({
                      y: hp(60),
                      animated: true,
                    });
                  }}
                />
                {isLoadingSuggestions && (
                  <ActivityIndicator
                    style={styles.loadingIndicator}
                    size="small"
                    color={colors.themeColor}
                  />
                )}
              </View>
              {suggestions.length > 0 && (
                <FlatList
                  data={suggestions}
                  keyExtractor={item => item.place_id}
                  renderItem={renderSuggestion}
                  style={styles.suggestionsList}
                  keyboardShouldPersistTaps="handled"
                />
              )}
              <PrimaryButton
                title={t('saveChanges')}
                onPress={handleSaveMeetingPoint}
                style={styles.saveButton}
                disabled={isSaving || !selectedLocation}>
                {isSaving ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <Text style={styles.saveButtonText}>{t('saveChanges')}</Text>
                )}
              </PrimaryButton>
              <PrimaryButton
                title={t('delete')}
                onPress={handleDelete}
                style={styles.deleteButton}
                textStyle={styles.deleteButtonText}
              />
            </View>
          </ScrollView>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

// Styles remain unchanged
const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: hp(10),
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  mapContainer: {
    height: hp(55),
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  transparentHeaderStyle: {
    marginTop: Platform.OS == 'ios' ? '12%' : 0,
    backgroundColor: 'transparent',
  },
  bottomContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 20,
    marginTop: hp(2),
    marginHorizontal: wp(2),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  meetingPointTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black,
    marginBottom: 10,
    fontFamily: Fonts.medium,
  },
  largeTextInput: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
    color: colors.txtGrey3,
    height: 50,
    flex: 1,
    fontFamily: Fonts.regular,
  },
  suggestionsList: {
    backgroundColor: colors.white,
    borderColor: colors.txtGray1,
    borderWidth: 0.3,
    borderRadius: 8,
    maxHeight: 250,
    marginTop: 5,
    marginBottom: 10,
  },
  suggestionItem: {
    padding: 12,
    borderBottomWidth: 0.2,
    borderBottomColor: colors.txtGray1,
  },
  suggestionText: {
    fontSize: 14,
    color: colors.blackSkatch,
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  saveButton: {
    marginTop: 10,
    width: '100%',
    backgroundColor: colors.themeColor,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: Fonts.bold,
  },
  deleteButton: {
    marginTop: 10,
    width: '100%',
    backgroundColor: colors.white,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'red',
  },
  deleteButtonText: {
    color: 'red',
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: Fonts.bold,
  },
  addressContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingIndicator: {
    position: 'absolute',
    right: 15,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: colors.txtGrey3,
    fontFamily: Fonts.regular,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: colors.txtGrey3,
    marginBottom: 10,
    textAlign: 'center',
    fontFamily: Fonts.regular,
  },
});

export default EditMeetingPoint;
