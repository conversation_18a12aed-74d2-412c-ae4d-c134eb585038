import React, {useState, useEffect, useCallback} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import CustomCheckbox from '../../Components/CustomCheckbox';
import {PrimaryButton} from '../../Components/CustomButton';
import icons from '../../Utils/icons';
import RadiusSlider from '../../Components/RadiusSlider';
import MeetingPointCard from '../../Components/MeetingPointCard';
import {
  useEditMeetingPreferenceMutation,
  useGetMeetingPreferenceQuery,
  useGetMeetingPointsQuery,
} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import {fp} from '../../Helper/ResponsiveDimensions';
import HelperTextComponent from '../../Components/HelperTipComp';

const {width} = Dimensions.get('window');

const MeetingPreference = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [radiusValue, setRadiusValue] = useState(0);
  const [isCheckboxSelected, setCheckboxSelected] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [meetingPointHelper, setMeetingPointHelper] = useState(false);
  const [openNewMeetingHelper, setOpenNewMeetingHelper] = useState(false);
  const {
    data: meetingPreferenceData,
    isLoading: isLoadingPreference,
    error: fetchPreferenceError,
    refetch: refetchPreference,
  } = useGetMeetingPreferenceQuery();

  const {
    data: meetingPointsData,
    isLoading: isLoadingMeetingPoints,
    error: fetchMeetingPointsError,
    refetch: refetchMeetingPoints,
  } = useGetMeetingPointsQuery();

  const [editPreference, {isLoading: isSaving}] =
    useEditMeetingPreferenceMutation();

  useEffect(() => {
    if (meetingPreferenceData?.data?.tutorMeetingPreference) {
      const {meeting_preference_radius, allow_student_meeting_points} =
        meetingPreferenceData.data.tutorMeetingPreference;
      setRadiusValue(meeting_preference_radius);
      setCheckboxSelected(allow_student_meeting_points);
      setHasUnsavedChanges(false);
    }
  }, [meetingPreferenceData]);

  useFocusEffect(
    useCallback(() => {
      refetchPreference();
      refetchMeetingPoints();
    }, [refetchPreference, refetchMeetingPoints]),
  );

  const handleRadiusChange = useCallback(value => {
    setRadiusValue(value);
    setHasUnsavedChanges(true);
  }, []);

  const handleCheckboxSelect = useCallback(() => {
    setCheckboxSelected(prev => !prev);
    setHasUnsavedChanges(true);
  }, []);

  const handleAddMeetingPoint = useCallback(() => {
    navigation.navigate('SetYourPreference');
  }, [navigation]);

  const handleEdit = useCallback(
    id => {
      navigation.navigate('EditMeetingPoint', {id});
    },
    [navigation],
  );

  const handleSave = useCallback(async () => {
    try {
      await editPreference({
        allow_student_meeting_points: isCheckboxSelected,
        meeting_preference_radius: radiusValue,
      }).unwrap();

      setHasUnsavedChanges(false);
      showToast('success', t('preferencesUpdated'), 'bottom', isRTL);
    } catch (err) {
      showToast('error', t('preferencesUpdateFailed'), 'bottom', isRTL);
    }
  }, [editPreference, isCheckboxSelected, radiusValue, t]);

  useEffect(() => {
    if (fetchPreferenceError) {
      Alert.alert(t('error'), t('errorLoadingPreferences'), [
        {text: t('retry'), onPress: refetchPreference},
      ]);
    }
  }, [fetchPreferenceError, refetchPreference, t]);

  useEffect(() => {
    if (fetchMeetingPointsError) {
      Alert.alert(t('error'), t('errorLoadingMeetingPoints'), [
        {text: t('retry'), onPress: refetchMeetingPoints},
      ]);
    }
  }, [fetchMeetingPointsError, refetchMeetingPoints, t]);

  if (isLoadingPreference || isLoadingMeetingPoints) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusContainer color="#fff" />
        <AppHeader
          isWhite={true}
          title={t('meetingPreference')}
          isBackBtn={true}
          backIcon={icons.backIcon}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#000" />
          <Text style={styles.loadingText}>
            {isLoadingPreference
              ? t('loadingPreferences')
              : t('loadingMeetingPoints')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const meetingPoints = meetingPointsData?.data?.rows || [];

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('meetingPreference')}
      />

      <ScrollView contentContainerStyle={styles.contentContainer}>
        <View style={{flexDirection: isRTL ? 'row-reverse' : 'row'}}>
          <Text
            style={[
              styles.sectionTitle,
              {
                textAlign: isRTL ? 'right' : 'left',
                marginRight: isRTL ? 0 : 8,
                marginLeft: isRTL ? 8 : 0,
              },
            ]}>
            {t('bookingPreferences')}
          </Text>
          <HelperTextComponent
            helperText={t('meetinPointHelperTxt')}
            setOpen={setMeetingPointHelper}
            open={meetingPointHelper}
            borderColor={colors.black}
            iconColor={colors.black}
          />
        </View>
        <View
          style={{
            alignSelf: 'center',
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <RadiusSlider
            min={0}
            max={120}
            initialValue={radiusValue}
            onValueChange={handleRadiusChange}
            disabled={isSaving}
            title={t('bookingPreferenceRadius')}
          />
        </View>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'baseline',
          }}>
          <Text
            style={[
              styles.sectionTitle,
              styles.meetingPointsTitle,
              {
                textAlign: isRTL ? 'right' : 'left',
                marginRight: isRTL ? 0 : 8,
                marginLeft: isRTL ? 8 : 0,
              },
            ]}>
            {t('meetingPoints')}
          </Text>
          <HelperTextComponent
            helperText={t('newMeetingPoingHelperTxt')}
            setOpen={setOpenNewMeetingHelper}
            open={openNewMeetingHelper}
            borderColor={colors.black}
            iconColor={colors.black}
          />
        </View>
        {meetingPoints.length > 0 ? (
          meetingPoints.map((point, index) => (
            <MeetingPointCard
              key={point.id}
              title={`${t('meetingPoint')} ${index + 1}`}
              address={point.full_address}
              onEdit={() => handleEdit(point.id)}
              isRtl={isRTL}
            />
          ))
        ) : (
          <Text style={styles.noMeetingPointsText}>{t('noMeetingPoints')}</Text>
        )}
      </ScrollView>
      <View style={{paddingHorizontal: 20}}>
        <CustomCheckbox
          label={t('allowStudentMeetingPoints')}
          isSelected={isCheckboxSelected}
          onSelect={handleCheckboxSelect}
          disabled={isSaving}
          showTooltip
          helperTxt={t('allowStudentMeetingHelperTxt')}
          // style={{alignSelf:}}
        />
      </View>

      <View style={styles.buttonContainer}>
        {meetingPoints.length < 3 && (
          <PrimaryButton
            title={t('addMeetingPoint')}
            onPress={handleAddMeetingPoint}
            style={{width: '100%'}}
            showTooltip
            helperTxt={t('addNewMeetingPointHelperTxt')}
            textStyle={{marginRight: 8}}
          />
        )}
        {hasUnsavedChanges && (
          <PrimaryButton
            title={isSaving ? t('saving') : t('savePreferences')}
            onPress={handleSave}
            disabled={isSaving}
            style={styles.saveButton}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent: 'space-between',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontSize: fp(1.6),
    marginBottom: 10,
    fontFamily: Fonts.medium,
    // Applied the medium font
  },
  meetingPointsTitle: {
    marginTop: 20,
    fontFamily: Fonts.medium, // Applied the medium font
  },
  buttonContainer: {
    padding: 20,
    marginBottom: 10,
    gap: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
    fontFamily: Fonts.medium, // Applied the regular font
  },
  saveButton: {
    marginTop: 10,
    width: '100%',
    fontFamily: Fonts.bold, // Applied the bold font for button text
  },
  noMeetingPointsText: {
    fontSize: fp(1.6),
    color: '#666',
    marginTop: 10,
    textAlign: 'center',
    fontFamily: Fonts.medium, // Applied the regular font
  },
});

export default MeetingPreference;
