import {Image, SafeAreaView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import ContactUsCard from '../../../Components/ContactUsCard';
import SupportComponent from '../../../Components/SupportComponent';
import icons from '../../../Utils/icons';
import {StatusContainer} from '../../../Components/StatusBar';
import {AppHeader} from '../../../Components/Header';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {applyShadowStyleIos} from '../../../Helper/ShadowStyleIos';
import {Fonts} from '../../../Utils/Fonts';
import colors from '../../../Utils/colors';
import {TouchableOpacity} from 'react-native';
import {supportEmail, supportPhone} from '../../../Utils/constant';

const ContactUs = () => {
  const navigation = useNavigation();
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <SafeAreaView>
      <StatusContainer />
      <AppHeader title={t('contact_us')} backIcon={icons.backbtn} isBackBtn />
      <ContactUsCard
        title={t('hereToHelp')}
        subtitle={t('ContactUsSubtitle')}
        image={icons.contactUsGirlImage}
        height={hp(16)}
        imgStyle={{bottom: 0}}
      />

      <SupportComponent phone={supportPhone} email={supportEmail} />

      <ContactUsCard
        title={t('chatWithAdmin')}
        subtitle={t('adminSupportMsg')}
        image={icons.chatIcons}
        isButton={true}
        imgHeight={80}
        imgWidth={80}
        imgStyle={{bottom: hp(1.5)}}
        onButtonPress={() => navigation.navigate('RaiseYourConcern')}
      />

      <TouchableOpacity
        onPress={() => navigation.navigate('ConcernScreen')}
        style={applyShadowStyleIos({
          //   borderColor: colors.black,
          //   borderWidth: 1,
          elevation: 1,
          //   height: hp(10),
          width: wp(90),
          backgroundColor: colors.white,
          alignSelf: 'center',
          borderRadius: fp(2),
          padding: fp(2),
          flexDirection: isRTL ? 'row-reverse' : 'row',
          justifyContent: 'space-between',
          marginBottom: hp(1.6),
        })}>
        <Text style={{fontFamily: Fonts.medium, fontSize: fp(1.8)}}>
          {t('raised_tickets')}
        </Text>
        <Image
          source={icons.rightArrowGrade}
          style={{transform: isRTL ? [{rotateY: '180deg'}] : []}}
        />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default ContactUs;
