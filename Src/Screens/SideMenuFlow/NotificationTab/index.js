import {
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import icons from '../../../Utils/icons';
import {AppHeader} from '../../../Components/Header';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import colors from '../../../Utils/colors';
import {Fonts} from '../../../Utils/Fonts';
import {Switch} from 'react-native-switch';
import {usePushNotificationPreferanceMutation} from '../../../Api/ApiSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {showToast} from '../../../Components/ToastHelper';

const NotficationSetting = () => {
  const {t, i18n} = useTranslation();
  const [isEnabled, setIsEnabled] = useState(true);
  const isRTL = i18n.language === 'ar';
  const [updateNotification, {isLoading}] =
    usePushNotificationPreferanceMutation({status: isEnabled});

  useEffect(() => {
    const getNotificationPreference = async () => {
      const savedStatus = await AsyncStorage.getItem('notification_preference');
      if (savedStatus !== null) {
        setIsEnabled(savedStatus === '1'); // Convert string back to boolean
      }
    };

    getNotificationPreference();
  }, []);

  const toggleSwitch = async val => {
    const newStatus = val ? '1' : '0';
    setIsEnabled(val);
    try {
      const response = await updateNotification({status: newStatus}).unwrap();
      console.log('Notification preference updated successfully', response);
      showToast('success', response?.message);
      await AsyncStorage.setItem('notification_preference', newStatus);
    } catch (error) {
      console.error('Failed to update notification preference:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={t('notifications')}
        backIcon={icons.backbtn}
        isBackBtn
        isWhite={true}
      />
      <View style={styles.body}>
        <View
          style={[styles.card, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          <View>
            <Text style={styles.title}>{t('notifications')}</Text>
            <Text>{t('turnONOFF')}</Text>
          </View>
          <Switch
            value={isEnabled}
            onValueChange={toggleSwitch}
            circleSize={25}
            barHeight={25}
            activeText={''}
            inActiveText={''}
            backgroundActive={colors.themeBackgroundTwo}
            backgroundInactive={colors.white}
            circleActiveColor={colors.themeColor}
            circleInActiveColor={colors.themeColor}
            circleBorderActiveColor={colors.themeColor}
            circleBorderInactiveColor={colors.themeColor}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default NotficationSetting;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  body: {
    flex: 1,
    paddingVertical: hp(2),
    paddingHorizontal: wp(2),
  },
  card: {
    marginHorizontal: wp(2),
    backgroundColor: colors.lightGreen,
    padding: 16,
    borderRadius: 10,

    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
});
