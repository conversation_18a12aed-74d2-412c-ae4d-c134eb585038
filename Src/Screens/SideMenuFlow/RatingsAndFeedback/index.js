import {
  ActivityIndicator,
  FlatList,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {StatusContainer} from '../../../Components/StatusBar';
import {AppHeader} from '../../../Components/Header';
import icons from '../../../Utils/icons';
import {useTranslation} from 'react-i18next';
import colors from '../../../Utils/colors';
import {useGetReviewsByStudentQuery} from '../../../Api/ApiSlice';
import ReviewCard from '../../../Components/ReviewCard';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import {Fonts} from '../../../Utils/Fonts';

const RantingsAndFeedback = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [reviews, setReviews] = useState([]);
  const [page, setPage] = useState(0);
  const {
    data: reviewData,
    isLoading: rivewLoading,
    error,
    isFetching,
    refetch: refetchReview,
  } = useGetReviewsByStudentQuery({
    page,
    limit: 10,
  });
  useEffect(() => {
    refetchReview();
  }, []);
  useEffect(() => {
    console.log('reviewData===', reviewData?.data?.rows);
    if (reviewData?.data?.rows) {
      setReviews(prev => [...prev, ...reviewData?.data?.rows]);
    }
  }, [reviewData]);
  const loadMoreReviews = () => {
    if (!isFetching && reviewData?.data?.rows?.length > 0) {
      setPage(prevPage => prevPage + 1);
    }
  };
  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer />
      <AppHeader
        title={t('rating_feedbacks')}
        backIcon={icons.backbtn}
        isBackBtn
      />
      <View style={styles.body}>
        <FlatList
          showsVerticalScrollIndicator={false}
          data={reviews}
          ItemSeparatorComponent={<View style={styles.saperator} />}
          ListEmptyComponent={<Text style={styles.noData}>{t('noData')}</Text>}
          renderItem={({item, index}) => {
            return (
              <ReviewCard
                key={index}
                image={item?.ReviewedUser?.image}
                name={item?.ReviewedUser?.name || t('name')}
                date={
                  new Date(item?.createdAt).toLocaleDateString() ||
                  t('date_review')
                }
                rating={item?.rating || '0'}
                reviewText={item?.review || t('reviewText')}
                isRTL={isRTL}
              />
            );
          }}
          keyExtractor={(item, index) => index.toString()}
          onEndReached={loadMoreReviews}
          onEndReachedThreshold={0.5} // Load more when the user scrolls 50% from the bottom
          ListFooterComponent={
            isFetching ? <ActivityIndicator size="small" /> : null
          } // Show loader
        />
      </View>
    </SafeAreaView>
  );
};

export default RantingsAndFeedback;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  body: {
    flex: 1,
    paddingVertical: hp(2),
    paddingHorizontal: wp(2),
  },
  saperator: {
    borderWidth: 1,
    borderColor: colors.lightgreay,
  },
  noData: {
    fontSize: fp(1.6),
    fontFamily: Fonts.bold,
    color: colors.greyLight,
    textAlign: 'center',
  },
});
