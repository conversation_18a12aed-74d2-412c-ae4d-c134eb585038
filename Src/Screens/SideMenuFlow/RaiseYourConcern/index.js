import {
  Alert,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Linking,
  Platform,
  TouchableOpacity,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {AppHeader} from '../../../Components/Header';
import icons from '../../../Utils/icons';
import {StatusContainer} from '../../../Components/StatusBar';
import {PrimaryButton} from '../../../Components/CustomButton';
import colors from '../../../Utils/colors';
import {PrimaryInput} from '../../../Components/Input';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import {Fonts} from '../../../Utils/Fonts';
import {
  useProfileDetailsQuery,
  useRiseYourConcernMutation,
} from '../../../Api/ApiSlice';
import {showToast} from '../../../Components/ToastHelper';
import {useSelector} from 'react-redux';
import {supportPhone} from '../../../Utils/constant';

const RaiseYourConcern = ({navigation}) => {
  const {t} = useTranslation();
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const number = supportPhone;

  const {data: userDetails, error} = useProfileDetailsQuery();
  console.log('userDetails==', userDetails?.data);
  useEffect(() => {
    console.log('userDetails===', userDetails);
    if (userDetails?.data) {
      setFullName(userDetails?.data?.name || '');
      setEmail(userDetails?.data?.email || '');
    }
  }, [userDetails]);

  const handleCallPress = async () => {
    const url = `tel:${number}`;
    await Linking.openURL(url);
  };

  const [riseConcern, {isLoading}] = useRiseYourConcernMutation();

  const isValidFullName = name => {
    const fullNameRegex = /^[A-Za-z]+(?: [A-Za-z]+){0,3}$/;
    return fullNameRegex.test(name) && name.length <= 30;
  };

  const isValidEmail = email => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async () => {
    try {
      if (!fullName.trim()) {
        showToast('error', t('fullName_required'));
        return false;
      }
      if (!isValidFullName(fullName)) {
        showToast('error', t('invalid_name'));
        return false;
      }

      if (email && !isValidEmail(email)) {
        showToast('error', t('invalid_email'));
        return false;
      }
      if (!message) {
        showToast('error', t('pleaseEnterMessage'));
        return false;
      }
      const payload = {
        full_name: fullName,
        email: email,
        message: message,
      };
      const response = await riseConcern(payload);
      if (response?.data?.status) {
        showToast('success', response?.data?.message);
        navigation.goBack();
      }
    } catch (error) {
      console.log('error rise concern:', error);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{flex: 1}}
      keyboardVerticalOffset={Platform.OS === 'ios' ? hp(4) : 0} // Adjust this value as needed
    >
      <SafeAreaView style={styles.container}>
        <StatusContainer />
        <AppHeader
          title={t('riseYourConcern')}
          isBackBtn={true}
          backIcon={icons.backIcon}
        />

        <ScrollView
          contentContainerStyle={styles.body}
          showsVerticalScrollIndicator={false}>
          <PrimaryInput
            editable={userDetails?.data?.name?.length > 0 ? false : true}
            title={t('fullName')}
            keyboardType="default"
            placeholder={t('placeholder_fullName')}
            value={fullName}
            textInputStyle={{height: hp(6)}}
            onChangeText={text => setFullName(text)}
            placeholderTextColor={colors.txtGrey1}
            maxLength={30}
            lableStyle={{width: '100%'}}
          />
          <PrimaryInput
            editable={userDetails?.data?.email?.length > 0 ? false : true}
            title={t('emailId')}
            keyboardType="email-address"
            containerStyle={{width: '100%'}}
            placeholder={t('placeholder_email')}
            value={email}
            textInputStyle={{height: hp(6)}}
            onChangeText={text => setEmail(text)}
            placeholderTextColor={colors.txtGrey1}
            lableStyle={{width: '100%'}}
          />
          <PrimaryInput
            title={t('yourMessage')}
            keyboardType="default"
            multiline
            textInputStyle={{height: hp(18), textAlignVertical: 'top'}}
            placeholder={t('placeHolderMessage')}
            value={message}
            onChangeText={text => setMessage(text)}
            placeholderTextColor={colors.txtGrey1}
            lableStyle={{width: '100%'}}
          />
          <View style={styles.bottomContainer}>
            <View style={styles.bottomView}>
              <Text style={styles.help}>{t('callNow')}</Text>
              <TouchableOpacity onPress={handleCallPress}>
                <Text style={styles.number}>{number}</Text>
              </TouchableOpacity>
            </View>
            <PrimaryButton
              loading={isLoading}
              title={t('riseConcern')}
              onPress={handleSubmit}
              style={{width: wp(95)}}
            />
          </View>
        </ScrollView>

        {/* Bottom Button */}
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default RaiseYourConcern;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  body: {
    flexGrow: 1,
    marginHorizontal: wp(3),
    marginVertical: hp(1.6),
  },
  bottomContainer: {
    bottom: 0,
    // position: 'absolute',
    // alignSelf: 'center',
    paddingBottom: hp(2),
    backgroundColor: colors.white,
  },
  bottomView: {
    marginVertical: hp(1),
    alignItems: 'center',
  },
  help: {
    fontFamily: Fonts.regular,
    color: colors.black,
    fontSize: fp(1.6),
  },
  number: {
    color: colors.themeColor,
    fontSize: fp(2),
    fontFamily: Fonts.semiBold,
    marginTop: hp(1),
  },
});
