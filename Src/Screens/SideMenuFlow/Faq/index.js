import {
  ActivityIndicator,
  FlatList,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState, useTransition} from 'react';
import icons from '../../../Utils/icons';
import {AppHeader} from '../../../Components/Header';
import {useTranslation} from 'react-i18next';
import colors from '../../../Utils/colors';
import {useGetStudentFaqsQuery} from '../../../Api/ApiSlice';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import {Fonts} from '../../../Utils/Fonts';

const FaqScreeen = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [activeIndex, setActiveIndex] = useState(null);
  const {data: faqData, isLoading} = useGetStudentFaqsQuery(i18n.language);

  const toggleAccordion = index => {
    setActiveIndex(activeIndex === index ? null : index);
  };
  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={t('faqs')}
        backIcon={icons.backbtn}
        isBackBtn
        isWhite={true}
      />
      <View style={styles.body}>
        <FlatList
          data={faqData?.data}
          keyExtractor={(item, index) => index.toString()}
          ListEmptyComponent={isLoading && <ActivityIndicator size={'large'} />}
          renderItem={({item, index}) => {
            return (
              <View style={styles.itemContainer}>
                <TouchableOpacity
                  onPress={() => toggleAccordion(index)}
                  style={[
                    styles.questionContainer,
                    {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  ]}>
                  <Text style={styles.question}>{item.question}</Text>
                  <View style={styles.iconContainer}>
                    <Image
                      source={
                        activeIndex === index
                          ? icons.downchevron
                          : icons.rightArrowGrade
                      }
                      tintColor={colors.themeColor}
                      style={{
                        transform: isRTL
                          ? [{rotate: '180deg'}]
                          : [{rotate: '0deg'}],
                      }}
                    />
                  </View>
                </TouchableOpacity>
                {activeIndex === index && (
                  <View style={styles.answerContainer}>
                    <Text style={styles.answer}>{item.answer}</Text>
                  </View>
                )}
              </View>
            );
          }}
        />
      </View>
    </SafeAreaView>
  );
};

export default FaqScreeen;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: colors.white},
  body: {
    flex: 1,
    paddingVertical: hp(2),
    paddingHorizontal: wp(2),
  },
  itemContainer: {
    marginBottom: hp(1),
    backgroundColor: colors.lightthemeColor,
    borderRadius: fp(1.8),
  },
  questionContainer: {
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),

    alignItems: 'center',
    justifyContent: 'space-between',
  },
  question: {
    fontSize: fp(1.8),
    color: colors.themeColor,
    fontFamily: Fonts.bold,
    lineHeight: hp(2.4),
  },
  answerContainer: {
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  answer: {
    fontSize: fp(1.4),
    color: colors.blackSkatch,
    lineHeight: 16,
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  arrow: {
    height: hp(3),
    width: wp(3),
  },
});
