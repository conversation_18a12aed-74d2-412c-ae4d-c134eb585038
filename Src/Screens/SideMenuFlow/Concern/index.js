import {
  ActivityIndicator,
  FlatList,
  Image,
  Pressable,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState, useTransition} from 'react';
import icons from '../../../Utils/icons';
import {AppHeader} from '../../../Components/Header';
import {useTranslation} from 'react-i18next';
import colors from '../../../Utils/colors';
import {useGetAllConcernsQuery} from '../../../Api/ApiSlice';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import {Fonts} from '../../../Utils/Fonts';

const ConcernScreen = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const {data: concernData, isLoading, refetch} = useGetAllConcernsQuery();

  useEffect(() => {
    refetch();
  }, [refetch]);

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={t('risedTicket')}
        backIcon={icons.backbtn}
        isBackBtn
        isWhite={true}
      />
      <View style={styles.body}>
        <FlatList
          data={concernData?.data?.rows}
          keyExtractor={(item, index) => index.toString()}
          ListEmptyComponent={
            isLoading ? (
              <ActivityIndicator size={'large'} />
            ) : (
              <Text style={styles.noData}>{t('no_raised_tickets')}</Text>
            )
          }
          renderItem={({item, index}) => {
            return (
              <Pressable
                onPress={() => navigation.navigate('ChatWithAdmin', {item})}
                style={styles.itemContainer}>
                <View
                  style={[
                    styles.questionContainer,
                    {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  ]}>
                  <Text style={styles.question}>{item.full_name}</Text>
                  {/* <Image
                    source={
                      activeIndex === index ? icons.upArrow : icons.arrowDown
                    }
                    style={styles.arrow}
                  /> */}
                </View>

                <View
                  style={[
                    styles.answerContainer,
                    {
                      flexDirection: isRTL ? 'row-reverse' : 'row',
                    },
                  ]}>
                  <Text style={[styles.answer, {width: wp(75)}]}>
                    {item.message}
                  </Text>
                  <Text
                    style={[
                      {
                        color:
                          item.status == 1
                            ? colors.orangeLight
                            : item.status == 2
                            ? colors.red
                            : colors.themeColor,
                        fontFamily: Fonts.semiBold,
                        // alignSelf: 'flex-end',
                        marginRight: wp(2),
                      },
                    ]}>
                    {item.status == 1
                      ? t('Open')
                      : item.status == 2
                      ? t('inProgress')
                      : t('closed')}
                  </Text>
                </View>
              </Pressable>
            );
          }}
        />
      </View>
    </SafeAreaView>
  );
};

export default ConcernScreen;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: colors.white},
  body: {
    flex: 1,
    paddingVertical: hp(2),
    paddingHorizontal: wp(2),
  },
  itemContainer: {
    marginBottom: hp(1),
    backgroundColor: colors.lightthemeColor,
  },
  questionContainer: {
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),

    alignItems: 'center',
    justifyContent: 'space-between',
  },
  question: {
    fontSize: fp(1.8),
    color: colors.themeColor,
    fontFamily: Fonts.bold,
  },
  answerContainer: {
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),

    alignItems: 'center',
    justifyContent: 'space-between',
  },
  answer: {
    fontSize: fp(1.4),
    color: colors.blackSkatch,
    lineHeight: hp(1.8),
    fontFamily: Fonts.medium,
  },
  arrow: {
    height: hp(3),
    width: wp(3),
  },
  noData: {
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
    color: colors.lightGrey,
  },
});
