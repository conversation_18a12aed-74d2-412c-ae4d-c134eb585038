import {
  Dimensions,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {AppHeader} from '../../../Components/Header';
import icons from '../../../Utils/icons';
import {Switch} from 'react-native-switch';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import colors from '../../../Utils/colors';
import {Fonts} from '../../../Utils/Fonts';
import {usePushNotificationPreferanceMutation} from '../../../Api/ApiSlice';
import {showToast} from '../../../Components/ToastHelper';
import AsyncStorage from '@react-native-async-storage/async-storage';

const {width} = Dimensions.get('screen');
const dynamicPadding = width * 0.04;
const dynamicRadius = width * 0.02;

const Settings = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [isEnabled, setIsEnabled] = useState(false);
  const buttons = [
    {label: t('faqs'), route: 'FaqScreeen'},
    {label: t('terms_of_service'), route: 'TermsOfService'},
    {label: t('aboutUs'), route: 'AboutUs'},
    {label: t('deleteAccount'), route: 'DeleteAccount'},
  ];
  useEffect(() => {
    const getNotificationPreference = async () => {
      const savedStatus = await AsyncStorage.getItem('notification_preference');
      if (savedStatus !== null) {
        setIsEnabled(savedStatus === '1'); // Convert string back to boolean
      }
    };

    getNotificationPreference();
  }, []);
  const onPressButton = route => {
    navigation.navigate(route);
  };
  const [updateNotification, {isLoading}] =
    usePushNotificationPreferanceMutation({status: isEnabled});

  const toggleSwitch = async val => {
    const newStatus = val ? '1' : '0';
    setIsEnabled(val);
    try {
      const response = await updateNotification({status: newStatus}).unwrap();
      console.log('Notification preference updated successfully', response);
      showToast('success', response?.message);
      await AsyncStorage.setItem('notification_preference', newStatus);
    } catch (error) {
      console.error('Failed to update notification preference:', error);
    }
  };
  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={t('settings')}
        backIcon={icons.backbtn}
        isBackBtn
        isWhite={true}
      />
      <View style={styles.body}>
        <View
          style={[styles.card, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          <View>
            <Text style={styles.title}>{t('notifications')}</Text>
            <Text>{t('turnONOFF')}</Text>
          </View>
          <Switch
            value={isEnabled}
            onValueChange={toggleSwitch}
            circleSize={25}
            barHeight={25}
            activeText={''}
            inActiveText={''}
            backgroundActive={colors.themeBackgroundTwo}
            backgroundInactive={colors.white}
            circleActiveColor={colors.themeColor}
            circleInActiveColor={colors.themeColor}
            circleBorderActiveColor={colors.themeColor}
            circleBorderInactiveColor={colors.themeColor}
          />
        </View>
        <View style={styles.buttonView}>
          {buttons?.map((item, index) => {
            return (
              <TouchableOpacity
                onPress={() => onPressButton(item.route)}
                style={[
                  styles.buttons,
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                ]}>
                <Text>{item?.label}</Text>
                <Image
                  source={icons.rightArrowGray}
                  style={[
                    styles.rightArrowGray,
                    {transform: [{rotate: isRTL ? '-180deg' : '0deg'}]},
                  ]}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Settings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  body: {
    flex: 1,
    paddingVertical: hp(2),
    paddingHorizontal: wp(2),
  },
  card: {
    marginHorizontal: wp(2),
    backgroundColor: colors.lightGreen,
    padding: 16,
    borderRadius: 10,

    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  buttonView: {
    marginHorizontal: wp(2),
    marginVertical: hp(2),
  },
  buttons: {
    backgroundColor: colors.white,

    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(2),
    paddingVertical: hp(2),
    marginBottom: hp(1),
    borderWidth: 1,
    borderColor: colors.greyLight,
    borderRadius: 10,
  },
  rightArrowGray: {
    width: dynamicPadding * 2,
    height: dynamicPadding * 2,
    // opacity: 0.5,
  },
});
