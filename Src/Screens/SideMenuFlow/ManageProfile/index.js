import React, {useCallback, useEffect, useState} from 'react';
import {
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {StatusContainer} from '../../../Components/StatusBar';
import {AppHeader} from '../../../Components/Header';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../../../Utils/colors';
import icons from '../../../Utils/icons';
import {
  useGetAllConnectedProfilesAfterLoginQuery,
  useGetAllConnectedProfilesQuery,
  useSwitchConnectedProfileMutation,
} from '../../../Api/ApiSlice';
import {useFocusEffect} from '@react-navigation/native';
import {t} from 'i18next';
import {IMAGE_BASE_URL} from '../../../Utils/getBaseUrl';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import {Fonts} from '../../../Utils/Fonts';
import {useTranslation} from 'react-i18next';
import {useDispatch, useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {setAuthData} from '../../../Features/authSlice';
import RNRestart from 'react-native-restart';

const ManageProfile = ({navigation}) => {
  const {userId} = useSelector(state => state?.auth);
  console.log('🚀 ~ ManageProfile ~ user_id:', userId);

  const [selectedProfile, setSelectedProfile] = useState('1');

  // const {
  //   data: allProfiles,
  //   isLoading,
  //   refetch,
  // } = useGetAllConnectedProfilesQuery();

  const [
    switchConnectedProfileApi,
    {data: switchProfileRes, isLoading: switchProfileLoading},
  ] = useSwitchConnectedProfileMutation();

  // const handleProfileSelect = profileId => {
  //   switchConnectedProfileApi(profileId);
  //   setSelectedProfile(profileId);
  // };
  const dispatch = useDispatch();
  async function handleProfileSelect(id) {
    console.log('🚀 ~ handleProfileSelect ~ id:', id);
    setSelectedProfile(id);
    try {
      console.log('🚀 ~ handleProfilePress ~ id:', id);
      const response = await switchConnectedProfileApi(id);
      console.log(
        '🚀 ~ switchConnectedProfileApi ~ response:',
        response?.data?.data,
      );

      const userData = {
        token: response?.data?.data?.token,
        userId: response?.data?.data?.id,
        user_type: response?.data?.data?.user_type,
        action_type: response?.data?.data?.action_type,
        profile_image: null,
        profile_accounts: response?.data?.data?.profile_accounts,
      };
      await AsyncStorage.setItem('user', JSON.stringify(userData));
      console.log('🚀 ~ handleProfilePress ~ userData:', userData);

      dispatch(setAuthData(userData));
      RNRestart?.Restart();
    } catch (error) {
      console.error('Error in handleProfilePress:', error);
      // You might want to add appropriate error handling here, such as:
      // - Showing an error message to the user
      // - Setting an error state
      // - Logging to an error tracking service
    }
  }

  const {
    data: allProfiles,
    isLoading,
    refetch,
  } = useGetAllConnectedProfilesAfterLoginQuery();

  useFocusEffect(
    useCallback(() => {
      console.log('allProfiles=====', allProfiles?.data?.list);
      refetch();
    }, [refetch]),
  );
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const filteredData = allProfiles?.data?.list.filter(
    item => item?.id !== userId,
  );
  console.log(
    '🚀 ~ ManageProfile ~ allProfiles?.data?.list:',
    allProfiles?.data?.list,
  );
  console.log('🚀 ~ ManageProfile ~ filteredData:', filteredData);
  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('manage_profile')}
      />
      {isLoading && <ActivityIndicator size={'large'} />}
      {!isLoading &&
        filteredData?.map(user => (
          <TouchableOpacity
            key={user.id}
            style={[
              styles.profileContainer,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}
            onPress={() => handleProfileSelect(user.id)}
            activeOpacity={0.8}>
            {user.image ? (
              <Image
                source={{uri: IMAGE_BASE_URL + user.image}}
                style={styles.profileImage}
              />
            ) : (
              <View style={styles.placeholderProfile}>
                <Text style={styles.placeholderText}>
                  {user.name.charAt(0)}
                </Text>
              </View>
            )}
            <View style={styles.profileDetails}>
              <Text
                style={[
                  styles.profileName,
                  {
                    textAlign: isRTL ? 'right' : 'left',
                    marginRight: isRTL ? 10 : 0,
                  },
                ]}>
                {user.name}
              </Text>
              <Text
                style={[styles.profileType, [{marginRight: isRTL ? 10 : 0}]]}>
                {user.user_type == '1'
                  ? t('student')
                  : user?.user_type == '2'
                  ? t('parent')
                  : t('tutor')}
              </Text>
              {/* {selectedProfile === user.id && (
              <LinearGradient
                colors={['#C6FFC9', '#D4EBFF']}
                style={styles.userTypeTag}>
                <Text style={styles.userTypeText}>User Type: {user.type}</Text>
              </LinearGradient>
            )} */}
            </View>
            {/* {selectedProfile === user.id && (
            <Image source={icons.selectedMark} style={styles.selectedMark} />
          )} */}
          </TouchableOpacity>
        ))}

      <TouchableOpacity
        style={[
          styles.profileContainer,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}
        onPress={() => navigation.navigate('AddAccount')}
        activeOpacity={0.8}>
        {/* <View style={styles.placeholderProfile}> */}
        <Image
          source={icons.addImgEditProfile}
          style={{width: fp(6), height: fp(6), marginRight: 16}}
        />

        {/* <Text
            style={{
              fontSize: fp(4.2),
              fontFamily: Fonts.poppinsRegular,
            }}>
            +
          </Text> */}
        {/* <Image
            source={icons.plusIcon}
            resizeMode="contain"
            style={styles.plusIcon}
          /> */}
        {/* </View> */}
        <Text style={[styles.addAccountText, {marginRight: isRTL ? 10 : 0}]}>
          {t('addAccount')}
        </Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default ManageProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.themeColor,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  profileContainer: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E0E0E0',
  },
  profileImage: {
    width: fp(6),
    height: fp(6),
    borderRadius: fp(8),
    marginRight: 16,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: 16,
    // fontWeight: 'bold',
    color: '#000000',
    fontFamily: Fonts.semiBold,
  },
  profileType: {
    fontSize: 14,
    color: '#6D6D6D',
    fontFamily: Fonts.medium,
    marginTop: hp(0.4),
  },
  userTypeTag: {
    marginTop: 6,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  userTypeText: {
    fontSize: 12,
    color: '#000000',
    fontWeight: '500',
  },
  selectedMark: {
    width: 30,
    height: 30,
  },
  placeholderProfile: {
    width: fp(6),
    height: fp(6),
    borderRadius: fp(8),
    backgroundColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  placeholderText: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  plusIcon: {
    color: '#6D6D6D',
    height: 30,
  },
  addAccountText: {
    fontSize: 16,
    color: '#000000',
    fontFamily: Fonts.medium,
  },
});
