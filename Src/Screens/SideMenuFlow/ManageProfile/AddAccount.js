import React, {useState} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  Modal,
} from 'react-native';
import {StatusContainer} from '../../../Components/StatusBar';
import colors from '../../../Utils/colors';
import icons from '../../../Utils/icons';
import {AppHeader} from '../../../Components/Header';
import MobileNumberInput from '../../../Components/MobileNumberInput';
import {useTranslation} from 'react-i18next';
import {PrimaryInput} from '../../../Components/Input';
import {maxLengthsByCountry} from '../../../Utils/CountryCodeLength';
import {
  useConnectNewProfileMutation,
  useUploadFileProfileMutation,
  useChooseYourGradeQuery,
  useLazyChooseYourCurriculumsQuery,
  useSubCategoryQuery,
} from '../../../Api/ApiSlice';
import {showToast} from '../../../Components/ToastHelper';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import {PrimaryButton} from '../../../Components/CustomButton';
import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
import {IMAGE_BASE_URL} from '../../../Utils/getBaseUrl';
import {Fonts} from '../../../Utils/Fonts';

const AddAccount = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [name, setName] = useState('');
  const [userType, setUserType] = useState({id: 1, type: 'Student'});
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [countryCode, setCountryCode] = useState('QA');
  const [callingCode, setCallingCode] = useState('974');
  const [email, setEmail] = useState('');
  const [isImagePickerVisible, setImagePickerVisible] = useState(false);
  const [image, setImage] = useState(null);
  const [grade, setGrade] = useState('');
  const [curriculum, setCurriculum] = useState('');
  const [classId, setClassId] = useState('');
  const [studentType, setStudentType] = useState(''); // New state for studentType
  const [isGradeDropdownOpen, setGradeDropdownOpen] = useState(false);
  const [isCurriculumDropdownOpen, setCurriculumDropdownOpen] = useState(false);
  const [isClassDropdownOpen, setClassDropdownOpen] = useState(false);
  const [isStudentTypeDropdownOpen, setStudentTypeDropdownOpen] =
    useState(false); // New state for studentType dropdown

  const userTypes = [
    {id: 1, type: 'Student', label: t('student')},
    {id: 2, type: 'Parent', label: t('parent')},
    {id: 3, type: 'Tutor', label: t('tutor')},
  ];
  const studentTypes = [
    {id: 1, type: 'Student', label: t('student')},
    {id: 2, type: 'Professional', label: t('professional')},
  ];

  const {data: gradesData} = useChooseYourGradeQuery();
  const [chooseYourCurriculum, {data: curriculumsData}] =
    useLazyChooseYourCurriculumsQuery();
  const {data: classData, isLoading: classLoading} = useSubCategoryQuery();

  React.useEffect(() => {
    chooseYourCurriculum({viewAll: true});
  }, [chooseYourCurriculum]);

  const handleSelectImage = () => {
    setImagePickerVisible(true);
  };

  const handleDropdownSelect = type => {
    setUserType(type);
    setIsDropdownOpen(false);
    if (type.id !== 1) {
      setGrade('');
      setCurriculum('');
      setClassId('');
      setStudentType(''); // Reset studentType when not Student
    }
  };

  const handleStudentTypeSelect = type => {
    setStudentType(type.id.toString());
    setStudentTypeDropdownOpen(false);
    if (type.id !== 1) {
      setGrade('');
      setCurriculum('');
      setClassId('');
    }
  };

  const isValidEmail = email => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleNameChange = text => {
    const validName = text.replace(/[^a-zA-Z\s]/g, '').slice(0, 20);
    setName(validName);
  };

  const [uploadFileProfile] = useUploadFileProfileMutation();
  const [addNewAccount, {isLoading}] = useConnectNewProfileMutation();

  const handleSaveProfile = async () => {
    if (!name.trim()) {
      showToast('error', t('fullName_required'));
      return;
    }
    if (name.trim().length < 2) {
      showToast('error', t('invalid_name'));
      return;
    }
    if (email && !isValidEmail(email)) {
      showToast('error', t('invalid_email'));
      return;
    }
    // if (
    //   userType.id === 1 &&
    //   studentType == '1' &&
    //   (!grade || !curriculum || !classId || !studentType)
    // ) {
    //   showToast('error', t('gradeCurriculumClassStudentTypeRequired')); // Updated validation message
    //   return;
    // }

    try {
      let payload = {
        email: email,
        name: name,
        user_type: userType.id.toString(),
        image: image,
      };

      if (userType.id === 1) {
        payload = {
          ...payload,
          grades_id: grade?.id?.toString(),
          curriculum_id: grade?.name == 'Higher Education' ? '' : curriculum,
          class_id: classId?.toString(),
          student_type: studentType, // Add studentType to payload
        };
      }

      const response = await addNewAccount(payload).unwrap();
      if (response?.status) {
        showToast('success', response?.message);
        navigation.goBack();
      }
    } catch (error) {
      console.log('error add new account', error?.data?.message);
      showToast('error', error?.data?.message);
    }
  };

  const handleImageResponse = async response => {
    if (response.didCancel) return;
    if (response.errorCode || response.errorMessage) {
      showToast('error', response.errorMessage || t('failedImageSelection'));
      return;
    }
    if (response?.assets && response.assets.length > 0) {
      const selectedImage = response.assets[0];
      const uploadResponse = await uploadFileProfile({
        uri: selectedImage.uri,
        type: selectedImage.type,
        fileName: selectedImage.fileName || 'profile.jpg',
      }).unwrap();
      if (uploadResponse?.data?.path) {
        setImage(uploadResponse.data.path);
        showToast('success', t('imageUploadSuccess'));
      } else {
        showToast('error', t('failedImageupload'));
      }
    }
  };

  const openImagePicker = () => {
    launchImageLibrary({mediaType: 'photo', quality: 0.5}, handleImageResponse);
    setImagePickerVisible(false);
  };

  const openCamera = () => {
    launchCamera({mediaType: 'photo', quality: 0.5}, handleImageResponse);
    setImagePickerVisible(false);
  };
  console.log('grade', grade);
  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('addAccount')} />
      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        <View style={styles.formContainer}>
          <TouchableOpacity
            onPress={handleSelectImage}
            style={styles.imagePicker}>
            {image ? (
              <Image
                source={{uri: IMAGE_BASE_URL + image}}
                style={styles.image}
              />
            ) : (
              <Text>{t('uploadImage')}</Text>
            )}
          </TouchableOpacity>
          <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
            {t('fullName')}
          </Text>
          <TextInput
            style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
            value={name}
            onChangeText={handleNameChange}
            placeholder={t('placeholder_fullName')}
            placeholderTextColor="#A9A9A9"
          />
          <PrimaryInput
            title={t('emailId')}
            keyboardType="email-address"
            containerStyle={{width: '100%'}}
            placeholder={t('placeholder_email')}
            value={email}
            onChangeText={text => setEmail(text)}
            placeholderTextColor={colors.txtGrey1}
          />
          <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
            {t('selectUserType')}
          </Text>
          <TouchableOpacity
            style={styles.dropdown}
            onPress={() => setIsDropdownOpen(!isDropdownOpen)}>
            <Text style={styles.dropdownText}>{userType?.label}</Text>
            <View style={styles.dropdownIcon}>
              <Image
                source={isDropdownOpen ? icons.upArrow : icons.downArrowBlack}
                style={styles.iconImage}
              />
            </View>
          </TouchableOpacity>
          {isDropdownOpen && (
            <ScrollView
              style={styles.dropdownList}
              nestedScrollEnabled={true}
              showsVerticalScrollIndicator={true}
              contentContainerStyle={styles.dropdownScrollContent}>
              {userTypes.map((type, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.dropdownItem}
                  onPress={() => handleDropdownSelect(type)}>
                  <Text
                    style={[styles.dropdownItemText, {color: colors.black}]}>
                    {type?.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
          {/* Student Type Dropdown */}
          {userType.id === 1 && (
            <View>
              <Text
                style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
                {t('selectStudentType')}
              </Text>
              <TouchableOpacity
                style={styles.dropdown}
                onPress={() =>
                  setStudentTypeDropdownOpen(!isStudentTypeDropdownOpen)
                }>
                <Text style={styles.dropdownText}>
                  {studentType
                    ? studentTypes.find(
                        item => item.id.toString() === studentType,
                      )?.label
                    : t('selectStudentType')}
                </Text>
                <Image
                  source={
                    isStudentTypeDropdownOpen
                      ? icons.upArrow
                      : icons.downArrowBlack
                  }
                  style={styles.iconImage}
                />
              </TouchableOpacity>
              {isStudentTypeDropdownOpen && (
                <ScrollView
                  style={styles.dropdownList}
                  nestedScrollEnabled={true}
                  showsVerticalScrollIndicator={true}
                  contentContainerStyle={styles.dropdownScrollContent}>
                  {studentTypes.map(item => (
                    <TouchableOpacity
                      key={item.id}
                      style={styles.dropdownItem}
                      onPress={() => handleStudentTypeSelect(item)}>
                      <Text style={styles.dropdownItemText}>{item.label}</Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              )}
            </View>
          )}

          {/* Student-specific fields */}
          {userType.id === 1 && studentType === '1' && (
            <>
              {/* Grade Dropdown */}
              <View>
                <Text
                  style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
                  {t('selectGrade')}
                </Text>
                <TouchableOpacity
                  style={styles.dropdown}
                  onPress={() => setGradeDropdownOpen(!isGradeDropdownOpen)}>
                  <Text style={styles.dropdownText}>
                    {grade ? grade?.name : t('selectGrade')}
                  </Text>
                  <Image
                    source={
                      isGradeDropdownOpen ? icons.upArrow : icons.downArrowBlack
                    }
                    style={styles.iconImage}
                  />
                </TouchableOpacity>
                {isGradeDropdownOpen && (
                  <ScrollView
                    style={styles.dropdownList}
                    nestedScrollEnabled={true}
                    showsVerticalScrollIndicator={true}
                    contentContainerStyle={styles.dropdownScrollContent}>
                    {gradesData?.data?.rows?.map(item => (
                      <TouchableOpacity
                        key={item.id}
                        style={styles.dropdownItem}
                        onPress={() => {
                          setGrade(item);
                          setGradeDropdownOpen(false);
                        }}>
                        <Text style={styles.dropdownItemText}>{item.name}</Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                )}
              </View>

              {/* Class Dropdown */}
              <View>
                <Text
                  style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
                  {t('selectClass')}
                </Text>
                <TouchableOpacity
                  style={styles.dropdown}
                  onPress={() => setClassDropdownOpen(!isClassDropdownOpen)}>
                  <Text style={styles.dropdownText}>
                    {grade
                      ? grade?.tlm_classes?.find(
                          item => item.id.toString() === classId,
                        )?.name
                      : t('selectClass')}
                  </Text>
                  <Image
                    source={
                      isClassDropdownOpen ? icons.upArrow : icons.downArrowBlack
                    }
                    style={styles.iconImage}
                  />
                </TouchableOpacity>
                {isClassDropdownOpen && (
                  <ScrollView
                    style={styles.dropdownList}
                    nestedScrollEnabled={true}
                    showsVerticalScrollIndicator={true}
                    contentContainerStyle={styles.dropdownScrollContent}>
                    {!grade ? (
                      <TouchableOpacity style={styles.dropdownItem}>
                        <Text style={styles.dropdownItemText}>
                          {t('selectGradeFirst')}
                        </Text>
                      </TouchableOpacity>
                    ) : (
                      grade.tlm_classes.map(item => (
                        <TouchableOpacity
                          key={item.id}
                          style={styles.dropdownItem}
                          onPress={() => {
                            setClassId(item.id.toString());
                            setClassDropdownOpen(false);
                          }}>
                          <Text style={styles.dropdownItemText}>
                            {item.name}
                          </Text>
                        </TouchableOpacity>
                      ))
                    )}
                  </ScrollView>
                )}
              </View>

              {/* Curriculum Dropdown */}
              {!grade?.is_higher_education && (
                <View>
                  <Text
                    style={[
                      styles.label,
                      {textAlign: isRTL ? 'right' : 'left'},
                    ]}>
                    {t('curriculum')}
                  </Text>
                  <TouchableOpacity
                    style={styles.dropdown}
                    onPress={() =>
                      setCurriculumDropdownOpen(!isCurriculumDropdownOpen)
                    }>
                    <Text style={styles.dropdownText}>
                      {curriculum
                        ? curriculumsData?.data?.rows?.find(
                            item => item.id.toString() === curriculum,
                          )?.name
                        : t('selectCurriculum')}
                    </Text>
                    <Image
                      source={
                        isCurriculumDropdownOpen
                          ? icons.upArrow
                          : icons.downArrowBlack
                      }
                      style={styles.iconImage}
                    />
                  </TouchableOpacity>
                  {isCurriculumDropdownOpen && (
                    <ScrollView
                      style={styles.dropdownList}
                      nestedScrollEnabled={true}
                      showsVerticalScrollIndicator={true}
                      contentContainerStyle={styles.dropdownScrollContent}>
                      {curriculumsData?.data?.rows?.map(item => (
                        <TouchableOpacity
                          key={item.id}
                          style={styles.dropdownItem}
                          onPress={() => {
                            setCurriculum(item.id.toString());
                            setCurriculumDropdownOpen(false);
                          }}>
                          <Text style={styles.dropdownItemText}>
                            {item.name}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </ScrollView>
                  )}
                </View>
              )}
            </>
          )}
        </View>
        <Modal animationType="slide" transparent visible={isImagePickerVisible}>
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>{t('selectImageSource')}</Text>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={openImagePicker}>
                <Text style={styles.modalButtonText}>
                  {t('chooseFromGallery')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setImagePickerVisible(false)}>
                <Text style={[styles.modalButtonText, {color: colors.red}]}>
                  {t('cancel')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </ScrollView>
      <PrimaryButton
        loading={isLoading}
        title={t('saveProfile')}
        onPress={handleSaveProfile}
      />
    </SafeAreaView>
  );
};

export default AddAccount;

// Styles remain unchanged unless you want to tweak them for the new dropdown
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  formContainer: {
    marginTop: 20,
    marginHorizontal: 20,
    flex: 1,
  },
  label: {
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    marginBottom: 8,
    color: colors.black,
    textAlign: 'left', // Default alignment
  },
  input: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 8,
    padding: 10,
    marginBottom: 20,
    fontSize: fp(1.6),
    color: '#333333',
    fontFamily: Fonts.medium,
    textAlign: 'left', // Default alignment
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 8,
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  dropdownText: {
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  dropdownIcon: {
    marginLeft: 10,
  },
  iconImage: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  dropdownList: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    marginBottom: 20,
    overflow: 'hidden',
    maxHeight: 180,
    backgroundColor: 'white',
    elevation: 3,
  },
  dropdownScrollContent: {
    paddingBottom: 5,
  },
  dropdownItem: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: colors.txtGrey,
  },
  dropdownItemText: {
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  imagePicker: {
    alignSelf: 'center',
    backgroundColor: colors.whiteFlash,
    height: hp(20),
    width: wp(90),
    marginVertical: hp(2),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: fp(1.8),
    color: colors.black,
    marginBottom: 15,
    fontFamily: Fonts.medium,
  },
  modalButton: {
    width: '100%',
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 5,
    marginVertical: 5,
    backgroundColor: colors.themeColor,
  },
  cancelButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.red,
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  image: {
    height: '100%',
    width: '100%',
    resizeMode: 'center',
  },
});
