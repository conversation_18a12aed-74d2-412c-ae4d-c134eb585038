import {
  Dimensions,
  Image,
  Modal,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {AppHeader} from '../../../Components/Header';
import icons from '../../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../../Helper/ResponsiveDimensions';
import colors from '../../../Utils/colors';
import {Fonts} from '../../../Utils/Fonts';
import {PrimaryButton} from '../../../Components/CustomButton';
import {DUMMY_USER_IMG, responsiveFontSize} from '../../../Utils/constant';
import {useDispatch, useSelector} from 'react-redux';
import {IMAGE_BASE_URL} from '../../../Utils/getBaseUrl';
import {useDeleteAccountMutation} from '../../../Api/ApiSlice';
import {showToast} from '../../../Components/ToastHelper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {updateSplashVisible} from '../../../Helper/SplashHelper/SplashHelper';
import {clearAuthData, setIsLoggedIn} from '../../../Features/authSlice';

const {width} = Dimensions.get('screen');

const DeleteAccount = ({navigation}) => {
  const userData = useSelector(state => state.auth);

  console.log('************', userData);
  const dispatch = useDispatch();

  const [deleteAccount, {isLoading}] = useDeleteAccountMutation();

  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [selectedId, setSelectedId] = useState(null);
  const [otherReason, setOtherReason] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const data = [
    {id: 1, label: t('noLongerUsing')},
    {id: 2, label: t('fountBatter')},
    {id: 3, label: t('privacyConcern')},
    {id: 4, label: t('tooManyEmails')},
    {id: 5, label: t('accountSecurity')},
    {id: 6, label: t('personalReason')},
    {id: 7, label: t('other')},
  ];
  const handleCancelLogout = () => {
    // Hide logout confirmation modal
    // setIsLogoutModalVisible(false);
    setModalVisible(false);
  };
  const handleLogout = async () => {
    try {
      // Set the splash screen visibility to false on logout
      await updateSplashVisible(null, false, true); // Mark as logout to hide splash screen
      await AsyncStorage.removeItem('user'); // Clear user data
      dispatch(clearAuthData()); // Clear auth data in Redux store
      dispatch(setIsLoggedIn(false)); // Set login state to false

      // showToast('success', t('logged_out'));

      // Close logout modal
      setModalVisible(false);

      // Reset navigation stack and navigate to WelcomeScreen
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{name: 'WelcomeScreen'}], // Navigate directly to WelcomeScreen
        }),
      );
    } catch (error) {
      console.log('Logout API error:', error);
      // showToast('error', t('logged_out_fail'), 'bottom', isRTL);
      setIsLogoutModalVisible(false); // Close the modal in case of error
    }
  };
  const onPressDelete = async () => {
    if (!otherReason) {
      showToast('error', t('reasonRequired'), 'bottom', isRTL);
      setModalVisible(false);
      return;
    }
    try {
      const response = await deleteAccount(otherReason).unwrap();
      console.log('Account deleted successfully:', response);
      if (response?.status) {
        showToast('success', response?.message, 'bottom', isRTL);
        handleLogout();
      }
      // Handle success (e.g., navigate to login screen or show a success message)
    } catch (error) {
      console.error('Error deleting account:', error);
      // Handle error (e.g., show error message)
    }
  };

  const CustomCheckbox = ({label, isSelected, onSelect}) => {
    return (
      <TouchableOpacity
        style={[
          styles.checkboxContainer,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}
        onPress={onSelect}>
        <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
          {isSelected && <Text style={styles.checkMark}>✓</Text>}
        </View>
        <Text
          style={[
            styles.checkboxLabel,
            {textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 10 : 0},
          ]}>
          {label}
        </Text>
      </TouchableOpacity>
    );
  };
  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={t('deleteAccount')}
        backIcon={icons.backbtn}
        isBackBtn
        isWhite={true}
      />
      <View style={styles.body}>
        {data.map(item => (
          <View style={styles.card}>
            <CustomCheckbox
              key={item.id}
              label={item.label}
              isSelected={selectedId === item.id}
              onSelect={() => {
                if (item?.id == 7) {
                  setSelectedId(item.id);
                  setOtherReason('');
                } else {
                  setSelectedId(item.id);
                  setOtherReason(item?.label);
                }
              }}
            />
          </View>
        ))}
        {selectedId == 7 && (
          <View>
            <TextInput
              multiline={true}
              placeholder={t('messagePlaceHolder')}
              placeholderTextColor={'#737373'}
              style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
              value={otherReason}
              onChangeText={setOtherReason}
            />
          </View>
        )}
      </View>
      <PrimaryButton
        title={t('Delete')}
        onPress={() => setModalVisible(true)}
      />
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={handleCancelLogout}>
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            {/* <View>
              <Image
                source={
                  userData?.profile_image
                    ? {uri: IMAGE_BASE_URL + userData?.profile_image}
                    : {uri: DUMMY_USER_IMG}
                }
                style={styles.img}
              />
            </View> */}
            <Text style={styles.modalTitle}>
              {t('areYouSureAccountDelete')}
            </Text>
            <Text style={styles.modalText}>{t('deleteMsg')}</Text>
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={handleCancelLogout}>
                <Text style={styles.cancelButtonText}>{t('cancel')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={onPressDelete}
                style={[styles.button, styles.logoutButton]}>
                <Text style={styles.logoutButtonText}>{t('Delete')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default DeleteAccount;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  body: {
    flex: 1,
    paddingVertical: hp(2),
    paddingHorizontal: wp(2),
  },
  checkboxContainer: {
    alignItems: 'center',
    // marginBottom: hp(1),
  },
  checkbox: {
    width: width * 0.05, // 5% of screen width
    height: width * 0.05,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.txtGrey,
    marginRight: width * 0.03, // 3% of screen width
    justifyContent: 'center',
    alignItems: 'center',
    // marginBottom: 10,
  },
  checkboxSelected: {
    backgroundColor: colors.themeColor,
    borderColor: colors.themeColor,
  },
  checkMark: {
    color: colors.white,
    fontSize: fp(1.6), // 3.5% of screen width for scalability
  },
  checkboxLabel: {
    fontSize: fp(1.6),
    // fontSize: width * 0.04, // 4% of screen width
    color: '#333',
    width: '80%',
    fontFamily: Fonts.medium,
  },
  card: {
    borderRadius: 10,
    padding: wp(2),
    borderWidth: 1,
    borderColor: colors.lightgreay,
    marginBottom: hp(1),
    justifyContent: 'center',
  },
  input: {
    height: hp(15),
    backgroundColor: '#f2f2f2',
    borderRadius: 10,
    padding: fp(2),
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    // color: 'black',
    // fontFamily: Fonts.bold,
    // fontSize: responsiveFontSize(14),
    // textAlign: 'center',
  },
  logoutButtonText: {
    color: 'white',
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    textAlign: 'center',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: wp(2),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    marginVertical: 15,
    textAlign: 'center',
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
    lineHeight: hp(2),
    width: wp(80),
  },
  modalText: {
    marginBottom: fp(1.4),
    fontFamily: Fonts.medium,
    color: colors.black,
    textAlign: 'center',
    fontSize: fp(1.6),
    lineHeight: hp(2),
    width: wp(80),
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
    width: '90%',
    marginHorizontal: wp(2),
    gap: wp(1.2),
  },
  button: {
    borderRadius: 10,
    width: '48%',
    padding: 10,
    height: hp(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.themeColor,
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    color: colors.themeColor,
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    textAlign: 'center',
  },
  img: {
    height: fp(10),
    width: fp(10),
    borderRadius: fp(8),
  },
});
