import {StyleSheet} from 'react-native';

import colors from '../../../Utils/colors';
import {hp, wp, fp} from '../../../Helper/ResponsiveDimensions';
import {responsiveFontSize} from '../../../Utils/constant';
import {Fonts} from '../../../Utils/Fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    backgroundColor: colors.themeColor,
    height: hp(7),
    alignItems: 'center',
    paddingHorizontal: wp(2),
  },
  name: {
    left: 15,
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(16),
    color: colors.white,
  },
  status: {
    fontFamily: Fonts.medium,
    fontSize: responsiveFontSize(12),
    left: 8,
  },
  statusView: {
    marginLeft: wp(5),
    alignItems: 'center',
    borderRadius: 15,
    paddingHorizontal: 8,
    paddingVertical: 5,
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: wp(18),
  },

  messageList: {
    flex: 1,
    paddingHorizontal: 10,
  },
  messageContainer: {
    maxWidth: '85%',
    marginVertical: 5,
    padding: 10,
  },
  selfMessage: {
    backgroundColor: colors.themeColor,
    marginRight: 10,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  otherMessage: {
    backgroundColor: colors.white,
    marginLeft: 10,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    borderTopRightRadius: 12,
  },
  messageText: {
    fontSize: responsiveFontSize(14),
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  timeText: {
    fontSize: fp(1.4),
    color: colors.blackSkatch,
    marginTop: 4,
    fontFamily: Fonts.medium,
  },
  inputContainer: {
    alignItems: 'center',
    padding: 10,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  textInput: {
    flex: 1,
    height: 40,
    borderColor: '#E5E5E5',
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 15,
    backgroundColor: '#F8F8F8',
  },
  sendButton: {
    marginLeft: 10,
    backgroundColor: colors.themeColor,
    height: 50,
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 30,
  },

  messageWrapper: {
    marginVertical: 5,
    elevation: 2,
  },
  selfWrapper: {
    alignItems: 'flex-end',
  },
  otherWrapper: {
    alignItems: 'flex-start',
  },
});

export default styles;
