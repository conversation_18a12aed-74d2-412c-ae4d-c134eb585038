import React, {useEffect, useRef, useState, useCallback, useMemo} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  Image,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {useSelector} from 'react-redux';
import moment from 'moment';
import {useTranslation} from 'react-i18next';
import {useSocket} from '../../../Helper/SocketHelper/SocketProvider';
import icons from '../../../Utils/icons';
import colors from '../../../Utils/colors';
import {fp, wp} from '../../../Helper/ResponsiveDimensions';
import {showToast} from '../../../Components/ToastHelper';
import {StatusContainer} from '../../../Components/StatusBar';
import styles from './styles';
import {applyShadowStyleIos} from '../../../Helper/ShadowStyleIos';
import useGlobalUnreadCount from '../../../Helper/CustomHooks/UseGloabalUnreadNotiCount';

const ChatWithAdmin = ({navigation, route}) => {
  const {socket, emit, on, off, onAny} = useSocket();
  const {item} = route?.params || {};
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const userData = useSelector(state => state?.auth);
  const flatListRef = useRef(null);
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState('');

  const chatData = useMemo(() => item, [item]);

  // Initialize chat and set up socket listeners
  const chatId = useMemo(() => chatData?.id, [chatData]);
  const {refetchUnreadCount} = useGlobalUnreadCount();

  useFocusEffect(
    React.useCallback(() => {
      refetchUnreadCount(); // Ensure fresh data when navigating to a screen with this header
    }, [refetchUnreadCount]),
  );
  useEffect(() => {
    if (!chatId) return;

    console.log('Connecting to socket...');
    const initChat = () => {
      const room_id = chatData?.id;
      const sender_type = 'user';
      const user_id = chatData?.user_id;
      emit('joinSupportRoom', {room_id, sender_type, user_id});
      console.log('Joined room:', room_id);
    };

    const handleSupportChatMessages = chat => {
      console.log('Received supportChatMessages:', chat);
      const allChat = chat.messages;
      setMessages(prevMessages => [...prevMessages, ...allChat]);
    };

    const handleReceiveSupportMessage = chat => {
      console.log('Received receiveSupportMessage:', chat);
      setMessages(prevMessages => [...prevMessages, chat]);
    };

    initChat();
    on('supportChatMessages', handleSupportChatMessages);
    on('receiveSupportMessage', handleReceiveSupportMessage);

    return () => {
      // console.log('Cleaning up socket listeners...');
      off('supportChatMessages', handleSupportChatMessages);
      off('receiveSupportMessage', handleReceiveSupportMessage);
    };
  }, [chatId, emit, on, off]);

  // onAny((event, data) => {
  //   console.log('Received socket event:', event, data);
  // });

  const sendMessage = useCallback(() => {
    if (message.trim() === '') {
      showToast('error', t('emptyMessageError'), 'bottom', isRTL);
      return;
    }

    const room_id = chatData?.id;
    const ticket_id = chatData?.id;
    const sender = userData?.userId;
    const sender_type = 'user';

    emit('sendSupportMessage', {
      room_id,
      ticket_id,
      sender,
      message,
      sender_type,
    });

    setMessage('');
    flatListRef.current?.scrollToEnd({index: 0, animated: true});
  }, [message, chatData?.id, userData?.userId, emit, t, isRTL]);

  // Render individual message
  const renderMessage = useCallback(({item}) => {
    const isSelf = item?.sender_type === 'user';
    return (
      <View
        style={[
          applyShadowStyleIos(styles.messageWrapper),
          isSelf ? styles.selfWrapper : styles.otherWrapper,
        ]}>
        <View style={{width: '75%'}}>
          <View style={{flexDirection: isSelf ? 'row-reverse' : 'row'}}>
            <View
              style={[
                styles.messageContainer,
                isSelf ? styles.selfMessage : styles.otherMessage,
              ]}>
              <Text
                style={[
                  styles.messageText,
                  {color: isSelf ? colors.white : colors.black},
                ]}>
                {item?.message}
              </Text>
              <Text
                style={[
                  styles.timeText,
                  {textAlign: isSelf ? 'left' : 'right'},
                ]}>
                {moment(item?.createdAt).format('hh:mm A')}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer />
      <View
        style={[
          styles.header,
          {
            flexDirection: isRTL ? 'row-reverse' : 'row',
            paddingRight: isRTL ? 10 : 0,
          },
        ]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            resizeMode="contain"
            style={[
              {
                height: fp(3),
                width: fp(3),
                tintColor: colors.white,
                transform: [{rotate: isRTL ? '-180deg' : '0deg'}],
              },
            ]}
            source={icons.backbtn}
          />
        </TouchableOpacity>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
          }}>
          <Text style={[styles.name]}>{t('admin')}</Text>
        </View>
      </View>
      <View style={{flex: 1, backgroundColor: 'white'}}>
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={item => item.id.toString()}
          style={styles.messageList}
          onContentSizeChange={() =>
            flatListRef.current?.scrollToEnd({index: 0, animated: true})
          }
        />
      </View>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          alignItems: 'center',
        }}>
        <View
          style={[
            styles.inputContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <TextInput
            style={[styles.textInput, {textAlign: isRTL ? 'right' : 'left'}]}
            placeholder={t('typeYourMessage')}
            value={message}
            onChangeText={setMessage}
          />
          <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
            <Image
              source={icons.send}
              style={{transform: [{rotate: isRTL ? '-180deg' : '0deg'}]}}
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ChatWithAdmin;
