import {
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {AppHeader} from '../../../Components/Header';
import {useTranslation} from 'react-i18next';
import icons from '../../../Utils/icons';
import {useGetStudentStaticContentQuery} from '../../../Api/ApiSlice';
import RenderHTML from 'react-native-render-html';
import {hp, wp} from '../../../Helper/ResponsiveDimensions';
import {StatusContainer} from '../../../Components/StatusBar';

const AboutUs = () => {
  const {t} = useTranslation();

  const {data: staticData, isLoading} = useGetStudentStaticContentQuery({
    pageType: 'about-us',
    user: 'tutor',
  });
  useEffect(() => {
    console.log('93284975980294', staticData);
  }, [staticData]);

  const styledHtmlContent = `
    <html>
        <head>
            <style>
                body {
                    color: #333; /* Default text color */
                    font-family: Arial, sans-serif; /* Default font family */
                }
                p {
                    color: #555; /* Custom text color for paragraphs */
                }
                h1, h2, h3, h4, h5, h6 {
                    color: #222; /* Custom text color for headings */
                }
                strong {
                    color: #000; /* Custom text color for strong elements */
                }
                ul {
                    color: #666; /* Custom text color for list items */
                }
            </style>
        </head>
        <body>
            ${staticData?.data?.content}
        </body>
    </html>
  `;

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer />
      <AppHeader title={t('aboutUs')} backIcon={icons.backbtn} isBackBtn />
      <ScrollView
        contentContainerStyle={{
          paddingVertical: hp(2),
          marginHorizontal: wp(2),
        }}>
        {isLoading ? (
          <ActivityIndicator size={'large'} />
        ) : (
          <RenderHTML
            contentWidth={'100%'}
            source={{html: styledHtmlContent}}
            baseStyle={{color: 'black', fontFamily: 'Arial, sans-serif'}}
            tagsStyles={{
              p: {color: 'gray'},
              h1: {color: 'black'},
              h2: {color: 'black'},
              h3: {color: 'black'},
              h4: {color: 'black'},
              h5: {color: 'black'},
              h6: {color: 'black'},
              strong: {color: 'black'},
              ul: {color: 'black'},
              li: {color: 'gray'},
            }}
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default AboutUs;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: colors.white},
});
