import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  FlatList,
  I18nManager,
  Image,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  useGetTutorProfileStatusQuery,
  useProfileDetailsQuery,
} from '../../Api/ApiSlice';
import SearchBar from '../../Components/SearchBar';
import {StatusContainer} from '../../Components/StatusBar';
import {Title} from '../../Components/Title';
import {showToast} from '../../Components/ToastHelper';
import TutorCardComponent from '../../Components/TutorCard';
import TutorSmallCard from '../../Components/TutorSmallCard';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import styles from './styles';
import TodayClassComponent from './TodayClassComponent';
import AsyncStorage from '@react-native-async-storage/async-storage';
import TutorHomeHeader from '../../Components/HomeHeader/TutorHomeHeader';

const HomeTutorScreen = ({navigation}) => {
  const {t} = useTranslation();

  const [langType, setLangType] = useState('');
  const [showSetupCard, setShowSetupCard] = useState(false);
  const [showClassComponents, setShowClassComponents] = useState(false);
  const handleGetStartedPress = () => {
    setShowSetupCard(true);
  };

  // const handleSetupNowPress = () => {
  //   showToast('success', t('coming_soon'));
  //   setShowClassComponents(true);
  // };

  const {
    data: tutorProfileStatusData,
    error: tutorProfileStatusError,
    isLoading: tutorProfileStatusLoading,
  } = useGetTutorProfileStatusQuery();

  useEffect(() => {
    if (tutorProfileStatusError) {
      showToast('error', tutorProfileStatusData?.message);
    } else if (tutorProfileStatusData) {
      console.log('Profile Status:', tutorProfileStatusData);
    }
  }, [
    tutorProfileStatusData,
    tutorProfileStatusError,
    tutorProfileStatusLoading,
  ]);

  const {data: profileData, error, isLoading} = useProfileDetailsQuery();

  const getLangType = async () => {
    const storedLangType = await AsyncStorage.getItem('selectedLang');
    return storedLangType ? JSON.parse(storedLangType) : '';
  };

  useEffect(() => {
    const fetchLangType = async () => {
      const type = await getLangType();
      setLangType(type);
    };
    fetchLangType();
  }, []);

  const logoSource =
    langType === 'ar' ? icons.taleemLogoWhite : icons.taleemLogoEnglish;

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <View style={styles.headerContainer}>
        <View style={styles.leftHeader}>
          <TouchableOpacity onPress={() => navigation.openDrawer()}>
            <Image source={icons.hamBurger} style={styles.hamburgerIcon} />
          </TouchableOpacity>

          <View style={styles.logoContainer}>
            <Image
              source={logoSource}
              style={styles.logoImage}
              resizeMode="contain"
            />
          </View>
        </View>

        <View style={styles.rightHeader}>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.locationContainer}
            onPress={() => showToast('success', t('coming_soon'))}>
            <Image
              source={icons.location}
              style={styles.locationIcon}
              resizeMode="contain"
            />
            <Text style={styles.locationText}>{'Qatar'}</Text>
            <Image
              source={icons.arrowDownWhite}
              style={styles.arrowDownIcon}
              resizeMode="contain"
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.notificationContainer}
            onPress={() =>
              showToast('success', t('notifications_unavailable'))
            }>
            <Image
              source={icons.notification}
              style={styles.notificationIcon}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.contentContainer}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <TutorHomeHeader
            showClassComponents={showClassComponents}
            showSetupCard={showSetupCard}
            handleSetupNowPress={handleSetupNowPress}
            profileData={profileData}
            handleGetStartedPress={handleGetStartedPress}
          />

          <Title
            text={t('quick_actions')}
            style={{
              ...styles.quickActionsTitle,
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
          />
          <View style={styles.viewContent}>
            <FlatList
              data={TutorCardData}
              numColumns={2}
              columnWrapperStyle={{justifyContent: 'space-between'}}
              contentContainerStyle={{marginHorizontal: 10}}
              renderItem={({item, index}) => {
                return (
                  <TutorSmallCard
                    title={t(item?.title)}
                    imageUri={item.image}
                    activeOpacity={0.8}
                    onPress={() => {
                      if (item.screen) {
                        navigation.navigate(item.screen);
                      } else {
                        showToast('success', t('coming_soon'));
                      }
                    }}
                  />
                );
              }}
            />
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default HomeTutorScreen;
