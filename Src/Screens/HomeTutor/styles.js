import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
  },
  headerContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  leftHeader: {
    flex: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  hamburgerIcon: {
    width: 24,
    height: 24,
  },
  logoContainer: {
    alignItems: 'center',
    paddingLeft: 5,
  },
  logoImage: {
    width: 74,
    height: 32,
  },
  rightHeader: {
    flex: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingRight: 10,
    minWidth: 120,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
    minWidth: 80,
  },
  locationIcon: {
    width: 15,
    height: 17,
  },
  locationText: {
    marginHorizontal: 6,
    color: colors.white,
    fontFamily: Fonts.medium,
    fontSize: responsiveFontSize(12),
  },
  arrowDownIcon: {
    width: 12,
    height: 12,
    alignSelf: 'center',
  },
  notificationContainer: {
    padding: 5,
  },
  notificationIcon: {
    width: 20,
    height: 20,
  },
  contentContainer: {
    backgroundColor: colors.white,
    flex: 1,
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
  },
  viewContent: {
    marginBottom: 50,
    marginHorizontal: 16,
  },
  quickActionsTitle: {
    fontSize: responsiveFontSize(20),
    fontFamily: Fonts.regular,
    marginTop: 10,
  },
});

export default styles;
