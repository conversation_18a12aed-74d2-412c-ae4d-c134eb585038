import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import icons from '../../Utils/icons';
import {PrimaryButton} from '../../Components/CustomButton';
import SearchBar from '../../Components/SearchBar';
import {Fonts} from '../../Utils/Fonts';
import {
  convertTo12HourFormat,
  convertToLocal12HourFormat,
} from '../../Helper/DateHelpers/DateHelpers';
import moment from 'moment';
import {useNavigation} from '@react-navigation/native';
import {ColorSpace} from 'react-native-reanimated';
import {hp, wp} from '../../Helper/ResponsiveDimensions';

const TodayClassComponent = ({
  title,
  onPress,
  count,
  data,
  search,
  setSearch,
  setActiveBtn,
  activeBtn,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [sessionData, setSessionData] = useState([]);
  const [date] = useState(new Date());

  const navigation = useNavigation();

  useEffect(() => {
    setSessionData(data);
  }, [data]);
  console.log('session Data====', JSON.stringify(sessionData));
  return (
    <View style={styles.container}>
      <View>
        <View
          style={{
            marginVertical: hp(1),
            marginRight: isRTL ? wp(7) : 0,
            marginLeft: wp(2.6),
          }}>
          <Text
            style={[styles.titleTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
            {title}
          </Text>
        </View>
        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          <TouchableOpacity
            onPress={() => setActiveBtn(1)}
            activeOpacity={0.9}
            style={[
              styles.btn,
              {
                backgroundColor:
                  activeBtn === 1 ? colors.themeColor : colors.white,
              },
            ]}>
            <Text
              style={[
                styles.btnTxt,
                {color: activeBtn === 1 ? colors.white : colors.searchGray},
              ]}>
              {t('all')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setActiveBtn(2)}
            activeOpacity={0.9}
            style={[
              styles.btn,
              {
                backgroundColor:
                  activeBtn === 2 ? colors.themeColor : colors.white,
              },
            ]}>
            <Text
              style={[
                styles.btnTxt,
                {color: activeBtn === 2 ? colors.white : colors.searchGray},
              ]}>
              {t('online')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setActiveBtn(3)}
            activeOpacity={0.9}
            style={[
              styles.btn,
              {
                backgroundColor:
                  activeBtn === 3 ? colors.themeColor : colors.white,
              },
            ]}>
            <Text
              style={[
                styles.btnTxt,
                {color: activeBtn === 3 ? colors.white : colors.searchGray},
              ]}>
              {t('face_to_face')}
            </Text>
          </TouchableOpacity>
        </View>

        <FlatList
          data={sessionData}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={{paddingBottom: hp(6)}}
          ListEmptyComponent={
            <Text
              style={{
                textAlign: 'center',
                color: colors.grey,
                fontFamily: Fonts.medium,
                marginTop: hp(2),
              }}>
              {t('noClasses')}
            </Text>
          }
          renderItem={({item}) => (
            <View style={styles.card}>
              <View
                style={[
                  styles.innerView,
                  {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
                ]}>
                <Text
                  style={[
                    styles.txtContent,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>{`${
                  item?.tlm_booking?.tlm_tutor_rate_card_recreational
                    ?.tlm_expertise?.name ||
                  item?.tlm_booking?.tlm_tutor_rate_card_academic?.tlm_expertise
                    ?.name ||
                  item?.tlm_booking?.tlm_tutor_rate_card_all_course
                    ?.tlm_expertise?.name ||
                  item?.tlm_booking?.class_title
                }`}</Text>
                <View
                  style={[
                    styles.content,
                    {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  ]}>
                  <Image source={icons.timing} />
                  <Text style={styles.txt}>{`${moment(date).format(
                    'DD MMM YYYY',
                  )} ${convertToLocal12HourFormat(item?.start_time)}`}</Text>
                </View>
                <View
                  style={[
                    styles.content,
                    {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  ]}>
                  <Image source={icons.person} />
                  <Text style={styles.txt}>
                    {isRTL
                      ? item?.tlm_booking?.tlm_sessions_type?.name_ar
                      : item?.tlm_booking?.tlm_sessions_type?.name}
                  </Text>
                </View>
                <View
                  style={[
                    styles.content,
                    {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  ]}>
                  <Image source={icons.school} />
                  <Text style={styles.txt}>
                    {item?.tlm_booking?.tlm_class_type?.name_ar}
                  </Text>
                </View>
              </View>
              <PrimaryButton
                style={{
                  width: '28%',
                  height: 35,
                  position: 'absolute',
                  right: isRTL ? 0 : 15,
                  left: isRTL ? 15 : 5,
                  bottom: 8,
                }}
                onPress={() => {
                  if (item?.type === 'openSession') {
                    navigation.navigate('ViewEnrolledOpenSessionDetails', {
                      openSessionId: item?.tlm_booking?.id,
                      openSessionIdTutor: item?.id,
                    });
                  } else {
                    navigation.navigate('StudentClassDetails', {item});
                  }
                }}
                textStyle={{fontSize: 14}}
                title={t('start')}
              />
            </View>
          )}
        />
      </View>
    </View>
  );
};

export default TodayClassComponent;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 3,
    // marginVertical: 8,
  },
  titleTxt: {
    fontSize: 16,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  row: {
    alignItems: 'center',
    paddingRight: 20,
    flexWrap: 'wrap',
  },
  btn: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 7,
    paddingHorizontal: wp(4),
    borderRadius: 20,
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 8,
    marginVertical: 8,
  },
  btnTxt: {
    fontSize: 13,
    fontFamily: Fonts.medium,
  },
  card: {
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 8,
    marginVertical: 4,
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  txtContent: {
    fontSize: 13,
    lineHeight: 18,
    color: colors.black,
    fontFamily: Fonts.bold,
  },
  content: {
    alignItems: 'center',
    marginVertical: hp(1),
  },
  txt: {
    marginLeft: 8,
    fontSize: 13,
    fontFamily: Fonts.medium,
    color: colors.searchGray,
  },
  innerView: {
    width: '70%',
  },
});
