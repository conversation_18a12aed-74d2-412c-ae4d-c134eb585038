import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  Image,
  ScrollView,
  Platform,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {styles} from './styles';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {showToast} from '../../Components/ToastHelper';
import CustomDropDown from '../../Components/CustomDropDown';
import {CLASS_TYPES, RECURRENCE_TYPE_DATA} from '../../Utils/constant';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {PrimaryButton} from '../../Components/CustomButton';
import moment from 'moment';
import {
  useAddOpenSessionMutation,
  useAddTutorScheduleApiMutation,
  useGetOpneSessionsQuery,
  useGetRateAllCardsQuery,
} from '../../Api/ApiSlice';
import {useDispatch, useSelector} from 'react-redux';
import CustomCheckbox from '../../Components/CustomCheckbox';
import TutorAddSlotModal from '../../Components/Custom_Modal/TutorAddSlotModal';
import {convertTo12HourFormat} from '../../Helper/DateHelpers/DateHelpers';
import LinearGradient from 'react-native-linear-gradient';
import CustomDatePicker from '../../Components/CustomDatePicker';
import {SelectClassTypeCard} from '../BookYourTutor';
import {ScheduleList} from '../BookYourTutor/RenderScheduleHour';
import {
  resetTutorSlots,
  updateSelectedSlots,
} from '../../Redux/Slices/Tutor/TutorSlotSlice';
import {transformToBookingSchedulesArray} from '../../Helper/TutorBookingHelpers';
import {PrimaryInput} from '../../Components/Input';
import {Fonts} from '../../Utils/Fonts';
import {openSessionPayload} from './openSessionPayload';
import {KeyboardAvoidingView} from 'react-native';
import HelperTextComponent from '../../Components/HelperTipComp';

const OpenSession = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const [startDate, setStartDate] = useState(new Date());
  const [selectedClassType, setSelectedClassType] = useState({});
  const [endDate, setEndDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showAddSlotModal, setShowAddSlotModal] = useState(false);
  const [rateCard, setRateCard] = useState('');
  const [numberSession, setNumberSession] = useState('');
  const [rateCardData, setRateCardData] = useState([]);
  const [onlineAvailablity, setOnlineAvailablity] = useState([]);
  const [customSelectedDays, setCustomSelectedDays] = useState([]);
  const {selectedSlots} = useSelector(state => state.tutorSlot);
  const isRTL = i18n.language === 'ar';
  const sortedData = transformToBookingSchedulesArray(selectedSlots).sort(
    (a, b) => new Date(a.date) - new Date(b.date),
  );
  const userData = useSelector(state => state.auth);
  const [openOnlineHelper, setOpenOnlineHelper] = useState(false);
  const [openEndDateHelper, setEndDateHelper] = useState(false);
  const [openClassTypeHelper, setOpenClassTypeHelper] = useState(false);
  const [openRateCardHelper, setOpenRateCardHelper] = useState(false);
  const [openAvailabilityHelper, setOpenAvailablityHelper] = useState(false);
  const [meetingPointHelper, setMeetingPointHelper] = useState(false);

  const handleSelect = (category, itemId) => {
    setSelectedFilters(prev => ({
      ...prev,
      [category]: prev[category] === itemId ? '' : itemId,
    }));
  };

  const [recurrenceType, setRecurrenceType] = useState({
    name: 'Daily',
    value: [0, 1, 2, 3, 4, 5, 6],
  });
  const [description, setDescription] = useState('');
  const [toTime, setToTime] = useState(moment().format('HH:mm:00'));
  const [fromTime, setFromTime] = useState(moment().format('HH:mm:00'));
  const [sessionSelect, setSessionSelect] = useState();

  const handleDateSelect = selectedDate => {
    setStartDate(selectedDate);
  };

  const {
    data: rateCards,
    error,
    isLoading,
    refetch,
  } = useGetRateAllCardsQuery({
    class_type_id: selectedClassType?.value == 'online' ? '1' : '2',
  });

  useEffect(() => {
    refetch();
  }, [selectedClassType]);

  useEffect(() => {
    if (rateCards?.data?.length) {
      const updatedData = rateCards?.data?.map(({title, ...rest}) => ({
        name: title,
        ...rest,
      }));
      setRateCardData(updatedData);
    }
  }, [rateCards]);

  const [addOpenSession, {isLoading: scheduleLoading}] =
    useAddOpenSessionMutation();

  const addOppenSessionTutor = async () => {
    if (fromTime > toTime) {
      showToast('error', t('startTimeErrorMessage'), 'bottom', isRTL);
      return;
    }
    try {
      const payload = {
        start_date: sortedData[0].date,
        end_date: sortedData[sortedData.length - 1].date,
        rate_card_id: rateCard?.id.toString(),
        rate_card_type:
          rateCard?.type == 'academic'
            ? '1'
            : rateCard?.type == 'recreational'
            ? '2'
            : '3',
        class_type_id: selectedClassType?.value == 'online' ? '1' : '2',
        title: `Class for ${rateCard?.name}`,
        open_session_schedules: sortedData,
        no_of_sessions: sortedData.length,
        enrollment_start_date: startDate,
        enrollment_end_date: endDate,
        description: description,
        ...openSessionPayload,
      };
      console.log('🚀 ~ addOppenSessionTutor ~ payload:', payload);

      const response = await addOpenSession(payload);
      if (response?.data?.status) {
        showToast('success', response?.data?.message, 'bottom', isRTL);
        navigation.goBack();
      } else {
        showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      }
    } catch (error) {
      console.error('Error in addScheduleApi:', error);
      showToast('error', response?.data?.message, 'bottom', isRTL);
    }
  };

  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    const subscription = navigation.addListener('focus', () => {
      setRefreshKey(prevKey => prevKey + 1);
    });
    return subscription;
  }, [navigation, openSessionPayload.address]);

  const dispatch = useDispatch();
  const handleDeleteSlot = item => {
    try {
      const date = item.displayDate;
      const startTime = item.start_time;
      const endTime = item.end_time;
      const slotId = item.id;

      const updatedSlotsForDate =
        selectedSlots[date]?.filter(
          slot => slot.start_time !== startTime || slot.end_time !== endTime,
        ) || [];

      const slotsToDispatch =
        updatedSlotsForDate.length > 0 ? updatedSlotsForDate : undefined;

      dispatch(updateSelectedSlots({date, slots: slotsToDispatch}));
    } catch (error) {
      console.error('🚀 ~ handleDeleteSlot ~ error:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container} key={refreshKey}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('create_open_session')}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{flex: 1}}
        keyboardVerticalOffset={Platform.OS === 'ios' ? -50 : 0} // Adjusted offset
      >
        <View style={styles.body}>
          <ScrollView
            style={{flexGrow: 1}} // Ensure ScrollView grows with content
            contentContainerStyle={{paddingBottom: hp(15)}} // Enough padding for button
            keyboardShouldPersistTaps="handled">
            <View style={styles.sectionContainer}>
              <View
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  alignItems: 'baseline',
                }}>
                <Text
                  style={[styles.sectionTitle, {marginLeft: isRTL ? 8 : 0}]}>
                  {t('select_start_enrollment_date')}
                </Text>
                <HelperTextComponent
                  helperText={t('enrollmentStartDate')}
                  setOpen={setOpenOnlineHelper}
                  open={openOnlineHelper}
                  borderColor={colors.black}
                  iconColor={colors.black}
                />
              </View>
              <CustomDatePicker
                initialDate={startDate}
                onDateChange={handleDateSelect}
                datePickerProps={{minimumDate: startDate}}
                isRTL={isRTL}
                marginBottom={hp(2)}
                marginVertical={1}
              />
              <View
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  alignItems: 'baseline',
                }}>
                <Text
                  style={[styles.sectionTitle, {marginLeft: isRTL ? 8 : 0}]}>
                  {t('select_end_enrollment_date')}
                </Text>
                <HelperTextComponent
                  helperText={t('enrolmentEndDate')}
                  setOpen={setEndDateHelper}
                  open={openEndDateHelper}
                  borderColor={colors.black}
                  iconColor={colors.black}
                />
              </View>
              <CustomDatePicker
                minimumDate={startDate}
                initialDate={endDate}
                onDateChange={date => setEndDate(date)}
                isRTL={isRTL}
                marginBottom={hp(2)}
                marginVertical={1}
              />
              <View
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  alignItems: 'baseline',
                }}>
                <Text
                  style={[styles.sectionTitle, {marginLeft: isRTL ? 8 : 0}]}>
                  {t('select_class_type')}
                </Text>
                <HelperTextComponent
                  helperText={t('selectClassTypeHelperTxt')}
                  setOpen={setOpenRateCardHelper}
                  open={openRateCardHelper}
                  borderColor={colors.black}
                  iconColor={colors.black}
                />
              </View>
              <View
                style={[
                  styles.row,
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                ]}>
                {CLASS_TYPES?.map((item, index) => (
                  <View key={item?.id}>
                    <SelectClassTypeCard
                      isSelected={selectedClassType.value === item?.value}
                      onPress={() => {
                        dispatch(resetTutorSlots());
                        openSessionPayload.address = '';
                        openSessionPayload.latitude = '';
                        openSessionPayload.longitude = '';
                        setSelectedClassType(item);
                      }}
                      color={
                        selectedClassType?.value === item?.value
                          ? colors.themeColor
                          : '#fff'
                      }
                      image={
                        item.value === 'online'
                          ? icons.onlineClassImage
                          : icons.offlineClassImage
                      }
                      title={t(
                        item.value === 'online' ? 'online' : 'face_to_face',
                      )}
                    />
                  </View>
                ))}
              </View>
            </View>

            <View style={styles.sectionContainer}>
              <View
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  alignItems: 'baseline',
                }}>
                <Text
                  style={[styles.sectionTitle, {marginLeft: isRTL ? 8 : 0}]}>
                  {t('select_availability')}
                </Text>
                <HelperTextComponent
                  helperText={t('timeSlotHelperTxt')}
                  setOpen={setOpenAvailablityHelper}
                  open={openAvailabilityHelper}
                  borderColor={colors.black}
                  iconColor={colors.black}
                />
              </View>
              <TouchableOpacity
                onPress={() => {
                  if (Object.keys(selectedClassType).length <= 0) {
                    showToast('error', t('emptyClassType'), 'bottom', isRTL);
                  } else {
                    navigation.navigate('SelectAvailableSlotTutor', {
                      classType: selectedClassType,
                    });
                  }
                }}>
                {!selectedSlots || Object.keys(selectedSlots).length === 0 ? (
                  <TouchableOpacity
                    onPress={() => {
                      if (Object.keys(selectedClassType).length <= 0) {
                        showToast(
                          'error',
                          t('emptyClassType'),
                          'bottom',
                          isRTL,
                        );
                      } else {
                        navigation.navigate('SelectAvailableSlotTutor', {
                          classType: selectedClassType,
                        });
                      }
                    }}
                    style={[
                      styles.selectHoursButton,
                      {flexDirection: isRTL ? 'row-reverse' : 'row'},
                    ]}>
                    <Text style={styles.noSlotsText}>{t('select_hours')}</Text>
                    <Image
                      source={
                        isRTL ? icons.leftArrowWhite : icons.rightArrowGray
                      }
                      style={[
                        styles.arrowIcon,
                        {width: isRTL ? 15 : 20, height: isRTL ? 15 : 20},
                      ]}
                      resizeMode="contain"
                    />
                  </TouchableOpacity>
                ) : (
                  <View>
                    <ScheduleList
                      key={JSON.stringify(selectedSlots)}
                      selectedSlots={selectedSlots}
                      onSelectHoursPress={() => {
                        if (Object.keys(selectedClassType).length <= 0) {
                          showToast(
                            'error',
                            t('emptyClassType'),
                            'bottom',
                            isRTL,
                          );
                        } else {
                          navigation.navigate('SelectAvailableSlotTutor', {
                            classType: selectedClassType,
                          });
                        }
                      }}
                      onDeleteSlot={handleDeleteSlot}
                    />
                  </View>
                )}
              </TouchableOpacity>
            </View>

            <View style={styles.sectionContainer}>
              <View
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  alignItems: 'baseline',
                }}>
                <Text
                  style={[
                    styles.sectionTitle,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {t('selectRateCard')}
                </Text>
                <HelperTextComponent
                  helperText={t('reateCardHelperTxt')}
                  setOpen={setOpenClassTypeHelper}
                  open={openClassTypeHelper}
                  borderColor={colors.black}
                  iconColor={colors.black}
                />
              </View>
              <CustomDropDown
                lable={''}
                data={rateCardData || []}
                backgroundColor={colors.white}
                defaultValue={rateCard}
                height={fp(6)}
                borderWidth={1}
                borderColor={colors.txtGrey1}
                onSelect={selected => setRateCard(selected)}
                marginVertical={0}
              />
              <View style={{marginTop: hp(2)}}>
                <PrimaryInput
                  title={`${t('description')}`}
                  keyboardType="default"
                  containerStyle={{width: '100%'}}
                  placeholder={t('enter_description')}
                  maxLength={500}
                  value={description}
                  lableStyle={[
                    styles.sectionTitle,
                    {marginLeft: isRTL ? 8 : 0},
                  ]}
                  onChangeText={setDescription}
                  textInputStyle={{
                    textAlignVertical: 'top',
                    lineHeight: hp(2),
                  }}
                  multiline={true}
                  showTooltip
                  helperTxt={t('openSessionDiscriptionHelper')}
                />
                <Text
                  style={{
                    textAlign: isRTL ? 'left' : 'right',
                    color: 'grey',
                    fontSize: fp(1.2),
                    marginTop: -hp(1.3),
                    fontFamily: Fonts.medium,
                  }}>
                  {description?.length}/500
                </Text>
              </View>
            </View>

            {selectedClassType?.name == 'Face to Face' && (
              <View style={styles.sectionContainer}>
                <View style={{flexDirection: isRTL ? 'row-reverse' : 'row'}}>
                  <Text
                    style={[styles.sectionTitle, {marginLeft: isRTL ? 8 : 0}]}>
                    {t('meeting_point')}
                  </Text>
                  <HelperTextComponent
                    helperText={t('meetinPointHelperTxt')}
                    setOpen={setMeetingPointHelper}
                    open={meetingPointHelper}
                    borderColor={colors.black}
                    iconColor={colors.black}
                  />
                </View>
                <TouchableOpacity
                  onPress={() =>
                    navigation.navigate('SelectMeetingPointTutorForOpenSession')
                  }
                  style={[
                    styles.MeetingPoint,
                    {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  ]}>
                  <Image
                    source={icons.locationGray}
                    resizeMode="contain"
                    style={{height: 24, width: 24}}
                  />
                  <Text style={styles.dateText}>
                    {openSessionPayload?.address
                      ? openSessionPayload?.address
                      : t('addMeeting')}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>
        </View>
        <View
          style={{
            bottom: 10,
            position: 'absolute',
            width: wp(95),
            alignSelf: 'center',
          }}>
          <PrimaryButton
            title={t('create_open_session')}
            onPress={addOppenSessionTutor}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default OpenSession;
