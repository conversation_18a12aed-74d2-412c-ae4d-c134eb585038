import {StyleSheet} from 'react-native';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {IS_ANDROID, responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';

export const styles = StyleSheet.create({
  container: {flex: 1, paddingBottom: 10, backgroundColor: colors.themeColor},
  body: {
    flex: 1,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: hp(2),
  },
  dateTxt: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.black,
  },
  datePicker: {
    paddingHorizontal: fp(1.6),
    marginVertical: fp(2),
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: fp(6.4),
  },
  input: {
    marginVertical: fp(2),
  },
  addBtn: {
    backgroundColor: colors.themeColor,
    width: fp(6.8),
    height: fp(6.8),
    marginLeft: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    marginBottom: hp(2),
  },
  statusBadge: {
    paddingVertical: hp(0.8),
    paddingHorizontal: wp(4),
    borderRadius: fp(3),
    alignItems: 'center',
  },
  sectionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  sectionTitle: {
    fontSize: fp(1.8),
    color: colors.black,
    fontFamily: Fonts.semiBold,
    marginBottom: hp(1.4),
    marginRight: 8,
  },
  row: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  selectHoursButton: {
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: wp(3),
    backgroundColor: colors.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.lightgreay,
  },
  noSlotsText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: colors.txtGrey1,
  },
  arrowIcon: {
    tintColor: colors.black,
    marginLeft: 7,
  },
  MeetingPoint: {
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 10,
    gap: 10,
  },
  dateText: {
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    color: colors.txtGrey1,
    width: wp(75),
  },
});
