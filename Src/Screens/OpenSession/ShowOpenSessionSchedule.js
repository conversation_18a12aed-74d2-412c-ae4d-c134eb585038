import React from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

import icons from '../../Utils/icons';
import {Fonts} from '../../Utils/Fonts';
import {DUMMY_USER_IMG, responsiveFontSize} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import TutorDetails from '../../Components/MyClass/TutorDetails';
import {useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import moment from 'moment';
import {PrimaryButton} from '../../Components/CustomButton';
import {useTranslation} from 'react-i18next';
import {convertToLocal12HourFormat} from '../../Helper/DateHelpers/DateHelpers';
import {openGoogleMaps} from '../../Helper/GoogleMapsHelpers';

const ShowOpenSessionSchedule = ({
  item,
  onCardPress,
  isTutorCard = true,
  showExperTise = true,
  showView = false,
  onPressView = () => {},
}) => {
  console.log('🚀 ~ SessionInfoCard ~ item:', JSON.stringify(item));
  const navigation = useNavigation();
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={onCardPress}
      style={styles.container}>
      <View
        style={[
          styles.innerHeader,
          {
            alignSelf: isRTL ? 'flex-end' : 'flex-start',
            flexDirection: isRTL ? 'row-reverse' : 'row',
          },
        ]}>
        <Text
          style={[
            styles.titleTxt,
            {textAlign: isRTL ? 'right' : 'left', width: fp(30)},
          ]}>
          {/* Algebra for Primary Students (Grade - 6th) */}
          {`${item?.tlm_open_session_schedules[0]?.tlm_open_session?.title}`}
        </Text>
      </View>

      <View
        style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Image
          source={icons.clockYellow}
          style={{height: fp(2.4), width: fp(2.4), tintColor: '#EBBE49'}}
          resizeMode="contain"
        />
        <Text style={styles.timeTxt}>
          {t('time')} :{' '}
          {convertToLocal12HourFormat(item?.start_time.toString())} -{' '}
          {convertToLocal12HourFormat(item?.end_time.toString())}
          <Text style={styles.innerTxt}></Text>
        </Text>
      </View>
      <View
        style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Image
          source={icons.calendarWhite}
          style={{height: fp(2.4), width: fp(2.4), tintColor: '#EBBE49'}}
          resizeMode="contain"
        />
        <Text style={styles.timeTxt}>
          {t('session')}:{' '}
          <Text style={styles.innerTxt}>
            {item?.tlm_open_session_schedules[0]?.tlm_open_session
              ?.class_type_id == 2
              ? 'Face To Face'
              : 'Online' || 'Online'}
          </Text>
        </Text>
      </View>
      {item?.tlm_open_session_schedules[0]?.tlm_open_session?.class_type_id ==
        2 && (
        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          <Image
            source={icons.locationGray}
            style={{height: fp(2.4), width: fp(2.4), tintColor: '#EBBE49'}}
            resizeMode="contain"
          />
          <Text style={[styles.timeTxt]}>
            {t('meetingPoint')} :{' '}
            <Text
              onPress={() =>
                openGoogleMaps(
                  item?.tlm_open_session_schedules[0]?.tlm_open_session
                    ?.latitude,
                  item?.tlm_open_session_schedules[0]?.tlm_open_session
                    ?.longitude,
                )
              }
              style={[
                // styles.innerTxt,
                {
                  textDecorationLine: 'underline',
                  color: colors.themeColor,
                  width: wp(30),
                  fontFamily: Fonts.poppinsRegular,
                  fontSize: fp(1.4),
                },
              ]}>
              {item?.tlm_open_session_schedules[0]?.tlm_open_session?.address}
            </Text>
          </Text>
        </View>
      )}

      {/* {showView && (
        <View style={{alignSelf: 'flex-end', width: wp(30)}}>
          <PrimaryButton onPress={onPressView} title={t('view')} />
        </View>
      )} */}
      {isTutorCard && <View style={styles.saperator} />}
      {isTutorCard && (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <TutorDetails
            imageUri={{
              uri: item?.tlm_user?.image
                ? IMAGE_BASE_URL + item?.tlm_user?.image
                : DUMMY_USER_IMG,
            }}
            name={item?.tlm_user?.name}
            occupation={item?.tlm_user?.tlm_tutor_profile?.expertise}
          />
          {/* <TouchableOpacity
            onPress={() =>
              navigation.navigate('ChatScreen', {
                item: item?.tlm_user,
              })
            }
            style={styles.messageBtn}>
            <Image source={icons.messageIcon} />
          </TouchableOpacity> */}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.lightgreay,
    padding: fp(2),
    width: wp(90),
    borderRadius: fp(1.4),
    alignSelf: 'center',
    marginTop: hp(1),
    // marginHorizontal:wp()
  },
  innerHeader: {
    justifyContent: 'space-between',
    width: wp(60),
    alignItems: 'center',
  },
  messageBtn: {
    backgroundColor: colors.themeColor,
    borderRadius: 30,
    padding: 8,
    height: 30,
    width: 30,
  },
  timeTxt: {
    fontFamily: Fonts.poppinsRegular,
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    left: 10,
  },
  innerTxt: {
    fontFamily: Fonts.poppinsRegular,
    fontSize: fp(1.4),
    color: colors.darkBlack,
  },
  row: {
    marginTop: hp(1.5),
    alignItems: 'center',
  },
  saperator: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    marginVertical: hp(2),
  },
  titleTxt: {
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
    color: colors.black,
  },
});

export default ShowOpenSessionSchedule;
