import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  View,
  TextInput,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
  Image,
} from 'react-native';
import MapView, {Marker} from 'react-native-maps';
import {useTranslation} from 'react-i18next';
import icons from '../../Utils/icons';
import {AppHeader} from '../../Components/Header';
import colors from '../../Utils/colors';
import {StatusContainer} from '../../Components/StatusBar';
import {PrimaryButton} from '../../Components/CustomButton';
import {useAddMeetingPointMutation} from '../../Api/ApiSlice';
import {debounce} from 'lodash';
import {API_KEY_PERSONAL, IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {showToast} from '../../Components/ToastHelper';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {requestLocationPermission} from '../../Helper/Location/LocationHelpers';
import Geolocation from 'react-native-geolocation-service';
import {openSessionPayload} from './openSessionPayload';

const SelectMeetingPointTutorForOpenSession = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  // Check if openSessionPayload has prefilled data
  const hasPrefilledData =
    openSessionPayload.address &&
    openSessionPayload.latitude &&
    openSessionPayload.longitude;

  const [address, setAddress] = useState(
    hasPrefilledData ? openSessionPayload.address : '',
  );
  const [suggestions, setSuggestions] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(
    hasPrefilledData
      ? {
          lat: openSessionPayload.latitude,
          lng: openSessionPayload.longitude,
          city: 'NA', // Default if not provided
          country: 'NA', // Default if not provided
        }
      : null,
  );
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoadingCurrentLocation, setIsLoadingCurrentLocation] =
    useState(false);
  const [shouldFetchSuggestions, setShouldFetchSuggestions] = useState(false);
  const [addMeetingPoint] = useAddMeetingPointMutation();
  const [region, setRegion] = useState({
    latitude: hasPrefilledData ? openSessionPayload.latitude : 0,
    longitude: hasPrefilledData ? openSessionPayload.longitude : 0,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [markerPosition, setMarkerPosition] = useState(
    hasPrefilledData
      ? {
          latitude: openSessionPayload.latitude,
          longitude: openSessionPayload.longitude,
        }
      : null,
  );

  const inputRef = useRef(null);
  const mapRef = useRef(null);
  const scrollViewRef = useRef(null);

  const debouncedFetchSuggestions = useRef(
    debounce(async query => {
      if (query.length > 2) {
        setIsLoadingSuggestions(true);
        try {
          const response = await fetch(
            `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
              query,
            )}&key=${API_KEY_PERSONAL}&language=en`,
          );
          const data = await response.json();
          if (data.status === 'OK') {
            setSuggestions(data.predictions);
          } else {
            setSuggestions([]);
          }
        } catch (error) {
          console.error('Error fetching Google Places suggestions:', error);
          setSuggestions([]);
        } finally {
          setIsLoadingSuggestions(false);
        }
      } else {
        setSuggestions([]);
      }
    }, 300),
  ).current;

  useEffect(() => {
    if (shouldFetchSuggestions) {
      debouncedFetchSuggestions(address);
    } else {
      setSuggestions([]);
    }
    return () => {
      debouncedFetchSuggestions.cancel();
    };
  }, [address, shouldFetchSuggestions]);

  const fetchPlaceDetails = async placeId => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${API_KEY_PERSONAL}`,
      );
      const data = await response.json();
      if (data.status === 'OK') {
        const {lat, lng} = data.result.geometry.location;
        const cityComponent =
          data.result.address_components.find(comp =>
            comp.types.includes('locality'),
          ) ||
          data.result.address_components.find(comp =>
            comp.types.includes('administrative_area_level_2'),
          );
        const countryComponent = data.result.address_components.find(comp =>
          comp.types.includes('country'),
        );
        const city = cityComponent ? cityComponent.long_name : '';
        const country = countryComponent ? countryComponent.long_name : '';

        const newRegion = {
          latitude: lat,
          longitude: lng,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        };
        setRegion(newRegion);
        setMarkerPosition({latitude: lat, longitude: lng});
        mapRef.current?.animateToRegion(newRegion, 1000);

        return {lat, lng, city, country};
      } else {
        console.error('Failed to fetch place details:', data.status);
        return null;
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
      return null;
    }
  };

  const fetchAddressFromCoordinates = async (latitude, longitude) => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${API_KEY_PERSONAL}&language=en`,
      );
      const data = await response.json();
      if (data.status === 'OK' && data.results.length > 0) {
        const formattedAddress = data.results[0].formatted_address;
        setAddress(formattedAddress);
        const cityComponent =
          data.results[0].address_components.find(comp =>
            comp.types.includes('locality'),
          ) ||
          data.results[0].address_components.find(comp =>
            comp.types.includes('administrative_area_level_2'),
          );
        const countryComponent = data.results[0].address_components.find(comp =>
          comp.types.includes('country'),
        );
        const city = cityComponent ? cityComponent.long_name : 'NA';
        const country = countryComponent ? countryComponent.long_name : 'NA';
        setSelectedLocation({lat: latitude, lng: longitude, city, country});
        return formattedAddress;
      } else {
        console.error('Reverse geocoding failed:', data.status);
        showToast('error', t('failedToRetrieveAddress'), 'bottom', isRTL);
        return null;
      }
    } catch (error) {
      console.error('Error fetching address from coordinates:', error);
      showToast('error', t('failedToRetrieveAddress'), 'bottom', isRTL);
      return null;
    }
  };

  const handleSuggestionPress = async suggestion => {
    setAddress(suggestion.description);
    setSuggestions([]);
    setShouldFetchSuggestions(false);
    const locationDetails = await fetchPlaceDetails(suggestion.place_id);
    if (locationDetails) {
      setSelectedLocation(locationDetails);
      scrollViewRef.current?.scrollTo({y: hp(60), animated: true});
    } else {
      showToast('error', t('failedToRetrieveLocationDetails'), 'bottom', isRTL);
    }
  };

  const handleCurrentLocation = useCallback(async () => {
    try {
      setIsLoadingCurrentLocation(true);
      const hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        showToast('error', t('locationPermissionDenied'), 'bottom', isRTL);
        setIsLoadingCurrentLocation(false);
        return;
      }

      Geolocation.getCurrentPosition(
        async position => {
          const {latitude, longitude} = position.coords;
          const newRegion = {
            latitude,
            longitude,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          };
          setRegion(newRegion);
          setMarkerPosition({latitude, longitude});
          mapRef.current?.animateToRegion(newRegion, 1000);
          await fetchAddressFromCoordinates(latitude, longitude);
          setShouldFetchSuggestions(false);
          setIsLoadingCurrentLocation(false);
        },
        error => {
          console.error('Geolocation error:', error);
          showToast('error', t('failedToGetLocation'), 'bottom', isRTL);
          setIsLoadingCurrentLocation(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
        },
      );
    } catch (error) {
      console.error('Error in handleCurrentLocation:', error);
      showToast('error', t('failedToGetLocation'), 'bottom', isRTL);
      setIsLoadingCurrentLocation(false);
    }
  }, [t, isRTL]);

  useEffect(() => {
    const initializeLocation = async () => {
      // Skip if prefilled data exists
      if (hasPrefilledData) {
        // Optionally fetch city and country if not provided
        if (!selectedLocation.city || !selectedLocation.country) {
          await fetchAddressFromCoordinates(
            openSessionPayload.latitude,
            openSessionPayload.longitude,
          );
        }
        return;
      }

      try {
        const hasPermission = await requestLocationPermission();
        if (!hasPermission) {
          console.log('Location permission denied');
          return;
        }

        Geolocation.getCurrentPosition(
          position => {
            const {latitude, longitude} = position.coords;
            const newRegion = {
              latitude,
              longitude,
              latitudeDelta: 0.0922,
              longitudeDelta: 0.0421,
            };
            setRegion(newRegion);
            setMarkerPosition({latitude, longitude});
            fetchAddressFromCoordinates(latitude, longitude);
            setShouldFetchSuggestions(false);
          },
          error => {
            console.error('Geolocation error:', error);
          },
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 10000,
          },
        );
      } catch (error) {
        console.error('Location initialization error:', error);
      }
    };

    initializeLocation();
    return () => {
      Geolocation.stopObserving();
    };
  }, []);

  const handleAddMeetingPoint = async () => {
    if (!selectedLocation || !address.trim()) {
      showToast('error', t('locationMandatory'), 'bottom', isRTL);
      return;
    }
    setIsSaving(true);
    const {lat, lng, city, country} = selectedLocation;
    const payload = {
      full_address: address,
      latitude: lat,
      longitude: lng,
      city: city === null ? 'NA' : city,
      country: country === null ? 'NA' : country,
    };
    try {
      // await addMeetingPoint(payload).unwrap();
      openSessionPayload.address = payload.full_address;
      openSessionPayload.latitude = payload.latitude;
      openSessionPayload.longitude = payload.longitude;
      setIsSaving(false);
      navigation.goBack();
      setAddress('');
      setSelectedLocation(null);
      setMarkerPosition(null);
      setShouldFetchSuggestions(false);
    } catch (error) {
      setIsSaving(false);
      console.error('Error saving meeting point:', error);
      showToast('error', t('saveError'), 'bottom', isRTL);
    }
  };

  const handleDragEnd = async event => {
    const {latitude, longitude} = event.nativeEvent.coordinate;
    const newPosition = {latitude, longitude};
    setMarkerPosition(newPosition);
    setRegion({
      ...region,
      latitude,
      longitude,
    });
    mapRef.current?.animateToRegion(
      {
        latitude,
        longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      },
      1000,
    );
    await fetchAddressFromCoordinates(latitude, longitude);
    setShouldFetchSuggestions(false);
  };

  const renderSuggestion = ({item}) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}>
      <Text style={styles.suggestionText}>{item.description}</Text>
    </TouchableOpacity>
  );

  return (
    <KeyboardAvoidingView
      style={styles.keyboardAvoidingView}
      behavior="padding"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.container}>
          <StatusContainer color={'transparent'} />
          <AppHeader
            backIcon={icons.backbtn}
            isBackBtn
            title={t('meeting_point')}
            style={styles.transparentHeaderStyle}
            isHome={false}
            isWhite={true}
          />
          <ScrollView
            ref={scrollViewRef}
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled">
            <View style={styles.mapContainer}>
              <MapView
                ref={mapRef}
                style={styles.map}
                region={region}
                initialRegion={region}
                showsUserLocation>
                {markerPosition && (
                  <Marker
                    coordinate={markerPosition}
                    image={{
                      uri: `${IMAGE_BASE_URL}document/1734345316053-TaleemMapPin.png`,
                    }}
                    draggable
                    onDragEnd={handleDragEnd}
                  />
                )}
              </MapView>
            </View>

            <View style={styles.bottomContainer}>
              <Text style={styles.meetingPointTitle}>
                {t('yourMeetingPoint')}
              </Text>
              <View style={styles.addressContainer}>
                <TextInput
                  ref={inputRef}
                  style={styles.largeTextInput}
                  placeholder={t('enterAddress')}
                  placeholderTextColor={colors.txtGrey3}
                  value={address}
                  onChangeText={text => {
                    setAddress(text);
                    setShouldFetchSuggestions(true);
                  }}
                  autoCorrect={false}
                  autoCapitalize="none"
                  onFocus={() => {
                    setShouldFetchSuggestions(true);
                    scrollViewRef.current?.scrollTo({
                      y: hp(60),
                      animated: true,
                    });
                  }}
                />
                {isLoadingSuggestions && (
                  <ActivityIndicator
                    style={styles.loadingIndicator}
                    size="small"
                    color={colors.themeColor}
                  />
                )}
              </View>

              <TouchableOpacity
                style={[
                  styles.locationButton,
                  {
                    flexDirection: isRTL ? 'row-reverse' : 'row',
                    alignSelf: isRTL ? 'flex-end' : 'flex-start',
                  },
                ]}
                onPress={handleCurrentLocation}
                disabled={isLoadingCurrentLocation}>
                {isLoadingCurrentLocation ? (
                  <ActivityIndicator size="small" color={colors.themeColor} />
                ) : (
                  <>
                    <Text style={styles.locationButtonText}>
                      {t('useCurrentLocation')}
                    </Text>
                    <Image
                      source={icons.MyLocation}
                      resizeMode="contain"
                      style={styles.locationIcon}
                    />
                  </>
                )}
              </TouchableOpacity>

              {suggestions.length > 0 && (
                <FlatList
                  data={suggestions}
                  keyExtractor={item => item.place_id}
                  renderItem={renderSuggestion}
                  style={styles.suggestionsList}
                  keyboardShouldPersistTaps="handled"
                />
              )}
              <PrimaryButton
                title={t('done')}
                onPress={handleAddMeetingPoint}
                style={styles.saveButton}
                disabled={isSaving || !selectedLocation}>
                {isSaving ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <Text style={styles.saveButtonText}>{t('done')}</Text>
                )}
              </PrimaryButton>
            </View>
          </ScrollView>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

// Styles remain unchanged
const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: hp(10),
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  mapContainer: {
    height: hp(62),
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  transparentHeaderStyle: {
    marginTop: Platform.OS === 'ios' ? '12%' : 0,
    backgroundColor: 'transparent',
  },
  bottomContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 20,
    marginTop: hp(2),
    marginHorizontal: wp(2),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  meetingPointTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black,
    marginBottom: 10,
    fontFamily: Fonts.medium,
  },
  largeTextInput: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
    color: colors.txtGrey3,
    height: 50,
    flex: 1,
    fontFamily: Fonts.medium,
  },
  suggestionsList: {
    backgroundColor: colors.white,
    borderColor: colors.txtGray1,
    borderWidth: 0.3,
    borderRadius: 8,
    maxHeight: 250,
    marginTop: 5,
    marginBottom: 10,
  },
  suggestionItem: {
    padding: 12,
    borderBottomWidth: 0.2,
    borderBottomColor: colors.txtGray1,
  },
  suggestionText: {
    fontSize: 14,
    color: colors.blackSkatch,
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  saveButton: {
    marginTop: 10,
    width: '100%',
    backgroundColor: colors.themeColor,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: Fonts.bold,
  },
  addressContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingIndicator: {
    position: 'absolute',
    right: 15,
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  locationButtonText: {
    fontSize: 14,
    color: colors.themeColor,
    fontFamily: Fonts.medium,
    marginRight: 5,
  },
  locationIcon: {
    width: 20,
    height: 20,
    tintColor: colors.themeColor,
  },
});

export default SelectMeetingPointTutorForOpenSession;
