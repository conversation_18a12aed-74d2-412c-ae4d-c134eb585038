import React, {useEffect} from 'react';
import MyLottieComponent from '../../Components/splashAnimation';
import {changeLang} from '../../Components/CommonFunction';
import {useSelector} from 'react-redux';

const SPLASH_DELAY = 5000; // Splash screen duration in milliseconds

const Splash = ({navigation}) => {
  const selectedLang = useSelector(state => state?.auth?.appLocale);

  useEffect(() => {
    const handleNavigation = async () => {
      // const selectedLang = await changeLang();
      console.log('🚀 ~ handleNavigation ~ selectedLang:', selectedLang);
      const targetScreen =
        selectedLang != null ? 'WelcomeScreen' : 'SelectLanguage';

      setTimeout(() => {
        // Notify parent component to stop showing the splash
        // onFinish(targetScreen);
        navigation.navigate(targetScreen);
      }, SPLASH_DELAY);
    };

    handleNavigation();
  }, [navigation, selectedLang]);

  return <MyLottieComponent />;
};

export default Splash;
