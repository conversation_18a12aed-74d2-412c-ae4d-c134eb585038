import React, {useState, useEffect, useCallback} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Modal,
  FlatList,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import TutorDetailsCard from '../../Components/Custom_Components/TutorDetailsCard';
import {PrimaryButton} from '../../Components/CustomButton';
import SmallFeturesButtonNonPrassable from '../../Components/Custom_Components/SmallFeturesButtonNonPrassable';
import styles from './styles';
import {StatusContainer} from '../../Components/StatusBar';
import ReviewCard from '../../Components/ReviewCard';
import {HeaderTutorDetail} from '../../Components/HeaderTutorDetail';
import {
  useGetTutorReviewsQuery,
  useGetTutorsDetailAcademicQuery,
} from '../../Api/ApiSlice';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {useDispatch, useSelector} from 'react-redux';
import {
  updateClassType,
  updateGroupStudents,
  updateMeetingTutorPoint,
  updatePackage,
  updateSelectedRateCardId,
  updateSlot,
  updateTutorData,
} from '../../Redux/Slices/Student/TutorBookingSlice';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {
  resetBookingSchedules,
  resetSelectedSlots,
  updateSelectedSlots,
} from '../../Redux/Slices/Student/SlotSlice';
import LinearGradient from 'react-native-linear-gradient';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {
  resetTutorBookingJson,
  tutorBookingJson,
} from '../../Api/Model/TutorBookingModel';
import {Fonts} from '../../Utils/Fonts';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import LinearGradView from '../../Components/LinearGradView/LinearGradView';
import {useFocusEffect} from '@react-navigation/native';
import {showToast} from '../../Components/ToastHelper';
import {BackgroundSourceType} from 'react-native-agora';

const TutorsDetails = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const userType = useSelector(state => state.auth.user_type);
  const isRTL = i18n.language === 'ar';
  const [selectedTab, setSelectedTab] = useState('about');
  const {id} = route?.params || {};
  console.log('🚀 ~ TutorsDetails ~ id:', id);
  const dispatch = useDispatch();
  const bookingFlowType = useSelector(
    state => state?.tutorBookingSlice?.bookingFlowType,
  );
  console.log('🚀 ~ TutorsDetails ~ bookingFlowType:', bookingFlowType);
  useEffect(() => {
    dispatch(updateSelectedRateCardId(''));
  }, []);

  const [page, setPage] = useState(0); // Track the current page
  const [reviews, setReviews] = useState([]);
  const navigateToBookYourTutor = () => {
    if (selectedCard) {
      navigation.navigate('BookYourTutor', {rate_card_id: selectedCard});

      Object.assign(tutorBookingJson, resetTutorBookingJson());
      dispatch(updateSelectedRateCardId(selectedCard));
      dispatch(updateSlot([]));
      dispatch(updateGroupStudents([]));
      dispatch(updatePackage({}));
      dispatch(updateClassType({}));
      dispatch(resetSelectedSlots());
      dispatch(updateMeetingTutorPoint({}));
    } else {
      showToast('warning', t('please_select_subject'));
    }
  };
  const navigateToBookYourTutorRecreational = () => {
    navigation.navigate('BookYourTutor', {rate_card_id: id});
    dispatch(updateSelectedRateCardId(id));
    Object.assign(tutorBookingJson, resetTutorBookingJson());
    dispatch(updateSlot([]));
    dispatch(updateGroupStudents([]));
    dispatch(updatePackage({}));
    dispatch(updateClassType({}));
    dispatch(resetSelectedSlots());
    dispatch(updateMeetingTutorPoint({}));
  };
  const [selectedCard, setSelectedCard] = useState(null);

  // Fetch tutor details from API
  console.log('🚀 ~ TutorsDetails ~ id:', id);
  const {
    data: tutorData,
    error,
    isLoading,
    refetch,
  } = useGetTutorsDetailAcademicQuery({id, bookingFlowType});

  const refetchDataOnFocus = useCallback(() => {
    refetch();
  }, [refetch]);

  useFocusEffect(refetchDataOnFocus);

  useEffect(() => {
    console.log('Tutor API Data:', JSON.stringify(tutorData));
    console.log('Loading Status:', isLoading);
    console.log('Error:', error);
    if (tutorData) {
      console.log('tutor id dispatched:', id);
      dispatch(updateTutorData(tutorData?.data));
    }
  }, [tutorData, isLoading, error, dispatch]);
  // useFocusEffect(dispatch(updateSlot({})));
  const handlePress = tab => setSelectedTab(tab);

  const tutorProfile = tutorData?.data?.profileData || {};
  const imageUrl = tutorProfile?.image
    ? {uri: `${IMAGE_BASE_URL}${tutorProfile?.image}`}
    : {uri: DUMMY_USER_IMG};

  const {
    data: tutorReviews,
    error: reviewError,
    isLoading: reviewLoading,
    isFetching,
    refetch: refetchReviews,
  } = useGetTutorReviewsQuery({
    id: id,
    page,
    limit: 10,
  });
  console.log(
    '🚀 ~ TutorsDetails ~ tutorProfile?.tlm_user?.id:',
    tutorProfile?.tlm_user?.id,
  );
  console.log('🚀 ~ TutorsDetails ~ tutorReviews:', tutorReviews);
  useEffect(() => {
    refetchReviews();
  }, []);
  useEffect(() => {
    if (tutorReviews?.data?.rows) {
      console.log(
        '🚀 ~ useEffect ~ tutorReviews?.data?.rows:',
        tutorReviews?.data?.rows,
      );
      setReviews(prev => [...prev, ...tutorReviews?.data?.rows]);
    }
  }, [tutorReviews]);

  // Function to fetch next page
  const loadMoreReviews = () => {
    if (!isFetching && tutorReviews?.data?.rows?.length > 0) {
      setPage(prevPage => prevPage + 1);
    }
  };

  // if (isLoading) {
  //   return (
  //     <SafeAreaView
  //       style={[styles.main, {justifyContent: 'center', alignItems: 'center'}]}>
  //       <TaleemLoader isLoading={isLoading} />
  //       {/* <Text style={{color: colors.themeColor, fontSize: 16}}>
  //         {t('Loading Tutor Details')}
  //       </Text> */}
  //     </SafeAreaView>
  //   );
  // }

  const HeadingSubheadingView = ({heading, value}) => {
    return (
      <View
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          justifyContent: 'space-between',
          marginBottom: hp(1),
        }}>
        <Text
          style={{
            fontFamily: Fonts.medium,
            textAlign: 'left',
            fontSize: heading == 'Subject' ? fp(1.6) : fp(1.4),
          }}>
          {heading}
        </Text>
        <Text
          style={{
            fontFamily: Fonts.semiBold,
            // width: wp(40),
            textAlign: 'right',
            maxWidth: wp(40),
            lineHeight: hp(2),
            fontSize: heading == 'Subject' ? fp(1.6) : fp(1.4),
          }}>
          {value}
        </Text>
      </View>
    );
  };
  const genderMap = {1: t('male'), 2: t('female')};

  return (
    <SafeAreaView style={styles.main}>
      <StatusContainer color={colors.themeColor} />
      <View style={styles.headerContainer}>
        <HeaderTutorDetail
          backIcon={isRTL ? icons.rightArrowLarge : icons.backbtn}
          isBackBtn
          title={t('tutorDetails')}
          share={true}
          isRTL={isRTL}
        />

        <View style={{marginLeft: 10}}>
          <TutorDetailsCard
            name={capitalizeFirstLetter(tutorProfile?.name) || t('name')}
            subject={
              tutorProfile?.tlm_user?.tlm_tutor_expertises?.map(
                item => item?.tlm_expertise?.name,
              ) || t('subject')
            }
            rating={tutorProfile?.ratings}
            location={tutorProfile?.address || ''}
            imageUrl={imageUrl}
            isTopTutor={false}
            experience={tutorProfile?.experience || t('experience')}
            nationality={tutorProfile?.nationality}
            occupation={tutorProfile?.occupation}
          />
        </View>
      </View>

      {/* Tab Buttons */}
      <View
        style={[
          styles.buttonGroup,
          {
            flexDirection: isRTL ? 'row-reverse' : 'row',
            // justifyContent: isRTL ? 'flex-end' : 'flex-start',
          },
        ]}>
        {['about', 'reviews'].map(tab => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.button,
              selectedTab === tab
                ? {
                    backgroundColor: colors.themeColor,
                    borderColor: colors.themeColor,
                  }
                : {backgroundColor: 'white', borderColor: '#ccc'},
            ]}
            onPress={() => handlePress(tab)}>
            <Text
              style={[
                styles.buttonText,
                selectedTab === tab ? {color: 'white'} : {color: 'black'},
              ]}>
              {t(tab)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Conditionally Rendered Sections */}
      {selectedTab === 'about' && (
        <ScrollView contentContainerStyle={styles.contentContainer}>
          <LinearGradient
            colors={['#C6FFC9', '#D4EBFF']}
            start={{
              x: Math.sin((0 * Math.PI) / 180),
              y: -Math.cos((100 * Math.PI) / 180),
            }}
            end={{
              x: Math.sin((50 * Math.PI) / 180),
              y: -Math.cos((200 * Math.PI) / 180),
            }}
            style={{
              // height: 150,
              // width: 200,
              borderRadius: 20,
              // marginVertical: hp(1),
              marginBottom: hp(1.6),
            }}>
            <View
              style={{
                borderRadius: 18,
                flex: 1,
                margin: 1.4,
                backgroundColor: '#fff',
                justifyContent: 'center',
              }}>
              <View style={styles.bio}>
                <Text
                  style={[styles.title, {textAlign: isRTL ? 'right' : 'left'}]}>
                  {t('bio')}
                </Text>
                <Text
                  style={[
                    styles.subTitle,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {tutorProfile?.bio || 'N/A'}
                </Text>
              </View>
            </View>
          </LinearGradient>

          {/* <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('grades')}</Text>
            <View
              style={[
                styles.buttonContainer,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {tutorProfile?.tlm_tutor_grades?.map(grade => (
                <SmallFeturesButtonNonPrassable
                  key={grade?.id}
                  style={styles.gradesButton}
                  title={
                    isRTL ? grade?.tlm_grade?.name_ar : grade?.tlm_grade?.name
                  }
                  textStyle={styles.gradesButtonText}
                  highlighted={true}
                  isRTL={isRTL}
                />
              ))}
            </View>
          </View>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('expertise_in')}</Text>
            <View
              style={[
                styles.buttonContainer,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {tutorProfile?.tlm_tutor_expertises?.map(exp => (
                <SmallFeturesButtonNonPrassable
                  key={exp}
                  style={styles.expertiseButton}
                  title={
                    isRTL
                      ? exp?.tlm_expertise?.name_ar
                      : exp?.tlm_expertise?.name
                  }
                  textStyle={styles.expertiseButtonText}
                />
              ))}
            </View>
          </View> */}
          <View style={styles.section}>
            <Text
              style={[
                styles.sectionTitle,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {t('gender')}
            </Text>
            <View
              style={[
                styles.buttonContainer,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {/* {tutorProfile?.tlm_tutor_rate_card_academics?.tlm_tutor_class_types?.map(
                session => (
                  <SmallFeturesButtonNonPrassable
                    key={session}
                    style={styles.availabilityButton}
                    title={
                      isRTL
                        ? session?.tlm_class_type?.name_ar
                        : session?.tlm_class_type?.name
                    }
                    textStyle={styles.availabilityButtonText}
                  />
                ),
              )} */}

              <SmallFeturesButtonNonPrassable
                key={tutorProfile?.gender}
                style={styles.availabilityButton}
                title={genderMap[tutorProfile?.gender]}
                textStyle={styles.availabilityButtonText}
              />
            </View>
          </View>
          <View style={styles.section}>
            <Text
              style={[
                styles.sectionTitle,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {t('experience')}
            </Text>
            <View
              style={[
                styles.buttonContainer,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {/* {tutorProfile?.tlm_tutor_rate_card_academics?.tlm_tutor_class_types?.map(
                session => (
                  <SmallFeturesButtonNonPrassable
                    key={session}
                    style={styles.availabilityButton}
                    title={
                      isRTL
                        ? session?.tlm_class_type?.name_ar
                        : session?.tlm_class_type?.name
                    }
                    textStyle={styles.availabilityButtonText}
                  />
                ),
              )} */}
              {tutorProfile?.experience && (
                <SmallFeturesButtonNonPrassable
                  key={tutorProfile?.experience}
                  style={styles.availabilityButton}
                  title={
                    tutorProfile?.experience > 1
                      ? `${tutorProfile?.experience} ${t('years')}`
                      : `${tutorProfile?.experience} ${t('year')}` || 0
                  }
                  textStyle={styles.availabilityButtonText}
                />
              )}
            </View>
          </View>
          {bookingFlowType == 'academic' && (
            <>
              <View style={styles.section}>
                <Text
                  style={[
                    styles.sectionTitle,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {t('select_subject')}
                </Text>
                <View
                  style={[
                    styles.buttonContainer,
                    {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  ]}>
                  {tutorProfile?.tlm_tutor_rate_card_academics?.map(exp => {
                    let classTypes = exp?.tlm_tutor_class_types?.map(
                      item => item?.tlm_class_type?.name,
                    );
                    const onlineClass = exp?.tlm_tutor_class_types.find(
                      item =>
                        item?.tlm_class_type?.value?.toLowerCase() === 'online',
                    );
                    const F2fClass = exp?.tlm_tutor_class_types.find(
                      item => item?.tlm_class_type?.value === 'faceToFace',
                    );

                    return (
                      <TouchableOpacity
                        onPress={() => setSelectedCard(exp.id)} // Select the card on press
                        style={
                          [
                            // styles.cardContainer,
                            // selectedCard === exp.id && styles.selectedCard, // Apply selected style
                          ]
                        }>
                        {/* <LinearGradView> */}
                        {/* <View
                          style={applyShadowStyleIos({
                            // height: hp(10),
                            width: wp(90),
                            justifyContent: 'center',
                            paddingHorizontal: hp(2),
                            paddingVertical: hp(2),
                            backgroundColor:
                              selectedCard == exp.id
                                ? colors.themeColorDimmed
                                : colors.white,
                            borderColor: colors.themeColor,
                            borderWidth: 0.2,
                            borderRadius: fp(1.2),
                            marginBottom: hp(1),
                          })}> */}
                        <View
                          style={{
                            width: wp(90),
                            backgroundColor:
                              selectedCard == exp.id
                                ? colors.themeColorDimmed
                                : colors.white,
                            marginBottom: hp(1),
                            borderColor: colors.themeBackgroundTwo,
                            borderWidth: fp(0.1),
                            borderRadius: fp(2),
                            padding: hp(2),
                          }}>
                          <HeadingSubheadingView
                            heading={t('subject')}
                            value={exp.tlm_subject?.name}
                          />
                          <HeadingSubheadingView
                            heading={t('grade')}
                            value={exp.tlm_grade?.name}
                          />
                          {exp.tlm_curriculum?.name && (
                            <HeadingSubheadingView
                              heading={t('curriculum')}
                              value={exp.tlm_curriculum?.name}
                            />
                          )}

                          <HeadingSubheadingView
                            heading={t('class_type')}
                            value={classTypes?.join(' • ')}
                          />
                          {onlineClass?.price && (
                            <HeadingSubheadingView
                              heading={t('price_for_online')}
                              value={`${onlineClass?.price} QAR`}
                            />
                          )}

                          {F2fClass?.price && (
                            <HeadingSubheadingView
                              heading={t('price_for_face_to_face')}
                              value={`${F2fClass?.price} QAR`}
                            />
                          )}

                          {/* <Text style={{fontFamily: Fonts.medium}}>
                    {exp?.tlm_grade?.name}
                  </Text>
                  <Text style={{fontFamily: Fonts.medium}}>
                    {exp?.tlm_curriculum?.name}
                  </Text> */}
                          {/* {exp?.tlm_tutor_class_types.map(item => {
                    return (
                      <>
                        <Text>{item?.tlm_class_type?.name}</Text>
                        <Text>{item?.price}</Text>
                      </>
                    );
                  })} */}
                        </View>
                        {/* </LinearGradView> */}
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>
            </>
          )}
        </ScrollView>
      )}

      {selectedTab === 'reviews' && (
        <FlatList
          data={reviews}
          keyExtractor={(item, index) => index.toString()}
          onEndReached={loadMoreReviews}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={
            <Text style={{fontFamily: Fonts.medium, alignSelf: 'center'}}>
              {t('noReviews')}
            </Text>
          }
          ListFooterComponent={
            isFetching ? <ActivityIndicator size="small" /> : null
          }
          renderItem={({item, index}) => {
            return (
              <ReviewCard
                key={index}
                name={item.Sender?.name || t('name')}
                date={
                  new Date(item.createdAt).toLocaleDateString() ||
                  t('date_review')
                }
                rating={item.rating || '0'}
                reviewText={item.review || t('reviewText')}
              />
            );
          }}
        />
      )}
      {/* {userType != '2' && ( */}
      <PrimaryButton
        onPress={
          bookingFlowType == 'academic'
            ? navigateToBookYourTutor
            : navigateToBookYourTutorRecreational
        }
        title={t('book_tutor')}
        style={{
          backgroundColor: colors.themeColor,
        }}
        textStyle={{fontSize: 16, color: colors.white}}
      />
      {/* )} */}

      <Modal
        transparent
        visible={isLoading}
        style={{alignItems: 'center', justifyContent: 'center'}}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator size={'large'} color={colors.themeBackground} />
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default TutorsDetails;
