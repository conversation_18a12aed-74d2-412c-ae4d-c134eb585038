import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: 'white',
    paddingBottom: fp(2),
  },
  headerContainer: {
    backgroundColor: colors.themeColor,
    paddingBottom: 20,
    paddingHorizontal: 1,
  },
  buttonGroup: {
    marginVertical: 20,
    marginHorizontal: 20,
  },
  button: {
    borderRadius: 20,
    paddingVertical: 7,
    paddingHorizontal: 15,
    marginHorizontal: 8,
    borderWidth: 1,
  },
  buttonText: {
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  bio: {
    // padding: 15,
    // // backgroundColor: colors.lightthemeColor,
    // borderRadius: 10,
    // marginBottom: 20,
    // backgroundColor: colors.themeColorDimmed,
    // borderColor: colors.themeColor,
    // borderWidth: 1,

    // borderRadius: ,
    borderRadius: 18,
    paddingVertical: 10,
    paddingHorizontal: 11,
    // margin: 5,
    backgroundColor: colors.themeColorDimmed,
    borderColor: colors.themeColor,
    // borderWidth: 1,
  },
  title: {
    fontSize: fp(1.7),
    color: colors.black,
    fontFamily: Fonts.semiBold,
    marginBottom: 7,
    lineHeight: hp(2.4),
  },
  subTitle: {
    fontSize: 14,
    color: colors.blackSkatch,
    fontFamily: Fonts.medium,
    lineHeight: hp(2.6),
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
    marginBottom: hp(1),
    marginLeft: wp(1.2),
  },
  buttonContainer: {
    flexWrap: 'wrap',
    gap: 2,
  },
  gradesButton: {
    borderRadius: 13,
    paddingVertical: 10,
    // paddingHorizontal: 11,
    margin: 5,
    // backgroundColor: colors.themeColorDimmed,
    // borderColor: colors.themeColor,
    // borderWidth: 1,
  },
  gradesButtonText: {
    fontSize: fp(1.8),
    color: colors.themeColor,
    fontFamily: Fonts.medium,
  },
  expertiseButton: {
    borderRadius: 13,
    paddingVertical: 10,
    // paddingHorizontal: 11,
    margin: 5,
    // backgroundColor: colors.themeColorDimmed,
    // borderColor: colors.themeColor,
    // borderWidth: 1,
  },
  expertiseButtonText: {
    fontSize: 14,
    color: colors.themeColor,
    fontFamily: Fonts.medium,
  },
  availabilityButton: {
    // borderRadius: 13,
    // paddingVertical: 10,
    // paddingHorizontal: 11,
    // margin: 5,
    // backgroundColor: colors.themeColorDimmed,
    // borderColor: colors.themeColor,
    // borderWidth: 1,
  },
  availabilityButtonText: {
    fontSize: 14,
    color: colors.themeColor,
    fontFamily: Fonts.medium,
  },
  bookButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 15,
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  bookButtonText: {
    fontSize: 18,
    color: 'white',
    fontFamily: Fonts.medium,
  },
});

export default styles;
