import {Dimensions, StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';
import {hp} from '../../Helper/ResponsiveDimensions';

const deviceWidth = Dimensions.get('window').width;
const deviceHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  inputStyle: {
    borderWidth: 1,
    borderColor: colors.lightisGrey,
    borderRadius: 12,
    width: 70,
    height: 50,
    textAlign: 'center',
    fontSize: responsiveFontSize(15),
    color: colors.black,
    fontFamily: Fonts.regular,
  },

  button: {
    width: deviceWidth * 0.95,
    marginBottom: 10,
  },
  titletext: {
    // marginTop: hp(2),
  },
  inputContainer: {
    width: '90%',
    alignSelf: 'center',

    justifyContent: 'space-between',
    marginTop: 30,
  },
  timerView: {
    marginVertical: 30,
    width: '90%',
    alignSelf: 'center',

    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timerTxt: {
    fontSize: responsiveFontSize(16),
    fontFamily: Fonts.medium,
    marginTop: 1,
    color: colors.black,
  },
});

export default styles;
