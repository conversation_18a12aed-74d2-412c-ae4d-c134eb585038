import AsyncStorage from '@react-native-async-storage/async-storage';
import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  I18nManager,
  Image,
  SafeAreaView,
  Text,
  TextInput,
  View,
} from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import {useDispatch} from 'react-redux';
import {useVerifyOtpMutation} from '../../Api/ApiSlice';
import {PrimaryButton} from '../../Components/CustomButton';
import {AppHeader} from '../../Components/Header';
import {TextLink} from '../../Components/Rest';
import {SubTitle, Title} from '../../Components/Title';
import {showToast} from '../../Components/ToastHelper';
import {setAuthData, setIsLoggedIn} from '../../Features/authSlice';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import styles from './styles';
import {WAS_KILLED} from '../../Utils/storageKeys';
import {isPending, isRejected} from '@reduxjs/toolkit';
import {ApiProvider} from '@reduxjs/toolkit/query/react';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import LottieView from 'lottie-react-native';
import {OtpInput} from 'react-native-otp-entry';
import {AudioFileRecordingType} from 'react-native-agora';

const OtpVerificationScreen = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dispatch = useDispatch();
  const numberOfInputs = 4;
  const [otp, setOtp] = useState(Array(numberOfInputs).fill(''));
  const inputRefs = useRef([]);
  const refRBSheet = useRef();
  const [timer, setTimer] = useState(30);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const intervalRef = useRef(null);
  const animationRef = useRef(null);
  const [langType, setLangType] = useState('');

  const [userType, setUserType] = useState(null);

  const [verifyOtp, {isLoading}] = useVerifyOtpMutation();

  const countryCode =
    route?.params?.userData?.country_code || 'default_country_code';
  const mobileNumber = route?.params?.userData?.mobile_no;
  const token = route?.params?.token;
  const action = route?.params?.action;

  useEffect(() => {
    animationRef.current?.play();

    // Or set a specific startFrame and endFrame with:
    animationRef.current?.play(30, 120);
  }, []);

  useEffect(() => {
    startTimer();
    return () => clearInterval(intervalRef.current);
  }, []);

  const getLangType = async () => {
    const storedLangType = await AsyncStorage.getItem('selectedLang');
    return storedLangType ? JSON.parse(storedLangType) : '';
  };

  useEffect(() => {
    const fetchLangType = async () => {
      const type = await getLangType();
      setLangType(type);
    };
    fetchLangType();
  }, []);

  const getUserType = async () => {
    const storedUserType = await AsyncStorage.getItem('userType');
    return storedUserType ? JSON.parse(storedUserType) : null;
  };

  useEffect(() => {
    const fetchUserType = async () => {
      const type = await getUserType();
      setUserType(type);
    };
    fetchUserType();
  }, []);

  const startTimer = () => {
    setTimer(30);
    setIsTimerActive(true);
    clearInterval(intervalRef.current);

    intervalRef.current = setInterval(() => {
      setTimer(prevTimer => {
        if (prevTimer <= 1) {
          clearInterval(intervalRef.current);
          setIsTimerActive(false);
          return 0;
        }
        return prevTimer - 1;
      });
    }, 1000);
  };

  const handleVerifyOtp = async () => {
    console.log('verifyOtp Func', otp);
    // const finalOTP = otp.join('');
    console.log({
      otp: otp,
      field_value: mobileNumber,
      country_code: countryCode,
      token: token,
      action: action,
      language: langType,
    });
    // if (validateOtp()) {
    try {
      const response = await verifyOtp({
        otp: otp,
        field_value: mobileNumber,
        country_code: countryCode,
        token: token,
        action: action,
        language: langType,
      }).unwrap();

      console.log('response for OTP', response);
      if (response.status === true) {
        const userData = {
          token: response.data.token,
          userId: response.data.id,
          user_type: response.data.user_type,
          action_type: response.data.action,
          profile_image: null,
          profile_accounts: response?.data?.profile_accounts,
        };
        console.log('🚀 ~ setTimeout ~ userData:', userData);
        await AsyncStorage.setItem('user', JSON.stringify(userData));

        showToast('success', response.message, 'bottom', isRTL);
        refRBSheet.current.open();

        setTimeout(() => {
          // if (action == 'social') {
          //   handleSocialLogin();
          // }
          if (userData.action_type === 'signup' && userData.user_type === '3') {
            console.log('sign up userType 3');
            refRBSheet.current.close();
            navigation.navigate('CompleteYourProfilePageOne');
            dispatch(setAuthData(userData));
          }
          // else if (
          //   userData.action_type === 'login' &&
          //   userData.user_type === '3'
          // ) {
          //   refRBSheet.current.close();
          //   console.log('login userType 3');
          //   dispatch(setAuthData(userData));
          //   dispatch(setIsLoggedIn(true));

          // navigation.navigate('TabTutorScreen')
          // navigation.reset({
          //   index: 0,
          //   routes: [{name: 'TabTutorScreen'}],
          // });
          // }
          else if (
            userData.action_type === 'login' &&
            // userData.user_type === '1' &&
            response?.data?.profile_accounts > 1
          ) {
            //dont mark IsLogged in as true
            //will do this in choose Profile screeen.,
            refRBSheet.current.close();
            navigation?.navigate('ChooseProfile');
            dispatch(setAuthData(userData));
          } else {
            refRBSheet.current.close();
            console.log('last else in every case');
            dispatch(setAuthData(userData));
            dispatch(setIsLoggedIn(true));
            // navigation.reset({
            //   index: 0,
            //   routes: [{name: 'TabScreen'}],
            // });
          }
        }, 2000);
      } else {
        showToast(
          'error',
          response.message || t('otpVarificationFailed'),
          'bottom',
          isRTL,
        );
        console.log('error in elseBlock', response.message, 'bottom', isRTL);
      }
    } catch (error) {
      showToast('error', t('otpVarificationFailed'), 'bottom', isRTL);
      console.log('error in catch', error);
    }
    // }
  };

  const handleResend = () => {
    if (isTimerActive) {
      showToast('error', t('wait_for_resend'), 'bottom', isRTL);
    } else {
      startTimer();
      showToast('success', t('otp_resent_successfully'), 'bottom', isRTL);
    }
  };

  const handleOtpChange = (value, index) => {
    const newOtp = [...otp];

    setOtp(newOtp);

    if (value.length === 1 && index < numberOfInputs - 1) {
      inputRefs.current[index + 1].focus();
    } else if (value.length === 0 && index > 0) {
      inputRefs.current[index - 1].focus();
    }
  };

  // const handleSocialLogin = async () => {
  //   const {socialLoginBody} = route?.params;

  //   try {
  //     const response = await socialSignin(socialLoginBody).unwrap();
  //     console.log('🚀 ~ handleSocialLogin ~ response:', response);
  //     if (response?.status) {
  //       if (socialLoginBody.user_type === '3') {
  //         console.log('sign up userType 3');
  //         refRBSheet.current.close();
  //         navigation.navigate('CompleteYourProfilePageOne');
  //         dispatch(setAuthData(socialLoginBody));
  //       } else {
  //         refRBSheet.current.close();
  //         console.log('last else in every case');
  //         dispatch(setAuthData(socialLoginBody));
  //         dispatch(setIsLoggedIn(true));
  //         // navigation.reset({
  //         //   index: 0,
  //         //   routes: [{name: 'TabScreen'}],
  //         // });
  //       }
  //     }
  //   } catch (error) {
  //     console.log('error in signUp Api---', error);

  //     showToast(
  //       'error',
  //       error.data?.message || 'Registration failed',
  //       'bottom',
  //       isRTL,
  //     ); //
  //   }
  // };

  // const renderInputs = () => {
  //   return otp.map((value, index) => (
  //     <TextInput
  //       key={index}
  //       value={value}
  //       ref={ref => (inputRefs.current[index] = ref)}
  //       onChangeText={text => handleOtpChange(text, index)}
  //       style={styles.inputStyle}
  //       keyboardType="numeric"
  //       maxLength={1}
  //       autoFocus={index === 0}
  //       textAlign={isRTL ? 'center' : 'center'}
  //     />
  //   ));
  // };
  const renderInputs = () => {
    return (
      <OtpInput
        numberOfDigits={4}
        focusColor={colors.themeBackground}
        autoFocus={true}
        hideStick={false}
        // placeholder="******"
        blurOnFilled={true}
        disabled={false}
        type="numeric"
        secureTextEntry={false}
        focusStickBlinkingDuration={500}
        onFocus={() => console.log('Focused')}
        onBlur={() => console.log('Blurred')}
        // onTextChange={text => handleOtpChange(text, index)}
        onFilled={text => setOtp(text)}
        textInputProps={{
          accessibilityLabel: 'One-Time Password',
        }}
        theme={{
          containerStyle: {flexDirection: isRTL ? 'row-reverse' : 'row'},
          pinCodeContainerStyle: {width: wp(20)},
          pinCodeTextStyle: {
            fontFamily: Fonts.medium,
            textAlign: isRTL ? 'right' : 'left',
          },
          focusStickStyle: {
            borderColor: colors.themeBackground,
            textAlign: isRTL ? 'right' : 'left',
          },
          focusedPinCodeContainerStyle: {
            borderColor: colors.themeBackground,
            textAlign: isRTL ? 'right' : 'left',
          },
          placeholderTextStyle: {
            alignSelf: 'center',
            textAlignVertical: 'center',
            textAlign: isRTL ? 'right' : 'left',
          },
          filledPinCodeContainerStyle: {
            textAlign: isRTL ? 'right' : 'left',
          },
          disabledPinCodeContainerStyle: {
            textAlign: isRTL ? 'right' : 'left',
          },
        }}
      />
    );
  };

  const validateOtp = () => {
    const finalOTP = otp.join('');
    if (otp.some(val => val === '')) {
      showToast('error', t('missing_otp'), 'bottom', isRTL);
      return false;
    }
    if (finalOTP.length < 4) {
      showToast('error', t('invalid_otp'), 'bottom', isRTL);
      return false;
    }
    return true;
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        backIcon={icons.backIcon}
        isBackBtn
        title={''}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />
      <Title
        text={t('enter_otp')}
        style={{
          textAlign: isRTL ? 'right' : 'left',
          fontFamily: Fonts.bold,
          fontSize: fp(2.8),
          paddingVertical: hp(1),
        }}
      />
      <SubTitle
        text={t('otp_sent_message')}
        style={{
          textAlign: isRTL ? 'right' : 'left',
          marginRight: isRTL ? 20 : 0,
        }}
      />

      <View
        style={[
          styles.inputContainer,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        {renderInputs()}
      </View>

      <View
        style={[
          styles.timerView,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <Text style={styles.timerTxt}>
          00:{timer < 10 ? `0${timer}` : timer}
        </Text>

        <TextLink
          onPress={handleResend}
          disabled={isTimerActive}
          style={{
            marginTop: 1,
            textAlign: I18nManager.isRTL ? 'right' : 'left',
          }}
          title={t('didnt_receive')}
          linkText={t('resend')}
          linkTextStyle={[
            isTimerActive
              ? {color: colors.offGrey}
              : {color: colors.themeColor},
          ]}
        />
      </View>

      <PrimaryButton
        onPress={handleVerifyOtp}
        title={t('verify_otp')}
        loading={isLoading}
        style={{alignSelf: I18nManager.isRTL ? 'center' : 'center'}}
      />

      <RBSheet
        ref={refRBSheet}
        height={200}
        openDuration={250}
        customStyles={{
          container: {
            padding: 20,
            borderTopLeftRadius: fp(3),
            borderTopRightRadius: fp(3),
            bottom: 0,
          },
        }}>
        <View>
          <LottieView
            ref={animationRef}
            source={require('../../Assets/Lottie/otpSuccessLottie.json')}
            style={{alignSelf: 'center', height: fp(10), width: fp(10)}}
            autoPlay
          />
          <Text
            style={{
              fontSize: fp(1.8),
              color: colors.darkBlack,
              fontFamily: Fonts.semiBold,
              alignSelf: 'center',
              marginTop: hp(2),
            }}>
            {t('otp_successful')}
          </Text>
        </View>
      </RBSheet>
    </SafeAreaView>
  );
};

export default OtpVerificationScreen;

//tutor Status represents the profile approval status of tutor from admin.
// tutorProfileCompletionStep reprents the step of tutor profile journey

// 0 = Pending
// 1 = approved
// 2 = rejected
// 3 = submitted
