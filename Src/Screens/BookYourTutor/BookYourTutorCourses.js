import {
  Image,
  SafeAreaView,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Platform,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import BookYourTutorCard from '../../Components/Custom_Components/BookYourTutorCard';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import DateTimePicker from '@react-native-community/datetimepicker';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import styles from './styles';
import PackageSelector from './PackageSelector';
import {PrimaryButton} from '../../Components/CustomButton';
import {showToast} from '../../Components/ToastHelper';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {
  useBookTutorMutation,
  useGetTutorsDetailAcademicQuery,
  useLazyGetBookingPriceAcademicQuery,
  useLazyGetTutorRateCardDetailsStudentAcademicQuery,
} from '../../Api/ApiSlice';
import {useDispatch, useSelector} from 'react-redux';
import {
  updateCalculatedBookingAmount,
  updateClassType,
  updateInstructions,
  updatePackageStartDate,
  updateSessionType,
  updateTotalDurationSelectedSlots,
} from '../../Redux/Slices/Student/TutorBookingSlice';
import {CLASS_TYPES, DUMMY_USER_IMG, SESSION_TYPES} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import TaleemBadge from '../../Components/Custom_Components/TaleemBadge';
import CourseDetailsCard from '../../Components/Student/CourseDetailsCard/CourseDetailsCard';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {tutorBookingJson} from '../../Api/Model/TutorBookingModel';
import {
  resetSelectedSlots,
  updateCurrentDate,
} from '../../Redux/Slices/Student/SlotSlice';
import {ScheduleList} from './RenderScheduleHour';
import TaleemDatePicker from '../../Components/TaleemDatePicker/TaleemDatePicker';
import moment from 'moment';
import {convertToDMY} from '../../Helper/DateHelpers/DateHelpers';
import {
  calculateBookingPrice,
  calculateBookingPriceCourses,
} from '../../Helper/TutorBookingHelpers/PriceCalculationHelper/BookingPriceHelper';
import {useFocusEffect} from '@react-navigation/native';
import {transformToBookingSchedulesArray} from '../../Helper/TutorBookingHelpers';
import {ToggleButton} from '../../Components/toggleButton';

const BookYourTutorCourses = ({navigation}) => {
  const {courseRateCardId, bookingFlowType, meetingPoint} = useSelector(
    state => state?.tutorBookingSlice,
  );
  console.log(
    '🚀 ~ BookYourTutorCourses ~ courseRateCardId:',
    courseRateCardId,
  );

  const [
    getTutorRateCardDetailsStudentAcademic,
    {data: rateCardData, error, isLoading},
  ] = useLazyGetTutorRateCardDetailsStudentAcademicQuery();

  const [
    getBookingPriceAcademic,
    {data: bookingPrice, error: priceError, isLoading: bookingPriceLoading},
  ] = useLazyGetBookingPriceAcademicQuery();

  const class_types = rateCardData?.data?.tutorClassTypes;
  console.log(
    '🚀 ~ BookYourTutorCourses ~ rateCardData:',
    JSON.stringify(rateCardData),
  );
  const session_types = rateCardData?.data?.tlm_tutor_class_sessions;

  useEffect(() => {
    getTutorRateCardDetailsStudentAcademic({
      rate_card_id: courseRateCardId,
      bookingFlowType,
    });
  }, [courseRateCardId, tutorBookingJson?.address]);

  // Add this inside your component
  useFocusEffect(
    React.useCallback(() => {
      // This will run when the screen comes into focus (including when returning from MeetingPoint)
      if (tutorBookingJson?.address) {
        // Force re-render or refresh data as needed
        getTutorRateCardDetailsStudentAcademic({
          rate_card_id: courseRateCardId,
          bookingFlowType,
        });

        // If you need to update other state based on the address
        // e.g., recalculate price or update UI elements
      }

      return () => {
        // Optional cleanup
      };
    }, [tutorBookingJson?.address]),
  );
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [classType, setClassType] = useState('');
  const [commissionRate, setCommissionRate] = useState(0);

  const [sessionType, setSessionType] = useState('');

  const {
    slot,
    groupStudents,
    packageStartDate,
    package: packageFromRedux,
  } = useSelector(state => state.tutorBookingSlice);

  const [instructions, setInstructions] = useState('');
  const [price, setPrice] = useState(null); // Store the calculated price

  const dispatch = useDispatch();
  function onChangeInstructions(text: string): void {
    setInstructions(text);
    dispatch(updateInstructions(text));
  }
  const {selectedSlots, bookingSchedules} = useSelector(
    state => state.slotSlice,
  );

  // Function to validate required details
  const validateDetails = () => {
    if (!courseRateCardId) {
      showToast('error', t('tuttorDataMissing'), 'bottom', isRTL);
      return false;
    }
    if (!classType) {
      showToast('error', t('selectClassType'), 'bottom', isRTL);
      return false;
    }

    if (!slot || Object.keys(slot).length === 0) {
      showToast('error', t('selectAvailabeSlots'), 'bottom', isRTL);
      return false;
    }
    return true;
  };

  const getSelectedHours = () => {
    // Check if the slot array has data
    if (slot && Object.keys(slot).length > 0) {
      // Calculate the sum of hours_duration
      const totalHours = slot.reduce((sum, item) => {
        return sum + parseFloat(item.hours_duration); // Convert hours_duration to a number and add to sum
      }, 0); // Add "hour" or "hours" based on the total

      const totalHoursWithLabel = `${totalHours} ${
        totalHours === 1 ? 'hour' : 'hours'
      }`;
      dispatch(updateTotalDurationSelectedSlots(totalHoursWithLabel));
      return totalHoursWithLabel;
    } else {
      return 'No slots';
    }
  };

  const isClassTypeAvailable = value => {
    if (!class_types || !Array.isArray(class_types) || !value) {
      return false;
    }
    return class_types.some(
      item =>
        item?.tlm_class_type?.value?.toLowerCase() === value?.toLowerCase(),
    );
  };

  const getFullClassTypeData = value => {
    if (!class_types || !Array.isArray(class_types) || !value) {
      return null;
    }
    return class_types.find(
      item =>
        item?.tlm_class_type?.value?.toLowerCase() === value?.toLowerCase(),
    );
  };

  const isSessionTypeAvailable = value => {
    if (!classType?.tlm_class_type?.tlm_tutor_class_sessions || !value) {
      return false;
    }
    const sessions = classType.tlm_class_type.tlm_tutor_class_sessions;
    if (!Array.isArray(sessions)) {
      return false;
    }
    return sessions.some(
      session =>
        session?.tlm_sessions_type?.name?.toLowerCase() ===
        value?.toLowerCase(),
    );
  };

  const getFullSessionTypeData = value => {
    if (!classType?.tlm_class_type?.tlm_tutor_class_sessions || !value) {
      return null;
    }
    const sessions = classType.tlm_class_type.tlm_tutor_class_sessions;
    if (!Array.isArray(sessions)) {
      return null;
    }
    return sessions.find(
      session =>
        session?.tlm_sessions_type?.name?.toLowerCase() ===
        value?.toLowerCase(),
    );
  };

  const [bookTutor, {isLoading: isTutorBookingLoading}] =
    useBookTutorMutation();
  console.log('🚀 ~ handleBookTutorCourses ~ payload2.classType:', classType);

  // Function to calculate price
  const handleBookTutorCourses = () => {
    if (!validateDetails()) {
      return; // Stop if validation fails
    }

    const sortedData = transformToBookingSchedulesArray(selectedSlots).sort(
      (a, b) => new Date(a.date) - new Date(b.date),
    );

    if (sessionType == 'Group') {
      if (groupStudents?.length < rateCardData?.data?.min_student_no) {
        showToast(
          'error',
          t('youMustSelect', {count: rateCardData?.data?.min_student_no}),
          'bottom',
          isRTL,
        );
        return;
      }

      if (groupStudents?.length > rateCardData?.data?.max_student_no) {
        showToast(
          'error',
          t('selectStudentError', {
            count: rateCardData?.data?.max_student_no,
          }),
          'bottom',
          isRTL,
        );
        return;
      }
      if (paymentType == 'split') {
        setPrice(price / (groupStudents?.length + 1));
      } else {
        tutorBookingJson.amount = price;
      }
    } else {
      setPrice(price);
    }

    const payload2 = {
      class_type_id: classType?.class_type_id,
      class_type:
        classType?.tlm_class_type?.name == 'Online' ? 'online' : 'faceToFace',
      sessions_id: getFullSessionTypeData(sessionType)?.sessions_id,
      session: sessionType?.toLowerCase(),
      tutor_user_id: rateCardData?.data?.tlm_user?.id,
      package_start_date: sortedData[0]?.date,
      instructions: instructions,
      rate_card_type: '3',
      class_title: rateCardData?.data?.course_title,
      token: rateCardData?.data?.bookingTempToken,
      rate_card_id: courseRateCardId,
      participants: tutorBookingJson.participants,
      meeting_point_preferred: 'student',
      address: tutorBookingJson?.address,
      zone: 'ff',
      building: 'building',
      latitude: tutorBookingJson?.latitude,
      longitude: tutorBookingJson?.longitude,
      country: '',
      amount: price,
      booking_schedules: sortedData,
      payment_type: paymentType,
      tutor_earned_amount: tutorEarnedAmount,
      //   save_address: false,
      //   meeting_venue_id: 1,
      //   student_id: '654',
    };
    console.log('🚀 ~ handleBookTutorCourses ~ payload2:', payload2);
    // Call the API
    bookTutor(payload2)
      .unwrap()
      .then(response => {
        navigation.navigate('BookYourTutorConfirmation', {
          bookingId: response?.data?.booking_id,
        });
        console.log('🚀 ~ handleCalculatePrice ~ response:', response);
        // setPrice(response?.data?.bookingPrice); // Update the price in state
        // setCommissionRate(response?.data?.rate);
        // dispatch(updateCalculatedBookingAmount(response?.data?.bookingPrice));
        // showToast('success', t('calculatePrice'), 'bottom', isRTL);
      })

      .catch(err => {
        console.error('Error calculating price:', err);
        showToast('error', t('priceCalculationFailed'), 'bottom', isRTL);
      });
  };

  const [paymentType, setPaymentType] = useState('split'); // Store the calculated price
  const [tutorEarnedAmount, setTutorEarnedAmount] = useState(); // Store the calculated price

  useEffect(() => {
    // if (isRateCardFetchedSuccessfully && rateCardData) {

    const classPrice = classType?.commissionedPrice;
    const classPriceRaw = classType?.price;

    const price = calculateBookingPriceCourses(
      classPrice,
      getSelectedHours() || 1,
      sessionType == 'Group' ? groupStudents.length + 1 : 1,
    );
    console.log('🚀 ~ useEffect ~ classPriceRaw:', classPriceRaw);
    console.log('🚀 ~ useEffect ~ classPrice:', classPrice);
    setTutorEarnedAmount(
      calculateBookingPriceCourses(
        classPriceRaw,
        getSelectedHours() || 1,
        sessionType == 'Group' ? groupStudents.length + 1 : 1,
      ),
    );
    setPrice(price);

    // }
  }, [
    classType,
    sessionType,
    groupStudents,
    JSON.stringify(slot),
    paymentType,
  ]);
  console.log('🚀 ~ renderPrice ~ sessionType:', sessionType);
  const renderPrice = () => {
    if (sessionType == 'Group') {
      if (price) {
        if (paymentType == 'split' && groupStudents?.length) {
          return `${(price / (groupStudents.length + 1)).toFixed(2)} ${t(
            'currency',
          )}`;
        } else {
          return `${price?.toFixed(2)} ${t('currency')}`;
        }
      } else {
        return '----';
      }
    } else {
      return `${price?.toFixed(2)} ${t('currency')}` || 0;
    }
  };

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <View style={[styles.headerContainer]}>
        <AppHeader
          backIcon={icons.backbtn}
          isBackBtn
          title={t('book_tutor_title')}
          style={{zIndex: 20}}
          isHome={true}
        />
        <></>
      </View>

      <ScrollView contentContainerStyle={{flexGrow: 1, paddingBottom: 20}}>
        {/* Header Section */}
        <View style={{marginTop: hp(1), paddingHorizontal: wp(4)}}>
          {rateCardData && (
            <CourseDetailsCard courseData={rateCardData?.data} />
          )}
        </View>
        {/* Class Type Selection */}
        {/* Class Type Selection */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('select_class_type')}</Text>
          <View style={styles.row}>
            <View style={styles.row}>
              {CLASS_TYPES?.map((item, index) => {
                const isAvailable = isClassTypeAvailable(item?.value) || false;
                return (
                  <View key={item?.id}>
                    <SelectClassTypeCard
                      isSelected={
                        classType?.tlm_class_type?.value === item?.value
                      }
                      onPress={() => {
                        if (isAvailable) {
                          const fullClassTypeData = getFullClassTypeData(
                            item?.value,
                          );
                          setSessionType('');
                          setClassType(fullClassTypeData);
                          dispatch(updateClassType(fullClassTypeData));
                          dispatch(resetSelectedSlots());
                        }
                      }}
                      color={
                        classType?.tlm_class_type?.value === item?.value
                          ? colors.themeColor
                          : '#fff'
                      }
                      image={
                        item.value === 'online'
                          ? icons.onlineClassImage
                          : icons.offlineClassImage
                      }
                      title={t(
                        item.value === 'online' ? 'online' : 'face_to_face',
                      )}
                    />
                    {!isAvailable && (
                      <Text
                        style={{
                          fontSize: fp(1.2),
                          marginLeft: wp(4),
                          marginTop: hp(0.2),
                        }}>
                        {t('notAvailableClass')}
                      </Text>
                    )}
                  </View>
                );
              })}
            </View>
          </View>
        </View>

        {/* Session Selection */}
        {/* Session Selection */}
        <View style={styles.sectionContainer}>
          {classType !== '' && (
            <>
              <Text style={styles.sectionTitle}>{t('select_session')}</Text>

              <View
                style={[
                  styles.row,
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                ]}>
                {SESSION_TYPES?.map(session => {
                  const color =
                    sessionType?.toLowerCase() === session?.value
                      ? colors.themeColor
                      : '#fff';
                  const isAvailable = isSessionTypeAvailable(session?.value);
                  return (
                    <View style={{}}>
                      <TouchableOpacity
                        style={[
                          styles.sessionButton,
                          {
                            backgroundColor: color,
                            borderWidth:
                              sessionType?.toLowerCase() === session?.value
                                ? 0
                                : 1,
                            borderColor: colors.lightgreay,
                          },
                        ]}
                        onPress={() => {
                          if (isAvailable) {
                            const fullSessionTypeData = getFullSessionTypeData(
                              session.value,
                            );
                            setSessionType(
                              fullSessionTypeData.tlm_sessions_type.name,
                            );
                            dispatch(
                              updateSessionType(
                                fullSessionTypeData.tlm_sessions_type,
                              ),
                            );
                            dispatch(resetSelectedSlots());
                          }
                        }}>
                        <Text
                          style={[
                            styles.sessionButtonText,
                            {
                              color:
                                sessionType?.toLowerCase() === session?.value
                                  ? '#fff'
                                  : colors.txtGrey1,
                            },
                          ]}>
                          {t(session.value)}
                        </Text>
                      </TouchableOpacity>
                      {!isAvailable && (
                        <Text
                          style={{
                            fontSize: fp(1.2),
                            marginLeft: wp(2),
                            marginTop: hp(0.2),
                            color: colors.lightGrey,
                          }}>
                          {t('notAvailableSession')}
                        </Text>
                      )}
                    </View>
                  );
                })}
              </View>
            </>
          )}
        </View>

        {/* {select student} */}

        {sessionType === 'Group' && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>{t('select_students')}</Text>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('StudentList', {
                  maxStudent: rateCardData?.data?.max_student_no - 1,
                  minStudent: rateCardData?.data?.min_student_no - 1,
                });
              }}
              style={[
                styles.MeetingPoint,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Text
                style={[
                  styles.dateText,
                  {textAlign: isRTL ? 'right' : 'left'},
                ]}>
                {groupStudents ? groupStudents?.length : t('select_students')}
              </Text>
              <Image
                source={isRTL ? icons.leftArrowWhite : icons.rightArrowGray}
                resizeMode="contain"
                style={{height: wp(4), width: wp(4), tintColor: colors.grey}}
              />
            </TouchableOpacity>
          </View>
        )}

        {/* Date Picker */}

        {/* iOS-specific Modal for DateTimePicker */}
        {showDatePicker && (
          <View style={styles.sectionContainer}>
            <TaleemDatePicker
              value={date}
              date={moment(date, 'DD-MM-YYYY').format('DD MMM YYYY')}
              setDate={date => {
                // setDate(new Date(date).toISOString().split('T')[0])
                setDate(convertToDMY(new Date(date)));
                if (date) {
                  // setDate(new Date(selectedDate).toISOString().split('T')[0]);
                  // setDate(new Date(selectedDate).toISOString().split('T')[0]);
                  // dispatch(
                  //   updatePackageStartDate(
                  //     date,
                  //     // selectedDate.toISOString().split('T')[0],
                  //   ),
                  // );
                }
              }}
              label={t('select_package_date')}
              minimumDate={new Date()} // Disallows past dates
              iconleft={icons.calanderImage}
            />
          </View>
        )}

        {/* Select Availability */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('select_availability')}</Text>
          <TouchableOpacity
            onPress={() => {
              if (!classType) {
                showToast('error', t('emptyClassType'), 'bottom', isRTL);
              }
              //  else if (date == '') {
              //   showToast('error', t('emptyPackageDate'), 'bottom', isRTL);
              // }
              //  else if (Object.keys(packageFromRedux).length <= 0) {
              //   showToast('error', t('emptyPackage'), 'bottom', isRTL);
              // }
              else if (!classType) {
                showToast('error', t('emptyClassType'), 'bottom', isRTL);
              } else {
                dispatch(updateCurrentDate(date));
                navigation.navigate('SelectAvailableSlot');
              }
            }}
            style={[styles.datePicker]}>
            {/* <Text style={styles.dateText}>
             
            </Text> */}
            {!selectedSlots || Object.keys(selectedSlots).length === 0 ? (
              <TouchableOpacity
                onPress={() => {
                  // if (packageStartDate == '') {
                  //   showToast('error', t('emptyPackageDate'), 'bottom', isRTL);
                  // } else if (Object.keys(packageFromRedux).length <= 0) {
                  //   showToast('error', t('emptyPackage'), 'bottom', isRTL);
                  // }
                  if (!classType) {
                    showToast('error', t('emptyClassType'), 'bottom', isRTL);
                  } else {
                    dispatch(updateCurrentDate(date));
                    navigation.navigate('SelectAvailableSlot');
                  }
                }}
                style={[
                  styles.selectHoursButton,
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                ]}>
                <Text style={styles.noSlotsText}>{t('select_hours')}</Text>

                <Image
                  source={isRTL ? icons.leftArrowWhite : icons.rightArrowGray}
                  style={[
                    styles.arrowIcon,
                    {width: isRTL ? 15 : 20, height: isRTL ? 15 : 20},
                  ]}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            ) : (
              <View>
                <ScheduleList
                  key={JSON.stringify(selectedSlots)}
                  selectedSlots={selectedSlots}
                  onSelectHoursPress={() => {
                    if (packageStartDate == '') {
                      showToast(
                        'error',
                        t('emptyPackageDate'),
                        'bottom',
                        isRTL,
                      );
                    } else if (!classType) {
                      showToast('error', t('emptyClassType'), 'bottom', isRTL);
                    } else {
                      dispatch(updateCurrentDate(date));
                      navigation.navigate('SelectAvailableSlot');
                    }
                  }}
                />
              </View>
            )}
          </TouchableOpacity>
        </View>
        {/* Meeting Point */}
        {classType?.tlm_class_type?.name == 'Face to Face' && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>{t('meeting_point')}</Text>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('MeetingPoint', {
                  is_tutor_available:
                    rateCardData?.data?.tlm_user?.tlm_tutor_profile
                      ?.allow_student_meeting_points,
                })
              }
              style={[
                styles.MeetingPoint,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Image
                source={icons.locationGray}
                resizeMode="contain"
                style={{height: 24, width: 24}}
              />
              <Text style={styles.dateText}>
                {tutorBookingJson?.address != ''
                  ? tutorBookingJson?.address
                  : t('addMeeting')}
                {/* {typeof meetingPoint === 'object' &&
                Object.keys(meetingPoint)?.length
                  ? // ? `${meetingPoint?.address}, ${meetingPoint?.city}, ${meetingPoint?.country}`
                    `${tutorBookingJson?.address}`
                  : typeof meetingPoint !== 'object'
                  ? meetingPoint
                  : t('addMeeting')} */}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Additional Instructions */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('instructions')}</Text>
          <TextInput
            placeholder={t('instructions_placeholder')}
            style={styles.instructionsInput}
            placeholderTextColor={colors.searchGray}
            onChangeText={onChangeInstructions}
            value={instructions}
          />
        </View>
        {sessionType === 'Group' && (
          <View
            style={{
              paddingHorizontal: 20,
              paddingVertical: 10,
              flexDirection: isRTL ? 'row-reverse' : 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                fontSize: fp(2),
                color: colors.black,
                fontFamily: Fonts.semiBold,
              }}>
              {t('select_payment_type')}
            </Text>
            <ToggleButton
              setPaymentType={setPaymentType}
              paymentType={paymentType}
            />
          </View>
        )}
        {/* Total Amount */}

        <View
          style={[
            styles.totalAmountContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <View>
            <Text style={styles.totalAmountText}>{t('total_amount')}</Text>
          </View>

          <Text style={styles.totalAmountValue}>
            {renderPrice()}
            {/* {price
              ? paymentType === 'split' && groupStudents?.length
                ? `${(price / groupStudents.length).toFixed(2)} ${t(
                    'currency',
                  )}`
                : `${price.toFixed(2)} ${t('currency')}`
              : '----'} */}

            {/* 120 Qatari Riyals */}
          </Text>
        </View>

        {/* Book Now Button */}
        <PrimaryButton
          onPress={
            () => handleBookTutorCourses()
            // navigation.navigate('BookYourTutorConfirmation', {
            // bookingData: {
            //   tutorData,
            //   packageStartingDate: date.toLocaleDateString('en-GB'),
            //   package: packageFromRedux,
            //   classType,
            //   sessionType,
            //   selectedSchedules: slot,
            // },
            // })
          }
          title={t('book_now')}
          style={{
            backgroundColor: colors.themeColor,
            marginBottom: 15,
          }}
          textStyle={{fontSize: 16, color: colors.white}}
          disabled={!price}
          activeOpacity={0}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

// ------------------------

const SelectClassTypeCard = ({color, image, title, isSelected, onPress}) => {
  const {t} = useTranslation();
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        backgroundColor: color,
        width: wp(43.5),
        borderRadius: 15,
        paddingHorizontal: wp(4),
        paddingVertical: hp(1),
        marginHorizontal: wp(1),
        borderWidth: 1,
        borderColor: isSelected ? colors.themeColor : colors.lightgreay,
      }}>
      <Image
        source={image}
        tintColor={isSelected ? colors.white : colors.txtGrey1}
        style={{
          height: fp(3.2),
          width: fp(3.2),
          marginBottom: hp(1),
          resizeMode: 'contain',
        }}
      />

      <Text
        style={{
          fontSize: 18,
          fontFamily: Fonts.regular,
          color: isSelected ? '#fff' : colors.txtGrey1,
        }}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default BookYourTutorCourses;
