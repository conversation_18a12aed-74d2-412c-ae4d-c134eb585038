import React, {useMemo, useState} from 'react';
import {
  Animated,
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import icons from '../../Utils/icons';
import {
  convertTo12HourFormat,
  convertToHoursAndMinutes,
  convertToLocal12HourFormat,
} from '../../Helper/DateHelpers/DateHelpers';
import LinearGradient from 'react-native-linear-gradient';
import {useTranslation} from 'react-i18next';
import moment from 'moment';
import {useDispatch} from 'react-redux';
import {updateTotalDurationSelectedSlots} from '../../Redux/Slices/Student/TutorBookingSlice';

// Transform the object data into a flat array for FlatList
const transformScheduleData = scheduleData => {
  return Object.entries(scheduleData)?.reduce((acc, [date, slots]) => {
    return [...acc, ...slots?.map(slot => ({...slot, displayDate: date}))];
  }, []);
};

// In your component:
export const ScheduleList = ({
  selectedSlots,
  onSelectHoursPress,
  onDeleteSlot,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [isExpanded, setIsExpanded] = useState(true);
  const [expandedDates, setExpandedDates] = useState({});
  const rotateAnimation = useState(new Animated.Value(0))[0];

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    Animated.timing(rotateAnimation, {
      toValue: isExpanded ? 0 : 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const toggleExpandDate = index => {
    setExpandedDates(prev => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const rotate = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  // Function to handle slot deletion
  const handleDeleteSlot = item => {
    if (onDeleteSlot) {
      onDeleteSlot(item); // Call the parent function to delete the slot
    }
  };

  const renderScheduleItem = ({item, index}) => {
    const formattedTime = isRTL
      ? `${convertToLocal12HourFormat(
          item.end_time,
        )} - ${convertToLocal12HourFormat(item.start_time)}`
      : `${convertToLocal12HourFormat(
          item.start_time,
        )} - ${convertToLocal12HourFormat(item.end_time)}`;

    return (
      <View style={styles.collapsibleContainer}>
        <TouchableWithoutFeedback onPress={() => toggleExpandDate(index)}>
          <View style={styles.slotContainer}>
            <View
              style={[
                styles.headerContainer,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <View
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  alignItems: 'center',
                }}>
                <Text style={[styles.dateText]}>{t('date')} : </Text>
                <Text style={[styles.dateText]}>
                  {isRTL
                    ? `${moment(item.displayDate).format('YYYY MMM DD')}`
                    : `${moment(item.displayDate).format('DD MMM YYYY')}`}
                </Text>
              </View>

              <View
                style={[
                  styles.durationContainer,
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                ]}>
                <LinearGradient
                  colors={['#C6FFC9', '#D4EBFF']}
                  start={{x: 0, y: 0}}
                  end={{x: 1, y: 1}}
                  style={styles.gradientContainer}>
                  <Text style={styles.durationText}>
                    {convertToHoursAndMinutes(Number(item.hours_duration))}
                  </Text>
                </LinearGradient>
                {/* Delete Button */}
                <TouchableOpacity
                  onPress={() => handleDeleteSlot(item)}
                  style={styles.deleteButton}>
                  <Image source={icons.cross} style={styles.deleteIcon} />
                </TouchableOpacity>
              </View>
            </View>
            <View
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: 'center',
              }}>
              <Text style={styles.timeText}>{t('time')} : </Text>
              <Text style={styles.timeText}>{formattedTime}</Text>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  };

  const scheduleArray = useMemo(
    () => transformScheduleData(selectedSlots),
    [selectedSlots],
  );

  const dispatch = useDispatch();
  const getSelectedHours = () => {
    if (selectedSlots && Object.keys(selectedSlots).length > 0) {
      const totalHours = Object.values(selectedSlots)
        .flat()
        .reduce((sum, slot) => {
          return sum + parseFloat(slot?.hours_duration);
        }, 0);

      const totalHoursWithLabel = `${totalHours} ${
        totalHours === 1 ? 'hour' : 'hours'
      }`;

      dispatch(updateTotalDurationSelectedSlots(totalHoursWithLabel));
      return convertToHoursAndMinutes(totalHours);
    } else {
      return 'No slots';
    }
  };

  return (
    <View style={styles.mainContainer}>
      <TouchableOpacity
        onPress={toggleExpand}
        style={[
          styles.selectHoursButton,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <View style={styles.selectHoursContent}>
          <Text style={styles.selectHoursText}>{t('select_hours')}</Text>
        </View>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            justifyContent: 'center',
            alignItems: 'center',
            gap: wp(1),
          }}>
          <LinearGradient
            colors={['#C6FFC9', '#D4EBFF']}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 1}}
            style={styles.gradientContainer}>
            <Text style={styles.durationText}>{getSelectedHours()}</Text>
          </LinearGradient>
          <Animated.Image
            source={icons.downArrowBlack}
            style={[styles.arrowIcon, {transform: [{rotate}]}]}
            resizeMode="contain"
          />
        </View>
      </TouchableOpacity>

      {isExpanded && scheduleArray.length > 0 && (
        <View style={styles.expandedContent}>
          <FlatList
            data={scheduleArray}
            renderItem={renderScheduleItem}
            keyExtractor={(item, index) => `${item.tutor_schedule_id}-${index}`}
            contentContainerStyle={styles.listContainer}
            ListFooterComponent={() => (
              <TouchableOpacity
                onPress={onSelectHoursPress}
                style={styles.addMoreButton}>
                <Text style={styles.addMoreText}>{t('addMoreSlots')}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: colors.white,
    borderRadius: 8,
    overflow: 'hidden',
  },
  selectHoursButton: {
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: wp(3),
    backgroundColor: colors.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.lightgreay,
  },
  selectHoursContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectHoursText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: colors.txtGrey1,
  },
  arrowIcon: {
    width: wp(4),
    height: wp(4),
  },
  expandedContent: {
    borderTopWidth: 0,
    borderColor: colors.lightgreay,
  },
  listContainer: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(1),
  },
  collapsibleContainer: {
    marginBottom: hp(1),
  },
  slotContainer: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: wp(3),
    borderWidth: 1,
    borderColor: colors.lightgreay,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: colors.black,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  gradientContainer: {
    borderRadius: 20,
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.5),
    alignItems: 'center',
    justifyContent: 'center',
  },
  durationText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.black,
  },
  deleteButton: {
    marginLeft: wp(2),
  },
  deleteIcon: {
    width: wp(4),
    height: wp(4),
    tintColor: colors.black,
  },
  timeText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: colors.txtGrey1,
    marginTop: hp(1),
  },
  addMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: wp(3),
    backgroundColor: colors.white,
    borderRadius: 8,
    marginTop: hp(2),
    borderWidth: 0.6,
    borderColor: colors.themeBackground,
  },
  addMoreText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: colors.themeBackground,
    marginRight: wp(2),
  },
});
