import {
  Image,
  SafeAreaView,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Platform,
  TouchableWithoutFeedback,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import BookYourTutorCard from '../../Components/Custom_Components/BookYourTutorCard';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import DateTimePicker from '@react-native-community/datetimepicker';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import styles from './styles';
import PackageSelector from './PackageSelector';
import {PrimaryButton} from '../../Components/CustomButton';
import {showToast} from '../../Components/ToastHelper';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {
  useBookTutorMutation,
  useGetBookingDetailsQuery,
  useGetTutorsDetailAcademicQuery,
  useLazyGetBookingPriceAcademicQuery,
  useLazyGetTutorRateCardDetailsStudentAcademicQuery,
  useLazyGetTutorSchedulesForStudentQuery,
  useStudentRescheduleSessionMutation,
} from '../../Api/ApiSlice';
import {useDispatch, useSelector} from 'react-redux';
import {
  updateCalculatedBookingAmount,
  updateClassType,
  updateInstructions,
  updatePackageStartDate,
  updateSessionType,
  updateTotalDurationSelectedSlots,
} from '../../Redux/Slices/Student/TutorBookingSlice';
import {CLASS_TYPES, DUMMY_USER_IMG, SESSION_TYPES} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {updateCurrentDate} from '../../Redux/Slices/Student/SlotSlice';
import {
  convertTo12HourFormat,
  convertToDMY,
  convertToHoursAndMinutes,
} from '../../Helper/DateHelpers/DateHelpers';
import {tutorBookingJson} from '../../Api/Model/TutorBookingModel';
import {
  transformToBookingSchedulesArray,
  validateBookingDetails,
} from '../../Helper/TutorBookingHelpers';
import {
  calculateBookingPrice,
  calculateBookingPriceForGroup,
} from '../../Helper/TutorBookingHelpers/PriceCalculationHelper/BookingPriceHelper';
import TaleemDatePicker from '../../Components/TaleemDatePicker/TaleemDatePicker';
import {ToggleButton} from '../../Components/toggleButton';
import LinearGradient from 'react-native-linear-gradient';
import {ScheduleList} from './RenderScheduleHour';

const RebookTutor = ({navigation, route}) => {
  const {
    tutorData,
    package: packageFromRedux,
    bookingFlowType,
    meetingPoint,
    packageStartDate,
  } = useSelector(state => state?.tutorBookingSlice);

  const bookibngData = useSelector(state => state?.tutorBookingSlice);
  const {selectedSlots, bookingSchedules} = useSelector(
    state => state.slotSlice,
  );

  const rate_card_id = tutorData?.profileData?.id;
  tutorBookingJson.rate_card_id = rate_card_id;
  tutorBookingJson.tutor_user_id = tutorData?.profileData?.tlm_user?.id;
  const {scheduleData, tutor, bookingDetails} = route?.params;
  console.log('schedule-----', bookingDetails);

  const rateCardId =
    bookingDetails?.tutor_rc_academic_id ||
    bookingDetails?.tutor_rc_courses_id ||
    bookingDetails?.tutor_rc_recreational_id;
  const [reschedule, {loading: rescheduleLoading}] =
    useStudentRescheduleSessionMutation();

  const [
    getTutorRateCardDetailsStudentAcademic,
    {
      data: rateCardData,
      error,
      isLoading: priceLoading,
      isSuccess: isRateCardFetchedSuccessfully,
    },
  ] = useLazyGetTutorRateCardDetailsStudentAcademicQuery();

  const {data: bookingRes, isLoading} = useGetBookingDetailsQuery(
    scheduleData?.bookingSchedule?.tlm_booking?.id,
  );

  const [bookTutor, {isLoading: isTutorBookingLoading}] =
    useBookTutorMutation();
  useEffect(() => {
    console.log('92478203', JSON.stringify(bookingRes?.data));
  }, [bookingRes]);

  const class_types = rateCardData?.data?.tutorClassTypes;
  const session_types = rateCardData?.data?.tlm_tutor_class_sessions;
  const packages = rateCardData?.data?.tlm_tutor_rate_card_packages;

  useEffect(() => {
    getTutorRateCardDetailsStudentAcademic({rate_card_id, bookingFlowType});
  }, []);

  useEffect(() => {
    if (isRateCardFetchedSuccessfully && rateCardData) {
      tutorBookingJson.token = rateCardData?.data?.bookingTempToken;
      tutorBookingJson.class_title = `${rateCardData?.data?.tlm_subject?.name} class for ${rateCardData?.data?.tlm_grade?.name} students`;
    }
  }, [isRateCardFetchedSuccessfully, rateCardData]); // Run the effect when the API call succeeds or data changes
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [date, setDate] = useState(convertToDMY(new Date()));
  // const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(true);
  const [classType, setClassType] = useState(bookingRes?.data?.tlm_class_type);

  const [sessionType, setSessionType] = useState(
    bookingRes?.data?.tlm_sessions_type,
  );

  const {slot, groupStudents} = useSelector(state => state.tutorBookingSlice);

  const [instructions, setInstructions] = useState('');
  const [price, setPrice] = useState(null); // Store the calculated price
  const [paymentType, setPaymentType] = useState('split'); // Store the calculated price

  const dispatch = useDispatch();
  function onChangeInstructions(text: string): void {
    setInstructions(text);
    dispatch(updateInstructions(text));
  }
  const [expandedDates, setExpandedDates] = useState({});

  console.log('3764892378');
  const [getTutorSchedulesForStudent, {isLoading: scheduleLoading}] =
    useLazyGetTutorSchedulesForStudentQuery();

  // const {data: tutorDetails} = useGetTutorsDetailAcademicQuery({
  //   tutorId,
  //   bookingFlowType: 'Academic',
  // }

  useEffect(() => {
    // if (isRateCardFetchedSuccessfully && rateCardData) {
    const packagePrice = packageFromRedux?.tlm_package_type?.price || 0;
    const packageDiscount = packageFromRedux?.tlm_package_type?.discount || 0;
    const classPrice = classType?.commissionedPrice;
    const groupDiscount = packageFromRedux?.tlm_package_type?.discount_group;
    let hours = 1;
    if (selectedSlots.length > 0) {
      hours = Object?.values(selectedSlots)
        .flat() // Flatten the arrays of slots from all dates
        .reduce((sum, slot) => {
          return sum + parseFloat(slot?.hours_duration);
        }, 0);
    }

    if (sessionType == 'Group') {
      const price = calculateBookingPriceForGroup(
        packagePrice,
        packageDiscount,
        classPrice,
        hours || 1,
        1,
        groupDiscount,
      );
      setPrice(price);
    } else {
      const price = calculateBookingPrice(
        packagePrice,
        packageDiscount,
        classPrice,
        hours || 1,
        1,
      );
      setPrice(price);
    }

    // }
  }, [
    classType,
    sessionType,
    JSON.stringify(selectedSlots), // Serialize selectedSlots for deep comparison
    JSON.stringify(packageFromRedux),
    paymentType,
  ]);
  console.log('line number 168===', meetingPoint);

  const handleBookTutor = () => {
    console.log('selectedSlots', selectedSlots);
    const totalHours = getSelectedHours();
    console.log('totalHours--', totalHours);
    const payload = {
      booking_id: bookingDetails?.id,
      booking_schedules: transformToBookingSchedulesArray(selectedSlots),
      hours_duration: totalHours,
    };

    console.log('🚀 ~ handleBookTutor ~ tutorBookingJson:', payload);

    // Call the API
    reschedule(payload)
      .unwrap()
      .then(response => {
        console.log('🚀 ~ handleBookTutor ~ response:', response);
        // navigation.navigate('BookYourTutorConfirmation', {
        //   bookingId: response?.data?.booking_id,
        // });
        // if (response?.data?.url) {
        //   navigation.navigate('PaymentScreen', {url: response?.data?.url});
        //   // showToast('success', response?.message);
        // } else {
        //   navigation.navigate('MyClass');
        //   showToast('success', response?.message);
        // }
        showToast('success', response?.message, 'bottom', isRTL);
        navigation?.navigate('My Class');
      })

      .catch(err => {
        console.error('Error calculating price:', err);
        // if (err?.data?.data?.wallet_status === 'insufficient') {
        //   navigation.navigate('Wallet');
        // }
        // if (err?.status == 409) {
        //   Alert.alert('Alert', err?.data?.message);
        // } else {
        showToast('error', err?.data?.message, 'bottom', isRTL);
        // }
      });
  };
  const getSelectedHours = () => {
    // Check if the slot object has data
    if (selectedSlots && Object.keys(selectedSlots).length > 0) {
      console.log('🚀 ~ getSelectedHours ~ selectedSlots:', selectedSlots);
      // console.log('🚀 ~ getSelectedHours ~ selectedSlots:', selectedSlots);

      // Calculate total hours across all dates
      const totalHours = Object.values(selectedSlots)
        .flat() // Flatten the arrays of slots from all dates
        .reduce((sum, slot) => {
          return sum + parseFloat(slot?.hours_duration);
        }, 0);
      console.log('989q374w8930', totalHours);
      return totalHours;
    } else {
      return 0;
    }
  };

  const imageUrl = tutorData?.profileData?.image
    ? {uri: `${IMAGE_BASE_URL}${tutor?.image}`}
    : {uri: DUMMY_USER_IMG};

  const isClassTypeAvailable = value => {
    if (!class_types || !Array.isArray(class_types) || !value) {
      return false;
    }
    return class_types.some(
      item =>
        item?.tlm_class_type?.value?.toLowerCase() === value?.toLowerCase(),
    );
  };

  const getFullClassTypeData = value => {
    if (!class_types || !Array.isArray(class_types) || !value) {
      return null;
    }
    return class_types?.find(
      item =>
        item?.tlm_class_type?.value?.toLowerCase() === value?.toLowerCase(),
    );
  };

  const isSessionTypeAvailable = value => {
    if (!classType?.tlm_class_type?.tlm_tutor_class_sessions || !value) {
      return false;
    }
    const sessions = classType?.tlm_class_type?.tlm_tutor_class_sessions;
    if (!Array.isArray(sessions)) {
      return false;
    }

    return sessions.some(
      session =>
        session?.tlm_sessions_type?.name?.toLowerCase() ===
        value?.toLowerCase(),
    );
  };

  const getFullSessionTypeData = value => {
    if (!classType?.tlm_class_type?.tlm_tutor_class_sessions || !value) {
      return null;
    }

    const sessions = classType?.tlm_class_type?.tlm_tutor_class_sessions;

    if (!Array.isArray(sessions)) {
      return null;
    }

    return sessions.find(
      session =>
        session?.tlm_sessions_type?.name?.toLowerCase() ===
        value?.toLowerCase(),
    );
  };

  const toggleExpandDate = index => {
    setExpandedDates(prev => ({
      ...prev,
      [index]: !prev[index],
    }));
  };
  console.log('tutorData?.profileData?.address', tutorData?.profileData);
  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}

      <ScrollView contentContainerStyle={{flexGrow: 1, paddingBottom: 20}}>
        {/* Header Section */}
        <View style={[styles.headerContainer]}>
          <AppHeader
            backIcon={icons.backbtn}
            isBackBtn
            title={t('rescheduleBooking')}
            style={{zIndex: 20}}
            isHome={true}
          />
          <></>
          <View style={{marginLeft: 0}}>
            <BookYourTutorCard
              name={tutor?.name}
              // title={tutorData?.profileData?.bio}
              title={tutor?.occupation}
              location={tutor?.address?.split(',')[0].trim()}
              rating={tutor?.tlm_tutor_profile?.total_ratings}
              reviews={tutor?.tlm_tutor_profile?.reviews?.length}
              // expertise={tutor?.tlm_tutor_profile?.expertise}
              experience={tutor?.tlm_tutor_profile?.experience}
              imgUrl={imageUrl}
              nationality={tutor?.nationality}
            />
          </View>
        </View>
        {/* Class Type Selection */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('select_class_type')}</Text>
          <View style={styles.row}>
            <View style={styles.row}>
              {CLASS_TYPES?.map((item, index) => {
                const isAvailable = isClassTypeAvailable(item?.value) || false;
                return (
                  <View key={item?.id}>
                    <SelectClassTypeCard
                      disabled={true}
                      isSelected={
                        bookingRes?.data?.tlm_class_type?.value === item?.value
                      }
                      onPress={() => {
                        if (isAvailable) {
                          const fullClassTypeData = getFullClassTypeData(
                            item?.value,
                          );
                          tutorBookingJson.class_type_id =
                            fullClassTypeData?.tlm_class_type?.id;
                          tutorBookingJson.class_type =
                            fullClassTypeData?.tlm_class_type?.value;
                          setSessionType('');
                          setClassType(fullClassTypeData);
                          dispatch(updateClassType(fullClassTypeData));
                        }
                      }}
                      color={
                        bookingRes?.data?.tlm_class_type?.value === item?.value
                          ? colors.themeColor
                          : colors.white
                      }
                      image={
                        item.value === 'online'
                          ? icons.onlineClassImage
                          : icons.offlineClassImage
                      }
                      title={t(
                        item.value === 'online' ? 'online' : 'face_to_face',
                      )}
                    />
                  </View>
                );
              })}
            </View>
          </View>
        </View>
        {/* Session Selection */}
        <View style={styles.sectionContainer}>
          <>
            <Text style={styles.sectionTitle}>{t('select_session')}</Text>

            <View style={styles.row}>
              {SESSION_TYPES?.map(session => {
                const color =
                  bookingRes?.data?.tlm_sessions_type?.name?.toLowerCase() ===
                  session?.value
                    ? colors.themeColor
                    : colors.white;
                const isAvailable = isSessionTypeAvailable(session?.value);
                return (
                  <View style={{width: wp(42)}}>
                    <TouchableOpacity
                      disabled={true}
                      style={[
                        styles.sessionButton,
                        {
                          backgroundColor: color,
                          borderWidth:
                            bookingRes?.data?.tlm_sessions_type?.name?.toLowerCase() ===
                            session?.value
                              ? 0
                              : 1,
                          borderColor: colors.lightgreay,
                        },
                      ]}
                      onPress={() => {
                        if (isAvailable) {
                          const fullSessionTypeData = getFullSessionTypeData(
                            session.value,
                          );
                          // console.log(
                          //   '🚀 ~ BookYourTutor ~ fullSessionTypeData:',
                          //   fullSessionTypeData,
                          // );
                          tutorBookingJson.sessions_id =
                            fullSessionTypeData?.tlm_sessions_type?.id;
                          tutorBookingJson.session =
                            fullSessionTypeData?.tlm_sessions_type?.name?.toLowerCase();
                          setSessionType(
                            fullSessionTypeData.tlm_sessions_type.name,
                          );
                          dispatch(
                            updateSessionType(
                              fullSessionTypeData.tlm_sessions_type,
                            ),
                          );
                        }
                      }}>
                      <Text
                        style={[
                          styles.sessionButtonText,
                          {
                            color:
                              bookingRes?.data?.tlm_sessions_type?.name?.toLowerCase() ===
                              session?.value
                                ? colors.white
                                : colors.txtGrey1,
                          },
                        ]}>
                        {t(session.value)}
                      </Text>
                    </TouchableOpacity>
                  </View>
                );
              })}
            </View>
          </>
        </View>
        {/* {select student} */}
        {sessionType === 'Group' && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>{t('select_students')}</Text>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('StudentList');
              }}
              style={styles.datePicker}>
              <Text style={styles.dateText}>
                {groupStudents ? groupStudents?.length : t('select_students')}
              </Text>
              <Image
                source={icons.rightArrowGray}
                resizeMode="contain"
                style={{height: 24, width: 24}}
              />
            </TouchableOpacity>
          </View>
        )}
        {/* Date Picker */}
        {showDatePicker && (
          <View style={styles.sectionContainer}>
            <TaleemDatePicker
              value={date}
              date={date}
              setDate={date => {
                // setDate(new Date(date).toISOString().split('T')[0])
                setDate(convertToDMY(new Date(date)));
                if (date) {
                  // setDate(new Date(selectedDate).toISOString().split('T')[0]);
                  // setDate(new Date(selectedDate).toISOString().split('T')[0]);
                  tutorBookingJson.package_start_date = date
                    ?.toISOString()
                    ?.split('T')[0];
                  dispatch(
                    updatePackageStartDate(
                      date,
                      // selectedDate.toISOString().split('T')[0],
                    ),
                  );
                }
              }}
              label={t('select_package_date')}
              minimumDate={new Date()} // Disallows past dates
              iconleft={icons.calanderImage}
            />
          </View>
        )}
        {/* Packages */}
        {/* <PackageSelector packageData={packages} /> */}
        {/* Select Availability */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('select_availability')}</Text>
          <TouchableOpacity
            onPress={() => {
              if (packageStartDate == '') {
                showToast('error', t('emptyPackageDate'), 'bottom', isRTL);
              } else if (Object.keys(packageFromRedux).length <= 0) {
                showToast('error', t('emptyPackage'), 'bottom', isRTL);
              } else if (!classType) {
                showToast('error', t('emptyClassType'), 'bottom', isRTL);
              } else {
                dispatch(updateCurrentDate(date));
                navigation.navigate('SelectAvailableSlot', {
                  rateCardId,
                  classType,
                });
              }
            }}
            style={styles.datePicker}>
            {/* <Text style={styles.dateText}>
             
            </Text> */}
            {!selectedSlots || Object.keys(selectedSlots).length === 0 ? (
              <TouchableOpacity
                onPress={() => {
                  if (packageStartDate == '') {
                    showToast('error', t('emptyPackageDate'), 'bottom', isRTL);
                  } else {
                    dispatch(updateCurrentDate(date));
                    navigation.navigate('SelectAvailableSlot', {
                      classType: bookingRes?.data?.tlm_class_type,
                      rateCardId,
                      tutor,
                      type: 'rebook',
                    });
                  }
                }}
                style={styles.selectHoursButton}>
                <Text style={styles.noSlotsText}>{t('select_hours')}</Text>

                <Image
                  source={icons.rightArrowGray}
                  style={styles.arrowIcon}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            ) : (
              <View>
                <ScheduleList
                  selectedSlots={selectedSlots}
                  onSelectHoursPress={() => {
                    if (packageStartDate == '') {
                      showToast(
                        'error',
                        t('emptyPackageDate'),
                        'bottom',
                        isRTL,
                      );
                    } else {
                      dispatch(updateCurrentDate(date));
                      navigation.navigate('SelectAvailableSlot', {
                        classType: bookingRes?.data?.tlm_class_type,
                        rateCardId,
                        tutor,
                        type: 'rebook',
                      });
                    }
                  }}
                />
              </View>
            )}
          </TouchableOpacity>
        </View>
        {/* Meeting Point */}
        {classType?.tlm_class_type?.name == 'Face to Face' && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>{t('meeting_point')}</Text>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('MeetingPoint', {is_tutor_available: true})
              }
              style={styles.MeetingPoint}>
              <Image
                source={icons.locationGray}
                resizeMode="contain"
                style={{height: 24, width: 24}}
              />
              <Text style={styles.dateText}>
                {typeof meetingPoint === 'object' &&
                Object.keys(meetingPoint)?.length
                  ? `${meetingPoint?.address}, ${meetingPoint?.city}, ${meetingPoint?.country}`
                  : typeof meetingPoint !== 'object'
                  ? meetingPoint
                  : t('addMeeting')}
              </Text>
            </TouchableOpacity>
          </View>
        )}
        {/* Additional Instructions */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('instructions')}</Text>
          <TextInput
            placeholder={t('instructions_placeholder')}
            style={styles.instructionsInput}
            placeholderTextColor={colors.searchGray}
            onChangeText={onChangeInstructions}
            value={instructions}
            maxLength={100}
            multiline
          />
        </View>
        {sessionType === 'Group' && (
          <View
            style={{
              paddingHorizontal: 20,
              paddingVertical: 10,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                fontSize: fp(2),
                color: colors.black,
                fontFamily: Fonts.semiBold,
              }}>
              {t('select_payment_type')}
            </Text>
            <ToggleButton
              setPaymentType={setPaymentType}
              paymentType={paymentType}
            />
          </View>
        )}
        {/* Total Amount */}

        {/* Book Now Button */}
        <PrimaryButton
          onPress={handleBookTutor}
          title={t('reschedule')}
          style={{
            backgroundColor: colors.themeColor,
            marginBottom: 15,
          }}
          textStyle={{fontSize: 16, color: colors.white}}
          loading={isTutorBookingLoading}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

// ------------------------

const SelectClassTypeCard = ({
  color,
  image,
  title,
  isSelected,
  onPress,
  disabled,
}) => {
  const {t} = useTranslation();
  return (
    <TouchableOpacity
      disabled={disabled}
      onPress={onPress}
      style={{
        backgroundColor: color,
        width: wp(43.5),
        borderRadius: 15,
        paddingHorizontal: wp(4),
        paddingVertical: hp(1),
        marginHorizontal: wp(1),
        borderWidth: 1,
        borderColor: isSelected ? colors.themeColor : colors.lightgreay,
      }}>
      <Image
        source={image}
        tintColor={isSelected ? colors.white : colors.txtGrey1}
        style={{
          height: fp(3.2),
          width: fp(3.2),
          marginBottom: hp(1),
          resizeMode: 'contain',
        }}
      />

      <Text
        style={{
          fontSize: 18,
          fontFamily: Fonts.regular,
          color: isSelected ? '#fff' : colors.txtGrey1,
        }}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default RebookTutor;
