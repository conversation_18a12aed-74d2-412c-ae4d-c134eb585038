import {
  Image,
  SafeAreaView,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Platform,
  TouchableWithoutFeedback,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import BookYourTutorCard from '../../Components/Custom_Components/BookYourTutorCard';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';

import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import styles from './styles';
import PackageSelector from './PackageSelector';
import {PrimaryButton} from '../../Components/CustomButton';
import {showToast} from '../../Components/ToastHelper';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {
  useBookTutorMutation,
  useGetBookingDetailsQuery,
  useGetTutorsDetailAcademicQuery,
  useLazyGetBookingPriceAcademicQuery,
  useLazyGetTutorRateCardDetailsStudentAcademicQuery,
} from '../../Api/ApiSlice';
import {useDispatch, useSelector} from 'react-redux';
import {
  updateCalculatedBookingAmount,
  updateClassType,
  updateInstructions,
  updatePackageStartDate,
  updateSessionType,
  updateTotalDurationSelectedSlots,
} from '../../Redux/Slices/Student/TutorBookingSlice';
import {CLASS_TYPES, DUMMY_USER_IMG, SESSION_TYPES} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {
  resetSelectedSlots,
  updateCurrentDate,
  updateSelectedSlots,
} from '../../Redux/Slices/Student/SlotSlice';
import {
  convertDecimalHoursToHoursAndMinutes,
  convertTo12HourFormat,
  convertToDMY,
  convertToHoursAndMinutes,
} from '../../Helper/DateHelpers/DateHelpers';
import {tutorBookingJson} from '../../Api/Model/TutorBookingModel';
import {
  transformToBookingSchedulesArray,
  validateBookingDetails,
} from '../../Helper/TutorBookingHelpers';
import {
  calculateBookingPrice,
  calculateBookingPriceForGroup,
  getDiscountedAmountForIndividual,
} from '../../Helper/TutorBookingHelpers/PriceCalculationHelper/BookingPriceHelper';
import TaleemDatePicker from '../../Components/TaleemDatePicker/TaleemDatePicker';
import {ToggleButton} from '../../Components/toggleButton';
import LinearGradient from 'react-native-linear-gradient';
import {ScheduleList} from './RenderScheduleHour';
import moment from 'moment';

const BookYourTutor = ({navigation, route}) => {
  const {
    tutorData,
    package: packageFromRedux,
    bookingFlowType,
    meetingPoint,
    packageStartDate,
    selectedRateCardId,
  } = useSelector(state => state?.tutorBookingSlice);
  console.log('🚀 ~ tutorData:', JSON.stringify(tutorData));
  const {rate_card_id} = route?.params || '';
  console.log('🚀 ~ BookYourTutor ~ rate_card_id:', rate_card_id);
  const bookibngData = useSelector(state => state?.tutorBookingSlice);
  const {selectedSlots, bookingSchedules} = useSelector(
    state => state.slotSlice,
  );
  console.log('🚀 ~ selectedSlots:', selectedSlots);
  console.log('🚀 ~ bookingFlowType:', bookingFlowType);
  // const rate_card_id = tutorData?.profileData?.id;
  console.log('🚀 ~ BookYourTutor ~ meetingPoint:', meetingPoint);
  tutorBookingJson.rate_card_id = rate_card_id;
  tutorBookingJson.tutor_user_id =
    bookingFlowType == 'academic'
      ? tutorData?.profileData?.id
      : tutorData?.profileData?.user_id;

  const [
    getTutorRateCardDetailsStudentAcademic,
    {
      data: rateCardData,
      error,
      isLoading: priceLoading,
      isSuccess: isRateCardFetchedSuccessfully,
    },
  ] = useLazyGetTutorRateCardDetailsStudentAcademicQuery();
  console.log(rateCardData?.data, '78789784asdfasdf');
  useEffect(() => {
    dispatch(updateSelectedSlots({}));
  }, []);

  const [bookTutor, {isLoading: isTutorBookingLoading}] =
    useBookTutorMutation();

  const class_types = rateCardData?.data?.tutorClassTypes;
  const session_types = rateCardData?.data?.tlm_tutor_class_sessions;
  const packages = rateCardData?.data?.tlm_tutor_rate_card_packages;

  useEffect(() => {
    getTutorRateCardDetailsStudentAcademic({rate_card_id, bookingFlowType});
  }, []);

  useEffect(() => {
    if (isRateCardFetchedSuccessfully && rateCardData) {
      tutorBookingJson.token = rateCardData?.data?.bookingTempToken;
      if (bookingFlowType == 'academic') {
        tutorBookingJson.class_title = rateCardData.data.tlm_subject.name
          ? `${rateCardData?.data?.tlm_subject?.name} ${t('class')} ${t(
              'for',
            )} ${rateCardData?.data?.tlm_grade?.name} ${t('students')}`
          : `${t('class')} ${t('for')} ${
              rateCardData?.data?.tlm_grade?.name
            } ${t('students')}`;
      } else if (bookingFlowType == 'recreational') {
        tutorBookingJson.class_title = rateCardData.data.tlm_expertise.name
          ? `${rateCardData.data.tlm_expertise.name} ${t('class')}`
          : `${t('recreational')} ${t('class')}`;
      } else if (bookingFlowType == 'courses') {
        // tutorBookingJson.class_title = rateCardData.data.tlm_expertise.name
        //   ? `${rateCardData.data.tlm_expertise.name} Class`
        //   : `Rereational Class`;
      }
    }

    console.log(
      '🚀 ~ useEffect ~ rateCardData?.data?.tlm_subject?.name:',
      rateCardData?.data?.tlm_subject?.name,
    );
  }, [isRateCardFetchedSuccessfully, rateCardData]); // Run the effect when the API call succeeds or data changes
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [date, setDate] = useState(convertToDMY(new Date()));
  // const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(true);
  const [classType, setClassType] = useState('');

  const [sessionType, setSessionType] = useState('');

  const {slot, groupStudents} = useSelector(state => state.tutorBookingSlice);

  const [instructions, setInstructions] = useState('');
  const [price, setPrice] = useState(null); // Store the calculated price
  const [paymentType, setPaymentType] = useState('split'); // Store the calculated price
  console.log('🚀 ~ paymentType:', paymentType);

  const dispatch = useDispatch();
  function onChangeInstructions(text: string): void {
    setInstructions(text);
    dispatch(updateInstructions(text));
  }
  const [expandedDates, setExpandedDates] = useState({});
  console.log('🚀 ~ useEffect ~ classType:', classType);
  useEffect(() => {
    // if (isRateCardFetchedSuccessfully && rateCardData) {
    const packagePrice = packageFromRedux?.tlm_package_type?.price || 0;
    const packageDiscount = packageFromRedux?.tlm_package_type?.discount || 0;
    const classPrice = classType?.commissionedPrice;
    const groupDiscount = packageFromRedux?.tlm_package_type?.discount_group;
    let hours = 1;
    if (selectedSlots.length > 0) {
      hours = Object?.values(selectedSlots)
        .flat() // Flatten the arrays of slots from all dates
        .reduce((sum, slot) => {
          return sum + parseFloat(slot?.hours_duration);
        }, 0);
    }

    if (sessionType == 'group') {
      const price = calculateBookingPriceForGroup(
        packagePrice,
        packageDiscount,
        classPrice,
        getSelectedHours() || 1,
        groupStudents?.length + 1 || 1,
        groupDiscount,
      );
      tutorBookingJson.tutor_earned_amount = calculateBookingPriceForGroup(
        packagePrice,
        packageDiscount,
        classType?.price,
        getSelectedHours() || 1,
        1,
        groupDiscount,
      );
      setPrice(price);
    } else {
      const price = calculateBookingPrice(
        packagePrice,
        packageDiscount,
        classPrice,
        getSelectedHours() || 1,
        1,
      );
      tutorBookingJson.tutor_earned_amount = calculateBookingPrice(
        packagePrice,
        packageDiscount,
        classType?.price,
        getSelectedHours() || 1,
        1,
      );
      setPrice(price);
    }

    // }
  }, [
    classType,
    sessionType,
    JSON.stringify(selectedSlots), // Serialize selectedSlots for deep comparison
    JSON.stringify(packageFromRedux),
    paymentType,
    groupStudents,
    groupStudents?.length,
  ]);
  console.log('asdfasdfasdfasdf', sessionType);
  console.log('🚀 ~ tutorData?.profileData?.id:', tutorData?.profileData?.id);

  const handleBookTutor = () => {
    const minHours = packageFromRedux?.tlm_package_type?.min_hour;
    const maxHour = packageFromRedux?.tlm_package_type?.max_hour;
    const packageDiscount = packageFromRedux?.tlm_package_type?.discount || 0;
    const classPrice = classType?.commissionedPrice;
    const groupDiscount = packageFromRedux?.tlm_package_type?.discount_group;
    const selectedHours = getSelectedHours();
    const packagePrice = packageFromRedux?.tlm_package_type?.price || 0;

    if (
      !validateBookingDetails(
        rate_card_id,
        classType,
        packageFromRedux?.id,
        selectedSlots,
        sessionType,
        isRTL,
      )
    ) {
      return;
    }

    if (sessionType == 'group') {
      if (groupStudents?.length + 1 < rateCardData?.data?.min_student_no) {
        showToast(
          'error',
          t('youMustSelect', {count: rateCardData?.data?.min_student_no}),
          'bottom',
          isRTL,
        );
        return;
      }

      if (groupStudents?.length + 1 > rateCardData?.data?.max_student_no) {
        showToast(
          'error',
          t('selectStudentError', {
            count: rateCardData?.data?.max_student_no,
          }),
          'bottom',
          isRTL,
        );
        return;
      }
      if (tutorBookingJson?.session == 'group') {
        if (paymentType == 'split') {
          tutorBookingJson.amount = price / (groupStudents?.length + 1);
        } else {
          tutorBookingJson.amount = price;
        }
      } else {
        tutorBookingJson.amount = price;
      }
    } else {
      tutorBookingJson.amount = price;
    }
    if (selectedHours < minHours) {
      showToast(
        'error',
        `Please select at least ${minHours} hours. Currently selected: ${convertDecimalHoursToHoursAndMinutes(
          selectedHours,
        )}.`,
        'bottom',
        isRTL,
      );
      return;
    } else if (selectedHours > maxHour) {
      showToast(
        'error',
        `Please select at most ${maxHour} hours. Currently selected: ${convertDecimalHoursToHoursAndMinutes(
          selectedHours,
        )}.`,
        'bottom',
        isRTL,
      );
    }
    console.log('🚀 ~ handleBookTutor ~ tutorBookingJson:', tutorBookingJson);
    const sortedData = transformToBookingSchedulesArray(selectedSlots).sort(
      (a, b) => new Date(a.date) - new Date(b.date),
    );

    tutorBookingJson.booking_schedules = sortedData;
    console.log(
      '🚀 ~ handleBookTutor ~ tutorBookingJson.booking_schedules:',
      tutorBookingJson.booking_schedules,
    );
    tutorBookingJson.package_start_date = sortedData[0]?.date;
    tutorBookingJson.instructions = instructions;

    tutorBookingJson.rate_card_type =
      bookingFlowType == 'academic'
        ? '1'
        : bookingFlowType == 'recreational'
        ? '2'
        : '3';

    // tutorBookingJson.amount = price;
    tutorBookingJson.payment_type = paymentType;
    tutorBookingJson.payment_breakdown.quantity = sortedData?.length;
    (tutorBookingJson.payment_breakdown.slot_amount = classPrice),
      (tutorBookingJson.payment_breakdown.total =
        classPrice * sortedData?.length),
      (tutorBookingJson.payment_breakdown.discount_percentage =
        packageDiscount),
      (tutorBookingJson.payment_breakdown.discounted_amount =
        getDiscountedAmountForIndividual(
          packagePrice,
          packageDiscount,
          classPrice,
          getSelectedHours() || 1,
          1,
        ));
    tutorBookingJson.payment_breakdown.grand_total = price;

    console.log('🚀 ~ handleBookTutor ~ tutorBookingJson:', tutorBookingJson);

    // Call the API
    bookTutor(tutorBookingJson)
      .unwrap()
      .then(response => {
        console.log('🚀 ~ handleBookTutor ~ response:', response);
        navigation.navigate('BookYourTutorConfirmation', {
          bookingId: response?.data?.booking_id,
        });
        // if (response?.data?.url) {
        //   navigation.navigate('PaymentScreen', {url: response?.data?.url});
        //   // showToast('success', response?.message);
        // } else {
        //   navigation.navigate('MyClass');
        //   showToast('success', response?.message);
        // }
        // showToast('success', response?.message);
      })

      .catch(err => {
        console.error('Error calculating price:', err);
        // if (err?.data?.data?.wallet_status === 'insufficient') {
        //   navigation.navigate('Wallet');
        // }
        // if (err?.status == 409) {
        //   Alert.alert('Alert', err?.data?.message);
        // } else {
        showToast('error', err?.data?.message, 'bottom', isRTL);
        // }
      });
  };

  const getSelectedHours = () => {
    // Check if the slot object has data
    if (selectedSlots && Object.keys(selectedSlots).length > 0) {
      // Calculate total hours across all dates
      const totalHours = Object.values(selectedSlots)
        .flat() // Flatten the arrays of slots from all dates
        .reduce((sum, slot) => {
          return sum + parseFloat(slot?.hours_duration);
        }, 0);

      // Format the total hours string
      const totalHoursWithLabel = `${totalHours} ${
        totalHours === 1 ? 'hour' : 'hours'
      }`;

      dispatch(updateTotalDurationSelectedSlots(totalHoursWithLabel));
      return totalHours;
      // return convertToHoursAndMinutes(totalHours);
    } else {
      return 1;
    }
  };

  const imageUrl = tutorData?.profileData?.image
    ? {uri: `${IMAGE_BASE_URL}${tutorData?.profileData?.image}`}
    : {uri: DUMMY_USER_IMG};

  const isClassTypeAvailable = value => {
    if (!class_types || !Array.isArray(class_types) || !value) {
      return false;
    }
    return class_types.some(
      item =>
        item?.tlm_class_type?.value?.toLowerCase() === value?.toLowerCase(),
    );
  };

  const getFullClassTypeData = value => {
    if (!class_types || !Array.isArray(class_types) || !value) {
      return null;
    }

    return class_types?.find(
      item =>
        item?.tlm_class_type?.value?.toLowerCase() === value?.toLowerCase(),
    );
  };

  const isSessionTypeAvailable = value => {
    console.log(
      '🚀 ~ classType?.tlm_class_type?.tlm_tutor_class_sessions:',
      classType?.tlm_class_type?.tlm_tutor_class_sessions,
    );
    console.log('🚀 ~ value:', value);
    if (!classType?.tlm_class_type?.tlm_tutor_class_sessions || !value) {
      return false;
    }
    const sessions = classType?.tlm_class_type?.tlm_tutor_class_sessions;
    if (!Array.isArray(sessions)) {
      return false;
    }

    return sessions.some(
      session =>
        session?.tlm_sessions_type?.name?.toLowerCase() ===
        value?.toLowerCase(),
    );
  };

  const getFullSessionTypeData = value => {
    if (!classType?.tlm_class_type?.tlm_tutor_class_sessions || !value) {
      return null;
    }

    const sessions = classType?.tlm_class_type?.tlm_tutor_class_sessions;

    if (!Array.isArray(sessions)) {
      return null;
    }

    return sessions.find(
      session =>
        session?.tlm_sessions_type?.name?.toLowerCase() ===
        value?.toLowerCase(),
    );
  };

  useEffect(() => {
    console.log('Updated selectedSlots:', selectedSlots);
  }, [selectedSlots]);

  const toggleExpandDate = index => {
    setExpandedDates(prev => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const renderPrice = () => {
    if (sessionType == 'Group') {
      if (price) {
        if (paymentType == 'split' && groupStudents?.length) {
          return `${(price / (groupStudents.length + 1)).toFixed(2)} ${t(
            'currency',
          )}`;
        } else {
          return `${price?.toFixed(2)} ${t('currency')}`;
        }
      } else {
        return '----';
      }
    } else {
      return `${price?.toFixed(2)} ${t('currency')}`;
    }
  };

  const trimmedAddress = tutorData?.profileData?.address?.trim(); // Remove leading/trailing spaces
  const words = trimmedAddress?.split(/[\s,،-]+/);
  const lastWord = words ? words[words?.length - 1] : '';
  const handleDeleteSlot = item => {
    console.log('🚀 ~ handleDeleteSlot ~ item:', item);
    try {
      const date = item.displayDate; // "2025-03-14"
      const startTime = item.start_time; // "18:00:00"
      const endTime = item.end_time; // "19:00:00"
      const slotId = item.id; // 1214

      // Filter out the slot that matches the id, date, start time, and end time
      const updatedSlotsForDate =
        selectedSlots[date]?.filter(
          slot => slot.start_time !== startTime || slot.end_time !== endTime,
        ) || [];

      // If there are no slots left, dispatch with undefined to remove the date key
      const slotsToDispatch =
        updatedSlotsForDate.length > 0 ? updatedSlotsForDate : undefined;

      dispatch(updateSelectedSlots({date, slots: slotsToDispatch}));
    } catch (error) {
      console.error('🚀 ~ handleDeleteSlot ~ error:', error);
    }
  };
  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('book_tutor_title')}
        style={{}}
        isHome={true}
      />
      <ScrollView contentContainerStyle={{flexGrow: 1, paddingBottom: 20}}>
        {/* Header Section */}
        <View style={[styles.headerContainer]}>
          <View
            style={{
              marginLeft: 0,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <BookYourTutorCard
              name={tutorData?.profileData?.name}
              // title={tutorData?.profileData?.bio}
              title={tutorData?.profileData?.occupation}
              location={lastWord}
              rating={tutorData?.profileData?.ratings}
              reviews={tutorData?.profileData?.reviews?.length}
              expertise={
                tutorData?.profileData?.tlm_tutor_expertises?.map(
                  item => item?.tlm_expertise?.name,
                ) || [rateCardData?.data?.tlm_expertise?.name]
              }
              experience={tutorData?.profileData?.experience}
              imgUrl={imageUrl}
              nationality={tutorData?.nationality}
            />
          </View>
        </View>
        {/* Class Type Selection */}
        <View style={styles.sectionContainer}>
          <Text
            style={[
              styles.sectionTitle,
              {textAlign: isRTL ? 'right' : 'left'},
            ]}>
            {t('select_class_type')}
          </Text>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <View
              style={[
                styles.row,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {CLASS_TYPES?.map((item, index) => {
                const isAvailable = isClassTypeAvailable(item?.value) || false;
                return (
                  <View key={item?.id}>
                    <SelectClassTypeCard
                      isSelected={
                        classType?.tlm_class_type?.value === item?.value
                      }
                      onPress={() => {
                        if (isAvailable) {
                          const fullClassTypeData = getFullClassTypeData(
                            item?.value,
                          );
                          console.log(
                            '🚀 ~ {CLASS_TYPES?.map ~ fullClassTypeData:',
                            fullClassTypeData,
                          );
                          tutorBookingJson.class_type_id =
                            fullClassTypeData?.tlm_class_type?.id;
                          tutorBookingJson.class_type =
                            fullClassTypeData?.tlm_class_type?.value;
                          setSessionType('');
                          setClassType(fullClassTypeData);
                          dispatch(updateClassType(fullClassTypeData));
                          dispatch(resetSelectedSlots());
                        }
                      }}
                      color={
                        classType?.tlm_class_type?.value === item?.value
                          ? colors.themeColor
                          : '#fff'
                      }
                      image={
                        item.value === 'online'
                          ? icons.onlineClassImage
                          : icons.offlineClassImage
                      }
                      title={t(
                        item.value === 'online' ? 'online' : 'face_to_face',
                      )}
                    />
                    {!isAvailable && (
                      <Text
                        style={{
                          fontSize: fp(1.2),
                          marginLeft: wp(4),
                          marginTop: hp(0.2),
                        }}>
                        {t('notAvailableClass')}
                      </Text>
                    )}
                  </View>
                );
              })}
            </View>
          </View>
        </View>
        {/* Session Selection */}
        <View style={styles.sectionContainer}>
          {classType !== '' && (
            <>
              <Text
                style={[
                  styles.sectionTitle,
                  {textAlign: isRTL ? 'right' : 'left'},
                ]}>
                {t('select_session')}
              </Text>
              <View
                style={[
                  styles.row,
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                ]}>
                {SESSION_TYPES?.map(session => {
                  console.log('🚀 ~ {CLASS_TYPES?.map ~ session:', session);

                  const color =
                    sessionType?.toLowerCase() === session?.value
                      ? colors.themeColor
                      : '#fff';
                  const isAvailable = isSessionTypeAvailable(t(session?.value));
                  return (
                    <View style={{}}>
                      <TouchableOpacity
                        style={[
                          styles.sessionButton,
                          {
                            backgroundColor: color,
                            borderWidth:
                              sessionType?.toLowerCase() === session?.value
                                ? 0
                                : 1,
                            borderColor: colors.lightgreay,
                          },
                        ]}
                        onPress={() => {
                          if (isAvailable) {
                            const fullSessionTypeData = getFullSessionTypeData(
                              t(session.value),
                            );
                            console.log(
                              '🚀 ~ fullSessionTypeData:',
                              fullSessionTypeData,
                            );
                            // console.log(
                            //   '🚀 ~ BookYourTutor ~ fullSessionTypeData:',
                            //   fullSessionTypeData,
                            // );
                            tutorBookingJson.sessions_id =
                              fullSessionTypeData?.tlm_sessions_type?.id;
                            tutorBookingJson.session =
                              fullSessionTypeData?.tlm_sessions_type
                                ?.class_type == '1'
                                ? 'individual'
                                : 'group';
                            setSessionType(
                              fullSessionTypeData.tlm_sessions_type
                                .class_type == '1'
                                ? 'individual'
                                : 'group',
                            );
                            dispatch(
                              updateSessionType(
                                fullSessionTypeData.tlm_sessions_type,
                              ),
                            );
                            dispatch(resetSelectedSlots());
                          }
                        }}>
                        <Text
                          style={[
                            styles.sessionButtonText,
                            {
                              color:
                                sessionType?.toLowerCase() === session?.value
                                  ? '#fff'
                                  : colors.txtGrey1,
                            },
                          ]}>
                          {t(session.value)}
                        </Text>
                      </TouchableOpacity>
                      {!isAvailable && (
                        <Text
                          style={{
                            fontSize: fp(1.2),
                            marginLeft: wp(2),
                            marginTop: hp(0.2),
                            color: colors.lightGrey,
                          }}>
                          {t('notAvailableSession')}
                        </Text>
                      )}
                    </View>
                  );
                })}
              </View>
            </>
          )}
        </View>
        {/* {select student} */}
        {sessionType === 'group' && (
          <View style={styles.sectionContainer}>
            <Text
              style={[
                styles.sectionTitle,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {t('select_students')}
            </Text>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('StudentList', {
                  maxStudent: rateCardData?.data?.max_student_no - 1,
                  minStudent: rateCardData?.data?.min_student_no - 1,
                });
              }}
              style={[
                styles.MeetingPoint,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Text
                style={[
                  styles.dateText,
                  {textAlign: isRTL ? 'right' : 'left'},
                ]}>
                {groupStudents ? groupStudents?.length : t('select_students')}
              </Text>
              <Image
                source={isRTL ? icons.leftArrowWhite : icons.rightArrowGray}
                resizeMode="contain"
                style={{height: wp(4), width: wp(4), tintColor: colors.grey}}
              />
            </TouchableOpacity>
          </View>
        )}
        {/* Date Picker */}
        {/* {showDatePicker && (
          <View style={styles.sectionContainer}>
            <TaleemDatePicker
              value={date}
              date={moment(date, 'DD-MM-YYYY').format('DD MMM YYYY')}
              setDate={date => {
                // setDate(new Date(date).toISOString().split('T')[0])
                setDate(convertToDMY(new Date(date)));
                if (date) {
                  // setDate(new Date(selectedDate).toISOString().split('T')[0]);
                  // setDate(new Date(selectedDate).toISOString().split('T')[0]);
                  tutorBookingJson.package_start_date = date
                    ?.toISOString()
                    ?.split('T')[0];
                  dispatch(
                    updatePackageStartDate(
                      date,
                      // selectedDate.toISOString().split('T')[0],
                    ),
                  );
                }
              }}
              label={t('select_package_date')}
              minimumDate={new Date()} // Disallows past dates
              iconleft={icons.calanderImage}
            />
          </View>
        )} */}
        {/* Packages */}
        <PackageSelector packageData={packages} />
        {/* Select Availability */}
        <View style={styles.sectionContainer}>
          <Text
            style={[
              styles.sectionTitle,
              {textAlign: isRTL ? 'right' : 'left'},
            ]}>
            {t('select_availability')}
          </Text>
          <TouchableOpacity
            onPress={() => {
              if (!classType) {
                showToast('error', t('emptyClassType'), 'bottom', isRTL);
              } else if (Object.keys(packageFromRedux).length <= 0) {
                showToast('error', t('emptyPackage'), 'bottom', isRTL);
              } else if (!classType) {
                showToast('error', t('emptyClassType'), 'bottom', isRTL);
              } else {
                dispatch(updateCurrentDate(date));
                navigation.navigate('SelectAvailableSlot');
              }
            }}
            style={[styles.datePicker]}>
            {/* <Text style={styles.dateText}>

            </Text> */}
            {!selectedSlots || Object.keys(selectedSlots).length === 0 ? (
              <TouchableOpacity
                onPress={() => {
                  if (packageStartDate == '') {
                    showToast('error', t('emptyPackageDate'), 'bottom', isRTL);
                  } else if (Object.keys(packageFromRedux).length <= 0) {
                    showToast('error', t('emptyPackage'), 'bottom', isRTL);
                  } else if (!classType) {
                    showToast('error', t('emptyClassType'), 'bottom', isRTL);
                  } else {
                    dispatch(updateCurrentDate(date));
                    navigation.navigate('SelectAvailableSlot');
                  }
                }}
                style={[
                  styles.selectHoursButton,
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                ]}>
                <Text style={styles.noSlotsText}>{t('select_hours')}</Text>

                <Image
                  source={isRTL ? icons.leftArrowWhite : icons.rightArrowGray}
                  style={[
                    styles.arrowIcon,
                    {width: isRTL ? 15 : 20, height: isRTL ? 15 : 20},
                  ]}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            ) : (
              <View>
                <ScheduleList
                  key={JSON.stringify(selectedSlots)}
                  selectedSlots={selectedSlots}
                  onSelectHoursPress={() => {
                    if (packageStartDate == '') {
                      showToast(
                        'error',
                        t('emptyPackageDate'),
                        'bottom',
                        isRTL,
                      );
                    } else if (Object.keys(packageFromRedux).length <= 0) {
                      showToast('error', t('emptyPackage'), 'bottom', isRTL);
                    } else if (!classType) {
                      showToast('error', t('emptyClassType'), 'bottom', isRTL);
                    } else {
                      dispatch(updateCurrentDate(date));
                      navigation.navigate('SelectAvailableSlot');
                    }
                  }}
                  onDeleteSlot={handleDeleteSlot}
                />
              </View>
            )}
          </TouchableOpacity>
        </View>
        {/* Meeting Point */}
        {classType?.tlm_class_type?.name == 'Face to Face' && (
          <View style={styles.sectionContainer}>
            <Text
              style={[
                styles.sectionTitle,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {t('meeting_point')}
            </Text>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('MeetingPoint', {
                  is_tutor_available:
                    rateCardData?.data?.tlm_user?.tlm_tutor_profile
                      ?.allow_student_meeting_points,
                })
              }
              style={[
                styles.MeetingPoint,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Image
                source={icons.locationGray}
                resizeMode="contain"
                style={{height: 24, width: 24}}
              />
              <Text style={styles.dateText}>
                {tutorBookingJson?.address != ''
                  ? tutorBookingJson?.address
                  : t('addMeeting')}
                {/* {typeof meetingPoint === 'object' &&
                Object.keys(meetingPoint)?.length
                  ? // ? `${meetingPoint?.address}, ${meetingPoint?.city}, ${meetingPoint?.country}`
                    `${tutorBookingJson?.address}`
                  : typeof meetingPoint !== 'object'
                  ? meetingPoint
                  : t('addMeeting')} */}
              </Text>
            </TouchableOpacity>
          </View>
        )}
        {/* Additional Instructions */}
        <View style={styles.sectionContainer}>
          <Text
            style={[
              styles.sectionTitle,
              {textAlign: isRTL ? 'right' : 'left'},
            ]}>
            {t('instructions')}
          </Text>
          <TextInput
            placeholder={t('instructions_placeholder')}
            style={styles.instructionsInput}
            placeholderTextColor={colors.searchGray}
            onChangeText={onChangeInstructions}
            value={instructions}
            maxLength={100}
            multiline
          />
        </View>
        {sessionType === 'group' && (
          <View
            style={{
              paddingHorizontal: 20,
              paddingVertical: 10,
              flexDirection: isRTL ? 'row-reverse' : 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                fontSize: fp(2),
                color: colors.black,
                fontFamily: Fonts.semiBold,
              }}>
              {t('select_payment_type')}
            </Text>
            <ToggleButton
              setPaymentType={setPaymentType}
              paymentType={paymentType}
            />
          </View>
        )}
        {/* Total Amount */}
        <View
          style={[
            styles.totalAmountContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <View>
            <Text style={styles.totalAmountText}>{t('total_amount')}</Text>
          </View>

          <Text style={styles.totalAmountValue}>
            {/* 120 Qatari Riyals */}
            {renderPrice()}
          </Text>
        </View>
        {/* Book Now Button */}
        <PrimaryButton
          onPress={handleBookTutor}
          title={t('book_now')}
          style={{
            backgroundColor: colors.themeColor,
            marginBottom: 15,
          }}
          textStyle={{fontSize: 16, color: colors.white}}
          loading={isTutorBookingLoading}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export const SelectClassTypeCard = ({
  color,
  image,
  title,
  isSelected,
  onPress,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        backgroundColor: color,
        width: wp(43.5),
        borderRadius: 15,
        paddingHorizontal: wp(4),
        paddingVertical: hp(1),
        marginHorizontal: wp(1),
        borderWidth: 1,
        borderColor: isSelected ? colors.themeColor : colors.lightgreay,
      }}>
      <Image
        source={image}
        tintColor={isSelected ? colors.white : colors.txtGrey1}
        style={{
          height: fp(3.2),
          width: fp(3.2),
          marginBottom: hp(1),
          resizeMode: 'contain',
          alignSelf: isRTL ? 'flex-end' : 'flex-start',
        }}
      />

      <Text
        style={{
          fontSize: 18,
          fontFamily: Fonts.regular,
          color: isSelected ? '#fff' : colors.txtGrey1,
        }}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default BookYourTutor;
