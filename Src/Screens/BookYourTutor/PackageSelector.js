import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Pressable,
} from 'react-native';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {updatePackage} from '../../Redux/Slices/Student/TutorBookingSlice';
import {useDispatch} from 'react-redux';
import {responsiveFontSize} from '../../Utils/constant';
import {
  resetSelectedSlots,
  updateTotalDays,
} from '../../Redux/Slices/Student/SlotSlice';
import TaleemBadge from '../../Components/Custom_Components/TaleemBadge';
import {tutorBookingJson} from '../../Api/Model/TutorBookingModel';

const PackageSelector = ({packageData}) => {
  console.log('🚀 ~ PackageSelector ~ packageData:', packageData);
  const dispatch = useDispatch();
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [expandedPackage, setExpandedPackage] = useState(null);

  const toggleExpand = (id, packageData) => {
    setExpandedPackage(expandedPackage === id ? null : id);

    // Trigger the callback with selected package data
    if (expandedPackage !== id) {
      dispatch(updatePackage(packageData));
      console.log('🚀 ~ toggleExpand ~ packageData:', packageData);
      tutorBookingJson.package_id = packageData?.tlm_package_type?.id;
      dispatch(
        updateTotalDays(
          packageData?.tlm_package_type?.total_days == 0
            ? 2
            : packageData?.tlm_package_type?.total_days,
        ),
      );
      dispatch(resetSelectedSlots());
    } else {
      tutorBookingJson.package_id = 0;
    }
  };

  // const selectPackage = ({id, packageData}) => {
  //   if (expandedPackage !== id) {
  //     dispatch(updatePackage(packageData));
  //   }
  // };

  return (
    <View style={styles.container}>
      <Text style={[styles.heading, {textAlign: isRTL ? 'right' : 'left'}]}>
        {t('availablePackages')}
      </Text>

      {packageData?.map(pkg => (
        <View
          key={pkg.id}
          style={[
            styles.packageWrapper,
            {
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'flex-start',
            },
          ]}>
          <Pressable
            style={[
              styles.radioContainer,
              {marginRight: isRTL ? 0 : 15, marginLeft: isRTL ? 15 : 0},
            ]}
            onPress={() => toggleExpand(pkg.id, pkg)}>
            <View
              style={[
                styles.radio,
                expandedPackage === pkg.id && styles.radioSelected,
              ]}>
              {expandedPackage === pkg.id && <View style={styles.radioInner} />}
            </View>
          </Pressable>

          <View style={styles.packageContainerWrapper}>
            <TouchableOpacity
              style={[
                styles.packageContainer,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}
              onPress={() => toggleExpand(pkg.id, pkg)}
              activeOpacity={0.7}>
              <View style={styles.packageInfo}>
                <Text
                  style={[
                    styles.packageTitle,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {t(pkg.tlm_package_type.name)}
                </Text>
                <Text style={styles.duration}>
                  {t('duration')} : {t('minimum')}{' '}
                  {pkg.tlm_package_type.min_hour > 1
                    ? pkg.tlm_package_type.min_hour + ' ' + t('hours')
                    : pkg.tlm_package_type.min_hour + ' ' + t('hour')}
                  {/* -{' '} */}
                  {/* {pkg.tlm_package_type.max_hour} {t('hr')} */}
                  {/* {t('minHour')}: {pkg.tlm_package_type.min_hour} -{' '}
                  {t('maxHour')}: {pkg.tlm_package_type.max_hour} */}
                </Text>
                {pkg.tlm_package_type?.discount > 0 && (
                  <View style={{}}>
                    <TaleemBadge
                      text={`${pkg.tlm_package_type?.discount}% off`}
                      isRTL={isRTL}
                    />
                  </View>
                )}
              </View>

              <Image
                source={
                  expandedPackage === pkg.id
                    ? icons.upArrow
                    : icons.downArrowBlack
                }
                style={styles.arrow}
                resizeMode="contain"
              />
            </TouchableOpacity>

            {expandedPackage === pkg.id && (
              <View style={styles.instructionsContainer}>
                <Text style={styles.instructionsHeading}>
                  {t('instructionsHeading')}
                </Text>
                <Text style={styles.instructions}>
                  {pkg?.tlm_package_type?.description || ''}
                  {/* Please select at least{' '}
                  {pkg.tlm_package_type?.min_hour > 0
                    ? `${pkg.tlm_package_type.min_hour} hours`
                    : `${pkg.tlm_package_type.min_hour} hour`}{' '}
                  when booking your tutoring package. */}
                </Text>
              </View>
            )}
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  heading: {
    fontSize: responsiveFontSize(16),
    fontFamily: Fonts.semiBold,
    marginBottom: 20,
    color: '#000',
  },
  packageWrapper: {
    marginBottom: 12,
  },
  radioContainer: {
    marginTop: 15,
  },
  radio: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#666',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioSelected: {
    borderColor: colors.themeColor,
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.themeColor,
  },
  packageContainerWrapper: {
    flex: 1,
  },
  packageContainer: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.txtGrey,
  },
  packageInfo: {
    flex: 1,
  },
  packageTitle: {
    fontSize: 18,
    fontFamily: Fonts.medium,
    color: '#000',
    marginBottom: 4,
  },
  duration: {
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    color: '#666',
    marginTop: hp(0.5),
  },
  instructionsContainer: {
    paddingVertical: 12,
    paddingHorizontal: 5,
  },
  instructionsHeading: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: '#000',
  },
  instructions: {
    fontSize: 14,
    color: colors.txtGrey1,
    marginTop: hp(0.8),
    fontFamily: Fonts.regular,
    lineHeight: hp(2),
    fontStyle: 'italic',
  },
  arrow: {
    width: 24,
    height: 24,
  },
});

export default PackageSelector;
