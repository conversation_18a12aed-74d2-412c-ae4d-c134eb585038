import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';

const styles = StyleSheet.create({
  headerContainer: {
    backgroundColor: colors.themeColor,
    paddingBottom: 20,
    paddingHorizontal: 5,
  },
  sectionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  sectionTitle: {
    fontSize: fp(2),
    color: colors.black,
    fontFamily: Fonts.semiBold,
    marginBottom: hp(1.4),
  },
  row: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  sessionButton: {
    width: wp(43.5),
    paddingVertical: 15,
    paddingHorizontal: wp(4),
    marginHorizontal: wp(1),
    borderRadius: 10,
    // alignItems: 'center',
  },
  sessionButtonText: {
    fontSize: 16,
    fontFamily: Fonts.semiBold,
  },
  datePicker: {
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 10,
    justifyContent: 'space-between',
  },
  MeetingPoint: {
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 10,
    gap: 10,
  },
  dateText: {
    fontSize: fp(1.8),
    fontFamily: Fonts.regular,
    color: colors.txtGrey1,
    width: wp(75),
  },
  calendarIcon: {
    height: 20,
    width: 20,
    tintColor: colors.themeColor,
  },
  instructionsInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 15,
    // marginHorizontal: 20,
    // marginVertical: 4,
    fontSize: fp(1.8),
    color: colors.txtGrey1,
    lineHeight: hp(2.4),
    fontFamily: Fonts.medium,
  },
  totalAmountContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  totalAmountText: {
    fontSize: 16,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  totalAmountValue: {
    fontSize: 16,
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  bookNowButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 15,
    borderRadius: 10,
    marginHorizontal: 20,
    alignItems: 'center',
  },
  bookNowButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  packageOption: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 15,
  },
  packageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.txtBlack,
  },
  packageDropdown: {
    marginTop: 10,
  },
  packageDuration: {
    fontSize: 14,
    color: colors.txtGrey1,
  },
  packageInstructions: {
    fontSize: 12,
    color: colors.txtGrey1,
  },
  collapsibleContainer: {
    marginVertical: 5,
  },
  datePicker: {
    // flexDirection: 'column',
    // alignItems: 'stretch',
    // paddingVertical: 10,
    // paddingHorizontal: 15,
    // borderWidth: 1,
    // borderColor: '#ddd',
    // borderRadius: 10,
    // // backgroundColor: '#F9F9F9',
    // marginBottom: 5,
  },

  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
  },
  timeText: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: colors.black,
    marginTop: 5,
  },
  arrowIcon: {
    tintColor: colors.black,
    marginLeft: 7,
  },
  durationText: {
    fontSize: fp(1.8),
    color: colors.black,
    paddingHorizontal: 14,
    paddingVertical: 4,
    borderRadius: 10,
    textAlignVertical: 'center',
    textAlign: 'center',
    // marginRight: 5,
    fontFamily: Fonts.regular,
  },
  selectHoursButton: {
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: wp(3),
    backgroundColor: colors.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.lightgreay,
  },
  noSlotsText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: colors.txtGrey1,
  },
  gradientContainer: {
    borderRadius: 20,
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.5),
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default styles;
