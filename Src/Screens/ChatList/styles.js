import {StyleSheet} from 'react-native';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    marginHorizontal: fp(2),
    marginVertical: fp(3),
  },
  row: {
    alignItems: 'center',
    marginBottom: fp(1),
    padding: fp(1),
    borderWidth: 0.5,
    borderRadius: 15,
    borderColor: colors.lightGrey,
  },
  img: {
    height: fp(5),
    width: fp(5),
    borderRadius: 30,
  },
  innerRow: {
    justifyContent: 'space-between',
    alignItems: 'center',
    width: fp(35),
  },
  name: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: colors.black,
  },

  msg: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.4),
    color: colors.searchGray,
  },
  noDataFound: {
    textAlign: 'center',
    fontSize: fp(1.6),
    color: colors.blackSkatch,
    fontFamily: Fonts.medium,
  },
});

export default styles;
