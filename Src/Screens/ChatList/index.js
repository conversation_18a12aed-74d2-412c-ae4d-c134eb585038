import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  Image,
  FlatList,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import styles from './styles';
import {StatusContainer} from '../../Components/StatusBar';
import icons from '../../Utils/icons';
import {useSelector} from 'react-redux';
import {AppHeader} from '../../Components/Header';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {useTranslation} from 'react-i18next';
import {useSocket} from '../../Helper/SocketHelper/SocketProvider';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import colors from '../../Utils/colors';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';

const ChatList = ({navigation}) => {
  const {socket, emit, on, off, onAny} = useSocket();
  const userData = useSelector(state => state?.auth);
  const s_id = userData?.userId;
  const [chatList, setChatList] = useState([]);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  // Function to fetch chat history
  const fetchChatHistory = () => {
    emit('getChatHistory', {s_id});
  };

  useEffect(() => {
    // Initial setup when component mounts
    emit('userConnected', {s_id});
    fetchChatHistory();

    // Socket listeners
    on('gotcha', data => {
      console.log('🚀 ~ gotcha:', data);
    });
    on('getChatHistory', chat => {
      let allChat = chat?.result?.rows || [];
      setChatList(allChat);
      console.log('🚀 ~ useEffect ~ allChat:', allChat);
    });

    on('error_callback', data => {
      console.log('Error:', data);
    });

    // Cleanup on unmount
    return () => {
      emit('disconnectChat');
      off('getChatHistory'); // Remove listener to avoid duplicates
      off('error_callback');
    };
  }, [s_id, emit, on, off]); // Dependencies ensure re-run if these change

  // Listen for screen focus to refresh chat history
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchChatHistory(); // Refetch chat history when screen is focused
    });

    return unsubscribe; // Cleanup listener on unmount
  }, [navigation, emit]);

  // Optional: Listen for real-time chat updates
  useEffect(() => {
    on('newMessage', data => {
      // Update chatList with new message or unread count
      setChatList(prevChatList => {
        const updatedChatList = prevChatList.map(chat => {
          if (chat.chatPerson.id === data.senderId) {
            return {
              ...chat,
              msg: data.message,
              unreadCount: chat.unreadCount + 1, // Increment unread count
              createdAt: data.timestamp,
            };
          }
          return chat;
        });
        return updatedChatList;
      });
    });

    return () => {
      off('newMessage');
    };
  }, [on, off]);

  const renderItem = ({item, index}) => {
    const created_at = new Date(item?.createdAt);
    const time = created_at?.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
    });
    return (
      <TouchableOpacity
        onPress={() => navigation.navigate('ChatScreen', {item})}
        style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Image
          source={{
            uri: item?.chatPerson?.image
              ? IMAGE_BASE_URL + item?.chatPerson?.image
              : DUMMY_USER_IMG,
          }}
          style={styles.img}
        />
        <View style={{marginLeft: 9}}>
          <View
            style={[
              styles.innerRow,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Text style={[styles.name, {marginRight: isRTL ? 10 : 0}]}>
              {item?.chatPerson?.name}
            </Text>
            <Text style={styles.msg}>{time}</Text>
          </View>

          <View
            style={[
              styles.innerRow,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Text
              style={[
                styles.msg,
                {
                  textAlign: isRTL ? 'right' : 'left',
                  marginRight: isRTL ? 10 : 0,
                },
              ]}>
              {item?.msg}
            </Text>
            {item?.unreadCount > 0 && (
              <View
                style={applyShadowStyleIos({
                  backgroundColor: colors.themeBackground,
                  width: fp(2),
                  height: fp(2),
                  borderRadius: fp(2),
                  justifyContent: 'center',
                  alignItems: 'center',
                  alignSelf: 'center',
                  marginTop: hp(0.2),
                })}>
                <Text
                  style={{
                    color: 'white',
                    fontSize: fp(1.3),
                    fontFamily: Fonts.medium,
                  }}>
                  {item?.unreadCount}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('chats')}
        titleStyle={{fontWeight: '600'}}
      />
      <View style={styles.content}>
        <FlatList
          data={chatList}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.chatPerson.id}-${index}`}
          ListEmptyComponent={
            <View>
              <Text style={styles.noDataFound}>{t('no_chats_found')}</Text>
            </View>
          }
        />
      </View>
    </SafeAreaView>
  );
};

export default ChatList;
