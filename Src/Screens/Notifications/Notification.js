import {<PERSON><PERSON><PERSON>, SafeAreaView, StyleSheet, Text, View} from 'react-native';
import React, {useEffect} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {AppHeader} from '../../Components/Header';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {
  useGetNotificationListQuery,
  useMarkAllNotiAsReadMutation,
} from '../../Api/ApiSlice';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import {Fonts} from '../../Utils/Fonts';
import moment from 'moment';
import {PrimaryButton} from '../../Components/CustomButton';
import {useTranslation} from 'react-i18next';
import {useSelector} from 'react-redux';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {showToast} from '../../Components/ToastHelper'; // Assuming you have this utility

const Notification = ({navigation}) => {
  const {
    data: notificationRes,
    isLoading: isNotificationLoading,
    refetch,
  } = useGetNotificationListQuery();

  const [markAllAsRead, {isLoading: isMarkingAllRead, error: markAllError}] =
    useMarkAllNotiAsReadMutation();

  const {t} = useTranslation();
  const userType = useSelector(state => state.auth.user_type);

  // Refetch notifications when the component mounts
  useEffect(() => {
    refetch();
  }, [refetch]);

  // Function to handle marking all as read
  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllAsRead().unwrap();
      if (response?.status) {
        // showToast('success', t('allNotificationsMarkedAsRead')); // Add translation key
        refetch(); // Refetch notifications to update the UI
      }
    } catch (error) {
      console.log('Error marking all as read:', error);
      showToast('error', t('Failed to mark all as read.')); // Add translation key
    }
  };

  // Optional: Automatically mark all as read on mount
  useEffect(() => {
    handleMarkAllAsRead();
  }, []); // Remove this if you want manual control via a button

  function renderNotification({item}) {
    return (
      <View
        style={[
          applyShadowStyleIos({
            borderWidth: fp(0.1),
            width: wp(90),
            borderColor: colors.lightisGrey,
            paddingHorizontal: wp(2),
            paddingVertical: hp(1.2),
            borderRadius: fp(1),
            alignSelf: 'center',
            backgroundColor: colors.white,
            marginVertical: hp(0.8),
          }),
        ]}>
        <Text
          style={{
            fontSize: fp(2),
            fontFamily: Fonts.medium,
            color: colors.black,
            paddingVertical: hp(0.6),
            lineHeight: hp(2.4),
          }}>
          {item?.title}
        </Text>
        <Text
          style={{
            fontSize: fp(1.6),
            fontFamily: Fonts.medium,
            color: colors.black,
            lineHeight: hp(2),
          }}>
          {item?.notification}
        </Text>
        <Text
          style={{
            fontSize: fp(1.4),
            fontFamily: Fonts.medium,
            color: colors.greyLight,
            lineHeight: hp(2),
            marginTop: hp(1),
          }}>
          {moment(item?.createdAt).format('DD MMM, YYYY, hh:mm A')}
        </Text>
        {userType === '1' &&
          item?.classification_type === 'booking_invitation' && (
            <View
              style={{
                alignSelf: 'flex-start',
                marginTop: hp(2),
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <PrimaryButton
                title={t('accept')}
                style={{width: wp(30), height: fp(4)}}
                textStyle={{
                  fontSize: fp(1.8),
                  fontFamily: Fonts.semiBold,
                }}
                onPress={() => {
                  navigation.navigate('BookYourTutorConfirmation', {
                    bookingId: item?.reference_id,
                  });
                }}
              />
            </View>
          )}
        {userType === '3' &&
          item?.classification_type === 'booking_confirmation_tutor' && (
            <View
              style={{
                alignSelf: 'flex-start',
                marginTop: hp(2),
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <PrimaryButton
                title={t('View')}
                style={{width: wp(30), height: fp(4)}}
                textStyle={{
                  fontSize: fp(1.8),
                  fontFamily: Fonts.semiBold,
                }}
                onPress={() => {
                  navigation.navigate('BookingDetailsTutor', {
                    bookingId: item?.reference_id,
                  });
                }}
              />
            </View>
          )}
      </View>
    );
  }

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
      <StatusContainer color={colors.white} />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('notifications')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />
      <TaleemLoader isLoading={isNotificationLoading || isMarkingAllRead} />

      {/* Optional: Add a button to manually mark all as read */}
      {/* <View style={{paddingHorizontal: wp(5), marginVertical: hp(1)}}>
        <PrimaryButton
          title={t('markAllAsRead')}
          style={{width: wp(40), height: fp(4.5)}}
          textStyle={{fontSize: fp(1.8), fontFamily: Fonts.semiBold}}
          onPress={handleMarkAllAsRead}
          loading={isMarkingAllRead}
        />
      </View> */}

      <FlatList
        data={notificationRes?.data?.rows}
        renderItem={renderNotification}
        keyExtractor={item => item.id.toString()} // Add keyExtractor for better performance
        ListEmptyComponent={
          !isNotificationLoading && (
            <Text
              style={{
                textAlign: 'center',
                fontSize: fp(1.8),
                color: colors.greyLight,
                marginTop: hp(2),
                fontFamily: Fonts.medium,
              }}>
              {t('no_notification_found')}
            </Text>
          )
        }
      />
    </SafeAreaView>
  );
};

export default Notification;

const styles = StyleSheet.create({});
