import {
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import GradBadge from '../../Components/GradBadge/GradBadge';
import {Fonts} from '../../Utils/Fonts';
import {useGetTutorDocumentsQuery} from '../../Api/ApiSlice';
import {useTranslation} from 'react-i18next';

const TutorDocuments = ({navigation}) => {
  const {
    data: TutorDocuments,
    isLoading,
    refetch,
  } = useGetTutorDocumentsQuery();

  useEffect(() => {
    refetch();
  }, []);

  console.log('🚀 ~ TutorDocuments ~ TutorDocuments:', TutorDocuments);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const renderCard = (title, doc) => {
    const docUri = {
      uri: doc,
      type:
        doc.split('.').pop().toLowerCase() == 'pdf'
          ? 'application/pdf'
          : 'image',
    };
    return (
      <TouchableOpacity
        onPress={() => navigation.navigate('DocViewer', {doc: docUri})}
        style={{
          borderColor: colors.lightGrey,
          borderWidth: fp(0.1),
          width: wp(90),
          paddingVertical: hp(2),
          borderRadius: fp(2),
          paddingHorizontal: wp(2),
          marginBottom: hp(2),
        }}>
        <View style={{marginLeft: wp(3)}}>
          <GradBadge text={t(title)} type="big" isRTL={isRTL} />
        </View>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            marginTop: hp(2),
            justifyContent: 'space-between',
          }}>
          <Text
            style={{
              fontFamily: Fonts.medium,
              fontSize: fp(2),
              marginLeft: wp(3),
              color: colors.black,
            }}>
            {doc?.split('-')?.pop()}
          </Text>
          {/* <TouchableOpacity>
            <Image
              source={icons.threeDots}
              style={{
                height: fp(4),
                width: fp(4),
                alignSelf: 'center',
                alignItems: 'center',
              }}
            />
          </TouchableOpacity> */}
        </View>
      </TouchableOpacity>
    );
  };
  console.log(TutorDocuments?.data, 'asdfasd');
  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('document')} />
      <View
        style={{
          flex: 1,
          // justifyContent: 'center',
          alignItems: 'center',
          marginTop: hp(2),
        }}>
        {TutorDocuments?.data?.academic_document &&
          renderCard(
            'academic_document',
            TutorDocuments?.data?.academic_document,
          )}
        {TutorDocuments?.data?.address_proof &&
          renderCard('address_proof', TutorDocuments?.data?.address_proof)}

        {TutorDocuments?.data?.id_photo &&
          renderCard('id_photo', TutorDocuments?.data?.id_photo)}
        {TutorDocuments?.data?.other_document &&
          renderCard('other_document', TutorDocuments?.data?.other_document)}
      </View>
    </SafeAreaView>
  );
};

export default TutorDocuments;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
});
