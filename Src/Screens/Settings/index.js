import React from 'react';
import {View, Text, SafeAreaView} from 'react-native';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';

const SettingsScreen = () => {
  const {t} = useTranslation();

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={t('notifications')}
        backIcon={icons.backbtn}
        isBackBtn
        isWhite={true}
      />
    </SafeAreaView>
  );
};

export default SettingsScreen;
