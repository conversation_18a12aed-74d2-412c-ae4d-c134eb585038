import {
  View,
  Text,
  Platform,
  SafeAreaView,
  TouchableOpacity,
  Image,
  FlatList,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {AppHeader} from '../../Components/Header';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import styles from './styles';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import LinearGradient from 'react-native-linear-gradient';
import {PrimaryButton} from '../../Components/CustomButton';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {
  useGetAllConnectedProfilesAfterLoginQuery,
  useGetEarningsQuery,
  useGetTotalErningsHistoryQuery,
  useRequesPayoutTutorMutation,
} from '../../Api/ApiSlice';
import DateTimePicker from '@react-native-community/datetimepicker';
import moment from 'moment';
import {Fonts} from '../../Utils/Fonts';
import TaleemEventCalendar from '../../Components/Calendar/TaleemEventCalendar';
import TaleemHeader from '../../Components/TaleemHeader/TaleemHeader';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {showToast} from '../../Components/ToastHelper';

const Earnings = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [showPicker, setShowPicker] = useState(false);
  const [earningDate, setEarningDate] = useState(new Date());
  const [history, setHistory] = useState([]);
  const today = new Date();

  const onChange = useCallback(selectedDate => {
    setShowPicker(false);
    const dateString = selectedDate?.dateString;
    // if (event.type === 'set' && selectedDate) {
    setEarningDate(dateString);
    // }
  }, []);
  const [addRequesPayout] = useRequesPayoutTutorMutation();

  const {data: earningData, error, isLoading, refetch} = useGetEarningsQuery();
  const {data: earningHistoryData} = useGetTotalErningsHistoryQuery({
    date: earningDate,
    page: 0,
    limit: 1000,
  });
  useEffect(() => {
    console.log('earningHistoryData', earningDate, earningHistoryData);
    setHistory(earningHistoryData?.data?.rows || []);
  }, [earningDate, earningHistoryData]);
  const [isWithdrawLoading, setIsWithdrawLoading] = useState(false);
  const handlePayoutRequest = async () => {
    console.log('handelapyout');
    setIsWithdrawLoading(true);
    try {
      // Check if earningData exists and has valid totalEarnings

      // Check if total earnings is sufficient
      if (earningData.data.totalEarnings <= 0) {
        showToast(
          'error',
          t('you_dont_have_sufficient_balance_for_payout'),
          'bottom',
          isRTL,
        );
        setIsWithdrawLoading(false);
        return;
      }

      // Prepare payout request payload
      const payload = {
        withdrawalAmount: earningData?.data?.totalEarnings,
      };

      // Make the payout request
      const response = await addRequesPayout(payload)
        .unwrap()
        .then(() => {
          refetch();
          setIsWithdrawLoading(false);
        });

      // Show success message and handle navigation
      showToast('success', t('requestSuccess'), 'bottom', isRTL);
      // navigation.goBack();
    } catch (error) {
      // Handle specific error cases
      setIsWithdrawLoading(false);
      console.log(error?.data?.message, 'error in handlePayoutout');

      // Show error toast
      showToast('error', error?.data?.message, 'bottom', isRTL);
    }
  };

  const renderItem = ({item}) => {
    return (
      <LinearGradient
        colors={['#C6FFC9', '#D4EBFF']} // Gradient colors
        style={styles.gradientBorder}>
        <TouchableOpacity
          style={[
            styles.itemCard,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <View
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
            }}>
            <View
              style={[
                styles.imageView,
                {
                  backgroundColor:
                    item?.type == 'debit' ? colors.lightRed : 'lightblue',
                },
              ]}>
              <Text style={[styles.balanceTxt]}>
                {item?.type == 'debit' ? 'W' : 'B'}
              </Text>
            </View>
            <View style={{width: wp(60)}}>
              <Text style={styles.withdrawl}>{item?.description}</Text>
              <Text style={[styles.withdrawl, {fontSize: fp(1.4)}]}>
                {moment(item?.createdAt).format('DD MMM YYYY, hh:mm A')}
              </Text>
            </View>
          </View>
          <View>
            <Text
              style={[
                styles.listAmount,
                {
                  color: item?.type == 'debit' ? colors.red : colors.green,
                },
              ]}>
              {item?.type == 'debit' ? `-${item.amount}` : `+${item.amount}`}
            </Text>
            <Text style={styles.withdrawl}>{item.currency}</Text>
          </View>
        </TouchableOpacity>
      </LinearGradient>
    );
  };
  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      {/* <AppHeader backIcon={icons.backbtn} isBackBtn title={t('earning')} /> */}
      <TaleemHeader />
      <View style={styles.body}>
        <TaleemLoader isLoading={isWithdrawLoading} />
        <LinearGradient
          style={{borderRadius: 10}}
          start={{
            x: Math.sin((0 * Math.PI) / 180),
            y: -Math.cos((100 * Math.PI) / 180),
          }}
          end={{
            x: Math.sin((50 * Math.PI) / 180),
            y: -Math.cos((200 * Math.PI) / 180),
          }}
          colors={['#C6FFC9', '#D4EBFF']}>
          <View style={[styles.card]}>
            <View
              style={[
                styles.cardHeader,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Text style={styles.balanceTxt}>{t('currentBalance')}</Text>
              <TouchableOpacity
                onPress={() => setShowPicker(true)}
                style={styles.btn}>
                <Text style={styles.balanceTxt}>
                  {/* {moment(today?.specific_date).format('DD MMM YYYY')} */}
                  {moment(today).format('DD MMM YYYY') ==
                  moment(earningDate).format('DD MMM YYYY')
                    ? t('today')
                    : moment(earningDate).format('DD MMM YYYY')}
                </Text>
                <Image source={icons.downchevron} style={{left: 8}} />
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: 'baseline',
              }}>
              <Text style={styles.amount}>
                {earningData?.data?.totalEarnings}
              </Text>
              <Text style={[styles.balanceTxt, {left: 8}]}>{'QAR'}</Text>
            </View>
          </View>
        </LinearGradient>
        <View
          style={[
            styles.cardHeader,
            {
              marginVertical: fp(2),
              flexDirection: isRTL ? 'row-reverse' : 'row',
            },
          ]}>
          <PrimaryButton
            style={{width: '49%', height: fp(6)}}
            title={t('request_payout')}
            // onPress={() => navigation.navigate('Withdrawal')}
            onPress={() => handlePayoutRequest()}
          />
          <PrimaryButton
            style={styles.btn2}
            textStyle={styles.btnTxt}
            title={t('addAccount')}
            onPress={() => navigation.navigate('AddBankAccount')}
          />
        </View>
        <View>
          <Text
            style={[
              styles.transactionTxt,
              {textAlign: isRTL ? 'right' : 'left'},
            ]}>
            {t('transaction_history')}
          </Text>
          <FlatList
            data={history}
            renderItem={renderItem}
            contentContainerStyle={{marginTop: fp(2), paddingBottom: hp(30)}}
            ListEmptyComponent={
              <View>
                <Text
                  style={{
                    textAlign: 'center',
                    color: colors.black,
                    fontFamily: Fonts.medium,
                    marginTop: hp(1),
                  }}>
                  {t('no_transaction_history')}
                </Text>
              </View>
            }
          />
        </View>
        {/* <Modal
          transparent
          visible={showPicker}
          animationType="fade"
          onRequestClose={() => setShowPicker(false)}
          style={{alignItems: 'center', justifyContent: 'center'}}>
          <TouchableWithoutFeedback onPress={() => setShowPicker(false)}>
            <View style={styles.calender}>
              <View
                style={{
                  backgroundColor: colors.white,
                  borderRadius: 10,
                  overflow: 'hidden',
                }}>
                {showPicker && (
                  <DateTimePicker
                    value={earningDate}
                    mode="date"
                    display={Platform.OS === 'ios' ? 'inline' : 'default'} // Ensures compatibility
                    onChange={onChange}
                    // minimumDate={today}
                  />
                )}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal> */}

        <Modal
          transparent
          visible={showPicker}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <TouchableWithoutFeedback onPress={() => setShowPicker(false)}>
            <View style={styles.calender}>
              <TaleemEventCalendar
                selectedDate={earningDate}
                handleDateSelect={onChange}
                // isLoading={isCalendarLoading}
                isShowAllInstructions={false}
                markedDates={{
                  [earningDate]: {
                    selected: true,
                    disableTouchEvent: true,
                    selectedDotColor: 'orange',
                  },
                }}
                isYellowDotInstruction={false}
              />
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </View>
    </SafeAreaView>
  );
};

export default Earnings;
