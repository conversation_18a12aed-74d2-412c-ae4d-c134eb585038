import {Image, StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useRef} from 'react';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import icons from '../../Utils/icons';
import LottieView from 'lottie-react-native';
import {PrimaryButton} from '../../Components/CustomButton';
import {Fonts} from '../../Utils/Fonts';
import {useTranslation} from 'react-i18next';

const PaymentSuccessfull = ({navigation}) => {
  //   const animationRef = useRef(null);
  const {t} = useTranslation();
  useEffect(() => {
    // animationRef.current?.play();
    // Or set a specific startFrame and endFrame with:
    // animationRef.current?.play(30, 120);
  }, []);
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: colors.themeColor,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <LottieView
        // ref={animationRef}
        source={require('../../Assets/Lottie/successAnimation.json')}
        style={{
          alignSelf: 'center',
          justifyContent: 'center',
          height: fp(30),
          width: fp(34),
        }}
        autoPlay={true}
        loop={false}
      />
      <Text
        style={{
          color: colors.white,
          fontFamily: Fonts.semiBold,
          fontSize: fp(2.2),
          top: -30,
        }}>
        {t('bookingSuccessfull')}
      </Text>
      <View style={{position: 'absolute', bottom: hp(2), width: wp(100)}}>
        <PrimaryButton
          onPress={() => {
            navigation?.navigate('My Class');
          }}
          title={t('viewBooking')}
          style={{
            backgroundColor: colors.white,
            marginBottom: 15,
          }}
          textStyle={{
            fontSize: fp(1.8),
            color: colors.black,
            fontFamily: Fonts.medium,
          }}
        />
        <PrimaryButton
          onPress={() => {
            navigation?.navigate('Home');
          }}
          title={t('exploreTutor')}
          style={{
            backgroundColor: colors.themeColor,
            marginBottom: 15,
          }}
          textStyle={{
            fontSize: fp(1.8),
            color: colors.white,
            fontFamily: Fonts.medium,
          }}
        />
      </View>
    </View>
  );
};

export default PaymentSuccessfull;

const styles = StyleSheet.create({});
