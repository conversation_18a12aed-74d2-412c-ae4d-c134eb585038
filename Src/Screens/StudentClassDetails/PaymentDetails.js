import {View, Text} from 'react-native';
import React from 'react';
import {styles} from './styles';
import {useTranslation} from 'react-i18next';
import {hp, wp} from '../../Helper/ResponsiveDimensions';
import {formatDateStringToPaymentUI} from '../../Helper/DateHelpers/DateHelpers';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import moment from 'moment';

const PaymentDetails = ({data}) => {
  console.log('🚀 ~ PaymentDetails ~ data:', data);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const DetialsSection = ({sectionTitle, sectionDetail}) => {
    return (
      <View
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginVertical: hp(1),
        }}>
        <Text style={[styles.package, {textAlign: isRTL ? 'right' : 'left'}]}>
          {sectionTitle}
        </Text>
        <Text style={[styles.package, {textAlign: isRTL ? 'right' : 'left'}]}>
          {sectionDetail}
        </Text>
      </View>
    );
  };
  return (
    <View style={{marginTop: hp(1)}}>
      <Text
        style={[
          styles.blackTxt,
          {
            left: isRTL ? 0 : 16,
            marginRight: isRTL ? 16 : 0,
            textAlign: isRTL ? 'right' : 'left',
          },
        ]}>
        {t('payment_details')}
      </Text>
      <View style={styles.card}>
        <DetialsSection
          sectionTitle={t('booking_id')}
          sectionDetail={`#${data?.id}`}
        />
        <DetialsSection
          sectionTitle={t('date')}
          sectionDetail={moment(data?.package_start_date).format('DD MMM YYYY')}
        />
        <DetialsSection
          sectionTitle={t('payment_mode')}
          sectionDetail={data?.payment_mode || 'DEBIT CARD'}
        />
        <DetialsSection sectionTitle={t('status')} sectionDetail={'Paid'} />
      </View>
    </View>
  );
};

export default PaymentDetails;
