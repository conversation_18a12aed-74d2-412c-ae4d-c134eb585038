import {
  View,
  Text,
  SafeAreaView,
  Image,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {styles} from './styles';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {useRoute} from '@react-navigation/native';
import colors from '../../Utils/colors';
import Schedule from './Schedule';
import PaymentDetails from './PaymentDetails';
import TutorDetails from '../../Components/MyClass/TutorDetails';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {PrimaryButton} from '../../Components/CustomButton';
import {
  useGetSudendtBookingDetailsQuery,
  useGetTutorClassDetailsQuery,
  useGetTutorOpenSessionDetailsByIdQuery,
  useGetTutorOtpenSessionDetailsByIdQuery,
} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/geBaseUrl';
import {
  convertToDMY,
  isShowJoinButton,
} from '../../Helper/DateHelpers/DateHelpers';
import {useSelector} from 'react-redux';
import moment from 'moment';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import MyClassHelper from '../../Helper/MyClassHelper/MyClassHelper';
import {openDirections} from '../../Helper/GoogleMapsHelpers';
import {Fonts} from '../../Utils/Fonts';

const StudentClassDetails = ({navigation}) => {
  const route = useRoute();
  const {item, type} = route?.params || {};
  console.log('🚀 ~ item:', item, type);
  const {t, i18n} = useTranslation();
  const {user_type} = useSelector(state => state?.auth);
  const [instructions, setInstructions] = useState('');
  const [data, setData] = useState();
  const isRTL = i18n.language === 'ar';

  const {
    data: studentBookingDetails,
    error,
    isLoading,
  } = useGetSudendtBookingDetailsQuery(item?.booking_id);

  const {data: tutorDetails} = useGetTutorClassDetailsQuery(item?.booking_id);

  const {data: tutorOpenBookingdetails} =
    useGetTutorOpenSessionDetailsByIdQuery(item?.booking_id);
  console.log('🚀 ~ tutorOpenBookingdetails:', tutorOpenBookingdetails);

  useEffect(() => {
    if (tutorOpenBookingdetails) {
      setData(tutorOpenBookingdetails?.data);
    }
  }, [studentBookingDetails, tutorDetails, tutorOpenBookingdetails]);

  function handleNavigateToLocPress() {
    openDirections(
      studentBookingDetails?.data?.latitude || tutorDetails?.data?.latitude,
      studentBookingDetails?.data?.longitude || tutorDetails?.data?.longitude,
    );
  }
  console.log(
    '🚀 ~ studentBookingDetails?.data?.tutor:',
    studentBookingDetails?.data?.tutor,
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('classDetails')} />
      <View style={{backgroundColor: colors.themeColor}}>
        <View style={styles.header}>
          <Text
            style={[styles.headigTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
            {item?.tlm_booking?.class_title || data?.title}
          </Text>
        </View>
        <View
          style={[
            styles.viewS,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Image source={icons.academicIcon} />
          <Text style={styles.txtCategory}>{`${t('category_label')} : ${
            item?.tlm_booking?.tutor_rc_academic_id
              ? t('academic')
              : item?.tlm_booking?.tutor_rc_recreational_id
              ? t('recreational')
              : t('courses')
          }`}</Text>
        </View>
        <View
          style={[
            styles.viewS,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Image
            source={icons.calanderYellow}
            style={{tintColor: colors.white}}
          />
          <Text
            style={[
              styles.txtCategory,
              {textAlign: isRTL ? 'right' : 'left'},
            ]}>{`${t('session')} : ${
            (item?.tlm_booking?.tlm_sessions_type?.name
              ? item?.tlm_booking?.tlm_sessions_type?.name
              : tutorDetails?.data?.tlm_class_type?.name) ||
            data?.tlm_sessions_type?.name
          }`}</Text>
        </View>
      </View>
      <ScrollView style={styles.innerContainer}>
        <View style={styles.card}>
          <View
            style={[
              styles.cardRow,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image source={icons.calanderYellow} />
            <Text style={styles.packageTxt}>
              {`${t('packageStartingDate')} :`}
              <Text style={styles.date}>
                {moment(
                  item?.date ||
                    tutorDetails?.data?.package_start_date ||
                    data?.start_date,
                ).format('DD MMM YYYY')}
              </Text>
            </Text>
          </View>
          {/* <View
            style={[
              styles.cardRow,
              {marginTop: 10, flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image source={icons.language} />
            <Text style={styles.packageTxt}>
              {`${t('language')} :`}
              <Text style={styles.date}>{t('englishL')}</Text>
            </Text>
          </View> */}
          <View
            style={[
              styles.cardBody,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <View style={{width: '48%', padding: 10}}>
              <Text
                style={[styles.package, {textAlign: isRTL ? 'right' : 'left'}]}>
                {t('package')}
              </Text>
              <Text
                style={[styles.details, {textAlign: isRTL ? 'right' : 'left'}]}>
                {studentBookingDetails?.data?.tlm_package_type?.name ||
                  tutorDetails?.data?.tlm_package_type?.name ||
                  'N/A'}
              </Text>
            </View>
            <View
              style={[
                styles.box,
                {
                  borderLeftWidth: isRTL ? 0 : 1,
                  borderRightWidth: isRTL ? 1 : 0,
                  borderColor: colors.txtGrey,
                },
              ]}>
              <Text
                style={[styles.package, {textAlign: isRTL ? 'right' : 'left'}]}>
                {t('class_type')}
              </Text>
              <Text
                style={[styles.details, {textAlign: isRTL ? 'right' : 'left'}]}>
                {studentBookingDetails?.data?.tlm_class_type?.name ||
                  tutorDetails?.data?.tlm_class_type?.name ||
                  data?.tlm_sessions_type?.name}
              </Text>
            </View>
          </View>
        </View>
        {/* Schedule */}
        <Schedule
          details={studentBookingDetails?.data || tutorDetails?.data || data}
          data={
            studentBookingDetails?.data?.tlm_booking_schedules ||
            tutorDetails?.data?.tlm_booking_schedules ||
            data?.tlm_tutor_schedules
          }
          type={type}
        />
        {/* Payment Details */}
        {user_type == '1' && type !== 'openSession' && (
          <PaymentDetails data={studentBookingDetails?.data} />
        )}
        {/* Additional Instructions */}
        {studentBookingDetails?.data?.instructions && (
          <View style={styles.sectionContainer}>
            <Text
              style={[styles.blackTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
              {t('instructions')}
            </Text>
            <TextInput
              editable={false}
              placeholder={t('instructions_placeholder')}
              style={styles.instructionsInput}
              placeholderTextColor={colors.searchGray}
              onChangeText={txt => setInstructions(txt)}
              value={studentBookingDetails?.data?.instructions}
            />
          </View>
        )}

        <View>
          {type !== 'openSession' && (
            <Text
              style={[
                styles.blackTxt,
                {
                  left: isRTL ? 0 : 16,
                  marginRight: isRTL ? 16 : 0,
                  textAlign: isRTL ? 'right' : 'left',
                },
              ]}>
              {user_type === '1' ? t('tutorDetails') : t('student_details')}
            </Text>
          )}
          {type !== 'openSession' && (
            <View style={styles.card}>
              <View
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <TutorDetails
                  onCardPress={
                    () => {}
                    // user_type == '3'
                    //   ? navigation.navigate('StudentProfileForTutor', {
                    //       studentId:
                    //         tutorDetails?.data?.tlm_booking_enrollments[0]
                    //           ?.tlm_user?.id,
                    //     })
                    //   : console.log('nothing to do')
                  }
                  imageUri={
                    // studentBookingDetails?.data?.tutor?.image ||
                    // tutorDetails?.data?.tlm_booking_enrollments[0]?.tlm_user
                    //   ?.image
                    //   ? {
                    //       uri:
                    //         IMAGE_BASE_URL +
                    //         (studentBookingDetails?.data?.tutor?.image ||
                    //           tutorDetails?.data?.tlm_booking_enrollments[0]
                    //             ?.tlm_user?.image),
                    //     }
                    //   :
                    {uri: DUMMY_USER_IMG}
                  }
                  name={
                    studentBookingDetails?.data?.tutor?.name ||
                    tutorDetails?.data?.tlm_booking_enrollments?.[0]?.tlm_user
                      ?.name
                  }
                  occupation={
                    studentBookingDetails?.data?.tutor?.tlm_tutor_profile
                      ?.expertise ||
                    tutorDetails?.data?.tlm_booking_enrollments?.[0]?.tlm_user
                      ?.tlm_student_academic_details[0]?.tlm_grade?.name
                  }
                />
                {/* <PrimaryButton title={'Chat'} /> */}
                {/* <TouchableOpacity
                  onPress={() =>
                    navigation.navigate('ChatScreen', {
                      item:
                        studentBookingDetails?.data?.tutor ||
                        tutorDetails?.data?.tlm_booking_enrollments[0]
                          ?.tlm_user,
                    })
                  }
                  style={styles.messageBtn}> */}
                <View style={{}}>
                  <PrimaryButton
                    onPress={() =>
                      navigation.navigate('ChatScreen', {
                        item:
                          studentBookingDetails?.data?.tutor ||
                          tutorDetails?.data?.tlm_booking_enrollments?.[0]
                            ?.tlm_user,
                      })
                    }
                    title={t('message_tutor')}
                    style={{
                      backgroundColor: 'transparent',
                      width: wp(28),
                      borderWidth: fp(0.1),
                      borderColor: colors.themeColor,
                      alignSelf: 'center',
                    }}
                    textStyle={{
                      fontSize: fp(1.6),
                      color: colors.themeColor,
                      fontFamily: Fonts.medium,
                    }}
                    loading={false}
                  />
                </View>
                {/* <Image source={icons.messageIcon} /> */}
                {/* </TouchableOpacity> */}
              </View>
            </View>
          )}
        </View>
        {(studentBookingDetails?.data?.address ||
          tutorDetails?.data?.address) &&
          (studentBookingDetails?.data?.tlm_class_type?.name ===
            'Face to Face' ||
            tutorDetails?.data?.tlm_class_type?.name === 'Face to Face') && (
            <View>
              <Text style={[styles.blackTxt, {left: 16}]}>
                {t('meetingPoint')}
              </Text>
              <View style={[styles.card, {padding: fp(2)}]}>
                <View style={[styles.cardRow, {flexDirection: 'row'}]}>
                  <Image
                    source={icons.locationGray}
                    style={{
                      tintColor: colors.themeColor,
                      height: fp(3),
                      width: fp(3),
                    }}
                  />
                  <View style={styles.meetingView}>
                    <Text
                      style={[
                        styles.meetingTxt,
                        {textAlign: 'center', textAlignVertical: 'center'},
                      ]}>
                      {t('meetingPoint')}
                    </Text>
                  </View>
                </View>
                <View
                  style={[
                    styles.cardRow,
                    {
                      marginTop: hp(1),
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                    },
                  ]}>
                  <Text
                    style={[
                      styles.meetingTxt,
                      {
                        color: '#737373',
                        width: wp(60),
                        fontSize: fp(1.8),
                        lineHeight: hp(2),
                      },
                    ]}>
                    Location :{' '}
                    {studentBookingDetails?.data?.address ||
                      tutorDetails?.data?.address}
                  </Text>
                  <Pressable onPress={handleNavigateToLocPress}>
                    <Image
                      source={icons.locationArrow}
                      style={{height: hp(4), width: hp(4)}}
                    />
                  </Pressable>
                </View>
              </View>
            </View>
          )}
        {/* {upcomingSlots && Object?.keys(upcomingSlots)?.length && ( */}
        {/* {isShowJoinButton(
          item?.date ||
            tutorDetails?.data?.package_start_date ||
            data?.start_date,


        )} */}
        {/* <PrimaryButton
          title={user_type == '3' ? t('start_class') : t('joinClass')}
          onPress={() =>
            navigation.navigate('ClassDetailsTwo', {
              is_tutor_available: true,
              data1: studentBookingDetails?.data,
              item: item,
            })
          }
        /> */}
        {/* )} */}
      </ScrollView>
      {/* <PrimaryButton
        title={user_type == '1' ? t('joinClass') : 'Start Class'}
        onPress={() => {
          // if (user_type == '1') {
          navigation.navigate('ClassDetailsTwo', {
            is_tutor_available: true,
            data: studentBookingDetails?.data,
            item: item,
          })
        }
      /> */}
    </SafeAreaView>
  );
};

export default StudentClassDetails;
