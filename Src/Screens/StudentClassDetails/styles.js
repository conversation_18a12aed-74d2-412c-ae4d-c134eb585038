import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  headigTxt: {
    color: colors.white,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  txtCategory: {
    left: 8,
    fontFamily: Fonts.regular,
    color: colors.white,
    fontFamily: Fonts.medium,
  },
  viewS: {
    alignItems: 'center',
    marginHorizontal: 16,
    marginVertical: 8,
  },
  innerContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  card: {
    borderWidth: 1,
    borderRadius: 8,
    borderColor: colors.txtGrey,
    marginHorizontal: 16,
    marginVertical: 10,
    padding: 10,
    backgroundColor: colors.white,
    elevation: 1,
    justifyContent: 'center',
  },
  cardRow: {
    alignItems: 'center',
  },
  packageTxt: {
    color: colors.txtGrey1,
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
    left: 8,
  },
  date: {
    color: colors.darkBlack,
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
  },
  cardBody: {
    marginTop: hp(2),
    // justifyContent: 'space-between',
  },
  package: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: '#878787',
    letterSpacing: wp(0.16),
  },
  details: {
    color: colors.black,
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    marginTop: 8,
  },
  box: {
    width: '48%',
    padding: 10,
  },
  blackTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  sectionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },

  instructionsInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 15,
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    height: fp(6),
    fontFamily: Fonts.medium,
    marginTop: fp(1.5),
  },
  messageBtn: {
    backgroundColor: colors.themeColor,
    borderRadius: 30,
    padding: 8,
    height: 30,
    width: 30,
  },
  containerDetails: {
    flex: 1,
    backgroundColor: colors.white,
  },
  cardGradient: {
    marginHorizontal: 16,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  innerHeader: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    // width: wp(85),
  },
  timeTxt: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    left: 10,
  },
  innerTxt: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.darkBlack,
    left: 8,
  },
  row: {
    marginTop: hp(1.5),
    alignItems: 'center',
  },
  mediumTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
  },
  bigTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  meetingView: {
    backgroundColor: colors.lightGreen,
    padding: 10,
    // height: fp(4),
    borderRadius: 30,
    marginLeft: 10,
    justifyContent: 'center',
  },
  meetingTxt: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.txtGrey2,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 18,
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  modalText: {
    marginBottom: 15,
    fontFamily: Fonts.regular,
    color: colors.black,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    marginHorizontal: 10,
    minWidth: 100,
  },
  cancelButton: {
    backgroundColor: colors.txtGrey,
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    color: 'black',
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    textAlign: 'center',
  },
  logoutButtonText: {
    color: 'white',
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    textAlign: 'center',
  },
});
