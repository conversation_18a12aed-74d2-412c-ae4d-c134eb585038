import {View, Text, FlatList} from 'react-native';
import React from 'react';
import {styles} from './styles';
import Accordian from '../../Components/Accordian';
import {useTranslation} from 'react-i18next';
import {useNavigation} from '@react-navigation/native';

const Schedule = ({data, details, type = ''}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigation = useNavigation();
  const allHaveOpenSessionId = data?.every(
    obj => obj?.hasOwnProperty('open_session_id') && obj?.open_session_id,
  );
  const lastItemId = data?.length > 0 ? data[data?.length - 1]?.id : null;
  return (
    <View style={{marginVertical: 10, marginHorizontal: 16}}>
      <Text style={[styles.blackTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
        {t('schedule')}
      </Text>
      <View>
        <FlatList
          data={data}
          nestedScrollEnabled
          renderItem={({item}) => {
            console.log('🚀 ~ Schedule ~ item:', item);
            if (allHaveOpenSessionId) {
              item = {
                id: item?.id,
                date: item?.specific_date,
                class_link: null,
                tlm_tutor_schedule: {
                  id: item?.id,
                  start_time: item?.start_time,
                  end_time: item?.end_time,
                  hours_duration: item?.hours_duration,
                },
              };
            }
            return (
              <Accordian
                item={item}
                onPressJoin={() =>
                  navigation.navigate('ClassDetailsTwo', {
                    item,
                    tutor: details?.tutor,
                    bookingDetails: details,
                    lastItemId,
                    type: type,
                  })
                }
                type={type}
                classType={details?.tlm_class_type?.name}
              />
            );
          }}
        />
      </View>
    </View>
  );
};

export default Schedule;
