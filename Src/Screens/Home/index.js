import React, {useEffect, useState, useCallback} from 'react';
import {
  Image,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  ActivityIndicator,
  I18nManager,
  RefreshControl,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {CommonActions, useFocusEffect} from '@react-navigation/native';
import {Title} from '../../Components/Title';
import {showToast} from '../../Components/ToastHelper';
import icons from '../../Utils/icons';
import styles from './styles';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {useDispatch, useSelector} from 'react-redux';
import {
  useGetCampaignDetailsQuery,
  useGetConnectedStudentsQuery,
  useGetStudentDetailQuery,
  useGetTutorBookingConfirmationNotificationQuery,
  useGetTutorBookingListHomeQuery,
  useLazyGetAllBookingQuery,
  useMarkActiveKidForParentMutation,
  useProfileDetailsQuery,
  useUpdateDeviceDetailsMutation,
} from '../../Api/ApiSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import TutorHomeHeader from '../../Components/HomeHeader/TutorHomeHeader';
import StudentHomeHeader from '../../Components/HomeHeader/StudentHomeHeader';
import HomeQuickActions from '../../Components/HomeQuickActions/HomeQuickActions';
import {resetBookingFlowType} from '../../Redux/Slices/Student/TutorBookingSlice';
import {requestUserPermission} from '../../Helper/Notifications/NotiHelper';
import {updateSplashVisible} from '../../Helper/SplashHelper/SplashHelper';
import {clearAuthData, setIsLoggedIn} from '../../Features/authSlice';
import TaleemHeader from '../../Components/TaleemHeader/TaleemHeader';
import {getReverseGeoCodeGoogleApi} from '../../Utils/constant';
import Geolocation from 'react-native-geolocation-service';
import {requestLocationPermission} from '../../Helper/Location/LocationHelpers';
import {updateCurrentLoc} from '../../Redux/Slices/General/CurrentLocSlice';
import {fp} from '../../Helper/ResponsiveDimensions';
import {updateSelectedKid} from '../../Redux/Slices/Parent/SelectedKidSlice';
import ParentHomeHeader from '../../Components/ParentHomeHeader';
import ParentStudentDetailCard from '../../Components/ParentHomeHeader/ParentStudentDetailCard';
import CampaignBanner from '../../Components/CampaignBanner';
import BannerNotificationHomeTutor from '../../Components/BannerNotificationHome';
import TodayClassComponent from '../HomeTutor/TodayClassComponent';
import DeviceInfo from 'react-native-device-info';
import {FCM_TOKEN} from '../../Utils/storageKeys';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';

const HomeScreen = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [userProfile, setUserProfile] = useState(null);
  const [currentLoc, setCurrentLoc] = useState({});
  const studentType = userProfile?.student_type;
  const [activeBtn, setActiveBtn] = useState(1);
  console.log('🚀 ~ HomeScreen ~ activeBtn:', activeBtn);
  const [classData, setClassData] = useState();

  const [getAllBookingApi] = useLazyGetAllBookingQuery();

  const fetchBookings = useCallback(
    async (
      date = new Date().toISOString().split('T')[0],
      classTypeId = null,
    ) => {
      try {
        const params = {
          userType: 'tutor',
          date: new Date().toISOString().split('T')[0],
        };

        if (activeBtn === 3) {
          params.class_type_id = '2';
        } else if (activeBtn === 2) {
          params.class_type_id = '1';
        }

        if (classTypeId) {
          params.class_type_id = classTypeId;
        }

        const res = await getAllBookingApi(params).unwrap();
        setClassData(res?.data?.rows);
        console.log(JSON.stringify(res), 'filtered booking data');
      } catch (error) {
        console.error('Error fetching bookings:', error);
      }
    },
    [getAllBookingApi, activeBtn],
  );

  const {data: connectedStudentsRes, refetch: refetchConnectedStudent} =
    useGetConnectedStudentsQuery();

  useEffect(() => {
    refetchConnectedStudent();
  }, []);

  const {appLocale} = useSelector(state => state?.auth);
  useEffect(() => {
    const getFcm = async () => {
      try {
        const token = await AsyncStorage.getItem('FCM_TOKEN');
        console.log('FCM Token:', token);
      } catch (error) {
        console.error('Error retrieving FCM token:', error);
      }
    };
    getFcm();
  }, []);

  useEffect(() => {
    const initializeLocation = async () => {
      try {
        const hasPermission = await requestLocationPermission();
        if (!hasPermission) {
          console.log('Location permission denied');
          return;
        }

        Geolocation.getCurrentPosition(
          position => {
            const {latitude, longitude} = position.coords;
            setCurrentLoc(prev => ({
              ...prev,
              region: {
                latitude,
                longitude,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
              },
            }));
            handleCurrentLocation(latitude, longitude);
          },
          error => {
            console.error('Geolocation error:', error);
          },
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 10000,
            distanceFilter: 10,
          },
        );
      } catch (error) {
        console.error('Location initialization error:', error);
      }
    };

    initializeLocation();
    return () => {
      Geolocation.stopObserving();
    };
  }, []);

  const handleCurrentLocation = useCallback(
    async (lat, lng) => {
      try {
        setCurrentLoc(prev => ({...prev, loading: true}));
        const url = getReverseGeoCodeGoogleApi(lat, lng);
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.status === 'OK' && data.results.length > 0) {
          const addressComponents = data.results[0].address_components;
          let country = '';
          addressComponents.forEach(component => {
            if (component.types.includes('country')) {
              country = component.long_name;
            }
          });
          const locationData = {
            address: data.results[0].formatted_address,
            country: country,
            region: {
              latitude: lat,
              longitude: lng,
              latitudeDelta: 0.0922,
              longitudeDelta: 0.0421,
            },
          };

          setCurrentLoc(prev => ({
            ...prev,
            ...locationData,
            loading: false,
          }));
          dispatch(updateCurrentLoc(locationData));
        } else {
          throw new Error('No results found');
        }
      } catch (error) {
        console.error('Error getting current location:', error);
        setCurrentLoc(prev => ({...prev, loading: false}));
        showToast('error', error.message);
      }
    },
    [currentLoc.region],
  );

  const dispatch = useDispatch();
  const {
    data: profileData,
    error,
    isLoading,
    refetch: refetchProfile,
  } = useProfileDetailsQuery();

  const {data: campaignRes} = useGetCampaignDetailsQuery();
  const {
    data: tutorIncomingBookingNotification,
    refetch: refetchTutorIncomingBookingNotification,
  } = useGetTutorBookingConfirmationNotificationQuery();

  const {isLoggedIn} = useSelector(state => state.auth);
  const fetchToken = async () => await AsyncStorage.getItem(FCM_TOKEN);

  const [updateDeviceInfo] = useUpdateDeviceDetailsMutation();

  const handleDeviceInfoUpdate = async () => {
    if (isLoggedIn) {
      let token = await fetchToken();
      let model = DeviceInfo.getModel();
      let deviceId = DeviceInfo.getDeviceId();
      const data = {
        device_token: token,
        device_model: model,
        device_id: deviceId,
        device_type: Platform.OS == 'android' ? '1' : '2',
        current_location: {
          address: currentLoc?.address,
          country: currentLoc?.country,
          latitude: currentLoc?.region?.latitude,
          longitude: currentLoc?.region?.longitude,
        },
      };
      try {
        const response = await updateDeviceInfo({data}).unwrap();
      } catch (error) {
        console.error('Error updating device info:', error);
      }
    }
  };

  useEffect(() => {
    handleDeviceInfoUpdate();
  }, [currentLoc]);

  useFocusEffect(
    useCallback(() => {
      refetchConnectedStudent();
      refetchTutorIncomingBookingNotification();
      if (profileData?.data?.user_type === '3') {
        fetchBookings();
      }
    }, [
      refetchConnectedStudent,
      refetchTutorIncomingBookingNotification,
      fetchBookings,
      profileData,
    ]),
  );
  useEffect(() => {
    if (profileData) {
      // Check for rate card setup status
      console.log('Rate card status:', profileData.data.rate_card_setup);
    }
  }, [profileData]);

  const {
    data: studentData,
    error: studentError,
    isLoading: studentLoading,
    refetch: refetchStudentData,
  } = useGetStudentDetailQuery();

  const refetchDataOnFocus = useCallback(() => {
    refetchProfile();
    refetchStudentData();
  }, [refetchProfile, refetchStudentData]);

  useFocusEffect(refetchDataOnFocus);

  useEffect(() => {
    dispatch(resetBookingFlowType());
  }, []);

  useEffect(() => {
    requestUserPermission();
  }, []);

  // Fetch bookings when activeBtn changes
  useEffect(() => {
    if (profileData?.data?.user_type === '3') {
      fetchBookings();
    }
  }, [activeBtn, fetchBookings, profileData]);

  async function handleLogout() {
    await updateSplashVisible(null, false, true);
    await AsyncStorage.removeItem('user');
    dispatch(clearAuthData());
    dispatch(setIsLoggedIn(false));
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{name: 'WelcomeScreen'}],
      }),
    );
  }

  const logoSource =
    appLocale === 'ar'
      ? icons.logo.welcomeLogoArabic
      : icons.logo.welcomeLogoEng;

  useEffect(() => {
    if (error && error?.data?.status === 401) {
      handleLogout();
      showToast('error', t('failedToLoadProfile'), 'bottom', isRTL);
    } else if (profileData) {
      console.log(profileData, 'Profile Data home screen');
      setUserProfile(profileData.data);
    }

    if (studentError) {
      // showToast('error', 'Failed to load student details');
    }
  }, [profileData, error, studentError, studentData]);

  const taleemStudentAcademicDetails =
    studentData?.data?.tlm_student_academic_details;
  const activeColor = colors.themeBackgroundTwo;
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = () => {
    setRefreshing(true);
    refetchProfile();
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const {selectedKid} = useSelector(state => state?.selectedKidSlice);
  const [markKidAsSelected] = useMarkActiveKidForParentMutation();

  const handleSelectStudent = item => {
    const isCurrentlySelected = item?.id === selectedKid?.id;

    if (isCurrentlySelected) {
      dispatch(updateSelectedKid(null));
      markKidAsSelected({id: item?.id, selected: false})
        .unwrap()
        .then(res => {
          console.log(res, 'response for deselecting student');
        })
        .catch(err => {
          console.log(err);
        });
    } else {
      dispatch(updateSelectedKid(item));
      markKidAsSelected({id: item?.id, selected: true})
        .unwrap()
        .then(res => {
          console.log(res, 'response for marking student');
        })
        .catch(err => {
          console.log(err);
        });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <TaleemHeader isLocationIcon={false} />
      <TaleemLoader isLoading={isLoading} />
      <View style={styles.contentContainer}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }>
          {campaignRes?.data?.rows.length > 0 && (
            <>
              <Title
                text={t('explore_campaigns')}
                style={{
                  ...styles.quickActionsTitle,
                  textAlign: isRTL ? 'right' : 'left',
                  fontSize: fp(1.8),
                }}
              />
              <CampaignBanner data={campaignRes?.data?.rows} />
            </>
          )}
          {profileData?.data?.user_type === '3' &&
            tutorIncomingBookingNotification?.data?.rows.length > 0 && (
              <BannerNotificationHomeTutor
                data={tutorIncomingBookingNotification.data.rows}
              />
            )}

          {isLoading ? (
            <ActivityIndicator
              size="large"
              color={colors.themeColor}
              style={{marginTop: 20}}
            />
          ) : profileData?.data?.user_type === '3' ? (
            <>
              <TutorHomeHeader
                data={classData}
                profileData={profileData}
                activeBtn={activeBtn}
                setActiveBtn={setActiveBtn}
                count={classData?.count}
              />
            </>
          ) : profileData?.data?.user_type == '2' ? (
            <>
              <ParentHomeHeader
                connectedStudentsRes={connectedStudentsRes?.data?.list}
                handleSelectStudent={handleSelectStudent}
                selectedKid={selectedKid}
              />
              {selectedKid && Object.keys(selectedKid).length > 0 && (
                <ParentStudentDetailCard selectedStudent={selectedKid} />
              )}
            </>
          ) : (
            <StudentHomeHeader
              taleemStudentAcademicDetails={taleemStudentAcademicDetails}
              studentType={studentType}
              userProfile={userProfile}
            />
          )}

          <>
            <Title
              text={t('quick_actions')}
              style={{
                ...styles.quickActionsTitle,
                textAlign: isRTL ? 'right' : 'left',
              }}
            />
            <HomeQuickActions
              taleemStudentAcademicDetails={taleemStudentAcademicDetails}
              studentType={studentType}
              userType={profileData?.data?.user_type}
            />
          </>

          {profileData?.data?.user_type === '3' && (
            <TodayClassComponent
              title={t('today_class')}
              // onPress={() => navigation.navigate('StudentClassDetails')}
              data={classData}
              count={classData?.count}
              activeBtn={activeBtn}
              setActiveBtn={setActiveBtn}
            />
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default HomeScreen;
