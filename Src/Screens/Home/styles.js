import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
  },
  headerContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    height: hp(7),
    marginBottom: hp(1),
    justifyContent: 'center',
  },
  leftHeader: {
    flex: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  hamburgerIcon: {
    width: fp(3),
    height: fp(3),
  },
  logoContainer: {
    alignItems: 'center',
    paddingLeft: 5,
  },
  logoImage: {
    width: fp(12), // Keep the width fixed
    height: fp(6),
  },
  rightHeader: {
    flex: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingRight: 10,
    minWidth: fp(7),
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
    minWidth: 80,
  },
  locationIcon: {
    width: fp(2.8),
    height: fp(2.5),
  },
  locationText: {
    marginHorizontal: 6,
    color: colors.white,
    fontFamily: Fonts.medium,
    fontWeight: '500',
    fontSize: fp(1.4),
  },
  arrowDownIcon: {
    width: 12,
    height: 12,
    alignSelf: 'center',
  },
  notificationContainer: {
    padding: 5,
  },
  notificationIcon: {
    width: fp(3.5),
    height: fp(3.5),
  },
  contentContainer: {
    backgroundColor: colors.white,
    flex: 1,
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
  },
  viewContent: {
    marginBottom: 50,
  },
  quickActionsTitle: {
    fontSize: fp(2.4),
    marginTop: 10,
    fontFamily: Fonts.extraBold,
  },
});

export default styles;
