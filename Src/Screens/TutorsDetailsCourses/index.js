import {
  SafeAreaView,
  Text,
  View,
  Image,
  Pressable,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import React, {useEffect} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {HeaderTutorDetail} from '../../Components/HeaderTutorDetail';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {useGetTutorsDetailCoursesQuery} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import styles from './styles';
import {PrimaryButton} from '../../Components/CustomButton';
import {useTranslation} from 'react-i18next';
import CourseDetailsCard from '../../Components/Student/CourseDetailsCard/CourseDetailsCard';
import {showToast} from '../../Components/ToastHelper';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {useDispatch, useSelector} from 'react-redux';
import {
  updateCourseRateCardId,
  updateTutorData,
} from '../../Redux/Slices/Student/TutorBookingSlice';
import {fp} from '../../Helper/ResponsiveDimensions';
import TutorDetails from '../../Components/MyClass/TutorDetails';

const TutorsDetailsCourses = ({navigation, route}) => {
  const {id} = route.params || {};
  const userType = useSelector(state => state.auth.user_type);
  console.log('🚀 ~ TutorsDetailsCourses ~ course id:', id);
  const {t} = useTranslation();
  const {
    data: GetTutorsDetailCoursesData,
    error,
    isLoading,
  } = useGetTutorsDetailCoursesQuery(id);
  const dispatch = useDispatch();
  useEffect(() => {
    console.log('Tutor API Data:', JSON.stringify(GetTutorsDetailCoursesData));
  }, [GetTutorsDetailCoursesData]);

  const courseData = GetTutorsDetailCoursesData?.data?.courseData || {};
  console.log(
    '🚀 ~ TutorsDetailsCourses ~ courseData:',
    JSON.stringify(courseData),
  );
  const tutorData = courseData?.tlm_user || {};
  // console.log("tutorData-----1-1-1-1-1-1-1-1-1-1----1-1-1",tutorData);

  const tutorId = courseData?.tlm_user?.id || {};
  const sessions = courseData?.tlm_tutor_class_sessions || [];
  const reviewCount = courseData?.reviews?.length || 0;

  // Fix tutor image URL

  const tutorImageUrl = tutorData.image
    ? {uri: `${IMAGE_BASE_URL}${tutorData.image}`} // Ensuring tutorData.image is properly concatenated with IMAGE_BASE_URL
    : {uri: DUMMY_USER_IMG};

  const handleProfileClick = id => {
    // navigation.navigate('TutorsDetails', {id: tutorId});
  };

  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color={colors.themeColor} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('errorLoadingCourseDetails')}</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <HeaderTutorDetail
        backIcon={icons.backbtn}
        isBackBtn
        title={t('courseDetails')}
        share={false}
      />

      <ScrollView contentContainerStyle={styles.contentContainer}>
        <CourseDetailsCard courseData={courseData} />
        <View style={styles.divider} />
        {/* <Text style={styles.sectionTitle}>
          {t('courseLanguage')}: {courseData?.language || 'English'}
        </Text> */}
        <Text style={styles.sectionTitle}>{t('courseDescription')}</Text>
        <Text style={styles.courseDescription}>
          {courseData?.description || t('noDescription')}
        </Text>
        <Text style={styles.sectionTitle}>{t('tutorDetails')}</Text>
        <TutorDetails
          imageUri={tutorImageUrl}
          name={capitalizeFirstLetter(tutorData.name) || 'No Name'}
          occupation={
            ''
            // user_type === '1'
            //   ? tutor?.tlm_tutor_profile?.expertise || 'No Expertise'
            //   : `${
            //       isRTL
            //         ? academicDetails?.tlm_grade?.name_ar
            //         : academicDetails?.tlm_grade?.name
            //     } ${
            //       academicDetails?.tlm_class
            //         ? isRTL
            //           ? academicDetails?.tlm_class?.name_ar
            //           : academicDetails?.tlm_class?.name
            //         : ''
            //     }`
          }
        />
        {/* <View style={styles.tutorContainer}>
          <Image source={tutorImageUrl} style={styles.tutorImage} />
          <View style={styles.tutorInfo}>
            <Text style={styles.tutorName}>
              {capitalizeFirstLetter(tutorData.name) || 'Tutor Name'}
            </Text>
            <Text style={styles.tutorRole}>{t('danceTutor')}</Text>
            <View style={styles.tutorDetails}>
              <Image source={icons.locationBlack} style={styles.locationIcon} />
              <Text style={styles.tutorLocation}>
                {tutorData.address || t('noAddress')}
              </Text>
            </View> */}
        {/* <View
              style={{
                flexDirection: 'row',
                // justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Image source={icons.starBlack} style={styles.starIcon} />
              <Text style={styles.tutorRating}>
                {tutorData.tlm_tutor_profile?.ratings || 'N/A'} {t('rating_en')}{' '}
                | {reviewCount} {t('reviews')}
              </Text>
            </View> */}
        {/* </View>
        </View> */}
        {/* <TouchableOpacity
          onPress={() => handleProfileClick(tutorId)}
          style={styles.viewProfileButton}>
          <Text style={styles.viewProfileText}>{t('view_profile')}</Text>
        </TouchableOpacity> */}

        {/* <View style={styles.priceContainer}>
          <Text style={styles.priceText}>{t('price')}</Text>
          <Text style={styles.priceText}>
            {courseData.sessions?.[0]?.price || courseData.price} QAR
            {sessions[0]?.price || courseData?.price || '120'} QAR
          </Text>
        </View> */}
      </ScrollView>

      <PrimaryButton
        onPress={() => {
          navigation.navigate('BookYourTutorCourses');
          dispatch(updateCourseRateCardId(id));
          dispatch(updateTutorData(tutorData));
        }}
        // onPress={() => showToast('success', t('coming_soon'))}
        title={t('book_now')}
        style={{
          backgroundColor: colors.themeColor,
          marginBottom: 15,
        }}
        textStyle={{fontSize: fp(2), color: colors.white}}
      />
    </SafeAreaView>
  );
};

export default TutorsDetailsCourses;
