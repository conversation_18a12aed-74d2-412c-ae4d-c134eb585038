import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: colors.red,
    fontSize: 16,
    textAlign: 'center',
    fontFamily: Fonts.medium,
  },
  contentContainer: {
    padding: 16,
  },
  courseImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  courseTitle: {
    fontSize: 20,
    color: colors.black,
    marginBottom: 8,
    fontFamily: Fonts.medium,
  },
  courseInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  courseText: {
    fontSize: 14,
    color: colors.txtGrey1,
    marginVertical: 5,
    fontFamily: Fonts.medium,
  },
  divider: {
    height: hp(0.06),
    width: wp(90),
    backgroundColor: colors.offGrey,
    marginTop: 2,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    color: colors.black,
    marginBottom: 16,
    fontFamily: Fonts.bold,
  },
  courseDescription: {
    fontSize: 14,
    color: colors.txtGrey1,
    marginBottom: 16,
    fontFamily: Fonts.medium,
  },
  tutorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    // width: wp(50),
    // marginRight: wp(9),
  },
  tutorImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  tutorInfo: {
    flex: 1,
  },
  tutorName: {
    fontSize: 16,
    color: colors.black,
    fontFamily: Fonts.bold,
  },
  tutorRole: {
    fontSize: 14,
    color: colors.txtGrey1,
    marginVertical: hp(0.8),
    fontFamily: Fonts.medium,
  },
  tutorDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    // width: wp(80),
  },
  tutorLocation: {
    fontSize: 12,
    color: colors.txtGrey1,
    marginRight: 8,
    fontFamily: Fonts.medium,
  },
  tutorRating: {
    fontSize: 12,
    color: colors.txtGrey1,
    fontFamily: Fonts.medium,
  },
  viewProfileButton: {
    backgroundColor: colors.themeColor,
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center',
    marginBottom: 50,
    width: '35%',
  },
  viewProfileText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // bottom: 0,
    // marginTop: hp(10),
    marginBottom: 16,
    marginTop: hp(12),
  },
  priceText: {
    fontSize: 16,
    color: colors.black,
    fontFamily: Fonts.bold,
    // marginVertical: hp(2),
  },
  priceAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.black,
  },
  bookButton: {
    backgroundColor: colors.themeColor,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  bookButtonText: {
    color: colors.white,
    fontSize: 18,
    fontFamily: Fonts.bold,
  },
  locationIcon: {
    width: 20,
    height: 20,
    marginRight: 4,
  },
  starIcon: {
    width: 20,
    height: 20,
    marginRight: 4,
  },
});

export default styles;
