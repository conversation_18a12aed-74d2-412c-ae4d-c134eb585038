import React, {useState, useCallback} from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Image,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import {useAddressMutation} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';
import {API_KEY_PERSONAL} from '../../Utils/getBaseUrl';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import Geolocation from 'react-native-geolocation-service';
import {requestLocationPermission} from '../../Helper/Location/LocationHelpers';

const StudentAddress = ({route}) => {
  console.log(route?.params, 'asdfasdf');
  const {fullAddress, building} = route?.params?.address || {};
  const navigation = useNavigation();
  const [inputText, setInputText] = useState(fullAddress || '');
  const [suggestions, setSuggestions] = useState([]);
  const [zone, setZone] = useState(route?.params?.address?.zone || '');
  const [street, setStreet] = useState(route?.params?.address?.street || '');
  const [buildingNumber, setBuildingNumber] = useState(building || '');
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isLoadingCurrentLocation, setIsLoadingCurrentLocation] =
    useState(false); // Added for loading state
  const [addressMutation] = useAddressMutation();

  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  // Fetch suggestions from Google Places API
  const fetchSuggestions = async query => {
    if (query.length > 2) {
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${query}&key=${API_KEY_PERSONAL}&language=en`,
        );
        const data = await response.json();
        if (data.status === 'OK') {
          setSuggestions(data.predictions);
        } else {
          setSuggestions([]);
        }
      } catch (error) {
        console.error('Error fetching Google Places suggestions:', error);
        setSuggestions([]);
      }
    } else {
      setSuggestions([]);
    }
  };

  const fetchPlaceDetails = async placeId => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${API_KEY_PERSONAL}`,
      );
      const data = await response.json();

      if (data.status === 'OK') {
        const {lat, lng} = data.result.geometry.location;

        // Extract address components
        const addressComponents = data.result.address_components;

        // Find specific components
        const zone = addressComponents.find(comp =>
          comp.types.includes('administrative_area_level_2'),
        )?.long_name;

        const street = addressComponents.find(comp =>
          comp.types.includes('route'),
        )?.long_name;

        const building = addressComponents.find(comp =>
          comp.types.includes('subpremise'),
        )?.long_name;

        const country = addressComponents.find(comp =>
          comp.types.includes('country'),
        )?.long_name;

        return {
          lat,
          lng,
          zone,
          street,
          building,
          country,
        };
      } else {
        console.error('Failed to fetch place details:', data.status);
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
    }
  };

  const fetchAddressFromCoordinates = async (latitude, longitude) => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${API_KEY_PERSONAL}&language=en`,
      );
      const data = await response.json();
      if (data.status === 'OK' && data.results.length > 0) {
        const formattedAddress = data.results[0].formatted_address;
        const addressComponents = data.results[0].address_components;

        const zone = addressComponents.find(comp =>
          comp.types.includes('administrative_area_level_2'),
        )?.long_name;

        const street = addressComponents.find(comp =>
          comp.types.includes('route'),
        )?.long_name;

        const building = addressComponents.find(comp =>
          comp.types.includes('subpremise'),
        )?.long_name;

        const country = addressComponents.find(comp =>
          comp.types.includes('country'),
        )?.long_name;

        setInputText(formattedAddress);
        setZone(zone || '');
        setStreet(street || '');
        setBuildingNumber(building || '');
        setSelectedLocation({
          lat: latitude,
          lng: longitude,
          zone,
          street,
          building,
          country,
        });
      } else {
        showToast('error', t('failedToRetrieveAddress'), 'bottom', isRTL);
      }
    } catch (error) {
      console.error('Error fetching address from coordinates:', error);
      showToast('error', t('failedToRetrieveAddress'), 'bottom', isRTL);
    }
  };

  const handleInputChange = text => {
    setInputText(text);
    fetchSuggestions(text);
  };

  const handleSuggestionPress = async suggestion => {
    setInputText(suggestion.description);
    setSuggestions([]);
    const locationDetails = await fetchPlaceDetails(suggestion.place_id);
    setBuildingNumber(locationDetails?.building || '');
    setZone(locationDetails?.zone || '');
    setStreet(locationDetails?.street || '');
    setSelectedLocation(locationDetails);
  };

  const handleCurrentLocation = useCallback(async () => {
    try {
      setIsLoadingCurrentLocation(true);
      const hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        showToast('error', t('locationPermissionDenied'), 'bottom', isRTL);
        setIsLoadingCurrentLocation(false);
        return;
      }

      Geolocation.getCurrentPosition(
        async position => {
          const {latitude, longitude} = position.coords;
          await fetchAddressFromCoordinates(latitude, longitude);
          setIsLoadingCurrentLocation(false);
        },
        error => {
          console.error('Geolocation error:', error);
          showToast('error', t('failedToGetLocation'), 'bottom', isRTL);
          setIsLoadingCurrentLocation(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
        },
      );
    } catch (error) {
      console.error('Error in handleCurrentLocation:', error);
      showToast('error', t('failedToGetLocation'), 'bottom', isRTL);
      setIsLoadingCurrentLocation(false);
    }
  }, [t, isRTL]);

  const handleSaveProfile = async () => {
    console.log('handleSaveProfile');
    try {
      // Uncomment this if you want to enforce location selection
      // if (!selectedLocation || !inputText.trim()) {
      //   showToast('error', t('locationMandatory'), 'bottom', isRTL);
      //   return;
      // }
      setIsUpdating(true);

      const payload = {
        address: inputText,
      };

      if (selectedLocation) {
        const {lat, lng, country} = selectedLocation;
        payload.latitude = lat;
        payload.longitude = lng;
        payload.country = country;
      }

      if (zone?.trim()) {
        payload.zone = zone.trim();
      }
      if (street?.trim()) {
        payload.street = street.trim();
      }
      if (buildingNumber?.trim()) {
        payload.building = buildingNumber.trim();
      }
      console.log('🚀 ~ handleSaveProfile ~ payload:', payload);

      const response = await addressMutation(payload).unwrap();

      setIsUpdating(false);
      showToast('success', t('addressSaved'), 'bottom', isRTL);
      navigation.goBack();
    } catch (error) {
      setIsUpdating(false);
      console.error('Error saving address:', error);
      showToast('error', t('failedSaveAddress'), 'bottom', isRTL);
    }
  };
  const label = {...styles?.labelStyle, textAlign: isRTL ? 'right' : 'left'};
  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        backIcon={icons.backIcon}
        isBackBtn
        title={t('address')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />

      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.mandatoryContainer}>
          <Text style={label}>{t('address')}*</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
              placeholder={t('typeLocation')}
              value={inputText}
              onChangeText={handleInputChange}
              placeholderTextColor={colors.txtGray1}
              multiline
              maxLength={120}
            />
          </View>
        </View>

        {suggestions.length > 0 && (
          <FlatList
            data={suggestions}
            keyExtractor={item => item.place_id}
            renderItem={({item}) => (
              <TouchableOpacity
                style={styles.suggestionItem}
                onPress={() => handleSuggestionPress(item)}>
                <Text style={styles.suggestionText}>{item.description}</Text>
              </TouchableOpacity>
            )}
            style={styles.suggestionsList}
          />
        )}

        <View style={styles.optionalContainer}>
          <Text style={label}>{t('Zone')}</Text>
          <TextInput
            style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
            placeholder={t('enterZone')}
            value={zone}
            onChangeText={text => setZone(text)}
            placeholderTextColor={colors.txtGray1}
          />
        </View>

        <View style={styles.optionalContainer}>
          <Text style={label}>{t('Street')}</Text>
          <TextInput
            style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
            placeholder={t('enterStreet')}
            value={street}
            onChangeText={text => setStreet(text)}
            placeholderTextColor={colors.txtGray1}
          />
        </View>

        {/* <View style={styles.optionalContainer}> */}
        <Text style={label}>{t('buildingNumber')}</Text>
        <TextInput
          style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
          placeholder={t('enterBuildingNumber')}
          value={buildingNumber}
          onChangeText={text => setBuildingNumber(text)}
          keyboardType="numeric"
          placeholderTextColor={colors.txtGray1}
        />
        {/* </View> */}

        <TouchableOpacity
          style={[
            styles.locationButton,
            {
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignSelf: isRTL ? 'flex-end' : 'flex-start',
            },
          ]}
          onPress={handleCurrentLocation}
          disabled={isLoadingCurrentLocation}>
          {isLoadingCurrentLocation ? (
            <ActivityIndicator size="small" color={colors.themeColor} />
          ) : (
            <>
              <Text style={styles.locationButtonText}>
                {t('useCurrentLocation')}
              </Text>
              <Image
                source={icons.MyLocation}
                resizeMode="contain"
                style={styles.locationIcon}
              />
            </>
          )}
        </TouchableOpacity>
      </ScrollView>

      <View style={styles.saveButtonContainer}>
        <TouchableOpacity style={styles.saveButton} onPress={handleSaveProfile}>
          {isUpdating ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.saveButtonText}>{t('saveAddress')}</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    padding: 16,
    paddingBottom: 80,
  },
  inputContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    padding: 10,
    fontSize: 14,
    marginBottom: hp(1),
    color: colors.black,
    fontFamily: Fonts.regular,
  },
  suggestionsList: {
    backgroundColor: colors.white,
    borderColor: colors.txtGray1,
    borderWidth: 1,
    borderRadius: 8,
    maxHeight: 150,
    marginBottom: 10,
  },
  suggestionItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.txtGray1,
  },
  suggestionText: {
    fontSize: 16,
    color: colors.black,
    fontFamily: Fonts.regular,
  },
  labelStyle: {
    fontSize: 14,
    color: colors.black,
    marginBottom: 5,
    fontFamily: Fonts.medium,
  },
  mandatoryContainer: {
    marginBottom: 16,
  },
  optionalContainer: {
    marginBottom: 16,
  },
  saveButtonContainer: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
  },
  saveButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  locationButtonText: {
    fontSize: fp(1.6),
    color: colors.themeColor,
    fontFamily: Fonts.medium,
    marginRight: 5,
  },
  locationIcon: {
    width: 20,
    height: 20,
    tintColor: colors.themeColor,
  },
  locationButton: {
    backgroundColor: colors.white,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    alignItems: 'center',
    marginBottom: 8,
    width: '60%',
    borderWidth: 1,
    borderColor: colors.txtGrey,
    elevation: 2,
    justifyContent: 'space-around',
  },
});

export default StudentAddress;
