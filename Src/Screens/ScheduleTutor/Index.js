import React, {useState, useEffect, useRef} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  Modal,
  TouchableWithoutFeedback,
  Platform,
} from 'react-native';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import LinearGradient from 'react-native-linear-gradient';
import styles from './styles';
import ScheduleCard from './ScheduleCard';
import {
  useDeleteTutorScheduleMutation,
  useEditTutorScheduleMutation,
  useGetClassTypesQuery,
  useGetOpneSessionsCalssesQuery,
  useLazyGetScheduleDatesForCalendarTutorQuery,
  useLazyGetTutorSchedulesQuery,
} from '../../Api/ApiSlice';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {useTranslation} from 'react-i18next';
import {showToast} from '../../Components/ToastHelper';
import {Fonts} from '../../Utils/Fonts';
import SessionInfoCard from '../BookOpenSession/SessionInfoCard';
import TutorAddSlotModal from '../../Components/Custom_Modal/TutorAddSlotModal';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import moment from 'moment';

import DateTimePicker from '@react-native-community/datetimepicker';
import TaleemEventCalendar from '../../Components/Calendar/TaleemEventCalendar';
import {
  getFirstAndLastDates,
  getFormattedBookingDatesWithSlots,
  getFormattedDatesWithSlots,
  getRemainingDatesFormatted,
} from '../../Helper/Calendar/FormatAvailableSlotDate';
import {
  convertToUTC,
  convertUTCToLocal,
} from '../../Helper/DateHelpers/DateHelpers';
import {resetTutorSlots} from '../../Redux/Slices/Tutor/TutorSlotSlice';
import {useDispatch} from 'react-redux';
import ShowOpenSessionSchedule from '../OpenSession/ShowOpenSessionSchedule';
import HelperTextComponent from '../../Components/HelperTipComp';

const ScheduleTutor = ({navigation}) => {
  const dispatch = useDispatch();
  const [selectedDate, setSelectedDate] = useState(
    new Date().toISOString().split('T')[0],
  );
  const [dates, setDates] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedTab, setSelectedTab] = useState(1);
  const [showAddSlotModal, setShowAddSlotModal] = useState(false);
  const [deleteType, setDeleteType] = useState('single');
  const [selectedItem, setSelectedItem] = useState(null);
  const [fromTime, setFromTime] = useState('');
  const [toTime, setToTime] = useState('');
  const [data, setData] = useState([]);
  const flatListRef = useRef(null);
  const [showPicker, SetShowPicker] = useState(false);
  const [UTCFromTime, setUTCFromTime] = useState('');
  const [UTCToTime, setUTCToTime] = useState('');
  const [recurrenceType, setRecurrenceType] = useState({
    name: 'Daily',
    value: [0, 1, 2, 3, 4, 5, 6],
  });
  const [isCalendarLoading, setIsCalendarLoading] = useState(false);
  const [customSelectedDays, setCustomSelectedDays] = useState([]);
  const [openHelperText, setOpenHelperText] = useState(false);
  const [openTextHelper, setOptenTextHelper] = useState(false);
  const {t, i18n} = useTranslation(); // Initialize translation hook
  const isRTL = i18n.language === 'ar';
  const [
    triggerGetSchedules,
    {data: tutorScheduleData, isLoading: slotLoading},
  ] = useLazyGetTutorSchedulesQuery({refetchOnFocus: true});
  const {data: openSessionData} = useGetOpneSessionsCalssesQuery(selectedDate);
  console.log('🚀 ~ tutorScheduleData:', JSON.stringify(tutorScheduleData));
  const {data: classTypeData, refetch: refetchClassTypeData} =
    useGetClassTypesQuery();
  const updatedClassTypeData = [
    ...(classTypeData?.data || []), // Spread the existing data (or an empty array if it's undefined)
    {id: 3, name: t('open_session')}, // Add the new object
  ];
  useEffect(() => {
    refetchClassTypeData();
  }, []);

  const handleGetSchedulesApi = async () => {
    await triggerGetSchedules({
      date: selectedDate,
      class_type_id: selectedTab,
    });
  };
  const handleGetSchedulesApiForOpenSession = async () => {
    await triggerGetSchedules({
      date: selectedDate,
      // class_type_id: selectedTab,
      isOpenSession: 1,
    });
  };
  const [deleteTutorSchedue, {isLoading: deleteLoading}] =
    useDeleteTutorScheduleMutation();
  useEffect(() => {
    if (selectedTab == '3') {
      handleGetSchedulesApiForOpenSession();
    } else {
      handleGetSchedulesApi();
    }
  }, [selectedDate, selectedTab]);

  useEffect(() => {
    setSelectedTab(classTypeData?.data?.[0]?.id);
  }, [classTypeData]);

  const [editTutorSchedule, {isLoading: scheduleLoading}] =
    useEditTutorScheduleMutation();

  const generateDatesForMonth = (year, month) => {
    return Array.from(
      {length: new Date(year, month + 1, 0).getDate()},
      (_, i) => {
        const date = new Date(year, month, i + 1);
        return new Date(
          Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()),
        );
      },
    );
  };

  const updateDates = baseDate => {
    const month = baseDate.getMonth();
    const year = baseDate.getFullYear();
    setDates(generateDatesForMonth(year, month));
  };

  useEffect(() => {
    const now = new Date();
    updateDates(now);

    const todayIndex = now.getDate() - 1;
    setCurrentIndex(todayIndex);

    setTimeout(() => {
      try {
        flatListRef.current?.scrollToIndex({
          animated: true,
          index: todayIndex,
          viewPosition: 0.5,
        });
      } catch (error) {
        console.error('Scroll to index failed', error);
      }
    }, 500);
  }, []);
  const getData = () => {
    // if (selectedTab == 3) {
    //   return openSessionData?.data?.rows || [];
    // }
    return tutorScheduleData?.data?.rows || [];
  };
  const [
    fetchSchedules,
    {data: scheduleCalendarData, isLoading: isLoadingSchedules, error},
  ] = useLazyGetScheduleDatesForCalendarTutorQuery();
  function handleGetScheduleDataForCalendar(year, month, startDate, endDate) {
    setIsCalendarLoading(true);
    const params = {
      startDate: startDate,
      endDate: endDate,
      class_type_id: selectedTab,
    };
    console.log(
      '🚀 ~ handleGetScheduleDataForCalendar ~ params.selectedTab:',
      params.selectedTab,
    );

    if (selectedTab == '3') {
      params.isOpenSession = 1;
    }
    fetchSchedules(params)
      .unwrap()
      .then(response => {
        const scheduleDatesRes = response?.data?.schedule_dates;
        const bookingDatesApiRes = response?.data?.booking_dates;

        // Format remaining dates with grey color
        const remainingDatesFormattedData = getRemainingDatesFormatted(
          scheduleDatesRes,
          bookingDatesApiRes,
          year,
          month,
          '400',
          colors.darkGrey,
        );

        // Format scheduled dates with bold and black
        const slotsDatesFormattedData = getFormattedDatesWithSlots(
          scheduleDatesRes,
          bookingDatesApiRes,
          'bold',
          colors.darkBlack,
        );

        // Format booking dates with yellow dots
        const bookingDatesFormattedData = getFormattedBookingDatesWithSlots(
          bookingDatesApiRes,
          {...slotsDatesFormattedData, ...remainingDatesFormattedData},
          'bold',
          colors.darkBlack,
        );

        // Merge all formatted dates
        const allFormattedDates = {
          ...remainingDatesFormattedData,
          ...slotsDatesFormattedData,
          ...bookingDatesFormattedData,
        };

        // Add selected date styling
        if (selectedDate) {
          console.log(
            'selectedDate 789854684',
            allFormattedDates[selectedDate],
          );
          allFormattedDates[selectedDate] = {
            ...allFormattedDates[selectedDate],
            // Ensure the background color is consistent
            // color: colors.themeBackground, // Background color for selected date
            // Merge custom styles without overriding fontWeight
            customStyles: {
              container: {
                backgroundColor: colors.themeBackground,
              },
              text: {
                color:
                  allFormattedDates[selectedDate]?.customStyles?.text?.color, // Text color for selected date
                fontWeight:
                  allFormattedDates[selectedDate]?.customStyles?.text
                    ?.fontWeight || '300',
              },
            },
          };
        }

        markedDatesRef.current = allFormattedDates;
        setIsCalendarLoading(false);
      })
      .catch(err => {
        console.error('Error:', err);
        showToast('error', err?.data?.message, 'bottom', isRTL);
        setIsCalendarLoading(false);
      });
  }

  useEffect(() => {
    const tempDate = selectedDate;

    // Example usage:
    const {firstDay, lastDay, year, month} = getFirstAndLastDates(tempDate);
    console.log('Last day of the month:', firstDay, lastDay, year, month);
    handleGetScheduleDataForCalendar(
      year,
      month,
      firstDay,
      lastDay,
      // FirstAndLastDateObj.firstDate,
      // FirstAndLastDateObj.lastDate,
    );
  }, [selectedDate]);

  const handleDateSelect = date => {
    const dateString = date.dateString;
    setSelectedDate(dateString);

    const updatedMarkedDates = {...markedDatesRef.current};

    // Reset any previous selected date
    Object.keys(updatedMarkedDates).forEach(dateKey => {
      if (updatedMarkedDates[dateKey]?.selected) {
        delete updatedMarkedDates[dateKey].selected;
        delete updatedMarkedDates[dateKey].color;
        delete updatedMarkedDates[dateKey].textColor;
      }
    });

    // Apply new selected date styles
    updatedMarkedDates[dateString] = {
      ...updatedMarkedDates[dateString],
      selected: true,
    };

    markedDatesRef.current = updatedMarkedDates;
    SetShowPicker(false);
  };

  const handleDelete = async (id, type) => {
    try {
      const body = {id: id, type: type};
      const response = await deleteTutorSchedue(body);
      if (response?.data?.status) {
        await handleGetSchedulesApi();
        showToast('success', response?.data?.message, 'bottom', isRTL);
      } else {
        showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      }
    } catch (error) {
      showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      console.log('error', error);
    }
  };

  const handleEditPress = item => {
    console.log('🚀 ~ item:', item);
    setSelectedTab([item?.class_type_id]); // Initialize with array
    setSelectedItem(item); // Set the selected item
    setShowAddSlotModal(true); // Open the modal

    // Pre-fill the fields with the selected item's data
    setToTime(convertUTCToLocal(item?.end_time));
    setFromTime(convertUTCToLocal(item?.start_time));
    setRecurrenceType({
      name: item?.tag,
      value: item?.days_of_week.map(day => parseInt(day, 10)) || [],
    });

    // setRecurrenceType({

    //   name: item?.tag,
    //   value: item?.days_of_week || [],
    // });
  };

  // const renderDateItem = ({item, index}) => {
  //   const isSelected = item.toDateString() === selectedDate.toDateString();
  //   const isPastDate = item < new Date().setHours(0, 0, 0, 0);

  //   const dateContent = (
  //     <TouchableOpacity
  //       style={[styles.dateItem, isSelected && styles.selectedDate]}
  //       onPress={() => !isPastDate && handleDateSelect(new Date(item), index)}
  //       disabled={isPastDate}>
  //       <Text style={[styles.dateText, isSelected && styles.selectedDateText]}>
  //         {item?.toLocaleDateString('en-US', {weekday: 'short'})}
  //       </Text>
  //       <Text
  //         style={[
  //           styles.dateText,
  //           isSelected && styles.selectedDateText,
  //           {fontFamily: Fonts.semiBold},
  //         ]}>
  //         {item.getDate()}
  //       </Text>
  //     </TouchableOpacity>
  //   );

  const handleFromTimeChange = time => {
    console.log('🚀 ~ //renderDateItem ~ time:', time);
    setFromTime(
      `${new Date(time).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false, // Use 24-hour format
      })}:00`,
    );
    setUTCFromTime(convertToUTC(time));
  };

  const handleToTimeChange = time => {
    setToTime(
      `${new Date(time).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false, // Use 24-hour format
      })}:00`,
    );
    setUTCToTime(convertToUTC(time));
  };

  const EditScheduleApi = async () => {
    if (fromTime > toTime) {
      showToast('error', t('startTimeErrorMessage'), 'bottom', isRTL);
      return;
    }
    try {
      const payload = {
        id: selectedItem?.id,
        start_time: UTCFromTime,
        end_time: UTCToTime,
        recurrence_type:
          recurrenceType.name == 'Monthly'
            ? recurrenceType.name.toLowerCase()
            : 'weekly',
        // month_dates:
        //   recurrenceType?.name === 'Monthly'
        //     ? [new Date(selectedDate)?.getDate().toString()]
        //     : [],
        days_of_week:
          // recurrenceType?.name === 'Weekly'
          //   ? [new Date(selectedDate).getDay().toString()]
          //   : recurrenceType?.name === 'Daily'
          //   ? ['0', '1', '2', '3', '4', '5', '6']
          //   : recurrenceType?.name === 'Weekend'
          //   ? ['6', '0']
          //   : recurrenceType?.name === 'Week Days'
          //   ? ['1', '2', '3', '4', '5']
          //   : recurrenceType?.name?.toLowerCase() == 'custom'
          //   ? //SelectedDays
          customSelectedDays.map(String),
        // : [],
        class_type_id: selectedTab,
        start_date: new Date(selectedDate).toISOString().split('T')[0],
        tag: recurrenceType?.name,
      };
      console.log('🚀 ~ EditScheduleApi ~ payload:', payload);

      const response = await editTutorSchedule(payload);
      if (response?.data?.status) {
        await handleSheduleData();
        showToast('success', response?.data?.message, 'bottom', isRTL);
        console.log('🚀 ~ EditScheduleApi ~ response?.data?.:', response?.data);
        triggerGetSchedules({
          date: new Date(selectedDate).toISOString().split('T')[0],
          class_type_id: selectedTab,
        });
        setShowAddSlotModal(false);
      } else {
        setShowAddSlotModal(false);
        showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      }
    } catch (error) {
      console.error('Error in addScheduleApi:', error);
      setShowAddSlotModal(false);
      // Show a toast with the error message
      showToast('error', response?.data?.message, 'bottom', isRTL);
    }
  };

  function selectedDaysCallback(params) {
    setCustomSelectedDays(params);
    console.log('🚀 ~ AddTutorSchedule ~ params:', params);
  }
  const markedDatesRef = useRef({});
  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <View style={{flex: 1, backgroundColor: colors.themeColor}}>
        <AppHeader
          backIcon={icons.backbtn}
          isBackBtn
          title={t('schedule')}
          navigateBack={() => navigation.navigate('Home')}
        />

        {/* <Text style={styles.monthTitle}>
          {selectedDate.toLocaleDateString('en-US', {
            month: 'long',
            year: 'numeric',
          })}
        </Text> */}
        {/* <View style={styles.dateContainer}>
          <TouchableOpacity onPress={handleLeftArrowPress}>
            <Image
              resizeMode="contain"
              source={icons.leftArrowWhite}
              style={styles.arrow}
            />
          </TouchableOpacity>
          <FlatList
            ref={flatListRef}
            horizontal
            data={dates}
            renderItem={renderDateItem}
            keyExtractor={item => item.toDateString()}
            showsHorizontalScrollIndicator={false}
            onScrollToIndexFailed={info => {
              console.warn('Scroll to index failed:', info);
              setTimeout(() => {
                flatListRef.current?.scrollToIndex({
                  index: info.index,
                  animated: true,
                  viewPosition: 0.5,
                });
              }, 500); // Retry after a short delay
            }}
          />
          <TouchableOpacity onPress={handleRightArrowPress}>
            <Image
              resizeMode="contain"
              source={icons.rightArrowWhite}
              style={styles.arrow}
            />
          </TouchableOpacity>
        </View> */}

        <View style={styles.bottomContainer}>
          <TouchableOpacity
            onPress={() => SetShowPicker(!showPicker)}
            activeOpacity={0.8}
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              // alignSelf: 'center',
              marginBottom: hp(1),
              justifyContent: 'space-between',
            }}>
            <View
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: 'center',
                // alignSelf: 'center',
              }}>
              <Text style={[styles.dayTxt, {marginRight: 5}]}>
                {t('select_date')}
              </Text>
              <HelperTextComponent
                helperText={t('selectDateHelper')}
                setOpen={setOpenHelperText}
                open={openHelperText}
                borderColor={colors.black}
                iconColor={colors.black}
              />
            </View>
            <View
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignSelf: 'center',
              }}>
              <Image
                source={icons.calanderYellow}
                style={{
                  height: fp(2.6),
                  width: fp(2.6),
                  alignSelf: 'center',
                  marginRight: isRTL ? 0 : 10,
                  marginLeft: isRTL ? 10 : 0,
                }}
                resizeMode="contain"
              />
              <Text
                style={[
                  styles.dayTxt,
                  {marginLeft: isRTL ? 0 : 0, marginRight: isRTL ? 0 : 0},
                ]}>
                {selectedDate
                  ? moment(selectedDate).isSame(moment(), 'day')
                    ? t('today')
                    : moment(selectedDate).format('DD MMM YYYY')
                  : ''}
              </Text>
              <Image
                source={icons.arrowDown}
                style={{
                  height: 24,
                  width: 24,
                  marginLeft: isRTL ? 0 : 5,
                  marginRight: isRTL ? 5 : 0,
                }}
                resizeMode="contain"
              />
            </View>
          </TouchableOpacity>

          {/* {Platform.OS == 'ios' ? (
            <Modal
              transparent
              visible={showPicker}
              style={{alignItems: 'center', justifyContent: 'center'}}>
              <TouchableWithoutFeedback onPress={() => SetShowPicker(false)}>
                <View style={styles.calender}>
                  <View
                    style={{backgroundColor: colors.white, borderRadius: 10}}>
                    <DateTimePicker
                      value={selectedDate}
                      mode="date"
                      display="inline"
                      onChange={handleDateSelect}
                      minimumDate={new Date()}
                      style={{width: wp(95), height: hp(45)}}
                    />
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Modal>
          ) : (
            showPicker && (
              <DateTimePicker
                value={selectedDate}
                mode="date"
                display="default"
                onChange={handleDateSelect}
                minimumDate={new Date()}
              />
            )
          )} */}

          <Modal
            transparent
            visible={showPicker}
            style={{
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <TouchableWithoutFeedback onPress={() => SetShowPicker(false)}>
              <View style={styles.calender}>
                <TaleemEventCalendar
                  selectedDate={selectedDate}
                  handleDateSelect={handleDateSelect}
                  markedDates={markedDatesRef.current}
                  isLoading={isCalendarLoading}
                  handleOnMonthChange={dateObj => {
                    const {firstDay, lastDay, year, month} =
                      getFirstAndLastDates(dateObj.dateString);
                    handleGetScheduleDataForCalendar(
                      year,
                      month,
                      firstDay,
                      lastDay,
                    );
                  }}
                  isShowAllInstructions={false}
                />
              </View>
            </TouchableWithoutFeedback>
          </Modal>

          {/* <Text style={styles.availableTxt}>{t('todayAvailability')}</Text> */}

          <View
            style={[
              styles.tabContainer,
              {
                flexDirection: isRTL ? 'row-reverse' : 'row',
                justifyContent: 'flex-start',
              },
            ]}>
            {updatedClassTypeData?.map(tab => (
              <TouchableOpacity
                key={tab?.id}
                style={[
                  styles.tabButton,
                  selectedTab === tab?.id && {
                    backgroundColor: colors.themeColor,
                  },
                  isRTL && {marginRight: 0, marginLeft: 8},
                ]}
                onPress={() => setSelectedTab(tab?.id)}>
                <Text
                  style={[
                    styles.tabButtonText,
                    selectedTab === tab?.id && styles.activeTabButtonText,
                  ]}>
                  {tab?.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <FlatList
            data={getData()}
            showsVerticalScrollIndicator={false}
            renderItem={({item}) => {
              console.log(
                '🚀 ~ //renderDateItem ~ item:',
                JSON.stringify(item),
              );
              return selectedTab === 3 ? (
                <ShowOpenSessionSchedule
                  isTutorCard={false}
                  item={item}
                  showExperTise={false}
                  showView={true}
                  onPressView={() =>
                    navigation.navigate('StudentClassDetails', {
                      item: {
                        booking_id: item?.id,
                      },
                      type: 'openSession',
                    })
                  }
                />
              ) : (
                // <ScheduleCard item={item} />
                <ScheduleCard
                  item={item}
                  selectedDate={selectedDate}
                  handleEditPress={() => {
                    handleEditPress(item);
                  }}
                  onCrossPress={() => {
                    Alert.alert(t('deleteSlot'), t('deleteSlotMessage'), [
                      {
                        text: t('cancel'),
                        onPress: () => console.log('Cancel Pressed'),
                        style: 'cancel',
                      },
                      {
                        text: t('deleteAll'),
                        onPress: () => handleDelete(item?.id, 'recurring'),
                      },
                      {
                        text: t('ok'),
                        onPress: () => handleDelete(item?.id, 'single'),
                      },
                    ]);
                  }}
                  availablity={selectedTab?.toString()}
                />
              );
            }}
            keyExtractor={item => item.id.toString()}
            ListEmptyComponent={
              <View>
                {slotLoading ? (
                  <TaleemLoader isLoading={slotLoading} />
                ) : (
                  <Text style={styles.noDataAvailable}>{t('noSchedules')}</Text>
                )}
              </View>
            }
            onScrollToIndexFailed={info => {
              console.warn('Scroll to index failed:', info);
              setTimeout(() => {
                flatListRef.current?.scrollToIndex({
                  index: info.index,
                  animated: true,
                  viewPosition: 0.5,
                });
              }, 500); // Retry after a short delay
            }}
          />

          <TouchableOpacity
            onPress={() => {
              navigation.navigate('OpenSession');

              dispatch(resetTutorSlots());
            }}
            style={[
              styles.saveButton,
              {flexDirection: 'row', justifyContent: 'center'},
            ]}>
            <Text style={[styles.saveButtonText, {marginRight: 8}]}>
              {t('create_open_session')}
            </Text>
            <HelperTextComponent
              helperText={t('setupPublicSesctionHelper')}
              setOpen={setOptenTextHelper}
              open={openTextHelper}
            />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('AddTutorSchedule')}
            style={styles.saveButton}>
            <Text style={styles.saveButtonText}>{t('addNewSchedule')}</Text>
          </TouchableOpacity>
        </View>
      </View>
      <TutorAddSlotModal
        showAddSlotModal={showAddSlotModal}
        editData={selectedItem} // Pass the selected item
        setShowAddSlotModal={setShowAddSlotModal}
        handleFromTimeChange={handleFromTimeChange}
        handleToTimeChange={handleToTimeChange}
        heading={t('editSlot')}
        recurrenceType={recurrenceType}
        setRecurrenceType={setRecurrenceType}
        handleAddButton={EditScheduleApi}
        selectedDaysCallback={selectedDaysCallback}
      />
    </SafeAreaView>
  );
};

export default ScheduleTutor;
