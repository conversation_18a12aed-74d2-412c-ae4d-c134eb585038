import React, {useState} from 'react';
import {
  View,
  Text,
  Pressable,
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {
  convertTo12HourFormat,
  convertToLocal12HourFormat,
} from '../../Helper/DateHelpers/DateHelpers';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {useNavigation} from '@react-navigation/native';
import {Fonts} from '../../Utils/Fonts';
import {useTranslation} from 'react-i18next';

const ScheduleCard = ({
  item,
  cardStyle,
  onCardPress = () => {},
  onCrossPress,
  handleEditPress,
}) => {
  console.log('Schedule Card item', item);
  const navigation = useNavigation();
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const status =
    item?.tlm_booking_schedules[0]?.tlm_booking?.status == '1'
      ? 'Booked'
      : item?.tlm_booking_schedules[0]?.tlm_booking?.status == '2'
      ? 'Cancelled'
      : 'Edit';

  const [isExpanded, setIsExpanded] = useState(false);
  // Toggle the expanded state
  const toggleExpand = () => setIsExpanded(!isExpanded);

  // Define status colors
  const statusColors = {
    Booked: ['#C6FFC9', '#D4EBFF'],
    Cancelled: ['#D4EBFF', '#D4EBFF'],
  };

  // Render status badge with gradient
  const renderStatusBadge = () => (
    <LinearGradient
      colors={statusColors[status] || ['#D4EBFF', '#D4EBFF']}
      start={{x: 0, y: 0}}
      end={{x: 1, y: 1}}
      style={styles.statusBadge}>
      <Text style={styles.statusText}>{status}</Text>
    </LinearGradient>
  );
  const handleEdit = () => {
    console.log('handleEdit');
  };
  return (
    <Pressable
      style={[styles.card, cardStyle]}
      onPress={() => {
        toggleExpand();
        onCardPress();
      }}>
      <View
        style={[
          styles.cardHeader,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <Text style={styles.timeText}>
          {convertToLocal12HourFormat(item?.start_time.toString())}
          {'   '}-{'   '}
          {convertToLocal12HourFormat(item?.end_time.toString())}
        </Text>

        <View
          style={[
            styles.iconContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          {status == 'Booked' || status == 'Cancelled' ? (
            renderStatusBadge()
          ) : (
            <TouchableOpacity onPress={handleEditPress}>
              <Image source={icons.pencil} style={styles.pencilIcon} />
            </TouchableOpacity>
          )}
          {/* { !== 'cancelled' && item.status !== 'booked' && (
            
          )} */}
          <Image
            source={isExpanded ? icons.upArrow : icons.downArrowBlack}
            style={[styles.expandIcon, {marginRight: fp(1)}]}
          />
          <TouchableOpacity
            style={{alignSelf: 'center'}}
            onPress={onCrossPress}>
            <Image
              tintColor={colors.greyLight}
              resizeMode="contain"
              source={icons.cross}
              style={styles.expandIcon}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Conditionally render details based on status and expanded state */}
      {isExpanded && (
        <View style={styles.detailsContainer}>
          {item?.tlm_booking_schedules?.length > 0 &&
            item?.tlm_booking_schedules[0]?.tlm_booking?.status == '1' && (
              <>
                <Text
                  style={[
                    styles.detailLabel,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {t('classDetails')}
                </Text>
                <Text
                  style={[
                    styles.detailText,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {item?.tlm_booking_schedules[0]?.tlm_booking?.class_title}
                </Text>
                <View
                  style={[
                    styles.infoRow,
                    {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  ]}>
                  <View
                    style={[
                      styles.infoColumn,
                      {alignItems: isRTL ? 'flex-end' : 'flex-start'},
                    ]}>
                    <Text style={styles.detailLabel}>{t('duration')}</Text>
                    <Text
                      style={
                        styles.detailText
                      }>{`${item?.hours_duration} hr`}</Text>
                  </View>
                  <View
                    style={[
                      styles.infoColumn,
                      {alignItems: isRTL ? 'flex-end' : 'flex-start'},
                    ]}>
                    <Text style={styles.detailLabel}>{t('Location')}</Text>
                    <Text style={styles.detailText}>
                      {item?.tlm_booking_schedules[0]?.tlm_booking?.address ||
                        t('online')}
                    </Text>
                  </View>
                  <Pressable
                    onPress={() =>
                      navigation.navigate('StudentClassDetails', {
                        item: {
                          booking_id:
                            item?.tlm_booking_schedules[0]?.tlm_booking?.id,
                        },
                      })
                    }
                    style={styles.viewButton}>
                    <Text style={styles.viewButtonText}>{t('view')}</Text>
                  </Pressable>
                </View>
              </>
            )}
          {item?.tlm_booking_schedules?.length > 0 &&
            item?.tlm_booking_schedules[0]?.tlm_booking?.status == '2' && (
              <>
                <Text
                  style={[
                    styles.detailLabel,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {t('reasons')}
                </Text>
                <Text
                  style={[
                    styles.detailText,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {
                    item?.tlm_booking_schedules[0]?.tlm_booking
                      ?.cancellation_reason
                  }
                </Text>
              </>
            )}
          {!item?.tlm_booking_schedules?.length && (
            <Text
              style={[
                styles.detailText,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {t('slotAvailable')}
            </Text>
          )}
        </View>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 20,
    marginVertical: 5,
    backgroundColor: '#FFF',
  },
  cardHeader: {
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeText: {
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  statusBadge: {
    paddingVertical: hp(0.8),
    paddingHorizontal: wp(4),
    borderRadius: fp(3),
    alignItems: 'center',
  },
  statusText: {
    fontSize: fp(1.6),
    color: '#1A1A1A',
    fontFamily: Fonts.medium,
  },
  iconContainer: {
    alignItems: 'center',
  },
  pencilIcon: {
    width: 16,
    height: 16,
    tintColor: colors.black,
    marginRight: 8,
  },
  expandIcon: {
    width: 16,
    height: 16,
    tintColor: colors.black,
    marginLeft: wp(1),
  },
  detailsContainer: {
    marginTop: hp(1.6),
  },
  detailLabel: {
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    // marginTop: hp(0.8),
    fontFamily: Fonts.medium,
  },
  detailText: {
    fontSize: fp(1.8),
    color: colors.black,
    marginVertical: hp(0.8),
    fontFamily: Fonts.medium,
  },
  infoRow: {
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: hp(1.2),
  },
  infoColumn: {
    flex: 1,
  },
  viewButton: {
    backgroundColor: colors.themeColor,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 20,
  },
  viewButtonText: {
    color: colors.white,
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
  },
});

export default ScheduleCard;
