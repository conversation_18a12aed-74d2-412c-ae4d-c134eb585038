import React, {useState, useEffect, useRef} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import styles from './styles';
import {useTranslation} from 'react-i18next';
import {
  useLazyGetScheduleDatesForCalendarTutorOpenSessionQuery,
  useLazyGetScheduleDatesForCalendarTutorQuery,
  useLazyGetTutorSchedulesForCalendarQuery,
  useLazyGetTutorSchedulesForOpenSessionQuery,
  useLazyGetTutorSchedulesForStudentQuery,
  useLazyGetTutorSchedulesQuery,
} from '../../Api/ApiSlice';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {useDispatch, useSelector} from 'react-redux';
import {
  updateSelectedSlots,
  updateSelectedSlotDate,
} from '../../Redux/Slices/Tutor/TutorSlotSlice';
import {showToast} from '../../Components/ToastHelper';
import {useRoute} from '@react-navigation/native';
import moment from 'moment';
import TaleemEventCalendar from '../../Components/Calendar/TaleemEventCalendar';
import {
  getFirstAndLastDates,
  getFormattedBookingDatesWithSlots,
  getFormattedDatesWithSlots,
  getRemainingDatesFormatted,
} from '../../Helper/Calendar/FormatAvailableSlotDate';
import {convertDecimalHoursToHoursAndMinutes} from '../../Helper/DateHelpers/DateHelpers';
import TimeSlot from '../SelectAvailableSlot/TimeSlot';

const SelectAvailableSlotTutor = ({navigation}) => {
  const dispatch = useDispatch();
  const route = useRoute();
  const {t, i18n} = useTranslation();
  const {classType} = route?.params || {}; // Tutor ID and class type from route params
  console.log('🚀 ~ SelectAvailableSlotTutor ~ classType:', classType);
  const {selectedSlots, selectedSlotDate} = useSelector(
    state => state.tutorSlot,
  );

  const [selectedDate, setSelectedDate] = useState(
    new Date().toISOString().split('T')[0],
  );
  const [isScheduleLoading, setIsScheduleLoading] = useState(false);
  const [showPicker, setShowPicker] = useState(true);
  const [isCalendarLoading, setIsCalendarLoading] = useState(false);
  const [localSelectedSlots, setLocalSelectedSlots] = useState([]);
  const [tutorScheduleData, setTutorScheduleData] = useState();
  const flatListRef = useRef(null);
  const markedDatesRef = useRef({});

  const [fetchSchedules, {data: scheduleCalendarData, isLoading, error}] =
    useLazyGetScheduleDatesForCalendarTutorOpenSessionQuery();

  const [
    triggerGetSchedules,
    {data: tutorScheduleDataRes, isLoading: slotLoading},
  ] = useLazyGetTutorSchedulesForOpenSessionQuery();

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    // Sync local state with Redux selected slots for the current date
    const dateSlots = selectedSlots[selectedDate] || [];
    setLocalSelectedSlots(dateSlots);
  }, [selectedDate, selectedSlots]);

  useEffect(() => {
    setIsScheduleLoading(true);
    triggerGetSchedules({
      date: new Date(selectedDate).toISOString().split('T')[0],
      class_type_id: classType.value == 'online' ? '1' : '2',
    })
      .unwrap()
      .then(response => {
        console.log('🚀 ~ useEffect ~ response:', response?.data);
        setIsScheduleLoading(false);
        setTutorScheduleData(response?.data);
      })
      .catch(err => {
        setIsScheduleLoading(false);
        console.error('Error fetching schedules:', err);
      });
  }, [selectedDate, classType]);

  const handleGetScheduleDataForCalendar = (
    year,
    month,
    startDate,
    endDate,
  ) => {
    setIsCalendarLoading(true);
    const params = {
      startDate: startDate,
      endDate: endDate,
      class_type_id: classType?.value == 'online' ? '1' : '2',
    };

    fetchSchedules(params)
      .unwrap()
      .then(response => {
        const scheduleDatesRes = response?.data?.schedule_dates;
        const bookingDatesApiRes = response?.data?.booking_dates || [];

        const remainingDatesFormattedData = getRemainingDatesFormatted(
          scheduleDatesRes,
          bookingDatesApiRes,
          year,
          month,
          '400',
          colors.darkGrey,
        );

        const slotsDatesFormattedData = getFormattedDatesWithSlots(
          scheduleDatesRes,
          bookingDatesApiRes,
          'bold',
          colors.darkBlack,
        );

        const bookingDatesFormattedData = getFormattedBookingDatesWithSlots(
          bookingDatesApiRes,
          {...slotsDatesFormattedData, ...remainingDatesFormattedData},
          'bold',
          colors.darkBlack,
        );

        const allFormattedDates = {
          ...remainingDatesFormattedData,
          ...slotsDatesFormattedData,
          ...bookingDatesFormattedData,
        };

        if (selectedDate) {
          allFormattedDates[selectedDate] = {
            ...allFormattedDates[selectedDate],
            customStyles: {
              container: {backgroundColor: colors.themeBackground},
              text: {
                color:
                  allFormattedDates[selectedDate]?.customStyles?.text?.color,
                fontWeight:
                  allFormattedDates[selectedDate]?.customStyles?.text
                    ?.fontWeight || '300',
              },
            },
          };
        }

        markedDatesRef.current = allFormattedDates;
        setIsCalendarLoading(false);
      })
      .catch(err => {
        console.error('Error:', err);
        showToast('error', err?.data?.message, 'bottom', isRTL);
        setIsCalendarLoading(false);
      });
  };

  useEffect(() => {
    const tempDate = selectedDate;
    const {firstDay, lastDay, year, month} = getFirstAndLastDates(tempDate);
    handleGetScheduleDataForCalendar(year, month, firstDay, lastDay);
  }, [selectedDate]);

  const handleSlotSelect = slot => {
    setLocalSelectedSlots(prevSelectedSlots => {
      const isAlreadySelected = prevSelectedSlots.some(
        selectedSlot => selectedSlot.id === slot.id,
      );

      const updatedSlots = isAlreadySelected
        ? prevSelectedSlots.filter(selectedSlot => selectedSlot.id !== slot.id)
        : [...prevSelectedSlots, slot];

      dispatch(
        updateSelectedSlots({
          date: selectedDate,
          slots: updatedSlots,
        }),
      );

      return updatedSlots;
    });
  };

  const handleDateSelect = date => {
    const dateString = date.dateString;
    setSelectedDate(dateString);

    const updatedMarkedDates = {...markedDatesRef.current};
    Object.keys(updatedMarkedDates)?.forEach(dateKey => {
      if (updatedMarkedDates[dateKey]?.selected) {
        delete updatedMarkedDates[dateKey].selected;
        delete updatedMarkedDates[dateKey].color;
        delete updatedMarkedDates[dateKey].textColor;
      }
    });

    updatedMarkedDates[dateString] = {
      ...updatedMarkedDates[dateString],
      selected: true,
      color: colors.purple,
      textColor: colors.white,
    };

    markedDatesRef.current = updatedMarkedDates;
    setShowPicker(false);
  };

  const getSelectedHours = () => {
    if (selectedSlots && Object.keys(selectedSlots).length > 0) {
      const totalHours = Object.values(selectedSlots)
        .flat()
        .reduce((sum, slot) => sum + parseFloat(slot?.hours_duration), 0);
      return totalHours;
    }
    return 0;
  };

  const handleSave = () => {
    const selectedHours = getSelectedHours();
    if (selectedHours === 0) {
      showToast('error', t('selectAtLeastOneSlot'), 'bottom', isRTL);
    } else {
      dispatch(updateSelectedSlotDate(selectedDate));
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('SelectAvailableSlot')}
      />

      <TouchableOpacity
        onPress={() => setShowPicker(!showPicker)}
        activeOpacity={0.8}
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          marginBottom: hp(1),
          justifyContent: 'space-between',
          marginHorizontal: wp(6),
          marginTop: hp(2),
        }}>
        <Text
          style={{
            fontSize: fp(1.8),
            color: colors.white,
            fontFamily: Fonts.bold,
          }}>
          {t('select_date')}
        </Text>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignSelf: 'center',
          }}>
          <Image
            source={icons.calanderYellow}
            style={{
              height: fp(2.6),
              width: fp(2.6),
              alignSelf: 'center',
            }}
            resizeMode="contain"
          />
          <Text
            style={[
              styles.dayTxt,
              {
                marginLeft: isRTL ? 0 : 10,
                marginRight: isRTL ? 10 : 0,
                color: colors.white,
              },
            ]}>
            {selectedDate
              ? moment(selectedDate).isSame(moment(), 'day')
                ? t('today')
                : moment(selectedDate).format('DD MMM YYYY')
              : ''}
          </Text>
          <Image
            source={icons.arrowDown}
            style={{height: 24, width: 24, marginLeft: 5}}
            resizeMode="contain"
            tintColor={colors.offWhite}
          />
        </View>
      </TouchableOpacity>

      <Modal transparent visible={showPicker}>
        <TouchableWithoutFeedback onPress={() => setShowPicker(false)}>
          <View style={styles.calender}>
            <TaleemEventCalendar
              selectedDate={selectedDate}
              handleDateSelect={handleDateSelect}
              markedDates={markedDatesRef.current}
              isLoading={isCalendarLoading}
              handleOnMonthChange={dateObj => {
                const {firstDay, lastDay, year, month} = getFirstAndLastDates(
                  dateObj.dateString,
                );
                handleGetScheduleDataForCalendar(
                  year,
                  month,
                  firstDay,
                  lastDay,
                );
              }}
              minDate={new Date().toISOString().split('T')[0]}
            />
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      <View style={styles.bottomContainer}>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('availableSlots')}</Text>
          {isLoading || isScheduleLoading ? (
            <ActivityIndicator size={'large'} color={colors.themeBackground} />
          ) : (
            <FlatList
              data={tutorScheduleData?.tutorSchedule?.rows}
              renderItem={({item}) => (
                <TimeSlot
                  item={item}
                  selected={localSelectedSlots.some(
                    slot =>
                      slot.start_time === item.start_time &&
                      slot.end_time === item.end_time,
                  )}
                  selectedSlots={localSelectedSlots}
                  currentDate={selectedDate}
                  onSelect={() => handleSlotSelect(item)}
                />
              )}
              keyExtractor={item => item.id.toString()}
              showsVerticalScrollIndicator={false}
              style={styles.slotsList}
              ListEmptyComponent={
                <View style={{justifyContent: 'center', alignItems: 'center'}}>
                  <Text
                    style={{
                      fontFamily: Fonts.semiBold,
                      fontSize: fp(1.8),
                      color: colors.black,
                    }}>
                    {t('noSlots')}
                  </Text>
                </View>
              }
              ListFooterComponent={
                <TouchableOpacity
                  onPress={handleSave}
                  style={styles.saveButton}>
                  <Text style={styles.saveButtonText}>{t('save')}</Text>
                </TouchableOpacity>
              }
            />
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SelectAvailableSlotTutor;
