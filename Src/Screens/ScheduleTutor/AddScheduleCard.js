import React, {useState} from 'react';
import {
  View,
  Text,
  Pressable,
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {
  convertTo12HourFormat,
  convertToLocal12HourFormat,
} from '../../Helper/DateHelpers/DateHelpers';
import CustomDropDown from '../../Components/CustomDropDown';
import {useTranslation} from 'react-i18next';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {useUpdateTutorScheduleMutation} from '../../Api/ApiSlice';
import {RECURRENCE_TYPE_DATA} from '../../Utils/constant';
import {showToast} from '../../Components/ToastHelper';
import {Fonts} from '../../Utils/Fonts';
import DayPicker from '../../Components/Custom_DayPicker/DayPicker';

const AddScheduleCard = ({
  item,
  selectedDate,
  refresh,
  onCrossPress,
  availablity,
}) => {
  console.log('🚀 ~ item:', item);
  const [isExpanded, setIsExpanded] = useState(false);
  const [recurrenceType, setRecurrenceType] = useState({
    name:
      item?.days_of_week?.length === 7
        ? 'Daily'
        : capitalizeFirstLetter(item?.recurrence_type),
    value: item?.days_of_week,
  });
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [updateTutorSchedule, {isLoading: scheduleLoading}] =
    useUpdateTutorScheduleMutation();

  const handleUpdateTutorSchedule = async () => {
    try {
      const payload = {
        start_time: item?.start_time,
        end_time: item?.end_time,
        recurrence_type: recurrenceType.name.toLowerCase(),
        month_dates:
          recurrenceType?.name === 'Monthly'
            ? [new Date(selectedDate).toISOString().split('T')[0]]
            : [],
        days_of_week:
          recurrenceType?.name === 'Weekly'
            ? [selectedDate.getDay().toString()]
            : recurrenceType?.name === 'Daily'
            ? ['0', '1', '2', '3', '4', '5', '6']
            : [],
        class_type_id: availablity,
      };

      const response = await updateTutorSchedule(payload, item?.id);

      showToast('success', response?.data?.message, 'bottom', isRTL);
    } catch (error) {
      console.error('Error in addScheduleApi:', error);

      // Show a toast with the error message
      showToast('danger', response?.data?.message, 'bottom', isRTL);
    }
  };

  function onRecurrencePress(recurrence_type) {
    setTimeout(() => {
      handleUpdateTutorSchedule(recurrence_type);
      setRecurrenceType(recurrence_type);
    }, 2000);
  }
  const days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
  return (
    <View>
      {/* <Pressable style={styles.card} onPress={() => setIsExpanded(!isExpanded)}> */}
      <Pressable style={[styles.card]} onPress={() => console.log('asdf')}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <View style={styles.cardHeader}>
            <Text style={styles.timeText}>
              {convertToLocal12HourFormat(item.start_time)}
              {'   '}-{'   '}
              {convertToLocal12HourFormat(item.end_time)}
            </Text>
          </View>

          <View
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              paddingVertical: hp(1),
            }}>
            {/* <View
              style={
                {
                  // backgroundColor: '#D4EBFF',
                  // borderRadius: fp(2),
                  // paddingVertical: fp(0.6),
                  // paddingHorizontal: fp(1.8),
                  // alignItems: 'center',
                  // justifyContent: 'center',
                  // marginVertical: hp(0.8),
                }
              }> */}
            {/* <Text
                style={{
                  color: colors.darkgray,
                  fontFamily: Fonts.medium,
                  fontSize: fp(1.6),
                }}>
                {item?.days_of_week?.length == 7 ? 'Daily' : 'Weekly'}
              </Text> */}
            {/* </View> */}

            <TouchableOpacity
              style={{marginHorizontal: fp(1), alignSelf: 'center'}}
              onPress={() => {
                setIsExpanded(!isExpanded);
              }}>
              <Image
                source={isExpanded ? icons.upArrow : icons.downArrowBlack}
                style={[
                  styles.expandIcon,
                  {marginHorizontal: fp(1), alignSelf: 'center'},
                ]}
              />
            </TouchableOpacity>

            {/* <CustomDropDown
          disabled={true}
          data={RECURRENCE_TYPE_DATA || []}
          lableStyle={styles.drowpdownLable}
          backgroundColor={'#D4EBFF'}
          defaultValue={recurrenceType}
          height={hp(2.4)}
          width={wp(24)}
          showIcon={false}
          onSelect={selectedRecurrenceType =>
            onRecurrencePress(selectedRecurrenceType)
          }
        /> */}
            <TouchableOpacity
              style={{alignSelf: 'center'}}
              onPress={onCrossPress}>
              <Image
                tintColor={colors.greyLight}
                resizeMode="contain"
                source={icons.cross}
                style={styles.plusIcon}
              />
            </TouchableOpacity>
          </View>
        </View>

        {isExpanded && (
          // <View style={styles.detailsContainer}>
          //   {item.tlm_booking_schedules?.length > 0 && (
          //     <>
          //       <Text
          //         style={[
          //           styles.detailLabel,
          //           {textAlign: isRTL ? 'right' : 'left'},
          //         ]}>
          //         {t('classDetails')}
          //       </Text>
          //       <Text
          //         style={[
          //           styles.detailText,
          //           {textAlign: isRTL ? 'right' : 'left'},
          //         ]}>
          //         {item?.title}
          //       </Text>
          //       <View
          //         style={[
          //           styles.infoRow,
          //           {flexDirection: isRTL ? 'row-reverse' : 'row'},
          //         ]}>
          //         <View
          //           style={[
          //             styles.infoColumn,
          //             {alignItems: isRTL ? 'flex-end' : 'flex-start'},
          //           ]}>
          //           <Text style={styles.detailLabel}>{t('duration')}</Text>
          //           <Text style={styles.detailText}>{item?.hours_duration}</Text>
          //         </View>
          //         <View
          //           style={[
          //             styles.infoColumn,
          //             {alignItems: isRTL ? 'flex-end' : 'flex-start'},
          //           ]}>
          //           <Text style={styles.detailLabel}>{t('Location')}</Text>
          //           <Text style={styles.detailText}>
          //             {item?.details?.location}
          //           </Text>
          //         </View>
          //         <Pressable style={styles.viewButton}>
          //           <Text style={styles.viewButtonText}>{t('view')}</Text>
          //         </Pressable>
          //       </View>
          //     </>
          //   )}
          //   {item.status === 'cancelled' && (
          //     <>
          //       <Text style={styles.detailLabel}>{t('reasons')}</Text>
          //       <Text style={styles.detailText}>{item?.details?.reason}</Text>
          //     </>
          //   )}
          //   {item.tlm_booking_schedules?.length < 1 && (
          //     <Text style={styles.detailText}>{t('slotAvailable')}</Text>
          //   )}
          // </View>\
          <View style={{marginVertical: hp(1.4)}}>
            <Text style={[styles.drowpdownLable, {marginBottom: hp(1)}]}>
              {t('repeatOn')}
            </Text>
            <View style={{flexDirection: 'row', gap: wp(1.2)}}>
              {item?.days_of_week.map((day, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => console.log('do nothing')}
                  style={[styles.dayButton, styles.selectedDayButton]}>
                  <Text style={[styles.dayText, styles.selectedDayText]}>
                    {days[day]}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderWidth: fp(0.1),
    borderColor: colors.lightisGrey,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 6,
    marginVertical: 5,
    width: wp(90),
    justifyContent: 'space-between',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeText: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  statusBadge: {
    paddingVertical: 5,
    paddingHorizontal: 12,
    borderRadius: 15,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    color: '#1A1A1A',
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pencilIcon: {
    width: 16,
    height: 16,
    tintColor: colors.black,
    marginRight: 8,
  },
  expandIcon: {
    width: 16,
    height: 16,
    tintColor: colors.black,
  },
  detailsContainer: {
    marginTop: 10,
  },
  detailLabel: {
    fontSize: 14,
    color: colors.txtGrey1,
    marginTop: 5,
  },
  detailText: {
    fontSize: 14,
    color: colors.black,
  },
  infoRow: {
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  infoColumn: {
    flex: 1,
    gap: 5,
  },
  viewButton: {
    backgroundColor: colors.themeColor,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 20,
  },
  viewButtonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
  plusIcon: {
    height: hp(2),
    width: hp(2),
    marginLeft: wp(1),
    // paddingHorizontal: 15,
    // paddingVertical: 20,
  },
  drowpdownLable: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  dayButton: {
    width: 38,
    height: 38,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 1,
  },
  selectedDayButton: {
    backgroundColor: colors.themeBackground,
  },
  dayText: {
    fontSize: fp(1.6),
    color: colors.themeBackground,
    fontFamily: Fonts.semiBold,
  },
  selectedDayText: {
    color: '#FFFFFF',
  },
});

export default AddScheduleCard;
