// styles.js
import {Dimensions, Platform, StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
const {height} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
    // paddingHorizontal: Platform.OS === 'android' ? 0 : 10,
  },
  monthTitle: {
    textAlign: 'center',
    color: '#fff',
    fontSize: 16,
    marginVertical: 10,
    fontFamily: Fonts.bold,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    paddingHorizontal: 10,
    marginBottom: 10,
  },
  arrow: {
    height: 10,
    width: 10,
    tintColor: '#fff',
    marginHorizontal: 5,
  },
  dateItem: {
    width: 40,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 3,
  },
  gradientWrapper: {
    borderRadius: 10,
  },
  selectedDate: {
    borderRadius: 10,
  },
  dateText: {
    color: '#fff',
    fontSize: 14,
    fontFamily: Fonts.regular,
  },
  selectedDateText: {
    color: '#333',
    fontWeight: 'bold',
  },
  bottomContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingTop: 20,
    width: '100%',
    paddingBottom: 20,
  },
  tabContainer: {
    marginVertical: 10,
    gap: 10,
    flexDirection: 'row',
    // flexWrap: 'wrap',
  },
  tabButton: {
    paddingVertical: 7,
    paddingHorizontal: 20,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    elevation: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
    marginBottom: 8,
    // minWidth: 100,
  },
  activeTabButton: {
    backgroundColor: colors.themeColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabButtonText: {
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
    textAlign: 'center',
  },
  activeTabButtonText: {
    fontSize: fp(1.6),
    color: '#fff',
    fontFamily: Fonts.medium,
    textAlign: 'center',
  },
  tabContentText: {
    fontSize: 16,
    color: colors.black,
    textAlign: 'center',
    marginVertical: 10,
    fontFamily: Fonts.medium,
  },
  saveButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    width: '100%',
    marginTop: 20,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: fp(1.8),
    fontFamily: Fonts.bold,
  },
  noDataAvailable: {
    fontFamily: Fonts.medium,
    color: colors.grey,
    fontSize: fp(1.6),
    marginTop: hp(2),
    textAlign: 'center',
  },
  availableTxt: {
    fontFamily: Fonts.bold,
    color: colors.black,
    fontSize: responsiveFontSize(16),
  },
  dayTxt: {
    marginLeft: 10,
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
    alignSelf: 'center',
  },
  calender: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.offBlack,
  },
  calender: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.offBlack,
    // borderRadius: fp(4),
  },
});

export default styles;
