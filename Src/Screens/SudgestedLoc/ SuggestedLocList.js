import {
  <PERSON><PERSON>ist,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import {useLazyGetTutorMeetingPointAddressQuery} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {DUMMY_COURSE_IMG2} from '../../Utils/constant';
import {useSelector} from 'react-redux';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';

const SubHeadingView = ({title = 'Heading', navigation}) => {
  return (
    <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
      <Text
        style={{
          color: colors.black,
          fontSize: fp(1.8),
          fontFamily: Fonts.medium,
          marginHorizontal: wp(3),
          marginTop: hp(2),
        }}>
        {title}
      </Text>
      <TouchableOpacity
        onPress={() => navigation?.navigate('ViewAllList', {type: title})}>
        <Text
          style={{
            color: colors.themeBackground,
            fontSize: fp(1.8),
            fontFamily: Fonts.medium,
            marginHorizontal: wp(3),
            marginTop: hp(2),
          }}>
          View all
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const SuggestedLocList = ({navigation}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredSuggestedVenues, setFilteredSuggestedVenues] = useState([]);
  const [filteredNearbyVenues, setFilteredNearbyVenues] = useState([]);
  const currentLoc = useSelector(state => state.currentLocSlice.currentLoc);
  console.log('🚀 ~ SuggestedLocList ~ currentLoc:', currentLoc);

  const [
    getTutorMeetingPointAddress,
    {data: tutorMeetingPointAddressData, isLoading},
  ] = useLazyGetTutorMeetingPointAddressQuery();

  useEffect(() => {
    getTutorMeetingPointAddress({
      type: 3,
      lat: currentLoc?.region?.latitude,
      long: currentLoc?.region?.longitude,
    });
  }, [currentLoc, getTutorMeetingPointAddress]);

  useEffect(() => {
    if (tutorMeetingPointAddressData?.data) {
      setFilteredSuggestedVenues(
        tutorMeetingPointAddressData.data.allMetingPoints || [],
      );
      setFilteredNearbyVenues(
        tutorMeetingPointAddressData.data.nearbyMeetingVenue || [],
      );
    }
  }, [tutorMeetingPointAddressData]);

  useEffect(() => {
    if (searchQuery) {
      const filteredSuggested =
        tutorMeetingPointAddressData?.data?.allMetingPoints?.filter(item =>
          item?.name?.toLowerCase().includes(searchQuery.toLowerCase()),
        ) || [];
      setFilteredSuggestedVenues(filteredSuggested);

      const filteredNearby =
        tutorMeetingPointAddressData?.data?.nearbyMeetingVenue?.filter(item =>
          item?.name?.toLowerCase().includes(searchQuery.toLowerCase()),
        ) || [];
      setFilteredNearbyVenues(filteredNearby);
    } else {
      setFilteredSuggestedVenues(
        tutorMeetingPointAddressData?.data?.allMetingPoints || [],
      );
      setFilteredNearbyVenues(
        tutorMeetingPointAddressData?.data?.nearbyMeetingVenue || [],
      );
    }
  }, [searchQuery, tutorMeetingPointAddressData]);

  const truncateAddress = address => {
    if (!address) return 'Address not available';
    return address.length > 20 ? `${address.substring(0, 20)}...` : address;
  };

  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const renderItem = ({item}) => {
    if (!item) return null;
    console.log('distance in suggested neeting point', item?.distance);
    return (
      <TouchableOpacity
        onPress={() => navigation?.navigate('SudgestedLoc', {id: item?.id})}
        style={{
          width: wp(45),
          marginHorizontal: wp(1),
          marginTop: hp(2),
        }}>
        <Image
          source={{
            uri: item?.image?.[0]
              ? `${IMAGE_BASE_URL}${item.image[0]}`
              : DUMMY_COURSE_IMG2,
          }}
          style={{height: hp(10), width: wp(38), borderRadius: fp(1)}}
        />
        <Text
          style={{
            fontSize: fp(1.6),
            fontFamily: Fonts.medium,
            lineHeight: hp(2),
            marginTop: hp(1),
            color: colors.black,
          }}>
          {item?.name || 'Unnamed Venue'}
        </Text>
        <Text
          style={{
            fontSize: fp(1.4),
            fontFamily: Fonts.medium,
            lineHeight: hp(2),
            marginTop: hp(0.4),
            color: colors.black,
          }}>
          {truncateAddress(item?.address)}
        </Text>
        {item?.distance > 0 && (
          <Text
            style={{
              fontSize: fp(1.6),
              fontFamily: Fonts.medium,
              lineHeight: hp(2),
              marginTop: hp(0.4),
              color: colors.black,
            }}>
            Only {item?.distance?.toFixed(2).toString()} km away
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
      <StatusContainer color={colors.white} />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('Select Venue')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />
      <Text
        style={{
          fontSize: fp(2.4),
          marginHorizontal: wp(3),
          fontFamily: Fonts.semiBold,
          color: colors.offBlack,
          lineHeight: hp(3),
          textAlign: 'left',
        }}>
        Discover amazing venues around you
      </Text>
      <View
        style={applyShadowStyleIos({
          flexDirection: isRTL ? 'row-reverse' : 'row',
          width: wp(80),
          marginTop: hp(2),
          paddingRight: 10,
          alignItems: 'center',
          width: wp(95),
          alignSelf: 'center',
        })}>
        <View
          style={{
            flex: 1,
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            backgroundColor: colors.white,
            borderRadius: 10,
            height: 35,
            marginLeft: isRTL ? 0 : 10,
            marginRight: isRTL ? 10 : 0,
            paddingHorizontal: 10,
            elevation: 2,
          }}>
          <Image
            style={{
              width: 18,
              height: 18,
              marginRight: isRTL ? 0 : 8,
              marginLeft: isRTL ? 8 : 0,
            }}
            tintColor={colors.darkGrey}
            source={icons.searchIcon}
            resizeMode="contain"
          />
          <TextInput
            style={{
              fontSize: 14,
              color: colors.searchGray,
              padding: 0,
              textAlign: isRTL ? 'right' : 'left',
              fontFamily: Fonts.medium,
              width: wp(70),
            }}
            placeholder={t('searchPlaceholder') || 'Search'}
            placeholderTextColor={colors.searchGray}
            value={searchQuery}
            onChangeText={text => setSearchQuery(text)}
          />
        </View>
      </View>
      <ScrollView>
        <SubHeadingView title="Suggested Venues" navigation={navigation} />
        <FlatList
          nestedScrollEnabled
          data={filteredSuggestedVenues}
          contentContainerStyle={{
            marginHorizontal: wp(4),
            paddingHorizontal: wp(2),
          }}
          numColumns={2}
          renderItem={renderItem}
          ListEmptyComponent={() => (
            <View
              style={{
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: hp(2),
              }}>
              <Text style={{fontFamily: Fonts.medium, fontSize: fp(1.4)}}>
                No Venues Found
              </Text>
            </View>
          )}
          keyExtractor={(item, index) =>
            item?.id ? String(item.id) : String(index)
          }
        />
        <View style={{marginTop: hp(1)}} />
        <SubHeadingView title="Nearby Venues" navigation={navigation} />
        <FlatList
          nestedScrollEnabled
          data={filteredNearbyVenues}
          contentContainerStyle={{
            marginHorizontal: wp(4),
            paddingHorizontal: wp(2),
          }}
          numColumns={2}
          renderItem={renderItem}
          ListEmptyComponent={() => (
            <View
              style={{
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: hp(2),
              }}>
              <Text style={{fontFamily: Fonts.medium, fontSize: fp(1.4)}}>
                No Nearby Venues Found
              </Text>
            </View>
          )}
          keyExtractor={(item, index) =>
            item?.id ? String(item.id) : String(index)
          }
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default SuggestedLocList;

const styles = StyleSheet.create({});
