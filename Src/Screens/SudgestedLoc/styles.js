import {StyleSheet} from 'react-native';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: 200,
    height: 200,
  },
  imageView: {
    borderRadius: 10,
    height: hp(30),
    marginHorizontal: wp(1),
    marginVertical: hp(1),
  },
  img: {
    height: hp(30),
    width: wp(90),
    resizeMode: 'cover',
    borderRadius: fp(2),
  },
  heading: {
    fontSize: fp(2),
    color: colors.black,
    fontFamily: Fonts.bold,
    width: wp(90),
  },
  heading2: {
    fontSize: fp(1.8),
    color: colors.black,
    fontFamily: Fonts.bold,
    width: wp(90),
  },
  addressView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
    width: wp(90),
  },
  address: {
    fontSize: fp(1.6),
    color: colors.blackSkatch,
    fontFamily: Fonts.medium,
    width: wp(80),
    lineHeight: hp(2),
  },
  info: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.offBlack,
    lineHeight: hp(2),
    width: wp(90),
    marginTop: hp(2),
  },
  flatListImg: {
    height: hp(10),
    width: wp(28),
    marginVertical: 10,
  },
});

export default styles;
