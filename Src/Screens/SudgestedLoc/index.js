import {Image, SafeAreaView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import TaleemHeader from '../../Components/TaleemHeader/TaleemHeader';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import styles from './styles';
import {FlatList} from 'react-native';
import {PrimaryButton} from '../../Components/CustomButton';
import {useTranslation} from 'react-i18next';
import {useGetSuggestedVenueDetailsQuery} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {DUMMY_COURSE_IMG2} from '../../Utils/constant';
import {tutorBookingJson} from '../../Api/Model/TutorBookingModel';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {useDispatch, useSelector} from 'react-redux';
import {updateCourseRateCardId} from '../../Redux/Slices/Student/TutorBookingSlice';
import {useDimensionsChange} from 'react-native-responsive-dimensions';

const SudgestedLoc = ({navigation, route}) => {
  const {id} = route?.params || {};
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const {data: venueDetails, isLoading} = useGetSuggestedVenueDetailsQuery(id);
  console.log('🚀 ~ SudgestedLoc ~ venueDetails:', venueDetails?.data);
  const {bookingFlowType} = useSelector(state => state?.tutorBookingSlice);
  console.log('🚀 ~ SudgestedLoc ~ bookingFlowType:', bookingFlowType);
  const {selectedRateCardId, courseRateCardId} = useSelector(
    state => state?.tutorBookingSlice,
  );
  const dispatch = useDispatch();
  const handlebookNow = () => {
    tutorBookingJson.meeting_point_preferred = 'suggested';
    tutorBookingJson.latitude = venueDetails?.data?.latitude;
    tutorBookingJson.longitude = venueDetails?.data?.longitude;
    tutorBookingJson.address = venueDetails?.data?.full_address;
    tutorBookingJson.meeting_venue_id = venueDetails?.data?.id;
    tutorBookingJson.save_address = false;
    if (bookingFlowType == 'courses') {
      navigation.navigate('BookYourTutorCourses');
      dispatch(updateCourseRateCardId(courseRateCardId));
    } else {
      navigation.navigate('BookYourTutor', {
        rate_card_id: selectedRateCardId,
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />

      <AppHeader isBackBtn={true} backIcon={icons.backIcon} />

      <View style={{flex: 1}}>
        <TaleemLoader isLoading={isLoading} />
        <View style={styles.imageView}>
          <Image
            source={{
              uri:
                venueDetails?.data?.image?.length > 0
                  ? `${IMAGE_BASE_URL}${venueDetails.data.image[0]}`
                  : // : DUMMY_COURSE_IMG2,
                    'https://www.freeiconspng.com/uploads/no-image-icon-6.png',
            }}
            style={styles.img}
          />
        </View>
        <Text style={[styles.heading, {textAlign: isRTL ? 'right' : 'left'}]}>
          {venueDetails?.data?.name}
        </Text>
        <View
          style={[
            styles.addressView,
            {
              flexDirection: isRTL ? 'row-reverse' : 'row',
              justifyContent: isRTL ? 'flex-end' : 'flex-start',
            },
          ]}>
          <Image
            source={icons.locationGray}
            resizeMode="contain"
            style={{height: 30, width: 30}}
          />
          <Text style={styles.address}>{venueDetails?.data?.full_address}</Text>
        </View>
        <View style={{alignItems: isRTL ? 'flex-end' : 'flex-start'}}>
          <Text style={styles.heading2}>{t('Details')}</Text>
          <Text style={styles.info}>{venueDetails?.data?.description}</Text>
        </View>
        <View style={{marginVertical: 10}}>
          <FlatList
            data={venueDetails?.data?.image.slice(1)}
            numColumns={3}
            columnWrapperStyle={{justifyContent: 'space-between'}}
            renderItem={({item, index}) => {
              return <Image source={item.url} style={styles.flatListImg} />;
            }}
          />
        </View>
      </View>
      <PrimaryButton
        onPress={handlebookNow}
        title={t('book_now')}
        style={{
          backgroundColor: colors.themeColor,
          marginBottom: 15,
        }}
        textStyle={{fontSize: 16, color: colors.white}}
        loading={false}
      />
    </SafeAreaView>
  );
};

export default SudgestedLoc;
