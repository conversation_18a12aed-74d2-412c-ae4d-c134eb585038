import {
  FlatList,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import {useLazyGetTutorMeetingPointAddressQuery} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {DUMMY_COURSE_IMG2} from '../../Utils/constant';
import {current, formatProdErrorMessage} from '@reduxjs/toolkit';
import {useSelector} from 'react-redux';

const ViewAllList = ({navigation, route}) => {
  const {type = 'all'} = route?.params;
  const SubHeadingView = ({title = 'Heading'}) => {
    return (
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <Text
          style={{
            color: colors.black,
            fontSize: fp(1.8),
            fontFamily: Fonts.medium,
            marginHorizontal: wp(3),
            marginTop: hp(2),
          }}>
          {title}
        </Text>
      </View>
    );
  };
  // const [suggestedMeetingPoints, setSuggestedMeetingPoints] = useState([])
  const [searchQuery, setSearchQuery] = useState('');
  const currentLoc = useSelector(state => state.currentLocSlice.currentLoc);
  console.log('🚀 ~ SuggestedLocList ~ currentLoc:', currentLoc);

  const [
    getTutorMeetingPointAddress,
    {data: tutorMeetingPointAddressData, isLoading},
  ] = useLazyGetTutorMeetingPointAddressQuery();
  console.log(
    '🚀 ~ SuggestedLocList ~ tutorMeetingPointAddressData:',
    tutorMeetingPointAddressData,
  );

  useEffect(() => {
    getTutorMeetingPointAddress({
      type: 3,
      lat: currentLoc?.region?.latitude,
      long: currentLoc?.region?.longitude,
    }).then(response => {
      console.log(
        '🚀 ~ handleOnSuggestionsPress ~ response:',
        response?.data?.data,
      );
    });
  }, []);
  const truncateAddress = address => {
    if (address.length > 20) {
      return address.substring(0, 40) + '...';
    }
    return address;
  };
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const renderItem = ({item}) => (
    <TouchableOpacity
      onPress={() => {
        navigation.navigate('SudgestedLoc', {id: item?.id});
      }}
      style={{
        // width: wp(95),
        // marginHorizontal: wp(1),
        marginTop: hp(2),
        elevation: 1,
        backgroundColor: colors.white,
        borderRadius: fp(1),
        padding: 10,
      }}>
      <Image
        source={{
          uri:
            item?.image?.length > 0
              ? `${IMAGE_BASE_URL}${item.image[0]}`
              : DUMMY_COURSE_IMG2,
        }}
        style={{
          height: hp(12),
          width: wp(84),
          //   marginTop: hp(0.8),
          borderRadius: fp(1),
          alignSelf: 'center',
        }}
      />
      <Text
        style={{
          fontSize: fp(1.6),
          fontFamily: Fonts.medium,
          lineHeight: hp(2),
          marginTop: hp(1),
          color: colors.black,
        }}>
        {item.name}
      </Text>
      <Text
        style={{
          fontSize: fp(1.4),
          fontFamily: Fonts.medium,
          lineHeight: hp(2),
          marginTop: hp(0.4),
          color: colors.black,
        }}>
        {truncateAddress(item.address)}
      </Text>
      {item.distance && (
        <Text
          style={{
            fontSize: fp(1.6),
            fontFamily: Fonts.medium,
            lineHeight: hp(2),
            marginTop: hp(0.4),
            color: colors.black,
          }}>
          Only {item?.distance.toFixed(2)} km away
        </Text>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
      <StatusContainer color={colors.white} />
      {/* Header */}
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('Select Venue')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />
      <Text
        style={{
          fontSize: fp(2.4),
          marginHorizontal: wp(3),
          fontFamily: Fonts.semiBold,
          color: colors.offBlack,
          lineHeight: hp(3),
          textAlign: 'left',
        }}>
        Discover amazing venues around you
      </Text>
      <View
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          width: wp(80),
          marginTop: hp(2),
          paddingRight: 10,
          alignItems: 'center',
          width: wp(95),
          alignSelf: 'center',
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            backgroundColor: colors.white,
            borderRadius: 10,
            height: 35,
            marginLeft: isRTL ? 0 : 10,
            marginRight: isRTL ? 10 : 0,
            paddingHorizontal: 10,
            elevation: 2,
          }}>
          <Image
            style={{
              width: 18,
              height: 18,
              marginRight: isRTL ? 0 : 8,
              marginLeft: isRTL ? 8 : 0,
            }}
            tintColor={colors.darkGrey}
            source={icons.searchIcon}
            resizeMode="contain"
          />
          <TextInput
            style={{
              //   flex: 1,
              fontSize: 14,
              color: colors.searchGray,
              padding: 0,
              textAlign: isRTL ? 'right' : 'left',
              fontFamily: Fonts.medium,
              // backgroundColor: 'red'
            }}
            placeholder={t('searchPlaceholder') || 'Search'}
            placeholderTextColor={colors.searchGray}
            value={searchQuery}
            onSubmitEditing={() => {
              console.log('onSubmitEditing');
            }}
            onChangeText={text => setSearchQuery(text)}
          />
        </View>
      </View>
      {/* <ScrollView> */}
      {SubHeadingView({title: type})}
      <FlatList
        nestedScrollEnabled
        data={
          type == 'Suggested Venues'
            ? tutorMeetingPointAddressData?.data?.allMetingPoints
            : tutorMeetingPointAddressData?.data?.nearbyMeetingVenue
        }
        contentContainerStyle={{
          marginHorizontal: wp(4),
          paddingHorizontal: wp(2),
          paddingBottom: hp(2),
        }}
        renderItem={renderItem}
        keyExtractor={(item, index) => index.toString()} // Add a keyExtractor for unique keys
      />
      {/* </ScrollView> */}
    </SafeAreaView>
  );
};

export default ViewAllList;

const styles = StyleSheet.create({});
