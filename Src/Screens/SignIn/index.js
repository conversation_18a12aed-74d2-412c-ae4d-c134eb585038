import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Platform,
  SafeAreaView,
  ScrollView,
  I18nManager,
  BackHandler,
} from 'react-native';
import {IconTextButton, PrimaryButton} from '../../Components/CustomButton';
import {AppHeader} from '../../Components/HeaderForLogin';
import MobileNumberInput from '../../Components/MobileNumberInput';
import {AppLogo, Or, TextLink} from '../../Components/Rest';
import {SubTitle, Title} from '../../Components/Title';
import {showToast} from '../../Components/ToastHelper';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import styles from './styles';
import {
  useSendOtpMutation,
  useSocialSigninMutation,
  useValidateSocialLoginApiMutation,
} from '../../Api/ApiSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useFocusEffect} from '@react-navigation/native';
import {Fonts} from '../../Utils/Fonts';
import {useDispatch, useSelector} from 'react-redux';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import auth from '@react-native-firebase/auth';
import appleAuth from '@invertase/react-native-apple-authentication';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {maxLengthsByCountry} from '../../Utils/CountryCodeLength';
import {setAuthData, setIsLoggedIn} from '../../Features/authSlice';
import * as Keychain from 'react-native-keychain';

const LoginScreen = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [mobileNumber, setMobileNumber] = useState('');
  const [callingCode, setCallingCode] = useState('974');
  const [countryCode, setCountryCode] = useState('QA');
  const [userType, setUserType] = useState(null);
  const langType = useSelector(state => state.auth.appLocale) || 'en';
  // const [langType, setLangType] = useState('');
  const [sendOtp, {isLoading: isSendOtpLoading}] = useSendOtpMutation();
  const [socialSignin] = useSocialSigninMutation(); // Social Sign-In Mutation
  const [validateSocialLoginApi] = useValidateSocialLoginApiMutation(); // validate is social logged in Mutation
  GoogleSignin.configure({
    webClientId:
      '128323862743-trqjgacsjuhrln2ech2fn2oj3v9mifgq.apps.googleusercontent.com', // Use your Web Client ID from Firebase Console
    iosClientId:
      '128323862743-vjdc9isgv7qdt0orq2fbeu2mt2hgo141.apps.googleusercontent.com',
    // / webClientId: WEB_CLIENT_ID, // Use your Web Client ID from Firebase Console
  });

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        navigation.navigate('WelcomeScreen');
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, [navigation]),
  );

  const validateMobileNumber = number => {
    const mobileNumberPattern = /^[0-9]{6,16}$/;
    return mobileNumberPattern.test(number);
  };

  const getUserType = async () => {
    const storedUserType = await AsyncStorage.getItem('userType');
    return storedUserType ? JSON.parse(storedUserType) : null;
  };

  useEffect(() => {
    const fetchUserType = async () => {
      const type = await getUserType();
      setUserType(type);
    };
    fetchUserType();
  }, []);
  const dispatch = useDispatch();
  const onGoogleButtonPress = async () => {
    console.log('📲 Triggering Google Sign-In...');
    try {
      const {idToken} = await GoogleSignin.signIn();
      console.log('✅ Google Sign-In successful. Token received: ', idToken);

      console.log('🔑 Generating Google Credential...');
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);

      console.log('🔒 Signing into Firebase with Google Credential...');
      const userCredential = await auth().signInWithCredential(
        googleCredential,
      );

      console.log('👤 Firebase User Credential Received:', userCredential);
      const user = userCredential.user;

      console.log('✅ Extracted Google User Info: ', {
        uid: user?.uid,
        displayName: user?.displayName,
        email: user?.email,
        photoURL: user?.photoURL,
      });

      console.log('🚀 Initiating Social Sign-In API Call...', {
        social_id: user.uid,
        login_type: 'google',
        name: user.displayName,
        email: user.email,
        social_token: idToken,
        country_code: callingCode,
        user_type: userType.toString() || '1',
      });
      const body = {
        social_id: user.uid,
        login_type: 'google',
        name: user.displayName,
        email: user.email,
        social_token: idToken,
        country_code: callingCode,
        user_type: userType.toString() || '1',
      };

      const validateSocialBody = {social_id: user?.uid};
      const response = await validateSocialLoginApi(
        validateSocialBody,
      ).unwrap();
      if (response?.data?.isAlreadyExists) {
        const response = await socialSignin(body).unwrap();
        if (response?.status) {
          const userData = {
            token: response.data.token,
            userId: response.data.id,
            user_type: response.data.user_type,
            action_type: response.data.action,
            profile_image: null,
            profile_accounts: response?.data?.profile_accounts,
          };
          if (response?.data?.profile_accounts > 1) {
            navigation?.navigate('ChooseProfile');
            dispatch(setAuthData(userData));
          } else {
            dispatch(setAuthData(userData));
            dispatch(setIsLoggedIn(true));
          }
        } else {
          console.log('❌ Social Sign-In Failed. Response: ', response);
          showToast('error', response?.message || t('googleLoginFailed'));
        }
      } else {
        //  navigation.navigate('CreateYourAccount', {
        //    mobileNumber: mobileNumber,
        //    countryCodeParam: countryCode,
        //    callingCodeParam: callingCode,

        //  });
        console.log('User does not exist, navigating to CreateYourAccount');
        navigation.navigate('CreateYourAccount', {body: body});
        //navigate to signup
      }
      console.log('🛠️ Social Sign-In API Response: ', response);
    } catch (error) {
      console.error('❌ Google Login Error: ', error);

      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        console.log('⚠️ User cancelled the login process.');
        showToast('error', t('googleLoginCancelled'));
      } else if (error.code === statusCodes.IN_PROGRESS) {
        console.log('⚠️ Login process already in progress.');
        showToast('error', t('googleLoginInProgress'));
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.log('⚠️ Google Play Services not available or outdated.');
        showToast('error', t('playServicesUnavailable'));
      } else if (error.code === statusCodes.DEVELOPER_ERROR) {
        console.log(
          '❌ DEVELOPER_ERROR: Check WEB_CLIENT_ID and Firebase configuration.',
        );
        showToast('error', t('googleLoginFailed'));
      } else {
        console.log('❌ Unknown error occurred during Google Sign-In.');
        showToast('error', t('googleLoginFailed'));
      }
    }
  };

  const handleGetStarted = async () => {
    const minLength = maxLengthsByCountry[countryCode] || 0;
    if (mobileNumber.length < minLength) {
      showToast(
        'error',
        t('mobile_number_min_length', {minLength}), // Pass minLength as a variable
      );

      return;
    }

    if (!mobileNumber) {
      showToast('error', t('missing_mobile_number'));
      return;
    }

    if (!validateMobileNumber(mobileNumber)) {
      showToast('error', t('invalid_phoneNumber'));
      // showToast('error', t('enter_mobile_number'));
      return;
    }

    try {
      const response = await sendOtp({
        mobileNumber,
        country_code: callingCode,
        language: langType,
        user_type: userType.toString(),
      });

      // console.log('response is following in SignIn--', response);

      if (response.error) {
        console.log(response.error, 'response error message');
        const errorMessage =
          response.error?.data?.message || 'Failed to send OTP';
        console.log('error Message in login is----', errorMessage);
        showToast('error', errorMessage);
        if (
          errorMessage == 'User does not exist.' ||
          response.error?.status === 409
        ) {
          navigation.navigate('CreateYourAccount', {
            mobileNumber: mobileNumber,
            countryCodeParam: countryCode,
            callingCodeParam: callingCode,
          });
        }
      } else {
        const {status, message} = response.data;

        if (status === true) {
          showToast('success', message);
          navigation.navigate('OtpVerificationScreen', {
            token: response?.data?.data?.token,
            action: 'login',
            userData: {
              mobile_no: mobileNumber,
              country_code: callingCode,
            },
            handleGetStarted,
          });
        } else {
          showToast('error', message);
        }
      }
    } catch (error) {
      showToast('error', t('unExpectedError'));
    }
  };

  // async function onAppleButtonPress() {
  //   console.log('apple button Pressed');
  //   // performs login request
  //   const appleAuthRequestResponse = await appleAuth.performRequest({
  //     requestedOperation: appleAuth.Operation.LOGIN,
  //     // Note: it appears putting FULL_NAME first is important, see issue #293
  //     requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
  //   });

  //   // get current authentication state for user
  //   // /!\ This method must be tested on a real device. On the iOS simulator it always throws an error.
  //   const credentialState = await appleAuth.getCredentialStateForUser(
  //     appleAuthRequestResponse.user,
  //   );
  //   // use credentialState response to ensure the user is authenticated
  //   if (credentialState === appleAuth.State.AUTHORIZED) {
  //     // user is authenticated
  //     const {identityToken, email, fullName, user} = appleAuthRequestResponse;
  //     // Log user credentials
  //     console.log('Identity Token:', identityToken);
  //     console.log('Email:', email);
  //     console.log('Full Name:', fullName);
  //     console.log('user:', user);
  //     if (email === null && fullName?.givenName === null)
  //       console.log('apple Login');
  //     // userSignupWithApple('Apple', null, user + '@apple.com');
  //     else if (email === null) console.log('asdf');
  //     // userSignupWithApple(
  //     //   fullName?.givenName,
  //     //   fullName?.familyName,
  //     //   user + '@apple.com',
  //     // );
  //     else console.log('asdf');
  //     // userSignupWithApple(fullName?.givenName, fullName?.familyName, email);
  //   }
  // }
  // async function onAppleButtonPress() {
  //   console.log('📲 Triggering Apple Sign-In...');
  //   try {
  //     // Perform login request
  //     const appleAuthRequestResponse = await appleAuth.performRequest({
  //       requestedOperation: appleAuth.Operation.LOGIN,
  //       requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
  //     });

  //     console.log(
  //       '✅ Apple Sign-In successful. Response received: ',
  //       appleAuthRequestResponse,
  //     );

  //     // Get current authentication state for user
  //     const credentialState = await appleAuth.getCredentialStateForUser(
  //       appleAuthRequestResponse.user,
  //     );

  //     if (credentialState === appleAuth.State.AUTHORIZED) {
  //       console.log('✅ User is authenticated with Apple');

  //       const {identityToken, email, fullName, user} = appleAuthRequestResponse;
  //       console.log('🚀 ~ onAppleButtonPress ~ fullName:', fullName);
  //       console.log(
  //         '🚀 ~ onAppleButtonPress ~ appleAuthRequestResponse:',
  //         appleAuthRequestResponse,
  //       );
  //       const userName = fullName.givenName + ' ' + fullName.familyName;
  //       // fullName?.givenName && fullName?.familyName
  //       //   ? `${fullName.givenName} ${fullName.familyName}`
  //       //   : fullName?.givenName;

  //       // --- Apple Sign-In: Save or Retrieve Name/Email using Keychain ---
  //       let finalEmail = email;
  //       let finalUserName = userName;
  //       const keychainKey = `apple_user_${user}`;

  //       if (email || userName) {
  //         // Save to Keychain for future logins
  //         try {
  //           await Keychain.setGenericPassword(
  //             keychainKey,
  //             JSON.stringify({email: email || `${user}@apple.com`, userName}),
  //           );
  //           console.log('✅ Saved Apple user info to Keychain');
  //         } catch (e) {
  //           console.log('❌ Failed to save Apple user info to Keychain', e);
  //         }
  //       } else {
  //         // Try to retrieve from Keychain
  //         try {
  //           const credentials = await Keychain.getGenericPassword({
  //             service: keychainKey,
  //           });
  //           if (credentials) {
  //             const parsed = JSON.parse(credentials.password);
  //             finalEmail = parsed.email;
  //             finalUserName = parsed.userName;
  //             console.log(
  //               '✅ Retrieved Apple user info from Keychain:',
  //               parsed,
  //             );
  //           } else {
  //             finalEmail = `${user}@apple.com`;
  //             finalUserName = '';
  //             console.log(
  //               '⚠️ No Apple user info found in Keychain, using fallback values.',
  //             );
  //           }
  //         } catch (e) {
  //           finalEmail = `${user}@apple.com`;
  //           finalUserName = '';
  //           console.log(
  //             '❌ Failed to retrieve Apple user info from Keychain',
  //             e,
  //           );
  //         }
  //       }

  //       // Use finalEmail and finalUserName below
  //       console.log('👤 Extracted Apple User Info: ', {
  //         uid: user,
  //         displayName: finalUserName,
  //         email: finalEmail, // Fallback email if not provided
  //         identityToken,
  //       });

  //       console.log('🚀 Initiating Social Sign-In API Call...', {
  //         social_id: user,
  //         login_type: 'apple',
  //         name: finalUserName,
  //         email: finalEmail,
  //         social_token: identityToken,
  //         country_code: callingCode,
  //         user_type: userType.toString() || '1',
  //       });

  //       const body = {
  //         social_id: user, // Apple's user ID
  //         login_type: 'apple',
  //         name: finalUserName,
  //         email: finalEmail, // Apple may not provide email on subsequent logins
  //         social_token: identityToken,
  //         country_code: callingCode,
  //         user_type: userType.toString() || '1',
  //       };

  //       const validateSocialBody = {social_id: user};
  //       const response = await validateSocialLoginApi(
  //         validateSocialBody,
  //       ).unwrap();

  //       console.log('🛠️ Social Validation API Response: ', response);

  //       if (response?.data?.isAlreadyExists) {
  //         const signInResponse = await socialSignin(body).unwrap();
  //         console.log('🛠️ Social Sign-In API Response: ', signInResponse);

  //         if (signInResponse?.status) {
  //           const userData = {
  //             token: signInResponse.data.token,
  //             userId: signInResponse.data.id,
  //             user_type: signInResponse.data.user_type,
  //             action_type: signInResponse.data.action,
  //             profile_image: null,
  //             profile_accounts: signInResponse?.data?.profile_accounts,
  //           };

  //           if (signInResponse?.data?.profile_accounts > 1) {
  //             navigation.navigate('ChooseProfile');
  //             dispatch(setAuthData(userData));
  //           } else {
  //             dispatch(setAuthData(userData));
  //             dispatch(setIsLoggedIn(true));
  //           }
  //         } else {
  //           console.log('❌ Social Sign-In Failed. Response: ', signInResponse);
  //           showToast(
  //             'error',
  //             signInResponse?.message || t('appleLoginFailed'),
  //           );
  //         }
  //       } else {
  //         navigation.navigate('CreateYourAccount', {body: body});
  //       }
  //     } else {
  //       console.log(
  //         '⚠️ Apple authentication state not authorized:',
  //         credentialState,
  //       );
  //       showToast('error', t('appleLoginFailed'));
  //     }
  //   } catch (error) {
  //     console.error('❌ Apple Login Error: ', error);

  //     if (error.code === appleAuth.Error.CANCELED) {
  //       console.log('⚠️ User cancelled the login process.');
  //       showToast('error', t('appleLoginCancelled'));
  //     } else if (error.code === appleAuth.Error.FAILED) {
  //       console.log('⚠️ Apple Sign-In failed.');
  //       showToast('error', t('appleLoginFailed'));
  //     } else if (error.code === appleAuth.Error.NOT_HANDLED) {
  //       console.log('⚠️ Request not handled.');
  //       showToast('error', t('appleLoginFailed'));
  //     } else if (error.code === appleAuth.Error.UNKNOWN) {
  //       console.log('⚠️ Unknown error occurred.');
  //       showToast('error', t('appleLoginFailed'));
  //     } else {
  //       console.log('❌ Unknown error occurred during Apple Sign-In.');
  //       showToast('error', t('appleLoginFailed'));
  //     }
  //   }
  // }
  async function onAppleButtonPress() {
    console.log('📲 Triggering Apple Sign-In...');
    try {
      // Perform login request
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
      });

      console.log(
        '✅ Apple Sign-In successful. Response:',
        appleAuthRequestResponse,
      );

      // Get current authentication state for user
      const credentialState = await appleAuth.getCredentialStateForUser(
        appleAuthRequestResponse.user,
      );

      if (credentialState !== appleAuth.State.AUTHORIZED) {
        console.log('⚠️ Apple authentication not authorized:', credentialState);
        showToast('error', t('appleLoginFailed'));
        return;
      }

      console.log('✅ User is authenticated with Apple');

      const {identityToken, email, fullName, user} = appleAuthRequestResponse;

      // Handle user info (with keychain storage)
      const userInfo = await handleAppleUserInfo(user, email, fullName);

      // Proceed with login/signup flow
      await processAppleAuthentication(
        user,
        identityToken,
        userInfo.email,
        userInfo.userName,
      );
    } catch (error) {
      handleAppleSignInError(error);
    }
  }

  // Extract user info handling to a separate function
  async function handleAppleUserInfo(userId, email, fullName) {
    const keychainKey = `apple_user_${userId}`;
    let finalEmail = email;
    let finalUserName = '';

    // If we have fullName from this login attempt, use and save it
    if (fullName && fullName.givenName) {
      finalUserName = [fullName.givenName, fullName.familyName]
        .filter(Boolean)
        .join(' ');

      // Save to keychain for future use
      try {
        await Keychain.setGenericPassword(
          keychainKey,
          JSON.stringify({
            email: email || `${userId}@apple.com`,
            userName: finalUserName,
          }),
        );
        console.log('✅ Saved Apple user info to Keychain');
      } catch (e) {
        console.log('❌ Failed to save Apple user info to Keychain', e);
      }
    } else {
      // No fullName in this login attempt, try to retrieve from keychain
      try {
        const credentials = await Keychain.getGenericPassword();

        // The key issue was here - you need to specify the service parameter correctly
        // when retrieving from keychain
        if (credentials && credentials.username === keychainKey) {
          const parsed = JSON.parse(credentials.password);
          finalEmail = parsed.email || finalEmail;
          finalUserName = parsed.userName || '';
          console.log('✅ Retrieved Apple user info from Keychain:', parsed);
        } else {
          // Try with explicit service parameter
          const serviceCredentials = await Keychain.getGenericPassword({
            service: keychainKey,
          });

          if (serviceCredentials) {
            const parsed = JSON.parse(serviceCredentials.password);
            finalEmail = parsed.email || finalEmail;
            finalUserName = parsed.userName || '';
            console.log(
              '✅ Retrieved Apple user info from Keychain with service param:',
              parsed,
            );
          } else {
            finalEmail = finalEmail || `${userId}@apple.com`;
            console.log(
              '⚠️ No Apple user info found in Keychain, using fallback values.',
            );
          }
        }
      } catch (e) {
        finalEmail = finalEmail || `${userId}@apple.com`;
        console.log('❌ Failed to retrieve Apple user info from Keychain', e);
      }
    }

    console.log('👤 Final Apple User Info:', {
      userId,
      userName: finalUserName,
      email: finalEmail,
    });
    return {email: finalEmail, userName: finalUserName};
  }

  // Process authentication with backend
  async function processAppleAuthentication(
    userId,
    identityToken,
    email,
    userName,
  ) {
    const body = {
      social_id: userId,
      login_type: 'apple',
      name: userName,
      email: email,
      social_token: identityToken,
      country_code: callingCode,
      user_type: userType.toString() || '1',
    };

    console.log('🚀 Initiating Social Validation API Call...', {
      social_id: userId,
    });

    try {
      const validateSocialBody = {social_id: userId};
      const response = await validateSocialLoginApi(
        validateSocialBody,
      ).unwrap();

      console.log('🛠️ Social Validation API Response:', response);

      if (response?.data?.isAlreadyExists) {
        await handleExistingUser(body);
      } else {
        navigation.navigate('CreateYourAccount', {body: body});
      }
    } catch (error) {
      console.error('❌ API Error:', error);
      showToast('error', t('appleLoginFailed'));
    }
  }

  // Handle existing user login
  async function handleExistingUser(body) {
    try {
      const signInResponse = await socialSignin(body).unwrap();
      console.log('🛠️ Social Sign-In API Response:', signInResponse);

      if (signInResponse?.status) {
        const userData = {
          token: signInResponse.data.token,
          userId: signInResponse.data.id,
          user_type: signInResponse.data.user_type,
          action_type: signInResponse.data.action,
          profile_image: null,
          profile_accounts: signInResponse?.data?.profile_accounts,
        };

        dispatch(setAuthData(userData));

        if (signInResponse?.data?.profile_accounts > 1) {
          navigation.navigate('ChooseProfile');
        } else {
          dispatch(setIsLoggedIn(true));
        }
      } else {
        console.log('❌ Social Sign-In Failed. Response:', signInResponse);
        showToast('error', signInResponse?.message || t('appleLoginFailed'));
      }
    } catch (error) {
      console.error('❌ Social Sign-In API Error:', error);
      showToast('error', t('appleLoginFailed'));
    }
  }

  // Handle Apple Sign-In errors
  function handleAppleSignInError(error) {
    console.error('❌ Apple Login Error:', error);

    if (error.code === appleAuth.Error.CANCELED) {
      console.log('⚠️ User cancelled the login process.');
      showToast('error', t('appleLoginCancelled'));
    } else if (error.code === appleAuth.Error.FAILED) {
      console.log('⚠️ Apple Sign-In failed.');
      showToast('error', t('appleLoginFailed'));
    } else if (error.code === appleAuth.Error.NOT_HANDLED) {
      console.log('⚠️ Request not handled.');
      showToast('error', t('appleLoginFailed'));
    } else if (error.code === appleAuth.Error.UNKNOWN) {
      console.log('⚠️ Unknown error occurred.');
      showToast('error', t('appleLoginFailed'));
    } else {
      console.log('❌ Unknown error occurred during Apple Sign-In.');
      showToast('error', t('appleLoginFailed'));
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader backIcon={icons.backIcon} isBackBtn title={''} />
      <ScrollView keyboardShouldPersistTaps="always" style={{marginBottom: 10}}>
        <AppLogo langType={langType} />
        {/* <Image
          source={require('../../Assets/Images/generalLogoEng.png')}
          resizeMode="contain"
          style={{width: fp(10), height: fp(10)}} */}
        {/* /> */}

        <Title
          text={t('login_Txt')}
          style={{
            textAlign: isRTL ? 'right' : 'left',
            fontFamily: Fonts.bold,
            fontSize: fp(2.8),
            paddingVertical: hp(1),
          }}
        />
        <SubTitle
          text={t('subTitle_Txt')}
          style={{
            textAlign: isRTL ? 'right' : 'left',
            marginRight: isRTL ? 20 : 0,
          }}
        />

        <MobileNumberInput
          title={`${t('mobile_Txt')}*`}
          value={mobileNumber}
          onChange={setMobileNumber}
          callingCode={callingCode}
          onCodeChange={setCallingCode}
          countryCode={countryCode}
          onCountryCodeChange={setCountryCode}
          placeholder={t('enter_number_Txt')}
          maxLength={maxLengthsByCountry[countryCode]}
          placeholderTextColor={colors.txtGrey1}
        />

        <PrimaryButton
          onPress={handleGetStarted}
          title={t('get_Started')}
          textStyle={{
            fontSize: fp(1.8),
            fontFamily: Fonts.medium,
            color: colors.white,
          }}
          loading={isSendOtpLoading}
          disabled={isSendOtpLoading}
        />

        <Or
          style={{alignSelf: I18nManager.isRTL ? 'flex-end' : 'flex-start'}}
        />

        <IconTextButton
          onPress={onGoogleButtonPress}
          title={t('continueGoogle_Txt')}
          icon={icons.googleLogo}
          style={{alignSelf: I18nManager.isRTL ? 'flex-end' : 'flex-start'}}
        />

        {Platform.OS === 'ios' && (
          <IconTextButton
            onPress={() => onAppleButtonPress()}
            title={t('continueApple_Txt')}
            icon={icons.appleLogo}
            style={{alignSelf: I18nManager.isRTL ? 'flex-end' : 'flex-start'}}
          />
        )}

        <TextLink
          onPress={() => navigation.navigate('CreateYourAccount')}
          title={t('donotaccount_Txt')}
          linkText={t('signUp_Txt')}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default LoginScreen;
