import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {IS_ANDROID, responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';

const styles = {
  imageBackground: {
    justifyContent: 'center',
  },
  languageContainer: {
    position: 'absolute',
    top: IS_ANDROID ? 20 : 20,
    right: 20,
    backgroundColor: colors.white,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 7,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1,
  },
  languageSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: wp(1),
  },
  languageText: {
    fontSize: fp(1.8),
    color: colors.black,
    fontFamily: Fonts.medium,
    marginLeft: wp(0.8),
  },
  arrowIcon: {
    width: fp(3),
    height: fp(3),
    // marginLeft: 5,
  },
  flagIcon: {
    width: fp(3.6),
    height: fp(3.6),
  },
  dropdown: {
    position: 'absolute',
    top: hp(6), // Adjust as needed
    right: wp(0), // Add some spacing from the right edge
    backgroundColor: colors.white,
    borderRadius: 5,
    // paddingVertical: 100, // Increase padding for better touchable area
    paddingHorizontal: 15,
    zIndex: 9, // Ensure it's above other elements
    elevation: 5, // For Android shadow
    // transform: [{translateY: hp(8)}], // Move the dropdown down by 40 pixels
    minWidth: wp(30), // Ensure dropdown has enough width
    // height: hp(12),
  },
  dropdownItem: {
    paddingVertical: 8,
    gap: wp(2),
    flexDirection: 'row',
  },
  dropdownText: {
    fontSize: fp(1.4),
    color: colors.grey3,
    alignSelf: 'center',
    fontFamily: Fonts.medium,
  },
  logoContainer: {
    // flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  logo: {
    // width: fp(22),
    // height: fp(22),
    // height: fp(10),
    width: fp(8),
  },
  logoText: {
    // width: fp(22),
    height: fp(22),
    // height: fp(10),
    marginLeft: wp(1.2),
  },
  welcomeContainer: {
    alignItems: 'center',
    marginTop: fp(4),
    marginBottom: fp(5),
    // flex: 0.3,
  },
  welcomeTitle: {
    fontSize: fp(3.6),
    fontFamily: Fonts.bold,
    color: colors.white,
  },
  welcomeSubtitle: {
    fontSize: fp(1.8),
    lineHeight: hp(3),
    color: '#c8c8c8',
    textAlign: 'center',
    marginVertical: 5,
    marginHorizontal: 35,
    fontFamily: Fonts.regular,
    width: wp(70),
  },
  buttonsContainer: {
    justifyContent: 'center',
    paddingHorizontal: '4%',
    paddingVertical: '6%',
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: '4%',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EBEBEB',
    padding: '3.5%',
    borderRadius: 10,
    marginHorizontal: '1%',
    marginVertical: '2%',
    justifyContent: 'space-between',
    width: '90%',
    elevation: 2,
  },
  buttonText: {
    fontSize: fp(1.8),
    color: colors.black,
    // flex: 1,
    marginLeft: 10,
    fontFamily: Fonts.semiBold,
  },
};

export default styles;
