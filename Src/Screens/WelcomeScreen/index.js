import {useEffect, useLayoutEffect, useState} from 'react';
import {
  Image,
  ImageBackground,
  Text,
  TouchableOpacity,
  View,
  TouchableWithoutFeedback,
  SafeAreaView,
  I18nManager,
  Pressable,
  BackHandler,
} from 'react-native';
import styles from './styles';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {changeLang} from '../../Components/CommonFunction';
import i18next from 'i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {SELECTED_LANG} from '../../Utils/storageKeys';
import {useDispatch, useSelector} from 'react-redux';
import {setAppLocale} from '../../Features/authSlice';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import HelperTextComponent from '../../Components/HelperTipComp';

const WelcomeScreen = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const {appLocale} = useSelector(state => state?.auth);
  const [isLoading, setIsLoading] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(
    appLocale == 'en' ? 'English' : 'Arabic',
  );
  const [openHelperText, setOpenHelperText] = useState(true);
  useEffect(() => {
    const backAction = () => {
      BackHandler.exitApp();
      // Returning true disables the back button
      return true;
    };

    // Add event listener
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    // Cleanup event listener on component unmount
    return () => backHandler.remove();
  }, []);
  useLayoutEffect(() => {
    getLang();
  }, []);

  // useEffect(() => {
  //   const clearUserTypeOnKill = async () => {
  //     await AsyncStorage.removeItem('userType');
  //   };
  //   const unsubscribe = navigation.addListener(
  //     'beforeRemove',
  //     clearUserTypeOnKill,
  //   );

  //   return unsubscribe;
  // }, [navigation]);

  async function getLang() {
    let getLang = await changeLang();

    getLang = getLang === 'en' ? 'English' : 'Arabic';
    setSelectedLanguage(getLang);
  }

  const toggleDropdown = async () => {
    setDropdownVisible(!dropdownVisible);
  };
  const dispatch = useDispatch();
  // const selectLanguage = async language => {
  //   console.log('🚀 ~ selectLanguage ~ language:', language);
  //   // Change the language in i18next
  //   await i18next.changeLanguage(language);

  //   // Update the Redux state
  //   dispatch(setAppLocale(language));

  //   // Update AsyncStorage
  //   await AsyncStorage.setItem(SELECTED_LANG, language);

  //   // Update local component state for immediate UI feedback
  //   setSelectedLanguage(language === 'en' ? 'English' : 'Arabic');
  //   setDropdownVisible(false);
  // };

  const selectLanguage = async language => {
    const languageMap = {
      en: 'English',
      ar: 'Arabic',
    };

    try {
      console.log('🚀 ~ selectLanguage ~ language:', language);

      // Show loading indicator (optional)
      setIsLoading(true);

      // Change the language in i18next
      await i18next.changeLanguage(language);

      // Update the Redux state
      dispatch(setAppLocale(language));

      // Update AsyncStorage
      await AsyncStorage.setItem(SELECTED_LANG, language);

      // Update local component state for immediate UI feedback
      setSelectedLanguage(languageMap[language] || language);
      setDropdownVisible(false);
    } catch (error) {
      console.error('Error in selectLanguage:', error);
    } finally {
      // Hide loading indicator
      setTimeout(() => {
        setIsLoading(false);
      }, 3000);
    }
  };

  const closeDropdown = () => {
    if (dropdownVisible) {
      setDropdownVisible(false);
    }
  };

  const navigateToLogin = async userType => {
    try {
      console.log('-----111------userType in welcome page::', userType);
      const userTypeValue =
        userType === 'student' ? '1' : userType === 'parent' ? '2' : '3';

      await AsyncStorage.setItem('userType', userTypeValue);
      console.log('UserType saved:', userTypeValue);

      navigation.navigate('Login', {userType: userTypeValue});
    } catch (error) {
      console.error('Error saving userType in AsyncStorage:', error);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={closeDropdown}>
      <LinearGradient colors={['#40A39B', '#002825']}>
        <SafeAreaView style={{height: '100%'}}>
          <TaleemLoader isLoading={isLoading} />
          <View style={{flex: 0.2, alignItems: 'center'}}>
            <View style={styles.languageContainer}>
              <TouchableOpacity
                onPress={toggleDropdown}
                activeOpacity={0.8}
                style={styles.languageSelector}>
                {/* <Image
                  source={
                    selectedLanguage == 'English'
                      ? icons.englishFlag
                      : icons.arabicFlag
                  }
                  resizeMode="contain"
                  style={styles.flagIcon}
                /> */}
                <Text style={styles.languageText}>
                  {t(selectedLanguage.toLowerCase())}
                </Text>
                <Image
                  source={icons.arrowDown}
                  tintColor={colors.black}
                  style={styles.arrowIcon}
                />
              </TouchableOpacity>

              {dropdownVisible && (
                <View style={styles.dropdown}>
                  <Pressable
                    onPress={() => selectLanguage('en')}
                    style={styles.dropdownItem}>
                    {/* <Image
                      source={icons.englishFlag}
                      resizeMode="contain"
                      style={styles.flagIcon}
                    /> */}
                    <Text style={styles.dropdownText}>English</Text>
                  </Pressable>
                  <Pressable
                    onPress={() => {
                      console.log('arabic selected');
                      selectLanguage('ar');
                    }}
                    style={[styles.dropdownItem]}>
                    {/* <Image
                      source={icons.arabicFlag}
                      resizeMode="contain"
                      style={styles.flagIcon}
                    /> */}
                    <Text style={styles.dropdownText}>عربي</Text>
                  </Pressable>
                </View>
              )}
            </View>
          </View>
          <View
            style={{
              flex: 0.38,
              backgroundColor: 'transparent',
              alignSelf: 'center',
              justifyContent: 'flex-end',
            }}>
            <View style={styles.logoContainer}>
              {/* <Image
                source={icons.logo.whiteLogoCap}
                style={styles.logo}
                resizeMode="contain"
              />
              <Image
                source={
                  selectedLanguage === 'English'
                    ? icons.logo.whiteLogoTextEng
                    : icons.logo.whiteLogoTextArabic
                }
                style={[
                  styles.logoText,
                  {width: selectedLanguage === 'English' ? fp(14) : fp(10)},
                ]}
                resizeMode="contain"
              /> */}
              <Image
                source={
                  selectedLanguage === 'English'
                    ? icons.logo.splashLogo
                    : icons.logo.welcomeLogoArabic
                }
                style={[
                  styles.logoText,
                  {width: selectedLanguage === 'English' ? fp(20) : fp(20)},
                ]}
                resizeMode="contain"
              />
            </View>
          </View>
          <View style={styles.welcomeContainer}>
            <Text style={styles.welcomeTitle}>{t('home_Header_Txt')}</Text>
            {/* {openHelperText && (
              <HelperTextComponent
                helperText={t('welcomeTooltip')}
                setOpen={setOpenHelperText}
                open={openHelperText}
              />
            )} */}
            <Text style={styles.welcomeSubtitle}>{t('subWelcomHeader')}</Text>
          </View>
          <View
            style={{
              flex: 0.42,
              justifyContent: 'flex-end',
              alignItems: 'center',
            }}>
            <View style={styles.buttonsContainer}>
              <SelectionButton
                onPress={() => navigateToLogin('student')}
                icon={icons.student}
                title={t('student_Txt')}
              />
              <SelectionButton
                onPress={() => navigateToLogin('parent')}
                icon={icons.parent}
                title={t('parent_Txt')}
              />
              <SelectionButton
                onPress={() => navigateToLogin('tutor')}
                icon={icons.tutor}
                title={t('tutor_Txt')}
              />
            </View>
          </View>
        </SafeAreaView>
      </LinearGradient>
      {/* </ImageBackground> */}
    </TouchableWithoutFeedback>
  );
};

const SelectionButton = ({icon, title, ...props}) => {
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <TouchableOpacity
      {...props}
      activeOpacity={0.8}
      style={[styles.button, isRTL && {flexDirection: 'row-reverse'}]}>
      <Image source={icon} />
      <Text
        style={[
          styles.buttonText,
          isRTL ? {marginLeft: 10} : {marginRight: 10},
        ]}>
        {title}
      </Text>
      <Image
        source={icons.next}
        style={{transform: isRTL ? [{rotate: '180deg'}] : [{rotate: '0deg'}]}}
      />
    </TouchableOpacity>
  );
};

export default WelcomeScreen;
