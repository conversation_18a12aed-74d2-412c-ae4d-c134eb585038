import {
  SafeAreaView,
  View,
  TextInput,
  Image,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {AppHeader} from '../../Components/Header';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import {useTranslation} from 'react-i18next';
import LinearGradient from 'react-native-linear-gradient';
import {useSubCategoryQuery} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {useDispatch, useSelector} from 'react-redux';
import {updateExpertiseIdRecreational} from '../../Redux/Slices/Student/TutorBookingSlice';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';

const ChooseCategory = ({navigation}) => {
  const disptach = useDispatch();
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [search, setSearch] = useState('');
  const {data: SubCategorData, error, isLoading} = useSubCategoryQuery();
  const [filterData, setFilterData] = useState([]);
  // const bookingFlowType = useSelector(
  //   state => state?.tutorBookingSlice?.bookingFlowType,
  // );

  useEffect(() => {
    console.log('SubCategorData API Data:', SubCategorData?.data?.rows);
    console.log('Loading Status:', isLoading);
    console.log('Error:', error);
    setFilterData(SubCategorData?.data?.rows);
  }, [SubCategorData, error, isLoading]);

  useEffect(() => {
    // Filter data when `search` changes
    if (search.trim() === '') {
      setFilterData(SubCategorData?.data?.rows); // Reset to original data if search is empty
    } else {
      const filtered = SubCategorData?.data?.rows.filter(item =>
        item.name.toLowerCase().includes(search.toLowerCase()),
      );
      setFilterData(filtered);
    }
  }, [search]);
  function onCategoryPress(expertise_id) {
    disptach(updateExpertiseIdRecreational(expertise_id));
    navigation.navigate('TutorList');
  }
  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
      <StatusContainer color={colors.white} />

      {/* Header */}
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('chooseCategory')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />

      {/* Search Bar */}
      <View style={{padding: 16}}>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            backgroundColor: colors.offWhite1,
            borderRadius: 10,
            paddingHorizontal: 12,
            height: 40,
          }}>
          <Image
            resizeMode="contain"
            style={{
              height: 20,
              width: 20,
              marginRight: isRTL ? 5 : 10,
              marginLeft: isRTL ? 10 : 5,
            }}
            source={icons.searchIcon}
          />
          <TextInput
            placeholder={t('searchPlaceholder')}
            style={{
              flex: 1,
              fontSize: 16,
              color: colors.searchGray,
              textAlign: isRTL ? 'right' : 'left',
            }}
            placeholderTextColor={colors.txtGrey1}
            value={search}
            onChangeText={txt => setSearch(txt)}
          />
        </View>
      </View>

      {/* Category Section */}
      <View style={{flex: 1, padding: 16}}>
        {isLoading ? (
          <ActivityIndicator
            size="large"
            color={colors.themeColor}
            style={{marginTop: 20}}
          />
        ) : error || !SubCategorData ? (
          <Text style={{textAlign: 'center', marginTop: 20}}>
            {t('categoryError')}
          </Text>
        ) : (
          <FlatList
            data={filterData}
            numColumns={2}
            keyExtractor={item => item.id.toString()}
            columnWrapperStyle={{
              justifyContent: 'space-between',
              flexDirection: isRTL ? 'row-reverse' : 'row',
            }}
            renderItem={({item}) => (
              <CategoryCard
                icon={
                  item.image
                    ? {uri: `${IMAGE_BASE_URL}${item.image}`}
                    : icons.defaultIcon
                }
                categoryName={item.name}
                navigation={navigation}
                expertise_id={item.id}
                onPress={() => onCategoryPress(item?.id)}
                isRTL={isRTL}
              />
            )}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const CategoryCard = ({
  categoryName,
  icon,
  navigation,
  expertise_id,
  onPress,
  isRTL,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{marginBottom: fp(2), width: '48%'}}>
      <LinearGradient
        colors={['#40A39B', '#40A39B']}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}
        style={{borderRadius: 10}}>
        <View
          style={{
            borderRadius: fp(1),
            justifyContent: 'center',
            padding: fp(2),
          }}>
          <Image
            source={icon}
            resizeMode="contain"
            tintColor={colors.white}
            style={{
              width: fp(4),
              height: hp(5),
              marginVertical: fp(0.8),
              alignSelf: isRTL ? 'flex-end' : 'flex-start',
            }}
          />

          <Text
            style={{
              fontSize: fp(1.8),
              fontFamily: Fonts.medium,
              color: colors.white,
              alignSelf: isRTL ? 'flex-end' : 'flex-start',
              fontSize: fp(2),
            }}>
            {categoryName}
          </Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default ChooseCategory;
