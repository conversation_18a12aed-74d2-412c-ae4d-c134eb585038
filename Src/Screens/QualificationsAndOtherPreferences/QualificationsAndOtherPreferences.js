import React, {useState, useEffect, useCallback} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  TextInput,
  ScrollView,
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  Image,
  Dimensions,
} from 'react-native';
import CountryPicker from 'react-native-country-picker-modal';
import colors from '../../Utils/colors';
import {StatusContainer} from '../../Components/StatusBar';
import FormHeader from '../../Components/FormHeader';
import {PrimaryButton} from '../../Components/CustomButton';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import CustomCheckbox from '../../Components/CustomCheckbox';
import {useTranslation} from 'react-i18next';
import {
  useGetQualificationsQuery,
  useGetGradesQuery,
  useGetExpertiseQuery,
  useGetTutorProfileQuery,
  useUpdateTutorProfileMutation,
} from '../../Api/ApiSlice';
import LinearGradient from 'react-native-linear-gradient';
import icons from '../../Utils/icons';
import {NavigationContainer, useFocusEffect} from '@react-navigation/native';
import {showToast} from '../../Components/ToastHelper';
import CustomDropDown from '../../Components/CustomDropDown';
import MultiSelectDropdown from '../../Components/MultiSelectComp';

const CustomPicker = ({
  label,
  options,
  selectedValue,
  onValueChange,
  borderColor = colors.txtGrey,
}) => {
  const {t} = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const windowWidth = Dimensions.get('window').width;
  return (
    <View style={styles.containerPicker}>
      <TouchableOpacity
        style={[styles.pickerInput, {borderColor}]}
        onPress={() => setModalVisible(true)}
        accessible={true}
        accessibilityLabel={label}>
        <Text style={styles.pickerText}>{selectedValue || t(label)}</Text>
        <Image source={icons.downchevron} style={styles.dropdownIcon} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}>
        <TouchableOpacity
          style={styles.modalOverlay}
          onPress={() => setModalVisible(false)}
        />
        <View style={[styles.modalContainer, {width: windowWidth * 0.85}]}>
          <FlatList
            data={options}
            keyExtractor={item => item.value}
            renderItem={({item}) => (
              <TouchableOpacity
                style={styles.modalOption}
                onPress={() => {
                  onValueChange(item.value);
                  setModalVisible(false);
                }}
                accessible={true}
                accessibilityLabel={item.label}>
                <Text style={styles.modalText}>{t(item.label)}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>
    </View>
  );
};

const QualificationsAndOtherPreferences = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const {
    data: tutorProfileData,
    error: tutorProfileDataError,
    isLoading: tutorProfileDataLoading,
    refetch: refetchTutorProfile,
  } = useGetTutorProfileQuery();

  const {data: qualificationsData, isLoading: isQualificationsLoading} =
    useGetQualificationsQuery();

  const {data: gradesData, isLoading: isGradesLoading} = useGetGradesQuery();

  const {
    data: expertiseData,
    isLoading: isExpertiseLoading,
    refetch: refetchExpertiseApi,
  } = useGetExpertiseQuery();
  useEffect(() => {
    refetchExpertiseApi();
  }, []);

  const [updateTutorProfile, {isLoading: isUpdating}] =
    useUpdateTutorProfileMutation();

  const [qualification, setQualification] = useState({});
  const [occupation, setOccupation] = useState('');
  const [nationality, setNationality] = useState('');
  const [bio, setBio] = useState('');
  const [experience, setExperience] = useState('');
  const [selectedGrades, setSelectedGrades] = useState([]);
  const [selectedExpertise, setSelectedExpertise] = useState([]);
  const [countryCode, setCountryCode] = useState('QA');
  const [pickerVisible, setPickerVisible] = useState(false);
  const [otherQualification, setOtherQualification] = useState('');
  const [otherExpertise, setOtherExpertise] = useState('');
  const handleCountrySelect = country => {
    setCountryCode(country.cca2);
    setNationality(country.name);
    setPickerVisible(false);
  };
  console.log('🚀 ~ useCallback ~ expertiseData:', expertiseData?.data?.rows);

  useFocusEffect(
    useCallback(() => {
      if (tutorProfileData && tutorProfileData.data.tlm_tutor_profile) {
        const profile = tutorProfileData.data.tlm_tutor_profile;
        console.log(JSON.stringify(profile), 'expertisee asdfasd;lkjfasdf;lkj');
        const selectedQualification = {
          id: profile.tlm_tutor_qualifications[0]?.tlm_qualification?.id,
          name: profile.tlm_tutor_qualifications[0]?.tlm_qualification?.name,
          value: profile.tlm_tutor_qualifications[0]?.tlm_qualification?.value,
        };

        setQualification(
          // profile.tlm_tutor_qualifications[0]?.tlm_qualification.name || '',
          selectedQualification,
        );
        setOtherQualification(profile?.other_qualification);
        setOccupation(profile.occupation);
        setOtherExpertise(profile?.other_expertise);
        setNationality(tutorProfileData?.data?.nationality);
        setBio(profile.bio);
        setExperience(profile.experience);
        setSelectedGrades(
          profile.tlm_tutor_grades.map(grade => grade.tlm_grade.name),
        );

        setSelectedExpertise(
          profile?.tlm_tutor_expertises
            ?.map(expertiseName => {
              const expertise = expertiseData?.data?.rows?.find(
                e => e.id === expertiseName?.tlm_expertise?.id,
              );

              if (expertise) {
                return {
                  id: expertise.id,
                  label: expertise.name,
                };
              }

              return null; // Return null if no expertise is found
            })
            .filter(Boolean), // Filter out any null values
        );
      }
    }, [tutorProfileData]),
  );
  console.log('🚀 ~ handleUpdate ~ selectedExpertise:', selectedExpertise);

  const handleUpdate = async () => {
    const payload = {
      // qualification: [
      //   {
      //     id: qualificationsData?.data?.rows.find(q => q.name === qualification)
      //       ?.id,
      //     name: qualification,
      //   },
      // ],
      qualification: [
        {
          id: qualification?.id,
          name: qualification?.name,
        },
      ],
      occupation,
      nationality,
      bio,
      experience,
      // grades: selectedGrades.map(gradeName => {
      //   const grade = gradesData?.data?.rows.find(g => g.name === gradeName);
      //   return {
      //     id: grade?.id,.....
      //     name: grade?.name,
      //   };
      // }),
      // other_grades: '',
      // expertise: selectedExpertise.map(expertiseName => {
      //   const expertise = expertiseData?.data?.rows.find(
      //     e => e.name === expertiseName.label,
      //   );
      //   return {
      //     id: expertise?.id,
      //     name: expertise?.name,
      //   };
      // }),
      // other_expertise: otherExpertise || '',
      other_qualification: otherQualification || '',
      academic_document: '',
      address_proof: '',
      id_photo: '',
      profile_completion_step: '1',
    };
    console.log(payload, 'api payload');
    try {
      const response = await updateTutorProfile(payload);
      if (response.error) {
        console.error('Error updating profile:', response.error);
        showToast('error', t('failedToUpdate'), 'bottom', isRTL);
      } else {
        showToast('success', t('profileUpdated'), 'bottom', isRTL);
        await refetchTutorProfile();
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error:', error);
      showToast('error', t('errorOccurred'), 'bottom', isRTL);
    }
  };

  if (
    tutorProfileDataLoading ||
    isQualificationsLoading ||
    isGradesLoading ||
    isExpertiseLoading
  ) {
    return (
      <SafeAreaView style={styles.container}>
        <ActivityIndicator size="large" color={colors.themeColor} />
      </SafeAreaView>
    );
  }

  if (tutorProfileDataError) {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.errorText}>{t('errorLoadingProfile')}</Text>
      </SafeAreaView>
    );
  }

  const handleRemoveExpertise = expertise => {
    setSelectedExpertise(selectedExpertise.filter(item => item !== expertise));
  };

  const qualificationOptions =
    qualificationsData?.data?.rows.map(qualification => ({
      label: qualification?.name,
      value: qualification?.value,
      id: qualification?.id,
    })) || [];

  const expertiseOptions =
    expertiseData?.data?.rows.map(expertise => ({
      label: expertise.name,
      value: expertise.name,
      id: expertise?.id,
    })) || [];
  console.log(
    '🚀 ~ QualificationsAndOtherPreferences ~ selectedExpertise:',
    selectedExpertise,
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.white} />
      <FormHeader
        title={t('editQualificationsPreferences')}
        titleStyle={{color: colors.black, fontWeight: 'bold'}}
        showBackButton={true}
        style={{backgroundColor: colors.white}}
        buttonStyle={{color: colors.black}}
      />

      <ScrollView contentContainerStyle={styles.content}>
        {/* <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('qualifications')}
        </Text> */}
        {/* <CustomPicker
          label={t('selectQualification')}
          options={
            qualificationsData?.data?.rows.map(q => ({
              label: q.name,
              value: q.name,
            })) || []
          }
          selectedValue={qualification || t('select')}
          onValueChange={selected => setQualification(selected)}
        /> */}

        <CustomDropDown
          lable={t('qualifications')}
          data={qualificationOptions?.map(({label, ...rest}) => ({
            name: label,
            ...rest,
          }))}
          isSearch={true}
          lableStyle={styles.drowpdownLable}
          backgroundColor={colors.txtGrey}
          height={40}
          onSelect={selectedValue => {
            console.log(
              '🚀 ~ QualificationsAndOtherPreferences ~ selectedValue:',
              selectedValue,
            );
            const selectedQual = qualificationsData?.data?.rows.find(
              qualification => qualification?.value === selectedValue?.value,
            );

            setQualification(selectedQual || {});
          }}
          defaultValue={qualification?.name}
          alreadySelectedItem={qualification}
        />
        {qualification.value?.toLowerCase() == 'other' && (
          <TextInput
            style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
            placeholder={t('enterQualification')}
            placeholderTextColor={colors.txtGrey1}
            value={otherQualification}
            autoFocus
            onChangeText={text => {
              const sanitizedText = text.replace(/[^A-Z a-z]/g, '');
              if (sanitizedText?.length <= 16) {
                setOtherQualification(sanitizedText);
              }
            }}
          />
        )}
        <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('occupation')}
        </Text>
        <TextInput
          style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
          placeholder={t('enterOccupation')}
          placeholderTextColor={colors.txtGrey1}
          value={occupation}
          onChangeText={text => {
            const sanitizedText = text.replace(/[^A-Za-z]/g, '');
            if (sanitizedText?.length <= 16) {
              setOccupation(sanitizedText);
            }
          }}
        />

        {/* Nationality */}
        <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('nationality')}
        </Text>
        <TouchableOpacity
          style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
          onPress={() => setPickerVisible(true)}>
          <Text
            style={{
              color: nationality ? colors.black : colors.txtGrey1,
              textAlign: isRTL ? 'right' : 'left',
            }}>
            {nationality || t('selectNationality')}
          </Text>
        </TouchableOpacity>
        <CountryPicker
          countryCode={countryCode}
          withFilter
          withFlag
          withAlphaFilter
          onSelect={handleCountrySelect}
          visible={pickerVisible}
          onClose={() => setPickerVisible(false)}
          renderFlagButton={() => null}
        />
        {/* Nationality */}

        <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('bio')}
        </Text>
        <TextInput
          style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
          placeholder={t('enterYourBio')}
          placeholderTextColor={colors.txtGrey1}
          multiline
          value={bio}
          onChangeText={text => {
            const sanitizedText = text?.replace(/[^a-zA-Z0-9\s]/g, '');
            if (sanitizedText?.length <= 500) {
              setBio(sanitizedText);
            }
          }}
          maxLength={500}
        />
        <Text
          style={{
            textAlign: isRTL ? 'left' : 'right',
            color: 'grey',
            fontSize: fp(1.2),
            marginTop: -hp(1.3),
            fontFamily: Fonts.medium,
          }}>
          {bio?.length}/500
        </Text>

        <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('totalYearsOfExperience')}
        </Text>
        <TextInput
          style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
          placeholder={t('enterExperience')}
          placeholderTextColor={colors.txtGrey1}
          keyboardType="numeric"
          value={experience.toString()}
          onChangeText={text => {
            const sanitizedText = text.replace(/[^0-9]/g, '');
            if (sanitizedText.length <= 2) {
              setExperience(sanitizedText);
            }
          }}
        />

        {/* <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('gradesTutoring')}
        </Text>
        <View style={styles.checkboxesContainer}>
          {gradesData?.data?.rows.map(grade => (
            <CustomCheckbox
              key={grade.id}
              label={grade.name}
              isSelected={selectedGrades.includes(grade.name)}
              onSelect={() => {
                if (selectedGrades.includes(grade.name)) {
                  setSelectedGrades(
                    selectedGrades.filter(g => g !== grade.name),
                  );
                } else {
                  setSelectedGrades([...selectedGrades, grade.name]);
                }
              }}
            />
          ))}
        </View> */}

        {/* <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('expertiseIn')}
        </Text>
        <CustomPicker
          label={t('selectExpertise')}
          options={
            expertiseData?.data?.rows.map(exp => ({
              label: exp.name,
              value: exp.name,
            })) || []
          }
          selectedValue=""
          onValueChange={selected => {
            if (!selectedExpertise.includes(selected)) {
              setSelectedExpertise([...selectedExpertise, selected]);
            }
          }}
        /> */}
        {/* <MultiSelectDropdown
          label={t('expertiseIn')}
          options={expertiseOptions}
          value={selectedExpertise}
          onChange={setSelectedExpertise}
        />
        {selectedExpertise.some(item => item.label === 'Others') && (
          <TextInput
            style={styles.input}
            placeholder={'Enter expertise'}
            placeholderTextColor={colors.txtGrey1}
            value={otherExpertise}
            autoFocus
            onChangeText={setOtherExpertise}
          />
        )}
        <View
          style={[
            styles.selectedExpertiseContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          {selectedExpertise.map((expertise, index) => {
            console.log(
              '🚀 ~ QualificationsAndOtherPreferences ~ expertise:',
              expertise,
            );

            // if (expertise.label === 'Others') {
            //   return (
            //     <TextInput
            //       style={styles.input}
            //       placeholder={'Enter expertise'}
            //       placeholderTextColor={colors.txtGrey1}
            //       value={otherExpertise}
            //       autoFocus
            //       onChangeText={setOtherExpertise}
            //     />
            //   ); // Don't render anything for "Other" in this section
            // }
            if (otherExpertise != '') {
            }
            return (
              <LinearGradient
                colors={['#C6FFC9', '#D4EBFF']}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 1}}
                style={{
                  borderRadius: 20,
                  paddingHorizontal: 2,
                  paddingVertical: 1,
                  marginRight: 20,
                  marginBottom: 10,
                }}
                key={index}>
                <View
                  style={[
                    styles.expertiseTag,
                    {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  ]}>
                  <Text style={styles.expertiseText}>{expertise?.label}</Text>
                  <TouchableOpacity
                    onPress={() => handleRemoveExpertise(expertise)}>
                    <Image
                      style={styles.removeImage}
                      resizeMode="center"
                      source={icons.cross}
                    />
                  </TouchableOpacity>
                </View>
              </LinearGradient>
            );
          })}
        </View> */}
      </ScrollView>

      <PrimaryButton
        style={styles.updateButton}
        title={t('update')}
        onPress={handleUpdate}
        textStyle={styles.updateText}
        loading={isUpdating}
      />
    </SafeAreaView>
  );
};

export default QualificationsAndOtherPreferences;

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 10,
    padding: 12,
    marginBottom: 16,
    color: colors.black,
    fontFamily: Fonts.regular,
  },
  checkboxesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  updateButton: {
    backgroundColor: colors.themeColor,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15,
    width: '90%',
    alignSelf: 'center',
  },
  updateText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: Fonts.regular,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginTop: 20,
  },
  /////////
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    padding: 16,
  },
  label: {
    fontSize: fp(1.6),
    color: colors.black,
    marginBottom: 8,
    fontFamily: Fonts.medium,
  },

  containerPicker: {
    marginBottom: 16,
    alignItems: 'center',
  },

  pickerInput: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    width: '100%',
    maxWidth: 400,
  },

  pickerText: {
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    flex: 1,
    fontFamily: Fonts.regular,
  },
  dropdownIcon: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  modalContainer: {
    top: '40%',
    left: '7.5%',
    backgroundColor: colors.white,
    borderRadius: 10,
    zIndex: 1,
  },
  modalOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.txtGrey2,
  },
  modalText: {
    fontSize: 16,
    color: colors.lightBlack,
    alignSelf: 'center',
    fontFamily: Fonts.regular,
  },

  input: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 10,
    padding: 12,
    marginBottom: 16,
    color: colors.black,
    fontFamily: Fonts.regular,
  },

  inputOthers: {
    height: 80,
    padding: 10,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    backgroundColor: colors.white,
    fontSize: 16,
    color: colors.black,
    marginBottom: 20,
    textAlignVertical: 'top',
  },
  drowpdownLable: {
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  checkboxesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  checkboxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    marginRight: 10,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  checkboxSelected: {
    backgroundColor: colors.themeColor,
    borderColor: colors.themeColor,
  },
  checkMark: {
    color: colors.white,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    fontFamily: Fonts.medium,
  },
  selectedExpertiseContainer: {
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  expertiseTag: {
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
    margin: 1,
  },
  expertiseText: {
    color: colors.black,
    fontSize: 14,
    fontFamily: Fonts.regular,
  },
  removeImage: {
    height: 20,
    width: 20,
  },
  continueButton: {
    backgroundColor: colors.themeColor,
    borderRadius: 8,
    alignItems: 'center',
    padding: 16,
    marginBottom: 15,
    width: '90%',
  },
  continueText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: Fonts.regular,
  },
  progressBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  progressBarFilled: {
    flex: 1,
    height: 5,
    backgroundColor: colors.themeColor,
    borderRadius: 5,
    marginRight: 3,
  },
  progressBarUnfilled: {
    flex: 1,
    height: 5,
    backgroundColor: colors.txtGrey,
    borderRadius: 5,
  },

  /////////
});
