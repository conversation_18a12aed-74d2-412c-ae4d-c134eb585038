import {
  SafeAreaView,
  Text,
  View,
  TextInput,
  ScrollView,
  Image,
} from 'react-native';
import React from 'react';
import SmallFeturesButtonNonPrassable from '../../Components/Custom_Components/SmallFeturesButtonNonPrassable';
import styles from './styles';
import {AppHeader} from '../../Components/Header';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import {useTranslation} from 'react-i18next';
import AllMembers from '../../Components/AllMembers';
import {Fonts} from '../../Utils/Fonts';

const Search = () => {
  const {t} = useTranslation();

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
      <StatusContainer color={colors.white} />

      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('search')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />

      <View style={{padding: 16}}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: colors.offWhite1,
            borderRadius: 10,
            paddingHorizontal: 12,
            height: 40,
          }}>
          <Image
            resizeMode="contain"
            style={{height: 20, width: 20, marginRight: 10, marginLeft: 5}}
            source={icons.searchIcon}
          />

          <TextInput
            placeholder={t('searchPlaceholder')}
            style={{flex: 1, fontSize: 16, color: colors.searchGray}}
            placeholderTextColor={colors.txtGrey1}
          />
        </View>
      </View>

      <ScrollView contentContainerStyle={{paddingHorizontal: 16}}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: Fonts.bold,
            color: colors.black,
            marginBottom: 12,
          }}>
          {t('resentSearch')}
        </Text>

        <View style={{flexDirection: 'row', flexWrap: 'wrap', gap: 8}}>
          <SmallFeturesButtonNonPrassable
            style={styles.recentSearchButton}
            title={t('mathsTutors')}
            textStyle={styles.recentSearchButtonText}
          />
          <SmallFeturesButtonNonPrassable
            style={styles.recentSearchButton}
            title={t('englishTutors')}
            textStyle={styles.recentSearchButtonText}
          />
          <SmallFeturesButtonNonPrassable
            style={styles.recentSearchButton}
            title={t('artTutor')}
            textStyle={styles.recentSearchButtonText}
          />
          <SmallFeturesButtonNonPrassable
            style={styles.recentSearchButton}
            title={t('artTutor')}
            textStyle={styles.recentSearchButtonText}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Search;
