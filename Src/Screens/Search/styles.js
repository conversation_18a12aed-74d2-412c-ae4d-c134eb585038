import {Dimensions, StyleSheet} from 'react-native';
import colors from '../../Utils/colors';

const deviceWidth = Dimensions.get('window').width;
const deviceHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({

  titletext: {
    fontSize: 16,
    color: colors.darkBlack,
    fontWeight: '700',
    alignSelf: 'center',
  },
  recentSearchButton: {
    backgroundColor: colors.white,
    borderColor: colors.themeColor,
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginBottom: 8,
  },
  recentSearchButtonText: {
    fontSize: 14,
    color: colors.themeColor,
  },
});

export default styles;
