import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  Image,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from 'react-native';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import styles from './styles';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {Title} from '../../Components/Title';
import {PrimaryButton} from '../../Components/CustomButton';
import {showToast} from '../../Components/ToastHelper';
import {PrimaryInput} from '../../Components/Input';
import {useGetTutorBookingDetailsQuery} from '../../Api/ApiSlice';
import {Fonts} from '../../Utils/Fonts';
import {wp} from '../../Helper/ResponsiveDimensions';

const ClassDetails = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const {title, selectedTabTitle, item} = route.params;
  const isRTL = i18n.language === 'ar';
  useEffect(() => {
    console.log('1783624879201', JSON.stringify(bookingDetailsData));
    console.log(
      'tutorBookingDetailsData',
      JSON.stringify(tutorBookingDetailsData),
    );
  }, [bookingDetailsData, tutorBookingDetailsData]);

  const [expandedItem, setExpandedItem] = useState('1');

  const [link, setLink] = useState('');
  const [meetingId, setMeetingId] = useState('');
  const [passcode, setPasscode] = useState('');

  const toggleExpand = itemId => {
    setExpandedItem(prev => (prev === itemId ? null : itemId));
  };
  const renderScheduleItem = ({item}) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.containerSchedule1}
      onPress={() => toggleExpand(item.id)}>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <Text style={{textAlign: 'center', fontSize: 15}}>{item.date}</Text>
        <Image
          source={
            expandedItem === item.id ? icons.chevronright : icons.downchevron
          }
          style={{alignSelf: 'center'}}
        />
      </View>

      {expandedItem === item.id && (
        <View>
          <Text style={styles.txtheadSchedule}>
            {t('time_label')}{' '}
            <Text style={styles.txtSchedule}>
              {item.tlm_tutor_schedule.start_time}
            </Text>
          </Text>
          <View style={{flexDirection: 'row'}}>
            <Text style={styles.txtschedulelabel}>
              {t('class_link')}
              <Text style={styles.txtschedulelink}>{''}</Text>
            </Text>
            <PrimaryButton
              style={{
                width: '23%',
                height: 35,
                position: 'absolute',
                right: 8,
              }}
              onPress={() =>
                showToast('success', t('coming_soon'), 'bottom', isRTL)
              }
              textStyle={{fontSize: 14}}
              title={t('start')}
            />
          </View>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <View style={{backgroundColor: colors.themeColor}}>
        <AppHeader
          backIcon={icons.backbtn}
          isBackBtn
          title={t('classDetails')}
          titleStyle={{fontWeight: '600'}}
        />
        <Title text={title} style={{color: colors.white, fontSize: 16}} />

        <View style={{marginTop: 8, marginBottom: 5}}>
          <View style={styles.content}>
            <Image
              source={icons.academicIcon}
              style={{height: 22, width: 22}}
              resizeMode="contain"
            />

            <Text style={styles.txtlabel}>
              {t('category_label')}
              <Text style={styles.txtvalue}>{selectedTabTitle}</Text>
            </Text>
          </View>

          <View style={styles.content}>
            <Image
              source={icons.calendarWhite}
              style={{height: 22, width: 22}}
              resizeMode="contain"
            />

            <Text style={styles.txtlabel}>
              {t('session_label')}
              <Text style={styles.txtvalue}>
                {bookingDetailsData?.data?.tlm_booking_enrollments?.length > 1
                  ? t('group')
                  : t('individual')}
              </Text>
            </Text>
          </View>
        </View>
      </View>
      <ScrollView contentContainerStyle={{paddingBottom: 20}}>
        <View style={styles.containerCard}>
          <View style={styles.txtcontent}>
            <Image
              source={icons.monthCalendar}
              style={{height: 20, width: 20}}
              resizeMode="contain"
            />
            <Text style={styles.txtheader}>
              {t('package_start')}
              <Text style={styles.txt}>
                {bookingDetailsData?.data?.package_start_date}
              </Text>
            </Text>
          </View>
          <View style={styles.txtcontent}>
            <Image
              source={icons.languageYellow}
              style={{height: 22, width: 22}}
              resizeMode="contain"
            />

            <Text style={styles.txtheader}>
              {t('language_label')}
              <Text style={styles.txt}>{t('englishL')}</Text>
            </Text>
          </View>

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View style={{flex: 1, flexDirection: 'column'}}>
              <Text style={styles.txtheader1}>{t('package')}</Text>
              <Text style={styles.txt1}>
                {bookingDetailsData?.data?.tlm_package_type?.name}
              </Text>
            </View>
            <View style={styles.partitionLine} />
            <View
              style={{flex: 1, flexDirection: 'column', marginLeft: wp(20)}}>
              <Text style={styles.txtheader1}>{t('class_type')}</Text>
              <Text style={styles.txt1}>
                {bookingDetailsData?.data?.tlm_class_type?.name}
              </Text>
            </View>
          </View>
        </View>

        <Title
          text={t('schedule')}
          style={{color: colors.black, fontFamily: Fonts.regular, fontSize: 20}}
        />

        <FlatList
          data={bookingDetailsData?.data?.tlm_booking_schedules}
          renderItem={renderScheduleItem}
          contentContainerStyle={{marginVertical: 10}}
          keyExtractor={item => item.id}
        />

        <Title
          text={t('payment_details')}
          style={{color: colors.black, fontSize: 20}}
        />

        <View style={styles.paymentCard}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              padding: 6,
            }}>
            <Text
              style={{
                color: colors.searchGray,
                fontSize: 14,
                lineHeight: 16,
                fontFamily: Fonts.regular,
              }}>
              {t('booking_id')}
            </Text>
            <Text
              style={{
                color: colors.black,
                fontSize: 14,
                lineHeight: 16,
                fontFamily: Fonts.regular,
              }}>
              {'1000546565'}
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              padding: 6,
            }}>
            <Text
              style={{
                color: colors.searchGray,
                fontSize: 14,
                lineHeight: 16,
                fontFamily: Fonts.regular,
              }}>
              {t('date')}
            </Text>
            <Text
              style={{
                color: colors.black,
                fontSize: 14,
                lineHeight: 16,
                fontFamily: Fonts.regular,
              }}>
              {t('payment_date_times')}
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              padding: 6,
            }}>
            <Text
              style={{
                color: colors.searchGray,
                fontSize: 14,
                lineHeight: 16,
                fontFamily: Fonts.regular,
              }}>
              {t('payment_mode')}
            </Text>
            <Text
              style={{
                color: colors.black,
                fontSize: 14,
                lineHeight: 16,
                fontFamily: Fonts.regular,
              }}>
              {t('debit_card')}
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              padding: 6,
            }}>
            <Text
              style={{
                color: colors.searchGray,
                fontSize: 14,
                lineHeight: 16,
                fontFamily: Fonts.regular,
              }}>
              {t('status')}
            </Text>
            <Text
              style={{
                color: colors.black,
                fontSize: 14,
                lineHeight: 16,
                fontFamily: Fonts.regular,
              }}>
              {t('success')}
            </Text>
          </View>
        </View>

        <Title
          text={t('instructions')}
          style={{color: colors.black, fontSize: 20}}
        />

        <View style={styles.instructionCard}>
          <Text
            style={{
              color: colors.searchGray,
              fontSize: 15,
              lineHeight: 16,
              fontFamily: Fonts.regular,
            }}>
            {bookingDetailsData?.data?.instructions}
          </Text>
        </View>
        <Title
          text={t('insert_link')}
          style={{color: colors.black, fontSize: 20}}
        />
        <View style={{marginTop: 10}}>
          <PrimaryInput
            keyboardType="url"
            containerStyle={{width: '93%', marginBottom: 0}}
            placeholder={t('class_link_placeholder')}
            value={link}
            onChangeText={text => setLink(text)}
            placeholderTextColor={colors.txtGrey1}
            textInputStyle={{height: 55}}
          />
        </View>
        <Title
          text={t('meeting_detail')}
          style={{color: colors.black, fontSize: 20, marginTop: 0}}
        />
        <View style={{marginTop: 10}}>
          <PrimaryInput
            keyboardType="numeric"
            containerStyle={{width: '93%', marginBottom: 0}}
            placeholder={t('meeting_id')}
            value={meetingId}
            onChangeText={text => setMeetingId(text)}
            placeholderTextColor={colors.txtGrey1}
            textInputStyle={{height: 55}}
          />

          <PrimaryInput
            keyboardType="default"
            containerStyle={{width: '93%', bottom: 0}}
            placeholder={t('passcode')}
            value={passcode}
            onChangeText={text => setPasscode(text)}
            placeholderTextColor={colors.txtGrey1}
            textInputStyle={{height: 55}}
          />
        </View>
        <Title
          text={t('student_details')}
          style={{color: colors.black, fontSize: 20, marginTop: 0}}
        />

        <View style={styles.studentCard}>
          <View style={{marginVertical: 10, flexDirection: 'row'}}>
            <Image
              source={icons.studentImg}
              style={{width: 35, height: 35, borderRadius: 10}}
              resizeMode="contain"
            />
            <View style={{marginLeft: 8}}>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: '700',
                  lineHeight: 18,
                  color: colors.black,
                }}>
                {'Naseem'}
              </Text>
              <Text
                style={{
                  fontSize: 12,
                  fontWeight: '400',
                  lineHeight: 16,
                  color: colors.searchGray,
                }}>
                {'Student Primary (6th)'}
              </Text>
            </View>

            {/* <View
              style={{
                backgroundColor: colors.themeColor,
                alignSelf: 'center',
              }}>
              <Image
                source={icons.messageIcon}
                style={{
                  width: 12,
                  height: 12,
                  borderRadius: 10,
                }}
                resizeMode="contain"
              />
            </View> */}
          </View>
          <Image source={icons.chatIcon} />
        </View>
        <PrimaryButton
          onPress={() => {
            navigation.navigate('StartClassScreen');
          }}
          title={t('start_class')}
          style={{width: '93%'}}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default ClassDetails;
