import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {fontSize: 25, color: colors.black, alignSelf: 'center'},

  content: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 12,
    marginHorizontal: 20,
  },
  txtcontent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    marginHorizontal: 10,
  },

  txtlabel: {
    marginHorizontal: 8,
    fontSize: 14,
    color: colors.white,
  },
  txtvalue: {
    marginLeft: 8,
    fontSize: 13,
    color: colors.white,
  },

  txtheader: {
    marginHorizontal: 8,
    fontSize: 14,
    color: colors.searchGray,
  },
  txt: {
    marginLeft: 8,
    fontSize: 13,
    color: colors.black,
  },

  txtheader1: {
    marginHorizontal: 8,
    marginVertical: 10,
    fontSize: 14,
    color: colors.searchGray,
  },
  txt1: {
    marginLeft: 8,
    marginVertical: 8,
    fontSize: 13,
    color: colors.black,
  },

  containerCard: {
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 15,
    marginTop: 15,
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 10,
  },

  partitionLine: {
    height: '80%',
    width: 1,
    backgroundColor: colors.txtGrey,
    marginHorizontal: 14,
  },

  containerSchedule: {
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 15,
    marginTop: 15,
    padding: 15,
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  txtheadSchedule: {
    width: '70%',
    marginVertical: 8,
    fontSize: 14,
    color: colors.searchGray,
  },
  txtSchedule: {
    marginLeft: 8,
    fontSize: 13,
    color: colors.black,
  },
  txtschedulelabel: {
    width: '58%',
    fontSize: 14,
    color: colors.searchGray,
  },
  txtschedulelink: {
    marginLeft: 8,
    fontSize: 13,
    color: colors.black,
  },
  containerSchedule1: {
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 15,
    marginTop: 15,
    padding: 15,
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  paymentCard: {
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 15,
    marginTop: 15,
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  instructionCard: {
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 15,
    marginTop: 15,
    padding: 10,
    paddingVertical: 18,
    backgroundColor: colors.white,
    borderRadius: 10,
  },

  studentCard: {
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 15,
    marginTop: 15,
    marginBottom: 20,
    padding: 10,
    paddingVertical: 18,
    backgroundColor: colors.white,
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

export default styles;
