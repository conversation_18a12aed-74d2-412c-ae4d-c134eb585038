import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    backgroundColor: colors.white,
    height: hp(7),
    paddingHorizontal: 16,

    alignItems: 'center',
  },
  name: {
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(16),
    color: colors.darkBlack,
  },
  status: {
    fontFamily: Fonts.medium,
    fontSize: responsiveFontSize(12),
    left: 8,
  },
  statusView: {
    alignItems: 'center',
    borderRadius: 15,
    paddingHorizontal: 8,
    paddingVertical: 5,
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: wp(18),
  },

  messageList: {
    flex: 1,
    paddingHorizontal: 10,
  },
  messageContainer: {
    maxWidth: '85%',
    marginVertical: 5,
    padding: 10,
  },
  selfMessage: {
    backgroundColor: colors.themeColor,
    marginRight: 10,
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  otherMessage: {
    backgroundColor: colors.white,
    marginLeft: 10,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    borderTopRightRadius: 10,
  },
  messageText: {
    fontSize: responsiveFontSize(14),
    fontFamily: Fonts.medium,
  },
  timeText: {
    fontSize: responsiveFontSize(12),
    color: '#888',
    marginTop: 2,
  },
  inputContainer: {
    alignItems: 'center',
    padding: 10,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  textInput: {
    flex: 1,
    height: 40,
    borderColor: '#E5E5E5',
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 15,
    backgroundColor: '#F8F8F8',
    fontFamily: Fonts.medium,
  },
  sendButton: {
    backgroundColor: colors.themeColor,
    height: 50,
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 30,
  },

  messageWrapper: {
    marginVertical: 5,
  },
  selfWrapper: {
    alignItems: 'flex-end',
  },
  otherWrapper: {
    alignItems: 'flex-start',
  },
});

export default styles;
