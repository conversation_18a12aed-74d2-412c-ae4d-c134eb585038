import {
  View,
  Text,
  SafeAreaView,
  Image,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Modal,
  StyleSheet,
} from 'react-native';
import React, {useRef, useState} from 'react';
import styles from './styles';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import {useSelector} from 'react-redux';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {fp, wp, hp} from '../../Helper/ResponsiveDimensions';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import {showToast} from '../../Components/ToastHelper';
import moment from 'moment';
import {useTranslation} from 'react-i18next';
import {useSocket} from '../../Helper/SocketHelper/SocketProvider';
import useGlobalUnreadCount from '../../Helper/CustomHooks/UseGloabalUnreadNotiCount';
import {useFocusEffect} from '@react-navigation/native';
import {Fonts} from '../../Utils/Fonts';

const ChatScreen = ({navigation, route}) => {
  const {socket, emit, on, off} = useSocket();
  const {item} = route?.params;
  const chatData = item;
  const userData = useSelector(state => state?.auth);
  const flatListRef = useRef(null);
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const initChat = () => {
    let r_id = chatData?.chatPerson?.id || chatData?.id;
    let s_id = userData.userId;
    emit('initChat', {r_id, s_id});
    getChatList(r_id, s_id);
  };

  const getChatList = (r_id, s_id) => {
    emit('getChatList', {r_id, s_id});
  };

  const {refetchUnreadCount} = useGlobalUnreadCount();

  useFocusEffect(
    React.useCallback(() => {
      // Refetch unread count
      refetchUnreadCount();

      // Initialize chat and set up listeners
      initChat();

      on('receiveMessage', newMessage => {
        console.log('Received message:', newMessage);
        if (newMessage?.result?.msg) {
          // Updated to check newMessage.result.msg
          setMessages(prevMessages => [...prevMessages, newMessage.result]); // Append newMessage.result
          setTimeout(
            () => flatListRef.current?.scrollToEnd({animated: true}),
            100,
          );
        }
      });

      on('getChatList', chat => {
        console.log('Chat list received:', chat);
        setMessages(chat?.result?.rows || []);
        setStatus(chat?.result?.chatPerson?.isOnline);
        setTimeout(
          () => flatListRef.current?.scrollToEnd({animated: true}),
          100,
        );
      });

      on('userStatus', status => {
        if (
          status?.user_id === chatData?.chatPerson?.id ||
          status?.user_id === chatData?.id
        ) {
          setStatus(status?.isOnline);
        }
      });

      on('error_callback', data => {
        console.log('Error callback:', data);
        if (
          data?.blocked_by != 'admin' && // Updated to check for admin
          data?.blocked_by_id == userData?.userId
        ) {
          Alert.alert('Alert', t('you_have_blocked_this_chat'), [
            {text: 'Unblock', onPress: () => blockUserSocketEvent('0')},
            {text: 'OK', onPress: () => navigation.goBack()},
          ]);
        } else {
          Alert.alert('Alert', t('your_chat_is_blocked'), [
            {text: 'OK', onPress: () => navigation.goBack()},
          ]);
        }
      });

      // Cleanup on unfocus
      return () => {
        off('receiveMessage');
        off('getChatList');
        off('userStatus');
        off('error_callback');
      };
    }, [chatData, userData.userId, navigation, refetchUnreadCount]),
  );

  const sendMessage = () => {
    if (message.trim() === '') {
      showToast('error', t('emptyMessageError'), 'bottom', isRTL);
      console.log('🚀 ~ ChatScreen ~ blockStatus:', blockStatus);
      console.log('🚀 ~ ChatScreen ~ blockStatus:', blockStatus);
      return;
    }

    let r_id = chatData?.chatPerson?.id || chatData?.id;
    let s_id = userData?.userId;
    let msg = message;
    let msg_type = 'text';

    const tempId = Date.now().toString();
    const newMessage = {
      id: tempId,
      s_id,
      r_id,
      msg,
      msg_type,
      added_on: new Date().toISOString(),
    };

    // Optimistic update
    // setMessages(prevMessages => [...prevMessages, newMessage]);
    setMessage('');
    setTimeout(() => flatListRef.current?.scrollToEnd({animated: true}), 100);

    if (r_id && s_id) {
      emit('sendMessage', {r_id, s_id, msg, msg_type}, response => {
        console.log('Send message response:', response);
        if (response?.success && response?.messageId) {
          // Update message ID from server if provided
          setMessages(prevMessages =>
            prevMessages.map(m =>
              m.id === tempId ? {...m, id: response.messageId} : m,
            ),
          );
        } else if (response?.error) {
          showToast('error', 'Failed to send message', 'bottom', isRTL);
          setMessages(prevMessages =>
            prevMessages.filter(m => m.id !== tempId),
          );
        }
      });
    }
  };
  const blockUserSocketEvent = blockStatus => {
    console.log('🚀 ~ ChatScreen ~ blockStatus:', blockStatus);

    let r_id = chatData?.chatPerson?.id || chatData?.id;
    let s_id = userData?.userId;
    emit(
      'blockUnblockUserChat',
      {
        r_id,
        s_id,
        status: blockStatus,
        user_type:
          userData?.userType == 1
            ? 'student'
            : userData?.userType == 2
            ? 'tutor'
            : 'parent',
      },
      response => {
        console.log('🚀 ~ ChatScreen ~ response:', response);
      },
    );
    showToast(
      'success',
      blockStatus == '1'
        ? t('user_blocked_success')
        : t('user_unblocked_success'),
      'bottom',
      isRTL,
    );
    navigation.goBack();
  };

  const renderMessage = ({item}) => {
    const isSelf = item?.s_id === userData?.userId;
    return (
      <View
        style={[
          styles.messageWrapper,
          isSelf ? styles.selfWrapper : styles.otherWrapper,
        ]}>
        <View style={{maxWidth: '75%'}}>
          <View
            style={{
              flexDirection: isSelf ? 'row-reverse' : 'row',
            }}>
            <Image
              source={
                isSelf
                  ? {
                      uri: userData?.profile_image
                        ? IMAGE_BASE_URL + userData?.profile_image
                        : DUMMY_USER_IMG,
                    }
                  : {
                      uri:
                        chatData?.chatPerson?.image || chatData?.image
                          ? IMAGE_BASE_URL +
                            (chatData?.chatPerson?.image || chatData?.image)
                          : DUMMY_USER_IMG,
                    }
              }
              style={{borderRadius: fp(4), height: fp(4), width: fp(4)}}
            />
            <View
              style={[
                styles.messageContainer,
                isSelf ? styles.selfMessage : styles.otherMessage,
              ]}>
              <Text
                style={[
                  styles.messageText,
                  {color: isSelf ? colors.white : colors.black},
                ]}>
                {item?.msg || '[Empty Message]'}
              </Text>
            </View>
          </View>
          <Text
            style={[styles.timeText, {textAlign: isSelf ? 'left' : 'right'}]}>
            {moment(item?.added_on || item?.createdAt).format('hh:mm A')}{' '}
            {/* Updated to handle createdAt */}
          </Text>
        </View>
      </View>
    );
  };

  const validateNumbers = text => {
    return text.replace(/\d{5,}/g, 'xxxxx');
  };

  const handleBlockUser = () => {
    // Close the modal first
    setModalVisible(false);

    // Show confirmation alert
    Alert.alert(
      t('block_user'),
      t('block_user_confirmation', {
        name: chatData?.chatPerson?.name || chatData?.name,
      }),
      [
        {
          text: t('cancel'),
          style: 'cancel',
        },
        {
          text: t('block'),
          style: 'destructive',
          onPress: () => {
            // Call the socket event to block the user
            blockUserSocketEvent('1'); // 1 for block
          },
        },
      ],
      {cancelable: true},
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View
        style={[styles.header, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            resizeMode="contain"
            style={[
              {
                height: fp(3),
                width: fp(3),
                tintColor: colors.black,
              },
            ]}
            source={isRTL ? icons.rightArrowLarge : icons.backbtn}
          />
        </TouchableOpacity>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            flex: 1,
          }}>
          <Image
            source={{
              uri:
                chatData?.chatPerson?.image || chatData?.image
                  ? IMAGE_BASE_URL +
                    (chatData?.chatPerson?.image || chatData?.image)
                  : DUMMY_USER_IMG,
            }}
            style={{
              left: isRTL ? 0 : 8,
              marginRight: isRTL ? 10 : 0,
              height: fp(4),
              width: fp(4),
              borderRadius: 30,
            }}
          />
          <Text
            style={[
              styles.name,
              {left: isRTL ? 0 : 15, marginRight: isRTL ? 15 : 0},
            ]}>
            {chatData?.chatPerson?.name || chatData?.name}
          </Text>
          <View
            style={[
              styles.statusView,
              {
                backgroundColor: status ? '#E8F9F2' : colors.lightGrey,
                marginRight: isRTL ? wp(5) : 0,
                marginLeft: isRTL ? 0 : wp(5),
              },
            ]}>
            <View
              style={{
                backgroundColor: status ? '#17C470' : colors.grey,
                height: 10,
                width: 10,
                borderRadius: 20,
              }}
            />
            <Text
              style={[
                styles.status,
                {color: status ? '#17C470' : colors.grey},
              ]}>
              {status ? t('online') : t('offline')}
            </Text>
          </View>
        </View>
        <TouchableOpacity onPress={() => setModalVisible(true)}>
          <Image
            source={icons.threeDots}
            style={{
              height: fp(4),
              width: fp(4),
              marginLeft: isRTL ? 0 : wp(2),
              marginRight: isRTL ? wp(2) : 0,
            }}
          />
        </TouchableOpacity>
      </View>
      <View style={{flex: 1, backgroundColor: '#E5E5E5'}}>
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={item => item.id.toString()}
          style={styles.messageList}
          onContentSizeChange={() =>
            setTimeout(
              () => flatListRef.current?.scrollToEnd({animated: true}),
              100,
            )
          }
        />
      </View>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          alignItems: 'center',
        }}>
        <View
          style={[
            styles.inputContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <TextInput
            style={[styles.textInput, {textAlign: isRTL ? 'right' : 'left'}]}
            placeholder={t('messagePlaceHolder')}
            value={message}
            onChangeText={text => setMessage(validateNumbers(text))}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              {marginRight: isRTL ? 10 : 0, marginLeft: isRTL ? 0 : 10},
            ]}
            onPress={sendMessage}>
            <Image
              source={icons.send}
              style={{transform: [{rotate: isRTL ? '-180deg' : '0deg'}]}}
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>

      {/* Modal for block user option */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <TouchableOpacity
          style={modalStyles.centeredView}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}>
          <View
            style={[
              modalStyles.modalView,
              {alignItems: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            <TouchableOpacity
              style={[
                modalStyles.option,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}
              onPress={handleBlockUser}>
              <Image
                source={icons.BlockUser}
                style={[
                  modalStyles.optionIcon,
                  {marginRight: isRTL ? 0 : 10, marginLeft: isRTL ? 10 : 0},
                ]}
              />
              <Text style={modalStyles.optionText}>{t('block_user')}</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
};

const modalStyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
  },
  modalView: {
    margin: 20,
    marginTop: hp(6), // Position below header
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    // width: wp(30),
  },
  option: {
    paddingVertical: 6,
    alignItems: 'center',
  },
  optionIcon: {
    width: 20,
    height: 20,
    tintColor: colors.black,
  },
  optionText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.black,
  },
});

export default ChatScreen;
