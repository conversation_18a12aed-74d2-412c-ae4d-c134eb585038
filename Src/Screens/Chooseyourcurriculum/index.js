import React, {useState, useCallback, useEffect} from 'react';
import {
  FlatList,
  Image,
  Pressable,
  SafeAreaView,
  Text,
  View,
  ActivityIndicator,
  I18nManager,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {PrimaryButton} from '../../Components/CustomButton';
import {AppHeader} from '../../Components/Header';
import {SubTitle, Title} from '../../Components/Title';
import {showToast} from '../../Components/ToastHelper';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import styles from './styles';
import {StatusContainer} from '../../Components/StatusBar';
import CurriculumCard from '../../Components/curriculamCard';
import {
  useChooseYourCurriculumsQuery,
  useLazyChooseYourCurriculumsQuery,
  useStudentAcademicMutation,
} from '../../Api/ApiSlice';
import {Fonts} from '../../Utils/Fonts';
import {useDispatch, useSelector} from 'react-redux';
import {updateBookingFlowType} from '../../Redux/Slices/Student/TutorBookingSlice';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {responsiveFontSize} from '../../Utils/constant';
import SearchBar from '../../Components/SearchBar';
import AppLoader from '../../Helper/AppLoader/AppLoader';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';

const RadioCard = React.memo(({item, onPress, selected, isRTL}) => {
  return (
    <Pressable
      onPress={() => onPress(item.id)}
      style={[
        styles.cardContainer,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
      ]}>
      <View style={styles.radioContainer}>
        <Image
          source={selected === item.id ? icons.checked : icons.unchecked}
          style={styles.radioIcon}
        />
      </View>
      <Text style={styles.text}>{item.name}</Text>
      <CurriculumCard flag={item.curriculum_image || icons.defaultFlag} />
    </Pressable>
  );
});

const ChooseyourCurriculum = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [selectedId, setSelectedId] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [isViewAll, setIsViewAll] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [
    chooseYourCurriculums,
    {data: curriculumData, error, isLoading: curriculamLoading},
  ] = useLazyChooseYourCurriculumsQuery();
  const [studentAcademic, {isLoading: isPosting}] =
    useStudentAcademicMutation();
  const dispatch = useDispatch();
  const {classId, gradeId} = route.params || {};

  const handleBtn = useCallback(id => {
    setSelectedId(id);
  }, []);
  const {addCurriculumFlowType} = useSelector(
    state => state.generalStudentSlice,
  );

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 500); // Delay of 500ms

    return () => clearTimeout(handler); // Cleanup timeout
  }, [searchQuery]);

  useEffect(() => {
    const params = {};
    setIsLoading(true);
    if (isViewAll) {
      params.viewAll = true;
    }
    if (debouncedQuery) {
      params.searchkey = debouncedQuery;
      // params.viewAll = isViewAll;
      chooseYourCurriculums(params);
      //api calling
      setIsLoading(false);
    } else {
      chooseYourCurriculums(params);
      setIsLoading(false);
      //api calling
    }
  }, [debouncedQuery]);
  function handleViewAll() {
    setIsLoading(true);
    const newViewAllState = !isViewAll;
    setIsViewAll(newViewAllState);
    const params = {};
    if (newViewAllState) {
      params.viewAll = true;
    }

    console.log('API Params:', params);
    chooseYourCurriculums(params);
    setIsLoading(false);
  }

  const handleContinue = useCallback(async () => {
    if (!selectedId) {
      showToast('error', t('select_curriculum'), 'bottom', isRTL);
      return;
    }

    try {
      const result = await studentAcademic({
        grades_id: gradeId,
        curriculum_id: selectedId,
        class_id: classId,
      }).unwrap();

      console.log('POST result:', result);

      if (addCurriculumFlowType == 'academic') {
        dispatch(updateBookingFlowType('academic'));

        navigation.navigate('TutorList');
      } else {
        navigation.navigate('Home');
      }
    } catch (error) {
      showToast('error', t('submitAcademicFailed'), 'bottom', isRTL);
      console.error('POST error:', error);
    }
  }, [selectedId, t, classId, studentAcademic, navigation]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <View style={{backgroundColor: colors.themeColor}}>
        <AppHeader backIcon={icons.backbtn} style={{}} isBackBtn title="" />
        <Title
          text={t('choose_curriculum')}
          style={{
            color: colors.white,
            marginTop: 16,
            textAlign: isRTL ? 'right' : 'left',
          }}
        />
        <SubTitle
          text={t('curriculum_subtitle')}
          style={{
            color: colors.txtGrey,
            marginTop: 5,
            textAlign: isRTL ? 'right' : 'left',
            width: wp(90),
          }}
        />
      </View>
      <SearchBar
        placeholder={t('searchCurriculum')}
        onChangeText={setSearchQuery}
        value={searchQuery}
        isRTL={isRTL}
      />
      <Text
        onPress={handleViewAll}
        style={{
          color: colors.black,
          marginTop: hp(1),
          textAlign: isRTL ? 'left' : 'right',
          width: wp(90),
          fontFamily: Fonts.semiBold,
          fontSize: fp(1.8),
          marginLeft: isRTL ? wp(2.6) : 0,
          marginRight: isRTL ? 0 : wp(2.6),
          textDecorationLine: 'underline',
        }}>
        {t('view')} {!isViewAll ? t('all') : t('less')}
      </Text>
      <View style={{flex: 1, backgroundColor: colors.white}}>
        {isLoading || curriculamLoading ? (
          <ActivityIndicator
            size="large"
            color={colors.themeColor}
            style={{marginTop: 20}}
          />
        ) : (
          <FlatList
            data={curriculumData?.data?.rows || []}
            keyExtractor={item => item.id.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.flatlistContainer}
            renderItem={({item}) => (
              <RadioCard
                item={item}
                onPress={handleBtn}
                selected={selectedId}
                isRTL={isRTL}
              />
            )}
            ListEmptyComponent={
              <Text
                style={{
                  alignSelf: 'center',
                  fontFamily: Fonts.semiBold,
                  fontSize: fp(1.8),
                }}>
                {t('noData')}
              </Text>
            }
          />
        )}
      </View>
      <PrimaryButton
        onPress={handleContinue}
        title={isPosting ? t('loading') : t('done')}
        style={{backgroundColor: colors.themeColor, marginBottom: 15}}
      />
    </SafeAreaView>
  );
};

export default ChooseyourCurriculum;
