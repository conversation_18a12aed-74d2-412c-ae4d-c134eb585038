import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {responsiveFontSize} from '../../Utils/constant';

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.black,
    marginTop: 20,
    marginLeft: 20,
  },

  containerBtn: {
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  row: {
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  button: {
    borderRadius: 20,
    paddingVertical: 7,
    paddingHorizontal: 15,
    marginLeft: 5,
    marginHorizontal: 8,
    borderWidth: 1,
    marginTop: 20,
  },
  buttonText: {
    fontSize: 16,
  },
  flatListContent: {},

  cardContainer: {
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 10,
    width: '90%',
    alignSelf: 'center',
    backgroundColor: colors.white,
    height: 70,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    marginVertical: 10,
    elevation: 1,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    // flex: 1,
  },
  radioContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioCircle: {
    height: 20,
    width: 20,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: colors.txtGrey1,
    backgroundColor: colors.white,
  },
  radioSelected: {
    backgroundColor: colors.themeColor,
  },
  text: {
    flex: 1,
    textAlign: 'left',
    marginLeft: fp(3),
    color: colors.darkBlack,
    fontSize: responsiveFontSize(13),
    fontFamily: Fonts.medium,
  },
  iconContainer: {
    backgroundColor: 'red',
    justifyContent: 'center',
    alignItems: 'center',
  },
  flatlistContainer: {marginTop: 15, paddingBottom: hp(2)},

  radioIcon: {
    width: 18,
    height: 18,
  },
});

export default styles;
