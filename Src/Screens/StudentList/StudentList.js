import {
  FlatList,
  Image,
  PermissionsAndroid,
  Platform,
  Pressable,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TextInput,
  RefreshControl,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../../Utils/Fonts';
import {
  usePostBookingInviteMutation,
  usePostContactsMutation,
  useSendSmsInviteMutation,
} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {PrimaryButton} from '../../Components/CustomButton';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {useDispatch, useSelector} from 'react-redux';
import {updateGroupStudents} from '../../Redux/Slices/Student/TutorBookingSlice';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import Contacts from 'react-native-contacts';
import {tutorBookingJson} from '../../Api/Model/TutorBookingModel';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';

const StudentList = ({navigation, route}) => {
  const {maxStudent, minStudent} = route?.params;
  console.log('🚀 ~ StudentList ~ maxStudent:', maxStudent);
  console.log('🚀 ~ StudentList ~ minStudent:', minStudent);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [postContacts, {isLoading: isStudentsLoading, data: studentList}] =
    usePostContactsMutation();
  const [postBookingInvite, {isLoading: isInvitationLoading}] =
    usePostBookingInviteMutation();
  const [sendSmsApi, {isLoading: sendSmsLoading}] = useSendSmsInviteMutation();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [isContactsLoading, setIsContactsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('members');
  const [searchQuery, setSearchQuery] = useState('');
  const dispatch = useDispatch();
  const [invitedUsers, setInvitedUsers] = useState(new Set());
  const [refreshing, setRefreshing] = useState(false);
  const {tutorData} = useSelector(state => state?.tutorBookingSlice);
  console.log('🚀 ~ StudentList ~ tutorData:', tutorData);

  // Get groupStudents from Redux store
  const groupStudents = useSelector(
    state => state.tutorBookingSlice.groupStudents,
  ); // Corrected path
  console.log('🚀 ~ StudentList ~ groupStudents:', groupStudents);

  // Initialize selectedStudents with groupStudents when the component mounts
  useEffect(() => {
    if (
      groupStudents &&
      Array.isArray(groupStudents) &&
      groupStudents.length > 0
    ) {
      setSelectedStudents([...groupStudents]); // Ensure a new array is created
    } else {
      setSelectedStudents([]); // Reset to empty if no groupStudents
    }
  }, [groupStudents]);

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      const formattedContacts = await getContacts();
      if (formattedContacts) {
        await postContacts({
          contacts: formattedContacts,
          tutor_id: tutorData?.profileData?.id,
        }).unwrap();
      }
      setRefreshing(false);
    } catch (err) {
      setRefreshing(false);
      showToast('error', t('failedToRefresh'), 'bottom', isRTL);
    }
  };

  const handleInvitePress = mobileNo => {
    console.log('🚀 ~ StudentList ~ mobileNo:', mobileNo);
    if (invitedUsers.has(mobileNo)) {
      showToast('info', t('invitationSentAlready'), 'bottom', isRTL);
    } else {
      handleSendSmsApi(mobileNo);
      setInvitedUsers(prev => new Set(prev).add(mobileNo));
    }
  };

  const handleSendSmsApi = mobileNo => {
    setIsLoading(true);
    const body = {mobile_no: mobileNo};
    sendSmsApi(body)
      .unwrap()
      .then(item => {
        setIsLoading(false);
        showToast('success', item?.message, 'bottom', isRTL);
        console.log(item, 'SMS API Response');
      })
      .catch(err => {
        setIsLoading(false);
        showToast('error', t('smsSentFailed'), 'bottom', isRTL);
        console.error('SMS API Error:', err);
      });
  };

  const handleCardPress = student => {
    const isAlreadySelected = selectedStudents.some(
      selected => selected?.id === student?.id,
    );
    const updatedSelection = isAlreadySelected
      ? selectedStudents.filter(selected => selected.id !== student.id)
      : [...selectedStudents, student];
    setSelectedStudents(updatedSelection);
    console.log('🚀 ~ handleCardPress ~ updatedSelection:', updatedSelection);
    dispatch(updateGroupStudents(updatedSelection));
  };

  const validateSelection = () => {
    if (selectedStudents.length < minStudent) {
      showToast(
        'error',
        t('youMustSelect', {count: minStudent}),
        'bottom',
        isRTL,
      );
      return false;
    } else if (selectedStudents.length > maxStudent) {
      showToast(
        'error',
        t('selectStudentError', {count: maxStudent}),
        'bottom',
        isRTL,
      );
      return false;
    } else {
      const ids = selectedStudents.map(item => item.id);
      tutorBookingJson.participants = ids;
      navigation.goBack();
    }
  };

  async function getContacts() {
    try {
      let permissionGranted = false;

      if (Platform.OS === 'android') {
        const permission = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_CONTACTS,
          {
            title: 'Contacts',
            message: t('contactPermissionMsg'),
            buttonPositive: t('acceptBareMortal'),
          },
        );

        if (permission === PermissionsAndroid.RESULTS.GRANTED) {
          permissionGranted = true;
        } else {
          showToast('error', t('allowContactPermission'), 'bottom', isRTL);
          return;
        }
      } else if (Platform.OS === 'ios') {
        const permissioniOS = await Contacts.requestPermission();
        if (permissioniOS === 'authorized') {
          permissionGranted = true;
        } else {
          showToast('error', t('allowContactPermission'), 'bottom', isRTL);
          return;
        }
      }

      if (permissionGranted) {
        setIsContactsLoading(true);
        const contacts = await Contacts.getAll();
        const formattedContacts = contacts
          .map(contact => {
            console.log('🚀 ~ getContacts ~ contact:', contact);
            if (contact.phoneNumbers && contact.phoneNumbers.length > 0) {
              return {
                mobile_no: contact.phoneNumbers[0].number,
                name: `${contact?.givenName} ${contact?.familyName}`,
              };
            }
            return null;
          })

          .filter(contact => contact !== null);
        setIsContactsLoading(false);
        return formattedContacts;
      }
    } catch (error) {
      setIsContactsLoading(false);
      console.error('Error fetching contacts:', error);
    }
  }

  useEffect(() => {
    const fetchAndPostContacts = async () => {
      try {
        const formattedContacts = await getContacts();
        if (formattedContacts) {
          await postContacts({
            contacts: formattedContacts,
            tutor_id: tutorData?.profileData?.id,
          }).unwrap();
        }
      } catch (err) {
        showToast('error', t('failedToPostContact'), 'bottom', isRTL);
      }
    };
    fetchAndPostContacts();
  }, [postContacts]);

  const filterRegisteredUsers = users => {
    if (!searchQuery) return users;
    return users.filter(
      user =>
        user?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user?.tlm_student_academic_details?.[0]?.tlm_grade?.name
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        user?.mobile_no?.toString().includes(searchQuery),
    );
  };
  const filterUnregisteredUsers = users => {
    if (!searchQuery) return users;
    return users.filter(
      user =>
        user?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user?.tlm_student_academic_details?.[0]?.tlm_grade?.name
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        user?.mobile_no?.toString().includes(searchQuery),
    );
  };

  const renderItem = (item, index) => {
    console.log('🚀 ~ renderItem ~ item:', item);
    const isSelected = selectedStudents.some(
      selected => selected.id === item.id,
    );
    return (
      <View
        key={index}
        style={[styles.card, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <View
          style={[
            styles.innerRow,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Image
            source={
              item?.image
                ? {uri: IMAGE_BASE_URL + item.image}
                : {uri: DUMMY_USER_IMG}
            }
            style={styles.img}
          />
          <View
            style={[
              styles.nameContainer,
              {
                alignItems: isRTL ? 'flex-end' : 'flex-start',
                marginLeft: isRTL ? 0 : wp(3),
                marginRight: isRTL ? wp(3) : 0,
              },
            ]}>
            <Text style={styles.name}>{item?.name}</Text>
            {item?.mobile_no && (
              <View
                style={{
                  backgroundColor: 'rgba(163, 153, 153, 0.2)',
                  borderRadius: fp(1),
                  padding: 4,
                  textAlign: 'center',
                  marginTop: hp(0.8),
                }}>
                <Text
                  style={[
                    // styles.grade,

                    {
                      fontSize: fp(1.4),
                      fontFamily: Fonts.medium,
                    },
                  ]}>{`${item?.country_code}${item?.mobile_no}`}</Text>
              </View>
            )}
            {item?.tlm_student_academic_details?.[0]?.tlm_grade?.name && (
              <Text
                style={[styles.grade, {textAlign: isRTL ? 'right' : 'left'}]}>
                {`${t('grade')} : ${
                  item.tlm_student_academic_details[0].tlm_grade.name
                }`}
              </Text>
            )}
          </View>
        </View>
        <Pressable
          onPress={() => handleCardPress(item)}
          style={[
            styles.actionButton,
            {
              backgroundColor: isSelected ? 'rgba(255,0,0,0.4)' : 'transparent',
              borderColor: isSelected ? colors.white : colors.themeColor,
            },
          ]}>
          <Text
            style={[
              styles.actionText,
              {
                color: isSelected ? colors.white : colors.themeBackground,
              },
            ]}>
            {isSelected ? t('remove') : t('Add')}
          </Text>
        </Pressable>
      </View>
    );
  };

  const renderUnregisteredItem = (item, index) => {
    console.log('🚀 ~ renderUnregisteredItem ~ item:', item);
    const isSelected = selectedStudents.some(
      selected => selected.mobile_no === item.mobile_no,
    );
    const isInvited = invitedUsers.has(item.mobile_no);

    return (
      <View
        key={index}
        style={[styles.card, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <View
          style={[
            styles.innerRow,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Image
            source={
              item?.image
                ? {uri: IMAGE_BASE_URL + item.image}
                : {uri: DUMMY_USER_IMG}
            }
            style={styles.img}
          />
          <View
            style={[
              styles.nameContainer,
              {
                alignItems: isRTL ? 'flex-end' : 'flex-start',
                marginLeft: isRTL ? 0 : wp(3),
                marginRight: isRTL ? wp(3) : 0,
              },
            ]}>
            <Text style={styles.name}>{item?.name}</Text>
            {item?.mobile_no && (
              <View
                style={{
                  backgroundColor: 'rgba(163, 153, 153, 0.2)',
                  borderRadius: fp(1),
                  padding: 4,
                  textAlign: 'center',
                  marginTop: hp(0.8),
                }}>
                <Text
                  style={[
                    // styles.grade,

                    {
                      fontSize: fp(1.4),
                      fontFamily: Fonts.medium,
                    },
                  ]}>{`${item?.mobile_no}`}</Text>
              </View>
            )}
          </View>
        </View>
        <View style={styles.actionContainer}>
          <Pressable
            onPress={() => handleInvitePress(item.mobile_no)}
            style={[
              styles.actionButton,
              styles.smsButton,
              isInvited && {backgroundColor: colors.themeBackground},
            ]}
            disabled={sendSmsLoading}>
            <Text
              style={[
                styles.actionText,
                {color: isInvited ? colors.white : colors.themeBackground},
              ]}>
              {isInvited ? t('invited') : t('invite')}
            </Text>
          </Pressable>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={{flex: 1}}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('select_students')}
        style={{zIndex: 20}}
        isHome={true}
      />
      <View style={{marginTop: hp(2), flex: 1}}>
        <TaleemLoader isLoading={isLoading} />
        <View
          style={[
            styles.chipContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Pressable
            style={[
              applyShadowStyleIos(styles.chip),
              activeTab === 'members' && styles.activeChip,
            ]}
            onPress={() => setActiveTab('members')}>
            <Text
              style={[
                styles.chipText,
                activeTab === 'members' && styles.activeChipText,
              ]}>
              {t('members')}
            </Text>
          </Pressable>
          <Pressable
            style={[
              applyShadowStyleIos(styles.chip),
              activeTab === 'contacts' && styles.activeChip,
            ]}
            onPress={() => setActiveTab('contacts')}>
            <Text
              style={[
                styles.chipText,
                activeTab === 'contacts' && styles.activeChipText,
              ]}>
              {t('contacts')}
            </Text>
          </Pressable>
        </View>

        <View
          style={applyShadowStyleIos({
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            backgroundColor: colors.white,
            borderRadius: 10,
            height: hp(4),
            marginRight: isRTL ? 10 : 0,
            paddingHorizontal: 10,
            elevation: 2,
            width: wp(95),
            alignSelf: 'center',
          })}>
          <Image
            style={{
              width: 18,
              height: 18,
              marginRight: isRTL ? 0 : 8,
              marginLeft: isRTL ? 8 : 0,
            }}
            tintColor={colors.darkGrey}
            source={icons.searchIcon}
            resizeMode="contain"
          />
          <TextInput
            style={{
              fontSize: 14,
              color: colors.searchGray,
              padding: 0,
              textAlign: isRTL ? 'right' : 'left',
              fontFamily: Fonts.medium,
              width: wp(70),
            }}
            placeholder={t('searchPlaceholder') || 'Search'}
            placeholderTextColor={colors.searchGray}
            value={searchQuery}
            onChangeText={text => setSearchQuery(text)}
          />
        </View>

        {activeTab === 'members' && (
          <FlatList
            data={filterRegisteredUsers(
              studentList?.data?.registeredUsers || [],
            )}
            contentContainerStyle={{paddingBottom: hp(10)}}
            renderItem={({item, index}) => renderItem(item, index)}
            keyExtractor={item => item?.id?.toString()}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[colors.themeColor]} // Android
                tintColor={colors.themeColor} // iOS
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                {isContactsLoading || isStudentsLoading ? (
                  <TaleemLoader
                    isLoading={isContactsLoading || isStudentsLoading}
                  />
                ) : (
                  <Text style={styles.emptyText}>{t('noMembersFound')}</Text>
                )}
              </View>
            }
          />
        )}

        {activeTab === 'contacts' && (
          <FlatList
            data={filterUnregisteredUsers(
              studentList?.data?.unregisteredUsers || [],
            )}
            contentContainerStyle={{paddingBottom: hp(10)}}
            renderItem={({item, index}) => renderUnregisteredItem(item, index)}
            keyExtractor={item => item?.id?.toString()}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[colors.themeColor]} // Android
                tintColor={colors.themeColor} // iOS
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                {isContactsLoading || isStudentsLoading ? (
                  <TaleemLoader isLoading={true} />
                ) : (
                  <Text style={styles.emptyText}>{t('noContactsFound')}</Text>
                )}
              </View>
            }
          />
        )}
        {activeTab === 'members' && (
          <View style={styles.buttonContainer}>
            <PrimaryButton
              onPress={validateSelection}
              title={t('submit')}
              style={{}}
              textStyle={{
                fontSize: fp(2),
                fontWeight: '500',
                color: colors.white,
              }}
            />
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

export default StudentList;

export const styles = StyleSheet.create({
  card: {
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 8,
    padding: 8,
    borderRadius: 10,
  },
  img: {
    height: fp(6),
    width: fp(6),
    borderRadius: fp(1),
  },
  name: {
    fontFamily: Fonts.semiBold,
    fontSize: fp(2),
    color: colors.black,
  },
  grade: {
    fontFamily: Fonts.medium,
    color: colors.darkGrey,
    fontSize: fp(1.8),
    marginTop: hp(0.6),
    width: wp(50),
  },
  nameContainer: {},
  innerRow: {
    alignItems: 'center',
  },
  chipContainer: {
    flexDirection: 'row',
    marginLeft: wp(2),
    marginBottom: hp(2),
  },
  chip: {
    paddingVertical: fp(1),
    paddingHorizontal: fp(3),
    borderRadius: fp(2),
    marginHorizontal: wp(2),
    backgroundColor: 'white',
    elevation: 2,
  },
  activeChip: {
    backgroundColor: colors.themeColor,
  },
  chipText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: colors.black,
  },
  activeChipText: {
    color: colors.white,
  },
  actionContainer: {
    flexDirection: 'row',
    gap: wp(2),
  },
  actionButton: {
    borderRadius: fp(1),
    borderWidth: fp(0.1),
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: fp(1),
    paddingHorizontal: fp(2),
  },
  smsButton: {
    borderColor: colors.themeColor,
    backgroundColor: colors.lightBlue,
  },
  actionText: {
    color: colors.themeBackground,
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontFamily: Fonts.medium,
    color: colors.lightGrey,
    fontSize: fp(1.6),
    marginTop: hp(4),
  },
  buttonContainer: {
    marginBottom: hp(2),
    paddingHorizontal: wp(4),
  },
});
