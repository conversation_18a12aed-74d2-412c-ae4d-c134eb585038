import React, {useState, useEffect} from 'react';
import {
  Alert,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
} from 'react-native';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {PrimaryButton} from '../../Components/CustomButton';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {
  validateAccountHolderName,
  validateAccountNumber,
  validateBankName,
  validateIBAN,
} from '../../Helper/BankAccountHelper/BankAccountValidators';
import {PrimaryInput} from '../../Components/Input';
import {Fonts} from '../../Utils/Fonts';
import {
  useAddBankAccountApiMutation,
  useGetBankAccountApiQuery,
  useMarkAccountAsPrimaryMutation,
} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';

const AddBankAccount = ({navigation}) => {
  const {t} = useTranslation();
  const [bankName, setBankName] = useState('');
  const [accountHolderName, setAccountHolderName] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [swiftCode, setSwiftCode] = useState('');
  const [iban, setIban] = useState('');
  const [errors, setErrors] = useState({
    bankName: '',
    accountHolderName: '',
    accountNumber: '',
    swiftCode: '',
    iban: '',
  });
  const [touched, setTouched] = useState({
    bankName: false,
    accountHolderName: false,
    accountNumber: false,
    swiftCode: false,
    iban: false,
  });
  const [primaryAccountId, setPrimaryAccountId] = useState(null); // Track the selected primary account

  const [addBankAccountApi] = useAddBankAccountApiMutation();
  const [updateBankAccountApi] = useMarkAccountAsPrimaryMutation();
  const {data: allAccountsRes, refetch} = useGetBankAccountApiQuery(undefined, {
    refetchOnFocus: true,
  });
  // console.log('🚀 ~ AddBankAccount ~ allAccountsRes:', allAccountsRes);

  useEffect(() => {
    refetch();
  }, []);

  useEffect(() => {
    if (touched.bankName) validateBankName(bankName);
    if (touched.accountHolderName) validateAccountHolderName(accountHolderName);
    if (touched.accountNumber) validateAccountNumber(accountNumber);
    if (touched.swiftCode) validateSwiftCode(swiftCode);
    if (touched.iban) validateIBAN(iban);
  }, [bankName, accountHolderName, accountNumber, swiftCode, iban, touched]);

  useEffect(() => {
    // Set the initial primary account based on API response
    if (allAccountsRes?.data?.length > 0) {
      const primaryAccount = allAccountsRes.data.find(item => item.is_primary);
      if (primaryAccount) {
        setPrimaryAccountId(primaryAccount.id);
      }
    }
  }, [allAccountsRes]);

  const validateBankName = value => {
    if (!value) {
      setErrors(prev => ({...prev, bankName: t('bankNameRequired')}));
      return false;
    } else {
      setErrors(prev => ({...prev, bankName: ''}));
      return true;
    }
  };

  const validateAccountHolderName = value => {
    if (!value) {
      setErrors(prev => ({
        ...prev,
        accountHolderName: t('accountHolderNameRequired'),
      }));
      return false;
    } else {
      setErrors(prev => ({...prev, accountHolderName: ''}));
      return true;
    }
  };

  const validateAccountNumber = value => {
    if (!value) {
      setErrors(prev => ({...prev, accountNumber: t('accountNumberRequired')}));
      return false;
    } else {
      setErrors(prev => ({...prev, accountNumber: ''}));
      return true;
    }
  };

  const validateSwiftCode = value => {
    if (value && value.length > 0) {
      // Only validate if user provides a value
      const swiftRegex = /^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$/;
      if (!swiftRegex.test(value)) {
        setErrors(prev => ({...prev, swiftCode: t('invalidSwiftCode')}));
        return false;
      }
    }
    setErrors(prev => ({...prev, swiftCode: ''}));
    return true;
  };

  const validateIBAN = value => {
    if (!value) {
      setErrors(prev => ({...prev, iban: t('ibanRequired')}));
      return false;
    } else if (!/^[A-Z]{2}\d{2}[A-Z0-9]{1,30}$/.test(value)) {
      setErrors(prev => ({...prev, iban: t('invalidIban')}));
      return false;
    } else {
      setErrors(prev => ({...prev, iban: ''}));
      return true;
    }
  };

  const handleAddAccount = () => {
    setTouched({
      bankName: true,
      accountHolderName: true,
      swiftCode: true,
      iban: true,
    });

    if (
      validateBankName(bankName) &&
      validateAccountHolderName(accountHolderName) &&
      validateSwiftCode(swiftCode) &&
      validateIBAN(iban)
    ) {
      const body = {
        account_holder_name: accountHolderName,
        account_number: iban,
        bank_name: bankName,
        swift_code: swiftCode,
        // Set as primary if it's the first account
      };
      addBankAccountApi(body)
        .unwrap()
        .then(res => {
          console.log('Bank added response:', res);
          refetch(); // Refresh the list after adding
          navigation.goBack();
        })
        .catch(err => {
          console.error('Error adding bank account:', err);
          Alert.alert('Error', t('addBankAccountError'));
        });
    } else {
      Alert.alert('Error', t('checkInputs'));
    }
  };

  const handleSetPrimary = async accountId => {
    setPrimaryAccountId(accountId);
    // Uncomment and integrate with API if needed later
    try {
      const body = {id: accountId};
      await updateBankAccountApi(body).unwrap();
      refetch();
      showToast('success', t('primaryAccountUpdated'));
      // Alert.alert('Success', );
    } catch (err) {
      console.error('Error updating primary account:', err);
      showToast('warning', t('updatePrimaryAccountError'));
      // Alert.alert('Error', t('updatePrimaryAccountError'));
    }
  };

  const HeadingSubheadingView = ({heading, value}) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginBottom: hp(1),
        }}>
        <Text
          style={{
            fontFamily: Fonts.medium,
            textAlign: 'center',
            fontSize: heading === 'Subject' ? fp(1.6) : fp(1.8),
          }}>
          {heading}
        </Text>
        <Text
          style={{
            fontFamily: Fonts.semiBold,
            textAlign: 'right',
            maxWidth: wp(40),
            lineHeight: hp(2),
            fontSize: heading === 'Subject' ? fp(1.6) : fp(1.8),
          }}>
          {value}
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('addAccount')} />
      <View style={styles.body}>
        <PrimaryInput
          title={t('bank_name')}
          placeholder={t('enter') + ' ' + t('bank_name')}
          value={bankName}
          lableStyle={styles.sessionModeTitle}
          inputMarginBottom={fp(1)}
          onChangeText={value => {
            setBankName(value);
            setTouched(prev => ({...prev, bankName: true}));
          }}
        />
        {touched.bankName && errors.bankName ? (
          <Text style={styles.errorText}>{errors.bankName}</Text>
        ) : null}

        <View style={{marginTop: hp(1)}}>
          <PrimaryInput
            title={t('account_holder_name')}
            placeholder={t('enter') + ' ' + t('account_holder_name')}
            value={accountHolderName}
            lableStyle={styles.sessionModeTitle}
            inputMarginBottom={fp(1)}
            onChangeText={value => {
              setAccountHolderName(value);
              setTouched(prev => ({...prev, accountHolderName: true}));
            }}
          />
        </View>
        {touched.accountHolderName && errors.accountHolderName ? (
          <Text style={styles.errorText}>{errors.accountHolderName}</Text>
        ) : null}

        <PrimaryInput
          title={t('IBAN')}
          placeholder={`*****************************`}
          value={iban}
          onChangeText={value => {
            setIban(value);
            setTouched(prev => ({...prev, iban: true}));
          }}
          inputMarginBottom={fp(1)}
          lableStyle={[styles.sessionModeTitle, {marginTop: hp(1)}]}
        />
        {touched.iban && errors.iban ? (
          <Text style={styles.errorText}>{errors.iban}</Text>
        ) : null}

        <PrimaryInput
          title={t('swift_code')}
          placeholder={`DOHBQA2D`}
          value={swiftCode}
          onChangeText={value => {
            setSwiftCode(value);
            setTouched(prev => ({...prev, swiftCode: true}));
          }}
          inputMarginBottom={fp(1)}
          lableStyle={[styles.sessionModeTitle, {marginTop: hp(1)}]}
        />
        {touched.swiftCode && errors.swiftCode ? (
          <Text style={styles.errorText}>{errors.swiftCode}</Text>
        ) : null}
      </View>

      {allAccountsRes?.data?.length > 0 && (
        <>
          <Text
            style={{
              fontSize: fp(1.8),
              fontFamily: Fonts.medium,
              marginLeft: wp(4),
              marginBottom: hp(1),
              marginTop: hp(2),
              textAlign: 'left',
            }}>
            {t('addedAccounts')}
          </Text>
          <ScrollView
            contentContainerStyle={{
              paddingBottom: hp(10), // Ensure space for the button
              alignItems: 'center',
            }}>
            {allAccountsRes?.data?.map(item => (
              <View
                key={item.id}
                style={{
                  borderColor: colors.themeBackground,
                  borderWidth: 0.8,
                  marginTop: hp(1),
                  padding: fp(1.6),
                  borderRadius: fp(2),
                  marginHorizontal: wp(2),
                  width: wp(90),
                  borderStyle: 'dashed',
                }}>
                <HeadingSubheadingView
                  heading={t('bank_name')}
                  value={item?.bank_name}
                />
                <HeadingSubheadingView
                  heading={t('account_number')}
                  value={`${'x'.repeat(10)}${item?.last_four_digits}`}
                />
                <TouchableOpacity
                  style={styles.radioContainer}
                  onPress={() => handleSetPrimary(item.id)}>
                  <View
                    style={[
                      styles.radioCircle,
                      {
                        borderColor:
                          primaryAccountId === item.id
                            ? colors.themeColor
                            : colors.grey,
                      },
                    ]}>
                    {primaryAccountId === item.id && (
                      <View style={styles.radioDot} />
                    )}
                  </View>
                  <Text style={styles.radioText}>{t('primary')}</Text>
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
        </>
      )}

      <View style={styles.buttonContainer}>
        <PrimaryButton onPress={handleAddAccount} title={t('addAccount')} />
      </View>
    </SafeAreaView>
  );
};

export default AddBankAccount;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: colors.white, marginBottom: fp(1)},
  body: {
    padding: fp(2),
  },
  sessionModeTitle: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.black,
  },
  errorText: {
    color: 'red',
    fontSize: fp(1.4),
    fontFamily: Fonts.medium,
    // marginBottom: fp(0.2),
  },
  buttonContainer: {
    position: 'absolute',
    bottom: hp(2),
    width: wp(100),
    alignItems: 'center',
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(1),
  },
  radioCircle: {
    width: fp(2.5),
    height: fp(2.5),
    borderRadius: fp(1.25),
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(2),
  },
  radioDot: {
    width: fp(1.2),
    height: fp(1.2),
    borderRadius: fp(0.6),
    backgroundColor: colors.themeColor,
  },
  radioText: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.black,
  },
});
