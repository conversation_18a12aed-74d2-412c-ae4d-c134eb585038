import React, {useState} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
} from 'react-native';
import {AppHeader} from '../../Components/Header';
import {showToast} from '../../Components/ToastHelper';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import {
  useSendOtpAuthMutation,
  useVerifyOtpAuthMutation,
} from '../../Api/ApiSlice';
import MobileNumberInput from '../../Components/MobileNumberInput';
import {useTranslation} from 'react-i18next';
import {wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {callingCodeToCountryCode} from '../../Utils/CountryCodeByCallingCode';

const ChangePhoneNumber = ({route, navigation}) => {
  const {mobileNo, countryCode} = route.params;
  console.log('🚀 ~ ChangePhoneNumber ~ countryCode:', countryCode);
  const [oldPhoneNumber, setOldPhoneNumber] = useState('');
  const [newPhoneNumber, setNewPhoneNumber] = useState('');
  const [callingCode, setCallingCode] = useState('');
  const [otp, setOtp] = useState('');
  const [isOtpModalVisible, setOtpModalVisible] = useState(false);
  const [token, setToken] = useState('');
  const {t, i18n} = useTranslation();
  const [sendOtp, {isLoading: isSendingOtp}] = useSendOtpAuthMutation();
  const [verifyOtp, {isLoading: isVerifyingOtp}] = useVerifyOtpAuthMutation();
  const isRTL = i18n.language === 'ar';
  const validatePhoneNumber = text => text.replace(/[^0-9]/g, '').slice(0, 10);

  const handleSendOtp = async () => {
    // if (oldPhoneNumber !== mobileNo) {
    //   showToast(
    //     'error',
    //     'Old phone number does not match the current phone number.',
    //   );
    //   return;
    // }

    if (newPhoneNumber.length !== 10) {
      showToast('error', t('mobileNumberError'), 'bottom', isRTL);
      return;
    }

    if (newPhoneNumber === oldPhoneNumber) {
      showToast('error', t('phoneMissmatch'), 'bottom', isRTL);
      return;
    }

    try {
      const response = await sendOtp({
        type: 'mobile_no',
        field_value: newPhoneNumber,
        action: 'update_fields',
      }).unwrap();

      setToken(response.data?.token || '');
      showToast('success', t('otpSentOnNewPhone'), 'bottom', isRTL);
      setOtpModalVisible(true);
    } catch (error) {
      console.error('Error sending OTP:', error);
      showToast(
        'error',
        error.data?.message || t('failedToSendOtp'),
        'bottom',
        isRTL,
      );
    }
  };

  const handleOtpSubmit = async () => {
    if (!otp.trim()) {
      showToast('error', t('emptyOtp'), 'bottom', isRTL);
      return;
    }

    try {
      await verifyOtp({
        otp,
        type: 'mobile_no',
        field_value: newPhoneNumber,
        action: 'update_fields',
        token,
      }).unwrap();

      setOtpModalVisible(false);
      showToast('success', t('phoneNumberChanged'), 'bottom', isRTL);
      navigation.goBack();
    } catch (error) {
      console.error('Error verifying OTP:', error);
      showToast(
        'error',
        error.data?.message || t('otpInvalid'),
        'bottom',
        isRTL,
      );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        backIcon={icons.backIcon}
        isBackBtn
        title={t('changeMobileNumber')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />
      <View style={styles.content}>
        <MobileNumberInput
          title={t('oldPhoneNumber')}
          value={mobileNo}
          // onChange={setMobileNumber}
          callingCode={countryCode}
          // onCodeChange={setCallingCode}
          // countryCode={countryCode}
          // onCountryCodeChange={setCountryCode}
          placeholder={t('enter_number_Txt')}
          // maxLength={maxLengthsByCountry[countryCode]}
          placeholderTextColor={colors.txtGrey1}
          callingCodeProp={countryCode}
          countryCodeProp={callingCodeToCountryCode[Number(countryCode)]}
          width={wp(90)}
          editable={false}
        />
        <MobileNumberInput
          title={t('newPhoneNumber')}
          value={newPhoneNumber}
          // onChange={setMobileNumber}
          onChangeText={text => setNewPhoneNumber(validatePhoneNumber(text))}
          callingCode={countryCode}
          onCodeChange={setCallingCode}
          // countryCode={countryCode}
          // onCountryCodeChange={setCountryCode}
          // placeholder={t('enter_number_Txt')}
          callingCodeProp={countryCode}
          countryCodeProp={callingCodeToCountryCode[Number(countryCode)]}
          // maxLength={maxLengthsByCountry[countryCode]}
          placeholderTextColor={colors.txtGrey1}
          width={wp(90)}
        />

        <TouchableOpacity
          style={styles.sendOtpButton}
          onPress={handleSendOtp}
          disabled={isSendingOtp}>
          {isSendingOtp ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.sendOtpButtonText}>{t('sendOtp')}</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* OTP Modal */}
      <Modal animationType="slide" transparent visible={isOtpModalVisible}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{t('enter_otp')}</Text>
            <TextInput
              style={[styles.input, styles.otpInput]}
              placeholder={t('enter_otp')}
              value={otp}
              onChangeText={setOtp}
              keyboardType="numeric"
              maxLength={6}
              placeholderTextColor="#ccc"
            />
            <TouchableOpacity
              style={styles.submitOtpButton}
              onPress={handleOtpSubmit}
              disabled={isVerifyingOtp}>
              {isVerifyingOtp ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <Text style={styles.submitOtpButtonText}>{t('submitOtp')}</Text>
              )}
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.submitOtpButton, styles.cancelButton]}
              onPress={() => setOtpModalVisible(false)}>
              <Text style={[styles.submitOtpButtonText, {color: colors.red}]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default ChangePhoneNumber;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    padding: 20,
  },
  label: {
    fontSize: 16,
    color: colors.black,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    marginBottom: 15,
    color: colors.black,
  },
  otpInput: {
    width: '80%',
    alignSelf: 'center',
    textAlign: 'center',
  },
  sendOtpButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  sendOtpButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.black,
    marginBottom: 15,
  },
  submitOtpButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    marginTop: 10,
    width: '100%',
  },
  submitOtpButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    backgroundColor: colors.white,
    borderColor: colors.red,
    borderWidth: 1,
  },
});
