import React, {useState} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
} from 'react-native';
import {showToast} from '../../Components/ToastHelper';
import {AppHeader} from '../../Components/Header';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {
  useSendOtpAuthMutation,
  useVerifyOtpAuthMutation,
} from '../../Api/ApiSlice';
import {Fonts} from '../../Utils/Fonts';
import {hp, wp} from '../../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';

const ChangeEmail = ({navigation, route}) => {
  const {email} = route.params; // Extract email from navigation params
  const [oldEmail, setOldEmail] = useState(email || '');
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [newEmail, setNewEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [token, setToken] = useState('');

  const [sendOtp, {isLoading: isSendingOtp}] = useSendOtpAuthMutation();
  const [verifyOtp, {isLoading: isVerifyingOtp}] = useVerifyOtpAuthMutation();
  console.log('🚀 ~ ChangeEmail ~ oldEmail:', oldEmail);
  const validateEmail = text => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(text);
  };

  const handleSendOtp = async () => {
    if (email && oldEmail.toLowerCase() == newEmail.toLowerCase()) {
      showToast('error', t('oldEmailNotMatched'), 'bottom', isRTL);
      return;
    }

    if (!validateEmail(newEmail)) {
      showToast('error', t('emailError'), 'bottom', isRTL);
      return;
    }

    try {
      const response = await sendOtp({
        type: 'email',
        field_value: newEmail.toLowerCase(),
        action: 'update_fields',
      }).unwrap();

      setToken(response.data?.token || '');
      showToast('success', t('otpSentSuccessfully'), 'bottom', isRTL);
      setIsModalVisible(true);
    } catch (error) {
      console.error('Error sending OTP:', error);
      showToast(
        'error',
        error.data?.message || t('failedToSendOtp'),
        'bottom',
        isRTL,
      );
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp.trim()) {
      showToast('error', t('emptyOtp'), 'bottom', isRTL);
      return;
    }

    try {
      await verifyOtp({
        otp,
        type: 'email',
        field_value: newEmail.toLowerCase(),
        action: 'update_fields',
        token,
      })
        .unwrap()
        .then(response => {
          console.log('🚀 ~ handleVerifyOtp ~ response:', response);
          setIsModalVisible(false);
          showToast('success', response?.message, 'bottom', isRTL);
          navigation.goBack();
        })
        .catch(error => {
          console.error('Error verifying OTP:', error);
          setIsModalVisible(false);
          showToast(
            'error',
            error.data?.message || t('otpInvalid'),
            'bottom',
            isRTL,
          );
        });
    } catch (error) {}
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        backIcon={icons.backIcon}
        isBackBtn
        title={t('changeEmail')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />
      <View style={styles.content}>
        {/* Old Email */}
        {email && (
          <>
            <Text style={styles.label}>{t('enterOldEmail')}</Text>
            <TextInput
              style={styles.input}
              placeholder={email}
              value={oldEmail}
              onChangeText={setOldEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholderTextColor="#ccc"
              editable={false}
            />
          </>
        )}

        {/* New Email */}
        <Text style={styles.label}>{t('enterNewEmail')}</Text>
        <TextInput
          style={styles.input}
          placeholder={'<EMAIL>'}
          value={newEmail}
          onChangeText={setNewEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          placeholderTextColor="#ccc"
        />

        {/* Send OTP Button */}
        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSendOtp}
          disabled={isSendingOtp}>
          {isSendingOtp ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.saveButtonText}>{t('sendOtp')}</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* OTP Modal */}
      <Modal visible={isModalVisible} transparent animationType="slide">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{t('enter_otp')}</Text>
            <TextInput
              style={[styles.input, styles.otpInput]}
              placeholder="******"
              value={otp}
              onChangeText={setOtp}
              keyboardType="numeric"
              placeholderTextColor="#ccc"
              maxLength={6}
            />
            <View style={{flexDirection: 'row', gap: wp(6), marginTop: hp(1)}}>
              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleVerifyOtp}
                disabled={isVerifyingOtp}>
                {isVerifyingOtp ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <Text style={styles.saveButtonText}> {t('verify_otp')}</Text>
                )}
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.saveButton, styles.cancelButton]}
                onPress={() => setIsModalVisible(false)}>
                <Text style={styles.saveButtonText}>{t('cancel')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default ChangeEmail;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    padding: 20,
  },
  label: {
    fontSize: 16,
    color: colors.black,
    marginBottom: 8,
    fontFamily: Fonts.medium,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    marginBottom: 15,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  otpInput: {
    width: wp(70),
    alignSelf: 'center',
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
    marginBottom: 10,
    width: '40%',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: Fonts.medium,
    marginBottom: 15,
  },
  cancelButton: {
    backgroundColor: colors.offRed,
  },
});
