import {
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';

import {useTranslation} from 'react-i18next';
import RenderHTML from 'react-native-render-html';

import {useSelector} from 'react-redux';
import icons from '../../Utils/icons';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {AppHeader} from '../../Components/Header';
import {useGetStudentStaticContentQuery} from '../../Api/ApiSlice';
import {Fonts} from '../../Utils/Fonts';

const PrivacyPolicyScreen = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const {user_type} = useSelector(state => state?.auth);

  const {data: staticData, isLoading} = useGetStudentStaticContentQuery({
    pageType: 'privacy-policy',
    user: user_type == 3 ? 'tutor' : user_type == '2' ? 'parent' : 'user',
  });
  console.log('🚀 ~ PrivacyPolicyScreen ~ staticData:', staticData);
  useEffect(() => {
    console.log('93284975980294', staticData);
  }, [staticData]);

  const styledHtmlContent = `
  <html>
      <head>
          <style>
              body {
                  color: #333; /* Default text color */
                  font-family: Arial, sans-serif; /* Default font family */
                  justify-content: ${
                    !staticData?.data?.content ? 'center' : 'left'
                  }
              }
              p {
                  color: #555; /* Custom text color for paragraphs */
              }
              h1, h2, h3, h4, h5, h6 {
                  color: #222; /* Custom text color for headings */
              }
              strong {
                  color: #000; /* Custom text color for strong elements */
              }
              ul {
                  color: #666; /* Custom text color for list items */
              }
          </style>
      </head>
      <body style="${
        !staticData?.data?.content
          ? 'display: flex; align-items: center; justify-content: center; height: 100vh; text-align: center;'
          : 'text-align: left;'
      }">
          ${staticData?.data?.content || t('noData')}
      </body>
  </html>
`;

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={t('privacyPolicy')}
        backIcon={icons.backbtn}
        isBackBtn
        isWhite={true}
      />
      <ScrollView
        contentContainerStyle={{
          paddingVertical: hp(2),
          marginHorizontal: wp(2),
        }}>
        {isLoading ? (
          <ActivityIndicator size={'large'} />
        ) : (
          <RenderHTML
            contentWidth={'100%'}
            source={{html: styledHtmlContent}}
            baseStyle={{
              color: 'black',
              fontFamily: Fonts.medium,
              direction: isRTL ? 'rtl' : 'ltr',
            }}
            tagsStyles={{
              p: {color: 'gray', direction: isRTL ? 'rtl' : 'ltr'},
              h1: {
                color: 'black',
                fontFamily: fp(1.2),
                direction: isRTL ? 'rtl' : 'ltr',
              },
              h2: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h3: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h4: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h5: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h6: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              strong: {color: 'black'},
              ul: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              li: {color: 'gray', direction: isRTL ? 'rtl' : 'ltr'},
              body: {marginHorizontal: wp(6), direction: isRTL ? 'rtl' : 'ltr'},
            }}
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default PrivacyPolicyScreen;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: colors.white},
});
