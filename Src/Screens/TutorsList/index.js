import {
  <PERSON><PERSON><PERSON>V<PERSON><PERSON>,
  <PERSON>rollView,
  Text,
  View,
  ActivityIndicator,
  I18nManager,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import styles from './styles';
import icons from '../../Utils/icons';
import TeacherCard from '../../Components/Custom_Components/TeacherCard';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import {FilterButton} from '../../Components/FilterButton';
import {
  useGetTutorsAcademicQuery,
  useGetTutorsRecreationalQuery,
  useGetVisitedTutorListQuery,
  usePutVisitedTutorListMutation,
} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import TutorListHeader from '../../Components/TutorListHeader';
import {useFocusEffect} from '@react-navigation/native';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {DUMMY_USER_IMG, studentFiltersRatingsMap} from '../../Utils/constant';
import {useSelector} from 'react-redux';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import BlinkingArrow from '../../Components/BlinkingArrow';

const TutorList = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const {selectedFilters} = route?.params || {};
  console.log('selectedFilters', selectedFilters);
  const {data: tutorVisitedList, refetch} = useGetVisitedTutorListQuery();

  const [putVisitedTutorList] = usePutVisitedTutorListMutation();
  console.log('🚀 ~ TutorList ~ tutorVisitedList:', tutorVisitedList);
  const [searchQuery, setSearchQuery] = useState('');
  const [isFiltePressed, setIsFiltePressed] = useState(false);
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [price, setPrice] = useState({
    from: '',
    to: '',
  });
  const {bookingFlowType, expertiseIdRecreational} = useSelector(
    state => state?.tutorBookingSlice,
  );
  useFocusEffect(
    useCallback(() => {
      // Perform some action when the screen is focused
      refetch();
    }, []),
  );
  const [tutorData, setTutorData] = useState(null);
  const [filteredData, setFilteredData] = useState(null);
  const mapRatingToNumber = rating => {
    switch (rating) {
      case 5:
        return 1;
      case 4:
        return 2;
      case 3:
        return 3;
      case 2:
        return 4;
      case 1:
        return 5;
      default:
        return null; // If no rating is selected, return null
    }
  };

  const getPrice = () => {
    if (selectedFilters?.Price == 1) {
      setPrice({from: '0', to: '50'});
    } else if (selectedFilters?.Price == 2) {
      setPrice({from: '50', to: '100'});
    } else if (selectedFilters?.Price == 3) {
      setPrice({from: '100', to: ''});
    } else {
      setPrice({from: '', to: ''});
    }
  };
  useEffect(() => {
    getPrice();
  }, [selectedFilters]);
  console.log('🚀 ~ TutorList ~ selectedFilters:', selectedFilters);
  const queryParams = Object.fromEntries(
    Object.entries({
      name: debouncedQuery || '', // Ensure debouncedQuery is not undefined
      gender: selectedFilters?.gender?.join(',') || '', // Use optional chaining and default to an empty string
      // ratingsFrom:
      //   studentFiltersRatingsMap?.[selectedFilters?.ratings?.[0]]?.from || '', // Safely access ratings map
      // ratingsTo:
      //   studentFiltersRatingsMap?.[selectedFilters?.ratings?.[0]]?.to || '', // Safely access ratings map
      ratingsFilter: selectedFilters?.ratings?.length > 0 ? '1' : '0',
      priceFilter: selectedFilters?.price?.[0] || '', // Ensure price is not undefined
      language:
        selectedFilters?.Language === 2
          ? 'ar'
          : selectedFilters?.Language === 1
          ? 'en'
          : '',
      distance: selectedFilters?.distance || '', // Default to an empty string
      curriculum_id: selectedFilters?.Curriculum?.join(',') || '', // Safely join Curriculum
      subject_id: selectedFilters?.subject?.join(',') || '', // Safely join Subject
      class_type_id: selectedFilters?.classtype?.join(','),
    }).filter(
      ([_, value]) => value !== '' && value !== null && value !== undefined, // Filter out invalid values
    ),
  );
  console.log('🚀 ~ TutorList ~ queryParams:', queryParams);

  const {
    data: academicData,
    error: academicError,
    isLoading: academicLoading,
    refetch: refetchTutorAcademicList,
  } = useGetTutorsAcademicQuery(queryParams, {
    skip: bookingFlowType !== 'academic',
  });

  const {
    data: recreationalData,
    isLoading: searchLoading,
    refetch: refetchTutorRecreationalList,
    error: searchError,
  } = useGetTutorsRecreationalQuery(
    {
      expertise_id: expertiseIdRecreational,
      name: debouncedQuery,
      gender: selectedFilters?.gender?.join(',') || '',
      // ratingsFrom:
      //   studentFiltersRatingsMap?.[selectedFilters?.ratings?.[0]]?.from || '', // Safely access ratings map
      // ratingsTo:
      //   studentFiltersRatingsMap?.[selectedFilters?.ratings?.[0]]?.to || '', // Safely access ratings map
      priceFilter: selectedFilters?.price?.[0] || '', // Ensure price is not undefined
      ratingsFilter: selectedFilters?.ratings?.[0],
      language:
        selectedFilters?.Language === 2
          ? 'ar'
          : selectedFilters?.Language === 1
          ? 'en'
          : '',
      distance: selectedFilters?.distance,
    },

    {skip: bookingFlowType !== 'recreational'},
  );

  useEffect(() => {
    if (bookingFlowType === 'academic' && academicData) {
      setTutorData(academicData);
    } else if (bookingFlowType === 'recreational' && recreationalData) {
      setTutorData(recreationalData);
    }
  }, [bookingFlowType, academicData, recreationalData]);

  useEffect(() => {
    if (bookingFlowType === 'academic') {
      refetchTutorAcademicList();
    } else if (bookingFlowType === 'recreational') {
      refetchTutorRecreationalList();
    }
  }, [bookingFlowType, refetchTutorAcademicList, refetchTutorRecreationalList]);
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      // Runs when the screen is about to be removed
      AsyncStorage.removeItem('filters')
        .then(() => console.log('Item removed from AsyncStorage'))
        .catch(err => console.log('Error removing item:', err));
    });

    return unsubscribe; // Clean up the listener on unmount
  }, [navigation]);

  useFocusEffect(
    useCallback(() => {
      if (bookingFlowType === 'academic') {
        refetchTutorAcademicList();
      } else if (bookingFlowType === 'recreational') {
        refetchTutorRecreationalList();
      }
    }, [
      bookingFlowType,
      refetchTutorAcademicList,
      refetchTutorRecreationalList,
    ]),
  );

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 500); // Delay of 500ms

    return () => clearTimeout(handler); // Cleanup timeout
  }, [searchQuery]);

  // useEffect(() => {
  //   if (debouncedQuery) {
  //     const filteredTutors = tutorData?.data?.rows?.filter(teacher =>
  //       teacher.name.toLowerCase().includes(debouncedQuery.toLowerCase()),
  //     );
  //     setFilteredData({...tutorData, data: {rows: filteredTutors}});
  //   } else {
  //     setFilteredData(tutorData);
  //   }
  // }, [debouncedQuery, tutorData]);

  const handleTeacherClick = teacher => {
    navigation.navigate('TutorsDetails', {
      id: teacher.id,
    });
  };

  const renderTutorCards = data => {
    return data?.rows?.map(teacher => {
      console.log('🚀 ~ TutorList ~ teacher:', teacher);
      // const subject = teacher?.tlm_user?.tlm_tutor_expertises?.map(
      //   item => item?.tlm_expertise?.name,
      // );
      const subject = teacher?.tlm_subject?.name
        ? [teacher.tlm_subject.name]
        : [];
      // const subject = [teacher?.occupation];
      const imageUrl = teacher.image
        ? {uri: `${IMAGE_BASE_URL}${teacher.image}`}
        : {uri: DUMMY_USER_IMG};
      console.log('🚀 ~ renderTutorCards ~ imageUrl:', imageUrl);
      return (
        <TeacherCard
          key={teacher.id}
          name={capitalizeFirstLetter(teacher.name)}
          subject={subject}
          price={teacher.price.toString()}
          rating={teacher?.ratings}
          imageUrl={imageUrl}
          isTopTutor={teacher.isTopTutor || false}
          onPress={() => handleTeacherClick(teacher)}
        />
      );
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />

      <TutorListHeader
        onBackPress={() => navigation.navigate('Home')}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />

      <View style={styles.content}>
        <Text style={[styles.title, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('tutorsTitle', {count: tutorData?.data?.rows?.length || 0})}
        </Text>

        {tutorData?.data?.rows?.length === 0 && (
          <View
            style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
            <Text
              style={{
                fontFamily: Fonts.medium,
                color: colors.grey,
              }}>
              No Tutor Found
            </Text>
          </View>
        )}
        <TaleemLoader isLoading={academicLoading || searchLoading} />
        <ScrollView
          contentContainerStyle={styles.gridContainer}
          showsVerticalScrollIndicator={false}>
          {/* {renderTutorCards(tutorData?.data)} */}
          {/* {debouncedQuery
              ? renderTutorCards(searchData?.data)
              : renderTutorCards(tutorData?.data)} */}
          {renderTutorCards(tutorData?.data)}
        </ScrollView>
        {/* )} */}
        {/* <View style={{marginTop: hp(1)}}> */}
        {tutorVisitedList?.data?.visited_tutor_list === false && (
          <BlinkingArrow />
        )}

        {/* </View> */}
        <FilterButton
          onPress={() => {
            putVisitedTutorList();
            navigation.navigate('FilterScreen');
          }}
          title={t('filter')}
          textStyle={{
            fontSize: fp(1.8),
            fontFamily: Fonts.regular,
            color: colors.white,
          }}
          style={{zIndex: 102}}
        />
      </View>
    </SafeAreaView>
  );
};

export default TutorList;
