import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Platform,
  SafeAreaView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import CustomDropDown from '../../Components/CustomDropDown';
import colors from '../../Utils/colors';
import CustomCheckbox from '../../Components/CustomCheckbox';
import {PrimaryInput} from '../../Components/Input';
import {PrimaryButton} from '../../Components/CustomButton';
import {showToast} from '../../Components/ToastHelper';
import {useNavigation, useRoute} from '@react-navigation/native';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import styles from './styles';
import {
  useChooseYourGradeQuery,
  useGetSubjectsTutorQuery,
  useGetClassAndSchedulesQuery,
  useGetPackgeTypesQuery,
  useEditAcademicRateCardMutation,
  useDeleteAcademicRateCardMutation,
  useLazyGetAcademicRateCardEditDataQuery,
  useLazyChooseYourCurriculumsQuery,
} from '../../Api/ApiSlice';

const EditAcademic = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigation = useNavigation();
  const route = useRoute();

  const {rateCardId, cummissionOnline, cummissionF2F, cummissionOpenSessaion} =
    route.params || {};
  console.log(
    'cummission===',
    cummissionOnline,
    cummissionF2F,
    cummissionOpenSessaion,
  );
  // API Hooks
  const [
    getAcademicRateCardEditData,
    {data: rateCardData, isLoading: isRateCardLoading, error: rateCardError},
  ] = useLazyGetAcademicRateCardEditDataQuery(rateCardId, {skip: !rateCardId});

  const [
    chooseYourCurriculum,
    {
      data: curriculumData,
      isLoading: isCurriculumLoading,
      error: curriculumError,
    },
  ] = useLazyChooseYourCurriculumsQuery();
  useEffect(() => {
    chooseYourCurriculum({viewAll: true});
  }, []);

  const {
    data: gradesData,
    isLoading: isGradesLoading,
    error: gradesError,
  } = useChooseYourGradeQuery();

  const {
    data: subjectsData,
    isLoading: isSubjectsLoading,
    error: subjectsError,
  } = useGetSubjectsTutorQuery();
  const {
    data: classAndSchedulesData,
    isLoading: isClassLoading,
    error: classError,
  } = useGetClassAndSchedulesQuery();
  const {
    data: packageTypesData,
    isLoading: isPackageLoading,
    error: packageError,
  } = useGetPackgeTypesQuery();

  const [editAcademicRateCard] = useEditAcademicRateCardMutation();
  const [deleteAcademicRateCard, {isLoading: isDeleting}] =
    useDeleteAcademicRateCardMutation();

  // State management
  const [optionalSubject, setOptionalSubject] = useState('');
  const [activeBtn, setActiveBtn] = useState(null);
  const [defaultGrade, setDefaultGrade] = useState(null);
  const [loading, setLoading] = useState(false);

  const [priceOnline, setPriceOnline] = useState('');
  const [priceFaceToFace, setPriceFaceToFace] = useState('');

  const [selectedOnlineSessions, setSelectedOnlineSessions] = useState([]);
  const [selectedF2FSessions, setSelectedF2FSessions] = useState([]);

  const [minStudents, setMinStudents] = useState('');
  const [maxStudents, setMaxStudents] = useState('');
  const [selectedPackages, setSelectedPackages] = useState([]);
  const [selectAddOptional, setSelectAddOptional] = useState(false);

  const [defaultCurriculum, setDefaultCurriculum] = useState(null);
  const [defaultSubject, setDefaultSubject] = useState(null);
  const [subject, setSubject] = useState(null);
  const [curriculum, setCurriculum] = useState(null);
  const [priceOpenSession, setPriceOpenSession] = useState('');
  const [selectOpenSession, setSelectOpenSession] = useState([]);

  // Identify online and face-to-face data from API
  const onlineData = classAndSchedulesData?.data?.find(item =>
    item.value.toLowerCase().includes('online'),
  );
  const faceToFaceData = classAndSchedulesData?.data?.find(item =>
    item.value.includes(t('faceToFace')),
  );

  //managing min student and max student
  const online_groupId = onlineData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'group',
  )?.id;
  const online_openSessionId = onlineData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'opensession',
  )?.id;

  const f2f_groupId = faceToFaceData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'group',
  )?.id;
  const f2f_openSessionId = faceToFaceData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'opensession',
  )?.id;
  const openSessionData = classAndSchedulesData?.data?.find(item =>
    item.value?.toLowerCase().includes('opensession'),
  );
  console.log('online_groupId', onlineData);
  useEffect(() => {
    getAcademicRateCardEditData(rateCardId);
  }, [rateCardId]);

  useEffect(() => {
    if (rateCardData?.data && curriculumData?.data && subjectsData?.data) {
      const data = rateCardData.data;
      console.log('🚀 ~ useEffect ~ data:', JSON.stringify(data));

      // Set Curriculum
      const matchedCurriculum = curriculumData?.data?.rows.find(
        curr => curr.id === data.curriculum_id,
      );
      setDefaultCurriculum(matchedCurriculum);
      setCurriculum(matchedCurriculum?.id || null);

      // Set Subject
      const matchedSubject = subjectsData?.data?.rows.find(
        subj => subj.id === data.subject_id,
      );
      setDefaultSubject(matchedSubject);
      setSubject(matchedSubject?.id || null);

      // Set active grade
      const matchedGrade = gradesData?.data?.rows.find(
        grade => grade.id === data.grades_id,
      );

      setDefaultGrade(matchedGrade);
      setActiveBtn(data.grades_id);

      // Optional subject
      if (data.optional_subject) {
        setOptionalSubject(data.optional_subject);
        setSelectAddOptional(true);
      }

      // Min and max students
      setMinStudents(data.min_student_no?.toString() || '');
      setMaxStudents(data.max_student_no?.toString() || '');

      // Set packages
      const packages = Array.isArray(data.tlm_tutor_rate_card_packages)
        ? data.tlm_tutor_rate_card_packages.map(pkg => pkg.package_id)
        : [];
      setSelectedPackages(packages);

      // Set class types (to get prices)
      const classTypes = data.tlm_tutor_class_types || [];
      // We'll capture prices for each mode independently
      classTypes.forEach(classType => {
        const classTypeName = classType.tlm_class_type.value;
        if (classTypeName.toLowerCase().includes('online')) {
          const price = parseFloat(classType.price).toString();
          setPriceOnline(price);
        } else if (classTypeName.includes('faceToFace')) {
          const price = parseFloat(classType.price).toString();
          setPriceFaceToFace(price);
        } else if (classTypeName.toLowerCase().includes('open session')) {
          const price = parseFloat(classType.price).toString();
          setPriceOpenSession(price);
        }
      });

      // Set selected sessions
      const allSelectedSessions = Array.isArray(data.tlm_tutor_class_sessions)
        ? data.tlm_tutor_class_sessions.map(session => session.sessions_id)
        : [];

      // Filter sessions into online and face to face arrays
      const onlineSessionIds =
        onlineData?.tlm_sessions_types.map(s => s.id) || [];
      const f2fSessionIds =
        faceToFaceData?.tlm_sessions_types.map(s => s.id) || [];
      const openSessoinIds =
        openSessionData?.tlm_sessions_types.map(s => s.id) || [];

      setSelectedOnlineSessions(
        allSelectedSessions.filter(sessionId =>
          onlineSessionIds.includes(sessionId),
        ),
      );

      setSelectedF2FSessions(
        allSelectedSessions.filter(sessionId =>
          f2fSessionIds.includes(sessionId),
        ),
      );
      setSelectOpenSession(
        allSelectedSessions.filter(sessionId =>
          openSessoinIds.includes(sessionId),
        ),
      );
    }
  }, [rateCardData, curriculumData, subjectsData, classAndSchedulesData]);

  const toggleOnlineSessionSelection = sessionId => {
    setSelectedOnlineSessions(prev => {
      const newSelected = prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId];
      console.log('🚀 ~ EditAcademic ~ newSelected:', newSelected);

      //  setOptions(prevOptions => ({
      //    ...prevOptions, // Keep other keys unchanged
      //    [key]: !prevOptions[key], // Toggle the selected key
      //  }));
      // setIsGroupSelected(
      //   newSelected.length > 0 || selectedF2FSessions.length > 0,
      // );
      return newSelected;
    });
  };

  const toggleF2FSessionSelection = sessionId => {
    setSelectedF2FSessions(prev => {
      const newSelected = prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId];

      return newSelected;
    });
  };

  const toggleOpenSessionSelection = sessionId => {
    setSelectOpenSession(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
  };

  const togglePackageSelection = packageId => {
    setSelectedPackages(prev =>
      prev.includes(packageId)
        ? prev.filter(id => id !== packageId)
        : [...prev, packageId],
    );
  };

  const validateForm = () => {
    if (
      selectedOnlineSessions.some(
        id => id === online_groupId || id === online_openSessionId,
      ) ||
      selectedF2FSessions.some(
        id => id === f2f_groupId || id === f2f_openSessionId,
      )
    ) {
      if (!minStudents || !maxStudents || !subject) {
        showToast('error', t('please_fill_all_fields'), 'bottom', isRTL);
        return false;
      }
      if (isNaN(minStudents) || isNaN(maxStudents)) {
        showToast(
          'error',
          t('min_and_max_students_must_be_numeric'),
          'bottom',
          isRTL,
        );
        return false;
      }
      if (parseInt(minStudents) >= parseInt(maxStudents)) {
        showToast(
          'error',
          t('min_students_must_be_less_than_max_students'),
          'bottom',
          isRTL,
        );
        return false;
      }
    }

    const anyOnlineSelected = selectedOnlineSessions.length > 0;
    const anyF2FSelected = selectedF2FSessions.length > 0;
    const anyOpenSelected = selectOpenSession.length > 0;

    if (!anyOnlineSelected && !anyF2FSelected && !anyOpenSelected) {
      showToast(
        'error',
        t('please_select_at_least_one_session_type'),
        'bottom',
        isRTL,
      );
      return false;
    }

    if (anyOnlineSelected && !priceOnline) {
      showToast('error', t('please_provide_online_price'), 'bottom', isRTL);
      return false;
    }

    if (anyF2FSelected && !priceFaceToFace) {
      showToast(
        'error',
        t('please_provide_face_to_face_price'),
        'bottom',
        isRTL,
      );
      return false;
    }
    if (anyOpenSelected && !priceOpenSession) {
      showToast('error', 'please add price', 'bottom', isRTL);
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    console.log('save pressed');
    if (!validateForm()) return;
    setLoading(true);
    try {
      const class_type_ids = [];
      if (selectedOnlineSessions.length > 0 && onlineData?.id) {
        class_type_ids.push({
          class_type_id: onlineData.id.toString(),
          price: parseFloat(priceOnline),
        });
      }
      if (selectedF2FSessions.length > 0 && faceToFaceData?.id) {
        class_type_ids.push({
          class_type_id: faceToFaceData.id.toString(),
          price: parseFloat(priceFaceToFace),
        });
      }
      if (selectOpenSession.length > 0 && openSessionData?.id) {
        class_type_ids.push({
          class_type_id: Number(openSessionData.id),
          price: parseFloat(priceOpenSession),
        });
      }
      const class_sessions = [
        ...selectedOnlineSessions.map(id => ({sessions_id: id.toString()})),
        ...selectedF2FSessions.map(id => ({sessions_id: id.toString()})),
        ...selectOpenSession.map(id => ({sessions_id: id.toString()})),
      ];

      const payload = {
        curriculum_id: curriculum
          ? curriculum.toString()
          : defaultCurriculum?.id.toString() || '',
        grades_id: activeBtn ? activeBtn.toString() : '',
        subject_id: subject
          ? subject.toString()
          : defaultSubject?.id.toString() || '',
        package_id: selectedPackages.map(id => id.toString()),
        class_sessions: class_sessions,
        class_type_ids: class_type_ids,
        optional_subject: optionalSubject,
        min_student_no: minStudents || '',
        max_student_no: maxStudents || '',
      };
      console.log('payload======', payload);
      await editAcademicRateCard({id: rateCardId, data: payload}).unwrap();
      showToast(
        'success',
        t('academic_rate_card_updated_successfully'),
        'bottom',
        isRTL,
      );
      navigation.goBack();
    } catch (error) {
      showToast(
        'error',
        error?.data?.message || t('error_in_saving_rate_card'),
        'bottom',
        isRTL,
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      t('confirm_delete'),
      t('are_you_sure_you_want_to_delete_this_card'),
      [
        {text: t('cancel'), style: 'cancel'},
        {text: t('delete'), style: 'destructive', onPress: confirmDelete},
      ],
      {cancelable: true},
    );
  };

  const confirmDelete = async () => {
    try {
      await deleteAcademicRateCard(rateCardId).unwrap();
      showToast(
        'success',
        t('academic_rate_card_deleted_successfully'),
        'bottom',
        isRTL,
      );
      navigation.goBack();
    } catch (error) {
      showToast(
        'error',
        error?.data?.message || t('error_in_deleting_rate_card'),
        'bottom',
        isRTL,
      );
    }
  };
  console.log('🚀 ~ EditAcademic ~ subject:', subject);
  // Loading States
  if (
    isRateCardLoading ||
    isCurriculumLoading ||
    isGradesLoading ||
    isSubjectsLoading ||
    isClassLoading ||
    isPackageLoading
  ) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.themeColor} />
        <Text style={styles.loadingText}>{t('loading')}</Text>
      </View>
    );
  }

  // Error States
  if (
    rateCardError ||
    curriculumError ||
    gradesError ||
    subjectsError ||
    classError ||
    packageError
  ) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('error_loading_data')}</Text>
        <TouchableOpacity>
          <Text style={styles.retryText}>{t('retry')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('edit_academic_rate_card')}
      />
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={styles.container}
            keyboardShouldPersistTaps="handled">
            {/* Grade Selection */}
            <CustomDropDown
              lable={t('selectGrade')}
              data={gradesData?.data?.rows || []}
              backgroundColor={colors.txtGrey}
              defaultValue={defaultGrade?.name || ''}
              height={40}
              onSelect={item => setActiveBtn(item.id)}
              alreadySelectedItem={defaultGrade}
            />
            {/* Curriculum Dropdown */}

            {curriculumData && (
              <CustomDropDown
                lable={t('selectCurriculum')}
                data={curriculumData?.data?.rows || []}
                backgroundColor={colors.txtGrey}
                defaultValue={defaultCurriculum?.name || ''}
                height={40}
                onSelect={selectedItem => setCurriculum(selectedItem.id)}
                alreadySelectedItem={defaultCurriculum}
              />
            )}

            {/* Grade Selection */}
            {/* <View
          style={[
            styles.section,
            {alignItems: isRTL ? 'flex-end' : 'flex-start'},
          ]}>
          <Text style={styles.grade}>{t('select_grade')}</Text>
          <FlatList
            data={gradesData?.data?.rows || []}
            horizontal
            contentContainerStyle={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
            }}
            showsHorizontalScrollIndicator={false}
            keyExtractor={item => item.id.toString()}
            renderItem={({item}) => (
              <TouchableOpacity
                onPress={() => setActiveBtn(item.id)}
                style={[
                  styles.topButton,
                  {
                    backgroundColor:
                      activeBtn === item.id ? colors.themeColor : colors.white,
                  },
                ]}>
                <Text
                  style={[
                    activeBtn === item.id ? {color: 'white'} : styles.btnTxt,
                  ]}>
                  {item.name}
                </Text>
              </TouchableOpacity>
            )}
          />
        </View> */}

            {/* Sessions Section */}
            <View
              style={[
                styles.sectionContainer,
                {alignItems: isRTL ? 'flex-end' : 'flex-start'},
              ]}>
              {/* <Text style={styles.sectionTitle}>{t('select_session')}</Text> */}

              {/* Face to Face Sessions */}
              {faceToFaceData && (
                <View
                  style={{
                    marginTop: 20,
                    alignItems: isRTL ? 'flex-end' : 'flex-start',
                  }}>
                  <Text
                    style={[
                      styles.sessionModeTitle,
                      // {textAlign: isRTL ? 'right' : 'left'},
                    ]}>
                    {faceToFaceData.name}
                  </Text>
                  <View
                    style={[
                      styles.checkboxView,
                      {flexDirection: isRTL ? 'row-reverse' : 'row'},
                    ]}>
                    {faceToFaceData.tlm_sessions_types.map((type, index) => (
                      <View
                        key={type.id}
                        style={{
                          // marginLeft: index % 2 === 1 ? 24 : 0,
                          marginBottom: 10,
                        }}>
                        <CustomCheckbox
                          label={type.name}
                          isSelected={selectedF2FSessions.includes(type.id)}
                          onSelect={() => toggleF2FSessionSelection(type.id)}
                        />
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {/* Online Sessions */}
              {onlineData && (
                <View
                  style={{
                    marginTop: 20,
                    alignItems: isRTL ? 'flex-end' : 'flex-start',
                  }}>
                  <Text style={styles.sessionModeTitle}>{onlineData.name}</Text>
                  <View
                    style={[
                      styles.checkboxView,
                      {flexDirection: isRTL ? 'row-reverse' : 'row'},
                    ]}>
                    {onlineData.tlm_sessions_types.map((type, index) => (
                      <View
                        key={type.id}
                        style={{
                          // marginLeft: index % 2 === 1 ? 24 : 0,
                          marginBottom: 10,
                        }}>
                        <CustomCheckbox
                          label={type.name}
                          isSelected={selectedOnlineSessions.includes(type.id)}
                          onSelect={() => toggleOnlineSessionSelection(type.id)}
                        />
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </View>
            {/* Open Sessions */}
            {/* {openSessionData && (
          <View
            style={{
              marginTop: 20,
              alignItems: isRTL ? 'flex-end' : 'flex-start',
            }}>
            <Text style={styles.sessionModeTitle}>{openSessionData.name}</Text>
            <View
              style={[
                styles.checkboxView,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {openSessionData?.tlm_sessions_types.map((type, index) => (
                <View
                  key={type.id}
                  style={{
                    marginLeft: index % 2 === 1 ? 24 : 0,
                    marginBottom: 10,
                  }}>
                  <CustomCheckbox
                    label={type.name}
                    isSelected={selectOpenSession.includes(type.id)}
                    onSelect={() => toggleOpenSessionSelection(type.id)}
                  />
                </View>
              ))}
            </View>
          </View>
        )} */}

            {/* Subject Dropdown */}
            <CustomDropDown
              lable={t('select_subject')}
              data={subjectsData?.data?.rows || []}
              labelStyle={styles.dropdownLabel}
              backgroundColor={colors.white}
              height={50}
              radius={12}
              defaultValue={defaultSubject?.name || ''}
              borderWidth={1}
              borderColor={colors.txtGrey}
              onSelect={selectedItem => setSubject(selectedItem.id)}
            />
            {/* 
        <PrimaryButton
          title={t('add_optional')}
          style={[
            styles.editBtn,
            {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
          ]}
          textStyle={styles.btnTxt}
          onPress={() => setSelectAddOptional(!selectAddOptional)}
        /> */}
            {subjectsData?.data?.rows.find(item => item.id === subject)?.name ==
              'Other' && (
              <PrimaryInput
                title={t('optional_subject')}
                placeholder={t('enter_optional_subject')}
                value={optionalSubject}
                lableStyle={styles.sessionModeTitle}
                onChangeText={setOptionalSubject}
                style={styles.optionalInput}
              />
            )}

            {/* Show Face to Face Price if selected */}
            {selectedF2FSessions.length > 0 && (
              <PrimaryInput
                keyboardType="numeric"
                title={
                  // cummissionF2F > 0
                  //   ? `${t('price_for_face_to_face')} (${cummissionF2F}%)`
                  //   : t('price_for_face_to_face')

                  cummissionF2F > 0
                    ? `${t('price_for_face_to_face')} (${t(
                        'commission_applied',
                      )} ${cummissionF2F}%)`
                    : t('price_for_face_to_face')
                }
                placeholder={t('180 QAR')}
                value={priceFaceToFace}
                lableStyle={styles.sessionModeTitle}
                onChangeText={text =>
                  setPriceFaceToFace(text.replace(/[^0-9]/g, ''))
                }
                style={styles.input}
              />
            )}

            {/* Show Online Price if selected */}
            {selectedOnlineSessions.length > 0 && (
              <PrimaryInput
                keyboardType="numeric"
                title={
                  cummissionOnline > 0
                    ? `${t('price_for_online')} (${t(
                        'commission_applied',
                      )} ${cummissionOnline}%)`
                    : t('price_for_online')
                }
                placeholder={t('150 QAR')}
                value={priceOnline}
                lableStyle={styles.sessionModeTitle}
                onChangeText={text =>
                  setPriceOnline(text.replace(/[^0-9]/g, ''))
                }
                style={styles.input}
              />
            )}
            {selectOpenSession.length > 0 && (
              <PrimaryInput
                title={
                  cummissionOpenSessaion > 0
                    ? `${t('price_for_open_session')} (${t(
                        'commission_applied',
                      )}${cummissionOpenSessaion}%)`
                    : t('price_for_open_session')
                }
                placeholder={t('120 qar')}
                value={priceOpenSession}
                lableStyle={styles.sessionModeTitle}
                onChangeText={text =>
                  setPriceOpenSession(text.replace(/[^0-9]/g, ''))
                }
                keyboardType="numeric"
              />
            )}

            {/* Minimum Students Input */}

            {(selectedOnlineSessions.some(
              id => id === online_groupId || id === online_openSessionId,
            ) ||
              selectedF2FSessions.some(
                id => id === f2f_groupId || id === f2f_openSessionId,
              )) && (
              <View>
                {/* Minimum Students Input */}
                <PrimaryInput
                  title={t('min_student')}
                  placeholder={t('min_student')}
                  value={minStudents}
                  lableStyle={styles.sessionModeTitle}
                  onChangeText={text => {
                    const value = text.replace(/[^0-9]/g, '');
                    if (
                      value &&
                      parseInt(value) > 1 &&
                      (!maxStudents || parseInt(value) < parseInt(maxStudents))
                    ) {
                      setMinStudents(value);
                    } else if (!value) {
                      setMinStudents('');
                    }
                  }}
                  keyboardType="numeric"
                  style={styles.input}
                />

                {/* Maximum Students Input */}
                <PrimaryInput
                  title={t('max_student')}
                  placeholder={t('max_student')}
                  value={maxStudents}
                  lableStyle={styles.sessionModeTitle}
                  onChangeText={text =>
                    setMaxStudents(text.replace(/[^0-9]/g, ''))
                  }
                  keyboardType="numeric"
                  style={styles.input}
                />
              </View>
            )}

            {/* Package Types Selection */}
            <View
              style={[
                styles.section,
                {alignItems: isRTL ? 'flex-end' : 'flex-start'},
              ]}>
              <Text style={styles.sectionTitle}>{t('package_type')}</Text>
              {packageTypesData?.data?.rows?.map(pkg => (
                <CustomCheckbox
                  key={pkg.id}
                  label={pkg.name}
                  isSelected={selectedPackages.includes(pkg.id)}
                  onSelect={() => togglePackageSelection(pkg.id)}
                />
              ))}
            </View>

            {/* Save & Delete Buttons */}
            <PrimaryButton
              loading={loading || isDeleting}
              onPress={handleSubmit}
              title={t('save')}
              style={styles.saveButton}
            />
            <PrimaryButton
              loading={loading || isDeleting}
              onPress={handleDelete}
              title={t('delete')}
              style={styles.deleteButton}
              textStyle={styles.deleteButtonText}
            />
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EditAcademic;
