import {View, Text, ScrollView, TouchableOpacity, Alert} from 'react-native';
import React, {useEffect, useState} from 'react';
import styles from './styles';
import {useTranslation} from 'react-i18next';
import CustomDropDown from '../../Components/CustomDropDown';
import {PrimaryInput} from '../../Components/Input';
import CustomCheckbox from '../../Components/CustomCheckbox';
import {PrimaryButton} from '../../Components/CustomButton';
import {
  useGetClassAndSchedulesQuery,
  useGetPackgeTypesQuery,
  useGetSessionsTutorQuery,
  useGettMyExpertiseQuery,
  useAddRecreationalRateCardMutation,
} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';
import colors from '../../Utils/colors';
import {useNavigation} from '@react-navigation/native';
import {fp} from '../../Helper/ResponsiveDimensions';
import {useSelector} from 'react-redux';
import {Fonts} from '../../Utils/Fonts';
import {useTutorCommissions} from '../../Helper/useGetTutorCommission';
import HelperTextComponent from '../../Components/HelperTipComp';

const Recreational = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigation = useNavigation();

  const {rateCardLength} = useSelector(state => state.rateCardSlice);
  // States for selected sessions
  const [selectedOnlineSessions, setSelectedOnlineSessions] = useState([]);
  const [selectedF2FSessions, setSelectedF2FSessions] = useState([]);

  // Price states
  const [onlinePrice, setOnlinePrice] = useState('');
  const [faceToFacePrice, setFaceToFacePrice] = useState('');

  const [selectOpenSession, setSelectOpenSession] = useState([]);
  const [priceOpenSession, setPriceOpenSession] = useState('');

  const [subject, setSubject] = useState({id: 0, name: t('selectExpertise')});
  const [loading, setLoading] = useState(false);
  console.log('🚀 ~ Recreational ~ subject:', subject);

  const {data: classAndSchedulesData} = useGetClassAndSchedulesQuery();
  const {data: packageData} = useGetPackgeTypesQuery();
  const {data: sessionData} = useGetSessionsTutorQuery();
  const {data: MyExpertiseData, refetch: refetchExpertiseData} =
    useGettMyExpertiseQuery();
  console.log(
    '🚀 ~ Recreational ~ MyExpertiseData:',
    JSON.stringify(MyExpertiseData),
  );
  const [addRecreationalRateCard] = useAddRecreationalRateCardMutation();
  const [selectAddOptional, setSelectAddOptional] = useState(false);
  const [minStudents, setMinStudents] = useState('');
  const [maxStudents, setMaxStudents] = useState('');
  const [selectedPackeges, setSelectedPackeges] = useState([]);
  const [selectedPackageIds, setSelectedPackageIds] = useState([]);
  const [optionalSubject, setOptionalSubject] = useState('');
  const {recreationalOnline, recreationalFaceToFace} = useTutorCommissions();
  const [openHelperText, setOpenHelperText] = useState(false);
  const [openOnlineHelper, setOpenOnlineHelper] = useState(false);
  const [openPackageHelper, setOpenPackegeHelper] = useState(false);
  useEffect(() => {
    if (sessionData?.data?.rows) {
      // Initialize selected packages and sessions arrays if needed
    }
    if (packageData?.data?.rows) {
      setSelectedPackeges(packageData.data.rows.map(() => false));
    }
  }, [sessionData, packageData]);
  useEffect(() => {
    refetchExpertiseData();
  }, []);

  const togglePackage = index => {
    const newSelectedPackages = selectedPackeges.map((isSelected, i) =>
      i === index ? !isSelected : isSelected,
    );
    setSelectedPackeges(newSelectedPackages);

    const newSelectedIds = packageData?.data?.rows
      .filter((_, i) => newSelectedPackages[i])
      .map(pkg => pkg.id.toString());
    setSelectedPackageIds(newSelectedIds);
  };

  // Identify the data for Online and Face to Face from API
  const onlineData = classAndSchedulesData?.data?.find(item =>
    item.value.toLowerCase().includes('online'),
  );
  const faceToFaceData = classAndSchedulesData?.data?.find(item =>
    item.value.includes('faceToFace'),
  );
  const openSessionData = classAndSchedulesData?.data?.find(item =>
    item.value.toLowerCase().includes('open session'),
  );

  //is show min or max student

  const online_groupId = onlineData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'group',
  )?.id;
  const online_openSessionId = onlineData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'opensession',
  )?.id;

  const f2f_groupId = faceToFaceData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'group',
  )?.id;
  const f2f_openSessionId = faceToFaceData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'opensession',
  )?.id;

  const toggleOnlineSessionSelection = sessionId => {
    setSelectedOnlineSessions(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
  };

  const toggleF2FSessionSelection = sessionId => {
    setSelectedF2FSessions(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
  };

  const toggleOpenSessionSelection = sessionId => {
    setSelectOpenSession(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
  };

  const validateForm = () => {
    if (
      selectedOnlineSessions.some(
        id => id === online_groupId || id === online_openSessionId,
      ) ||
      selectedF2FSessions.some(
        id => id === f2f_groupId || id === f2f_openSessionId,
      )
    ) {
      if (!minStudents || !maxStudents) {
        showToast('error', t('please_fill_all_fields'), 'bottom', isRTL);
        return false;
      }
      if (isNaN(minStudents) || isNaN(maxStudents)) {
        showToast('error', t('min_and_max_students_numeric'), 'bottom', isRTL);
        return false;
      }
    }
    if (!subject.id) {
      showToast('error', t('please_select_expertise'), 'bottom', isRTL);
      return false;
    }

    const anyOnlineSelected = selectedOnlineSessions.length > 0;
    const anyF2FSelected = selectedF2FSessions.length > 0;
    const anyOpenSelected = selectOpenSession.length > 0;

    if (!anyOnlineSelected && !anyF2FSelected && !anyOpenSelected) {
      showToast(
        'error',
        t('please_select_at_least_one_session_type'),
        'bottom',
        isRTL,
      );
      return false;
    }

    if (anyOnlineSelected && (!onlinePrice || Number(onlinePrice) <= 0)) {
      showToast('error', t('please_enter_valid_online_price'), 'bottom', isRTL);
      return false;
    }

    if (anyF2FSelected && (!faceToFacePrice || Number(faceToFacePrice) <= 0)) {
      showToast(
        'error',
        t('please_enter_valid_face_to_face_price'),
        'bottom',
        isRTL,
      );
      return false;
    }
    if (
      anyOpenSelected &&
      (!priceOpenSession || Number(priceOpenSession) <= 0)
    ) {
      showToast('error', t('addPriceError'), 'bottom', isRTL);
      return false;
    }

    if (selectedPackageIds.length === 0) {
      showToast(
        'error',
        t('please_select_at_least_one_package'),
        'bottom',
        isRTL,
      );
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const anyOnlineSelected = selectedOnlineSessions.length > 0;
    const anyF2FSelected = selectedF2FSessions.length > 0;
    const anyOpenSelected = selectOpenSession.length > 0;

    const class_type_ids = [];
    if (anyOnlineSelected && onlineData?.id) {
      class_type_ids.push({
        class_type_id: onlineData.id.toString(),
        price: parseInt(onlinePrice, 10),
      });
    }
    if (anyF2FSelected && faceToFaceData?.id) {
      class_type_ids.push({
        class_type_id: faceToFaceData.id.toString(),
        price: parseInt(faceToFacePrice, 10),
      });
    }
    if (anyOpenSelected && openSessionData?.id) {
      class_type_ids.push({
        class_type_id: Number(openSessionData.id),
        price: parseFloat(priceOpenSession),
      });
    }

    const class_sessions = [
      ...selectedOnlineSessions.map(id => ({sessions_id: id.toString()})),
      ...selectedF2FSessions.map(id => ({sessions_id: id.toString()})),
      ...selectOpenSession.map(id => ({sessions_id: id.toString()})),
    ];

    const payload = {
      min_student_no: minStudents,
      max_student_no: maxStudents,
      expertise_id: subject.id.toString(),
      package_id: selectedPackageIds,
      class_sessions: class_sessions,
      class_type_ids: class_type_ids,
      other_expertise: optionalSubject,
    };
    console.log('🚀 ~ handleSubmit ~ payload:', payload);

    try {
      setLoading(true);
      await addRecreationalRateCard(payload).unwrap();
      showToast(
        'success',
        t('recreational_rate_card_added_successfully'),
        'bottom',
        isRTL,
      );
      if (rateCardLength > 0) {
        navigation.goBack();
      } else {
        navigation?.navigate('ScheduleTutor');
      }
    } catch (error) {
      showToast(
        'error',
        error?.data?.message || t('something_went_wrong'),
        'bottom',
        isRTL,
      );
    } finally {
      setLoading(false);
    }
  };

  const renderFaceToFaceSessions = () => {
    if (!faceToFaceData) return null;
    return (
      <View style={{alignItems: isRTL ? 'flex-end' : 'flex-start'}}>
        <View style={{flexDirection: 'row'}}>
          <Text style={styles.sectionTitle}>{faceToFaceData.name}</Text>
          <HelperTextComponent
            helperText={t('recreationalF2FHelpe')}
            setOpen={setOpenHelperText}
            open={openHelperText}
            borderColor={colors.black}
            iconColor={colors.black}
          />
        </View>
        <View
          style={[
            styles.checkboxView,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          {faceToFaceData.tlm_sessions_types.map((type, index) => (
            <View key={type.id} style={{}}>
              <CustomCheckbox
                label={type.name}
                isSelected={selectedF2FSessions.includes(type.id)}
                onSelect={() => toggleF2FSessionSelection(type.id)}
              />
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderOnlineSessions = () => {
    if (!onlineData) return null;
    return (
      <View
        style={{marginTop: 10, alignItems: isRTL ? 'flex-end' : 'flex-start'}}>
        <View style={{flexDirection: 'row'}}>
          <Text style={styles.sectionTitle}>{onlineData.name}</Text>
          <HelperTextComponent
            helperText={t('recreationalOnlineHelper')}
            setOpen={setOpenOnlineHelper}
            open={openOnlineHelper}
            borderColor={colors.black}
            iconColor={colors.black}
          />
        </View>
        <View
          style={[
            styles.checkboxView,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          {onlineData.tlm_sessions_types.map((type, index) => (
            <View key={type.id} style={{}}>
              <CustomCheckbox
                label={type.name}
                isSelected={selectedOnlineSessions.includes(type.id)}
                onSelect={() => toggleOnlineSessionSelection(type.id)}
              />
            </View>
          ))}
        </View>
      </View>
    );
  };
  const renderOpenSessions = () => {
    if (!openSessionData) return null;
    return (
      <View
        style={{marginTop: 20, alignItems: isRTL ? 'flex-end' : 'flex-start'}}>
        <Text style={styles.sectionTitle}>{openSessionData.name}</Text>
        <View
          style={[
            styles.checkboxView,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          {openSessionData.tlm_sessions_types.map((type, index) => (
            <View key={type.id} style={{}}>
              <CustomCheckbox
                label={type.name}
                isSelected={selectOpenSession.includes(type.id)}
                onSelect={() => toggleOpenSessionSelection(type.id)}
              />
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderPriceInputs = () => {
    const anyOnlineSelected = selectedOnlineSessions.length > 0;
    const anyF2FSelected = selectedF2FSessions.length > 0;
    const anyOpenSelected = selectOpenSession.length > 0;

    return (
      <View style={{marginTop: 20}}>
        {anyOnlineSelected && (
          <PrimaryInput
            title={
              recreationalOnline?.rate > 0
                ? `${t('price_for_online')} (${t('commission_applied')} ${
                    recreationalOnline?.rate
                  }%)`
                : t('price_for_online')
            }
            keyboardType="numeric"
            containerStyle={{width: '100%'}}
            placeholder={t('150 QAR')}
            maxLength={30}
            value={onlinePrice}
            onChangeText={text =>
              setOnlinePrice(text.replace(/[^0-9]/g, '').substring(0, 6))
            }
            showTooltip
            helperTxt={t('priceForOnlineRecreational')}
            lableStyle={{marginRight: 8}}
          />
        )}

        {anyF2FSelected && (
          <PrimaryInput
            title={
              recreationalFaceToFace?.rate > 0
                ? `${t('price_for_face_to_face')} (${t('commission_applied')} ${
                    recreationalFaceToFace?.rate
                  }%)`
                : t('price_for_face_to_face')
            }
            keyboardType="numeric"
            containerStyle={{width: '100%'}}
            placeholder={t('180 QAR')}
            maxLength={30}
            value={faceToFacePrice}
            onChangeText={text =>
              setFaceToFacePrice(text.replace(/[^0-9]/g, '').substring(0, 6))
            }
            showTooltip
            helperTxt={t('priceForF2FHelperRecreational')}
            lableStyle={{marginRight: 5}}
          />
        )}
        {anyOpenSelected && (
          <PrimaryInput
            title={t('Price for Open Session')}
            keyboardType="numeric"
            containerStyle={{width: '100%'}}
            placeholder={t('120 QAR')}
            maxLength={30}
            value={priceOpenSession}
            onChangeText={text =>
              setPriceOpenSession(text.replace(/[^0-9]/g, '').substring(0, 6))
            }
          />
        )}
        {(selectedOnlineSessions.some(
          id => id === online_groupId || id === online_openSessionId,
        ) ||
          selectedF2FSessions.some(
            id => id === f2f_groupId || id === f2f_openSessionId,
          )) && (
          <View>
            <PrimaryInput
              title={t('minStudent')}
              placeholder={t('minStudent')}
              value={minStudents}
              lableStyle={styles.sessionModeTitle}
              // onChangeText={text => setMinStudents(text.replace(/[^0-9]/g, ''))}
              onChangeText={text => {
                const value = text.replace(/[^0-9]/g, '');
                if (
                  value &&
                  parseInt(value) > 1 &&
                  (!maxStudents || parseInt(value) < parseInt(maxStudents))
                ) {
                  setMinStudents(value);
                } else if (!value) {
                  setMinStudents('');
                }
              }}
              keyboardType="numeric"
              showTooltip
              helperTxt={t('minStudentRecreational')}
            />
            <PrimaryInput
              title={t('maxStudent')}
              placeholder={t('maxStudent')}
              lableStyle={styles.sessionModeTitle}
              value={maxStudents}
              // onChangeText={text => setMaxStudents(text.replace(/[^0-9]/g, ''))}
              onChangeText={text => {
                const value = text.replace(/[^0-9]/g, '');
                setMaxStudents(value);
                // if (value && parseInt(value) > parseInt(minStudents)) {
                //   setMaxStudents(value);
                // } else if (!value) {
                //   setMaxStudents('');
                // }
              }}
              keyboardType="numeric"
              showTooltip
              helperTxt={t('maxStudentRecreational')}
            />
          </View>
        )}
      </View>
    );
  };

  const expertiseOptions =
    MyExpertiseData?.data?.rows?.map(item => ({
      id: item.id,
      name: item.name,
      is_extra: item?.is_extra,
    })) || [];

  return (
    <ScrollView showsVerticalScrollIndicator={false} style={{}}>
      <View style={{marginVertical: 10}}>
        <Text
          style={[styles.sectionTitle, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('add_expertise')} ({t('rate_per_hour')})
        </Text>
      </View>
      <View>
        {MyExpertiseData && (
          <CustomDropDown
            data={expertiseOptions}
            lable={t('select_expertise')}
            defaultValue={subject}
            onSelect={selected => setSubject(selected)}
            labelStyle={styles.sectionTitle}
            backgroundColor="#fff"
            height={50}
            radius={12}
            borderWidth={1}
            borderColor="#d3d3d3"
            showTooltip
            helperTxt={t('recreationalSubjectHelper')}
            lableStyle={{marginRight: 8}}
          />
        )}
      </View>
      {/* <PrimaryButton
        title={`+ ${t('add_optional')}`}
        style={[styles.editBtn, {alignSelf: isRTL ? 'flex-end' : 'flex-start'}]}
        textStyle={{
          fontSize: fp(1.8),
          fontFamily: Fonts.semiBold,
          color: colors.themeColor,
        }}
        onPress={() => setSelectAddOptional(!selectAddOptional)}
      /> */}

      {subject?.is_extra && (
        <PrimaryInput
          title={t('other_expertise')}
          placeholder={t('enter_other_expertise')}
          value={optionalSubject}
          lableStyle={styles.sessionModeTitle}
          onChangeText={setOptionalSubject}
        />
      )}
      {/* <View style={styles.sectionContainer}>
        <Text
          style={[styles.sectionTitle, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('select_session')}
        </Text>
      </View> */}

      {/* Render both Online and Face to Face sessions */}
      {renderFaceToFaceSessions()}
      {renderOnlineSessions()}
      {renderOpenSessions()}
      {renderPriceInputs()}

      <View
        style={{
          // marginVertical: 10,
          alignItems: isRTL ? 'flex-end' : 'flex-start',
        }}>
        <View style={{flexDirection: isRTL ? 'row-reverse' : 'row'}}>
          <Text style={styles.sectionTitle}>{t('packageType')}</Text>
          <HelperTextComponent
            helperText={t('packageTypeRecreationalHelper')}
            setOpen={setOpenPackegeHelper}
            open={openPackageHelper}
            borderColor={colors.black}
            iconColor={colors.black}
          />
        </View>
        <View style={{marginTop: 10}}>
          {packageData?.data?.rows.map((checkbox, index) => (
            <CustomCheckbox
              key={index}
              label={checkbox.name}
              isSelected={selectedPackeges[index]}
              onSelect={() => togglePackage(index)}
            />
          ))}
        </View>
      </View>

      <View style={{marginBottom: fp(1.7)}}>
        <PrimaryButton
          loading={loading}
          title={t('save')}
          onPress={handleSubmit}
          style={{width: '100%'}}
        />
      </View>
    </ScrollView>
  );
};

export default Recreational;
