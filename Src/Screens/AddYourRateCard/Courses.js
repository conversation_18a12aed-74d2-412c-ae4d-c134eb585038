// Courses.js

import {View, Text, ScrollView, TouchableOpacity} from 'react-native';
import React, {useEffect, useState} from 'react';
import styles from './styles';
import {PrimaryInput} from '../../Components/Input';
import {useTranslation} from 'react-i18next';
import CustomDropDown from '../../Components/CustomDropDown';
import {SCREEN_HEIGHT} from '../../Utils/constant';
import {PrimaryButton} from '../../Components/CustomButton';
import {
  useCourseSubCategoriesQuery,
  useGetClassAndSchedulesQuery,
  useAddCourseRateCardMutation,
  useGetCourseLevelsQuery,
} from '../../Api/ApiSlice';
import CustomCheckbox from '../../Components/CustomCheckbox';
import colors from '../../Utils/colors';
import {showToast} from '../../Components/ToastHelper';
import {useNavigation} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {useTutorCommissions} from '../../Helper/useGetTutorCommission';
import HelperTextComponent from '../../Components/HelperTipComp';

const Courses = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigation = useNavigation();
  const {rateCardLength} = useSelector(state => state.rateCardSlice);
  const [courseTitle, setCourseTitle] = useState('');
  const [description, setDescription] = useState('');
  const [duration, setDuration] = useState('');
  const [selectLevel, setSelectLevel] = useState('');

  const [onlinePrice, setOnlinePrice] = useState('');
  const [faceToFacePrice, setFaceToFacePrice] = useState('');

  const [selectedOnlineSessions, setSelectedOnlineSessions] = useState([]);
  const [selectedF2FSessions, setSelectedF2FSessions] = useState([]);

  const [selectedCategory, setSelectedCategory] = useState(null);
  const [loading, setLoading] = useState(false);
  const [courseLevels, setCourseLevels] = useState(null);
  const [selectOpenSession, setSelectOpenSession] = useState([]);
  const [priceOpenSession, setPriceOpenSession] = useState('');
  const [optionalSubject, setOptionalSubject] = useState('');

  const [addCourseRateCard] = useAddCourseRateCardMutation();
  const {data: classAndSchedulesData} = useGetClassAndSchedulesQuery();
  const {data: CourseSubCategorData} = useCourseSubCategoriesQuery(2);
  const {data: courseLevelData} = useGetCourseLevelsQuery();
  const [openHelperText, setOpenHelperText] = useState(false);
  const [openOnlineHelper, setOpenOnlineHelper] = useState(false);

  const extractCourseLevels = data => {
    if (data && Array.isArray(data.data)) {
      setCourseLevels(data.data);
    } else {
      console.error('Invalid courseLevelData format:', data);
    }
  };

  useEffect(() => {
    extractCourseLevels(courseLevelData);
  }, [courseLevelData]);

  const onlineData = classAndSchedulesData?.data?.find(item =>
    item.value.toLowerCase().includes('online'),
  );
  const faceToFaceData = classAndSchedulesData?.data?.find(item =>
    item.value.includes('faceToFace'),
  );
  const openSessionData = classAndSchedulesData?.data?.find(item =>
    item.value.toLowerCase().includes('opensession'),
  );

  //is show min or max student
  const [minStudents, setMinStudents] = useState('');
  const [maxStudents, setMaxStudents] = useState('');

  const online_groupId = onlineData?.tlm_sessions_types?.find(
    item => item.value.toLowerCase() === 'Group',
  )?.id;
  const online_openSessionId = onlineData?.tlm_sessions_types?.find(
    item => item.value.toLowerCase() === 'OpenSession',
  )?.id;

  const f2f_groupId = faceToFaceData?.tlm_sessions_types?.find(
    item => item.value.toLowerCase() === 'Group',
  )?.id;
  const f2f_openSessionId = faceToFaceData?.tlm_sessions_types?.find(
    item => item.value.toLowerCase() === 'OpenSession',
  )?.id;

  const toggleOnlineSessionSelection = sessionId => {
    setSelectedOnlineSessions(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
  };

  const toggleF2FSessionSelection = sessionId => {
    setSelectedF2FSessions(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
  };
  const toggleOpenSessionSelection = sessionId => {
    setSelectOpenSession(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
  };

  const validateInputs = () => {
    if (!courseTitle) return t('please_enter_the_course_title');
    if (!description) return t('please_enter_the_description');
    if (!duration || !/^\d{1,5}$/.test(duration)) {
      return t('course_duration_must_be_a_numeric_value_up_to_5_digits');
    }

    const anyOnlineSelected = selectedOnlineSessions.length > 0;
    const anyF2FSelected = selectedF2FSessions.length > 0;
    const anyOpenSelected = selectOpenSession.length > 0;

    if (!anyOnlineSelected && !anyF2FSelected && !anyOpenSelected) {
      return t('please_select_at_least_one_session');
    }

    if (anyOnlineSelected && (!onlinePrice || !/^\d+$/.test(onlinePrice))) {
      return t('please_enter_a_valid_price_for_online_sessions');
    }

    if (
      anyF2FSelected &&
      (!faceToFacePrice || !/^\d+$/.test(faceToFacePrice))
    ) {
      return t('please_enter_a_valid_price_for_face_to_face_sessions');
    }
    if (anyOpenSelected && !priceOpenSession) {
      showToast('error', t('addPriceError'), 'bottom', isRTL);
      return false;
    }

    return null;
  };

  const handleSubmit = async () => {
    const errorMessage = validateInputs();
    if (errorMessage) {
      showToast('error', errorMessage, 'bottom', isRTL);
      return;
    }

    const anyOnlineSelected = selectedOnlineSessions.length > 0;
    const anyF2FSelected = selectedF2FSessions.length > 0;

    const class_type_ids = [];
    const onlineClassTypeId = onlineData?.id;
    const f2fClassTypeId = faceToFaceData?.id;

    if (anyOnlineSelected && onlineClassTypeId) {
      class_type_ids.push({
        class_type_id: onlineClassTypeId.toString(),
        price: parseInt(onlinePrice),
      });
    }

    if (anyF2FSelected && f2fClassTypeId) {
      class_type_ids.push({
        class_type_id: f2fClassTypeId.toString(),
        price: parseInt(faceToFacePrice),
      });
    }
    if (selectOpenSession.length > 0 && openSessionData?.id) {
      class_type_ids.push({
        class_type_id: Number(openSessionData.id),
        price: parseFloat(priceOpenSession),
      });
    }

    const class_sessions = [
      ...selectedOnlineSessions.map(id => ({sessions_id: id.toString()})),
      ...selectedF2FSessions.map(id => ({sessions_id: id.toString()})),
      ...selectOpenSession.map(id => ({sessions_id: id})),
    ];

    const payload = {
      course_title: courseTitle,
      description,
      course_duration: duration,
      course_category_id: selectedCategory?.id?.toString(),
      // course_level_id: selectLevel.id?.toString(),
      class_sessions,
      class_type_ids,
      other_category: optionalSubject,
      min_student_no: minStudents,
      max_student_no: maxStudents,
    };

    try {
      setLoading(true);
      await addCourseRateCard(payload).unwrap();
      showToast('success', t('course_added_successfully'), 'bottom', isRTL);
      if (rateCardLength > 0) {
        navigation.goBack();
      } else {
        navigation?.navigate('ScheduleTutor');
      }
      setCourseTitle('');
      setDescription('');
      setDuration('');
      setSelectedCategory(null);
      setSelectLevel('');
      setSelectedOnlineSessions([]);
      setSelectedF2FSessions([]);
      setSelectOpenSession([]);
      setOnlinePrice('');
      setFaceToFacePrice('');
      setPriceOpenSession('');
    } catch (error) {
      showToast(
        'error',
        error?.data?.message || t('something_went_wrong'),
        'bottom',
        isRTL,
      );
    } finally {
      setLoading(false);
    }
  };
  const {coursesOnline, coursesFaceToFace} = useTutorCommissions();
  return (
    <ScrollView showsVerticalScrollIndicator={false} style={{marginTop: hp(1)}}>
      <View style={{}}>
        <PrimaryInput
          title={t('course_title')}
          keyboardType="default"
          containerStyle={{width: '100%'}}
          placeholder={t('enter_course_title')}
          maxLength={50}
          lableStyle={styles.sessionModeTitle}
          value={courseTitle}
          onChangeText={setCourseTitle}
          showTooltip
          helperTxt={t('courseTitleHelper')}

          // inputMarginBottom={0}
        />
        <View>
          <PrimaryInput
            title={`${t('description')}`}
            keyboardType="default"
            containerStyle={{width: '100%'}}
            placeholder={t('enter_description')}
            maxLength={500}
            value={description}
            lableStyle={styles.sessionModeTitle}
            onChangeText={setDescription}
            textInputStyle={{
              // height: SCREEN_HEIGHT * 0.13,asdfasdfa
              textAlignVertical: 'top',
              lineHeight: hp(2.6),
            }}
            multiline={true}
            showTooltip
            helperTxt={t('discriptionHelper')}
          />
          <Text
            style={{
              textAlign: isRTL ? 'left' : 'right',
              color: 'grey',
              fontSize: fp(1.2),
              marginTop: -hp(1.3),
              fontFamily: Fonts.medium,
            }}>
            {description.length}/500
          </Text>
        </View>

        <PrimaryInput
          // title={`${t('course_duration')} (In hours)`}
          title={`${t('course_duration')}`}
          keyboardType="numeric"
          containerStyle={{width: '100%'}}
          placeholder={`${t('enter_course_duration')} in hours`}
          maxLength={5}
          lableStyle={styles.sessionModeTitle}
          value={duration}
          onChangeText={text => setDuration(text.replace(/[^0-9]/g, ''))}
          inputMarginBottom={0}
          showTooltip
          helperTxt={t('courseDureation')}
        />
      </View>

      <CustomDropDown
        data={CourseSubCategorData?.data?.rows}
        lable={t('select_category')}
        backgroundColor="white"
        defaultValue={{id: 0, name: t('select_category')}}
        onSelect={setSelectedCategory}
        height={50}
        radius={12}
        borderWidth={1}
        borderColor={'grey'}
        showTooltip
        helperTxt={t('selectCategory')}
        lableStyle={{marginRight: 8}}
        // marginVertical={0}
      />

      {selectedCategory?.is_extra && (
        <View style={{marginTop: hp(2)}}>
          <PrimaryInput
            title={t('optional_category')}
            placeholder={t('enter_category')}
            value={optionalSubject}
            lableStyle={styles.sessionModeTitle}
            onChangeText={setOptionalSubject}
            inputMarginBottom={0}
          />
        </View>
      )}
      {/* <View style={{marginTop: hp(2)}}>
        <CustomDropDown
          data={courseLevels}
          lable={t('select_level')}
          backgroundColor="white"
          defaultValue={{id: 0, name: t('select_level')}}
          onSelect={setSelectLevel}
          height={50}
          radius={12}
          borderWidth={1}
          borderColor={'grey'}
          marginVertical={0}
        />
      </View> */}

      {/* 
      <View
        style={[
          styles.sectionContainer,
          {alignItems: isRTL ? 'flex-end' : 'flex-start'},
        ]}>
        <Text style={[styles.sectionTitle, {marginVertical: 10}]}>
          {t('select_session')}
        </Text>
      </View> */}

      {faceToFaceData && (
        <View
          style={{
            marginTop: hp(1.4),
            alignItems: isRTL ? 'flex-end' : 'flex-start',
          }}>
          <View style={{flexDirection: 'row'}}>
            <Text style={styles.sectionTitle}>{faceToFaceData.name}</Text>
            <HelperTextComponent
              helperText={t('coursesF2FHelper')}
              setOpen={setOpenHelperText}
              open={openHelperText}
              borderColor={colors.black}
              iconColor={colors.black}
            />
          </View>
          <View
            style={[
              styles.checkboxView,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            {faceToFaceData.tlm_sessions_types.map((type, index) => (
              <View
                key={type.id}
                style={{marginLeft: index % 2 === 1 ? 24 : 0}}>
                <CustomCheckbox
                  label={type.name}
                  isSelected={selectedF2FSessions.includes(type.id)}
                  onSelect={() => toggleF2FSessionSelection(type.id)}
                />
              </View>
            ))}
          </View>
        </View>
      )}

      {onlineData && (
        <View
          style={{
            marginTop: 10,
            alignItems: isRTL ? 'flex-end' : 'flex-start',
          }}>
          <View style={{flexDirection: isRTL ? 'row-reverse' : 'row'}}>
            <Text
              style={[
                styles.sessionModeTitle,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {onlineData.name}
            </Text>
            <HelperTextComponent
              helperText={t('coursesOnlineHelper')}
              setOpen={setOpenOnlineHelper}
              open={openOnlineHelper}
              borderColor={colors.black}
              iconColor={colors.black}
            />
          </View>
          <View
            style={[
              styles.checkboxView,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            {onlineData.tlm_sessions_types.map((type, index) => (
              <View
                key={type.id}
                style={{marginLeft: index % 2 === 1 ? 24 : 0}}>
                <CustomCheckbox
                  label={type.name}
                  isSelected={selectedOnlineSessions.includes(type.id)}
                  onSelect={() => toggleOnlineSessionSelection(type.id)}
                />
              </View>
            ))}
          </View>
        </View>
      )}
      {/* Open Sessions */}
      {/* {openSessionData && (
        <View
          style={{
            marginTop: 20,
            alignItems: isRTL ? 'flex-end' : 'flex-start',
          }}>
          <Text style={styles.sessionModeTitle}>{openSessionData.name}</Text>
          <View
            style={[
              styles.checkboxView,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            {openSessionData?.tlm_sessions_types.map((type, index) => (
              <View
                key={type.id}
                style={{
                  marginLeft: index % 2 === 1 ? 24 : 0,
                  marginBottom: 10,
                }}>
                <CustomCheckbox
                  label={type.name}
                  isSelected={selectOpenSession.includes(type.id)}
                  onSelect={() => toggleOpenSessionSelection(type.id)}
                />
              </View>
            ))}
          </View>
        </View>
      )} */}

      {selectedOnlineSessions.length > 0 && (
        <PrimaryInput
          title={
            coursesOnline?.rate > 0
              ? `${t('price_for_online')} (${t('commission_applied')} ${
                  coursesOnline?.rate
                }%)`
              : t('price_for_online')
          }
          keyboardType="numeric"
          containerStyle={{width: '100%'}}
          placeholder={t('150 QAR')}
          value={onlinePrice}
          onChangeText={value => {
            const filteredValue = value.replace(/[^0-9]/g, '').substring(0, 6);
            setOnlinePrice(filteredValue);
          }}
          showTooltip
          helperTxt={t('coursesPriceOnline')}
          lableStyle={{marginRight: 8}}
        />
      )}

      {selectedF2FSessions.length > 0 && (
        <PrimaryInput
          title={
            coursesFaceToFace?.rate > 0
              ? `${t('price_for_face_to_face')} (${t('commission_applied')}${
                  coursesFaceToFace?.rate
                }%)`
              : t('price_for_face_to_face')
          }
          keyboardType="numeric"
          containerStyle={{width: '100%'}}
          placeholder={t('180 QAR')}
          value={faceToFacePrice}
          onChangeText={value => {
            const filteredValue = value?.replace(/[^0-9]/g, '').substring(0, 6);
            setFaceToFacePrice(filteredValue);
          }}
          showTooltip
          helperTxt={t('coursesPriceF2F')}
          lableStyle={{marginRight: 8}}
        />
      )}

      {[
        ...selectedOnlineSessions,
        ...selectedF2FSessions,
        ...selectOpenSession,
      ].some(session =>
        [online_groupId, online_openSessionId, f2f_groupId, f2f_openSessionId]
          .filter(Boolean) // Remove undefined values
          .includes(Number(session)),
      ) && (
        <View>
          <PrimaryInput
            title={t('minStudent')}
            placeholder={t('minStudent')}
            value={minStudents}
            lableStyle={styles.sessionModeTitle}
            onChangeText={text => {
              const value = text.replace(/[^0-9]/g, '');
              if (
                value &&
                parseInt(value) > 1 &&
                (!maxStudents || parseInt(value) < parseInt(maxStudents))
              ) {
                setMinStudents(value);
              } else if (!value) {
                setMinStudents('');
              }
            }}
            keyboardType="numeric"
            showTooltip
            helperTxt={t('coursesMinStudentHelper')}
          />
          <PrimaryInput
            title={t('maxStudent')}
            placeholder={t('maxStudent')}
            lableStyle={styles.sessionModeTitle}
            value={maxStudents}
            onChangeText={text => {
              const value = text.replace(/[^0-9]/g, '');
              setMaxStudents(value);
            }}
            keyboardType="numeric"
            showTooltip
            helperTxt={t('coursesMaxStudentHelper')}
          />
        </View>
      )}

      <View style={{marginBottom: 20, marginTop: 10}}>
        <PrimaryButton
          style={{width: '100%'}}
          title={t('save')}
          onPress={handleSubmit}
          loading={loading}
        />
      </View>
    </ScrollView>
  );
};

export default Courses;
