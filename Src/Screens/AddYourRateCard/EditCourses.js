import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {useRoute, useNavigation} from '@react-navigation/native';
import CustomDropDown from '../../Components/CustomDropDown';
import colors from '../../Utils/colors';
import CustomCheckbox from '../../Components/CustomCheckbox';
import {PrimaryInput} from '../../Components/Input';
import {PrimaryButton} from '../../Components/CustomButton';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import styles from './styles';
import {showToast} from '../../Components/ToastHelper';
import {
  useCourseSubCategoriesQuery,
  useGetClassAndSchedulesQuery,
  useDeleteCourseRateCardMutation,
  useGetCourseRateCardEditDataQuery,
  useEditCourseRateCardMutation,
  useGetCourseLevelsQuery,
} from '../../Api/ApiSlice';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';

const EditCourses = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigation = useNavigation();
  const route = useRoute();
  const {courseCardId, cummissionOnline, cummissionF2F} = route.params || {
    courseCardId: null,
  };

  const [formData, setFormData] = useState({
    course_title: '',
    description: '',
    course_duration: '',
    course_category_id: '',
    course_level_id: '',
    selectedClassType: null,
    class_sessions: [],
    class_type_ids: [],
  });
  //is show min or max student
  const [minStudents, setMinStudents] = useState('');
  const [maxStudents, setMaxStudents] = useState('');
  const [classTypePrices, setClassTypePrices] = useState({});
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [courseLevels, setCourseLevels] = useState([]);
  const [selectedLevel, setSelectedLevel] = useState(null);

  const {data: courseSubCategories} = useCourseSubCategoriesQuery(2);
  const {data: classAndSchedules} = useGetClassAndSchedulesQuery();
  const {
    data: editData,
    isLoading,
    refetch,
  } = useGetCourseRateCardEditDataQuery(courseCardId);
  useEffect(() => {
    refetch();
  }, []);

  const [editCourseRateCard] = useEditCourseRateCardMutation();
  const [deleteCourseRateCard] = useDeleteCourseRateCardMutation();
  const {data: courseLevelData} = useGetCourseLevelsQuery();
  const [optionalSubject, setOptionalSubject] = useState('');

  const extractCourseLevels = data => {
    if (data && Array.isArray(data.data)) {
      setCourseLevels(data.data);
    } else {
      console.error('Invalid courseLevelData format:', data);
    }
  };

  const selectedClassTypeObj = classAndSchedules?.data?.find(
    type => type.name === formData.selectedClassType,
  );
  const onlineClassType = classAndSchedules?.data?.find(
    type => type.value.toLowerCase() === 'Online',
  );
  const f2fClassType = classAndSchedules?.data?.find(
    type => type.value === 'faceToFace',
  );
  useEffect(() => {
    extractCourseLevels(courseLevelData);
    if (editData?.data) {
      const data = editData.data;
      console.log('🚀 ~ useEffect ~ data:', data?.min_student_no);
      if (data.other_category) {
        setOptionalSubject(data?.other_category);
      }
      setFormData({
        course_title: data.course_title || '',
        description: data.description || '',
        course_duration: data.course_duration || '',
        course_category_id: data.course_category_id?.toString() || '',
        course_level_id: data.course_level_id?.toString() || '',
        optional_category: data?.optional_category,
        selectedClassType:
          data.tlm_tutor_class_types?.[0]?.tlm_class_type?.name || 'Online',
        class_sessions:
          data.tlm_tutor_class_sessions?.map(session => ({
            sessions_id: session.sessions_id.toString(),
          })) || [],
        class_type_ids:
          data.tlm_tutor_class_types?.map(type => ({
            class_type_id: type.tlm_class_type.id.toString(),
            price: type.price,
          })) || [],
      });

      setSelectedCategory({
        id: data.course_category_id,
        name: data.tlm_course_category?.name || '',
        is_extra: data.tlm_course_category?.is_extra || false,
      });

      setSelectedLevel({
        id: data.course_level_id,
        name: data.tlm_course_level?.name || '',
      });
      setMinStudents(data.min_student_no?.toString() || '');
      setMaxStudents(data.max_student_no?.toString() || '');
      const prices = {};
      data.tlm_tutor_class_types?.forEach(type => {
        prices[type.tlm_class_type.id] = type.price;
      });
      setClassTypePrices(prices);
    }
  }, [editData]);

  const online_groupId = onlineClassType?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'Group',
  )?.id;
  const online_openSessionId = onlineClassType?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'OpenSession',
  )?.id;
  const f2f_groupId = f2fClassType?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'Group',
  )?.id;
  const f2f_openSessionId = f2fClassType?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'OpenSession',
  )?.id;

  const handleSubmit = async () => {
    try {
      const payload = {
        course_title: formData.course_title,
        description: formData.description,
        course_duration: formData.course_duration,
        course_category_id: formData.course_category_id,
        course_level_id: formData.course_level_id,
        class_sessions: formData.class_sessions,
        class_type_ids: Object.entries(classTypePrices).map(([id, price]) => ({
          class_type_id: id,
          price: parseFloat(price),
        })),
        other_category: optionalSubject,
        min_student_no: minStudents,
        max_student_no: maxStudents,
      };
      console.log('🚀 ~ handleSubmit ~ payload:', payload);
      console.log('🚀 ~ handleSubmit ~ courseCardId:', courseCardId);
      await editCourseRateCard({id: courseCardId, data: payload}).unwrap();

      showToast('success', t('Course_Updated_Successfully'), 'bottom', isRTL);

      navigation.goBack();
    } catch (error) {
      console.log('🚀 ~ handleSubmit ~ error:', error);
      showToast(
        'error',
        error?.data?.message || t('Failed_To_Update_Course'),
        'bottom',
        isRTL,
      );
    }
  };

  const handleDelete = async () => {
    Alert.alert(
      t('Confirm_Delete'),
      t('Are_you_sure_you_want_to_delete_this_course'),
      [
        {text: t('Cancel'), style: 'cancel'},
        {
          text: t('Delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteCourseRateCard(courseCardId).unwrap();
              showToast(
                'success',
                t('Course_Deleted_Successfully'),
                'bottom',
                isRTL,
              );
              navigation.goBack();
            } catch (error) {
              showToast(
                'error',
                error.message || t('Failed_To_Delete_Course'),
                'bottom',
                isRTL,
              );
            }
          },
        },
      ],
    );
  };

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}

      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('Edit_Course')} />

      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.container}
            keyboardShouldPersistTaps="handled">
            <PrimaryInput
              title={t('Course_Title')}
              keyboardType="default"
              containerStyle={{width: '100%', marginTop: 20}}
              placeholder={t('Enter_course_title')}
              maxLength={50}
              value={formData.course_title}
              onChangeText={text =>
                setFormData(prev => ({...prev, course_title: text}))
              }
            />

            <View>
              <PrimaryInput
                title={`${t('description')}`}
                keyboardType="default"
                containerStyle={{width: '100%'}}
                placeholder={t('enter_description')}
                maxLength={500}
                value={formData.description}
                lableStyle={styles.sessionModeTitle}
                onChangeText={text =>
                  setFormData(prev => ({...prev, description: text}))
                }
                textInputStyle={{
                  // height: SCREEN_HEIGHT * 0.13,asdfasdfa
                  textAlignVertical: 'top',
                  lineHeight: hp(3),
                }}
                multiline={true}
              />
              <Text
                style={{
                  textAlign: 'right',
                  color: 'grey',
                  fontSize: fp(1.2),
                  marginTop: -hp(1.3),
                  fontFamily: Fonts.medium,
                }}>
                {formData.description.length}/500
              </Text>
            </View>
            <PrimaryInput
              title={t('Course_Duration')}
              keyboardType="numeric"
              containerStyle={{width: '100%', marginTop: 20}}
              placeholder={t('Enter_course_duration')}
              maxLength={5}
              value={formData.course_duration}
              onChangeText={text =>
                setFormData(prev => ({...prev, course_duration: text}))
              }
              inputMarginBottom={0}
            />

            <CustomDropDown
              data={courseSubCategories?.data?.rows || []}
              lable={t('Select_Category')}
              labelStyle={styles.dropdownLabel}
              backgroundColor="white"
              defaultValue={selectedCategory?.name}
              height={50}
              onSelect={item => {
                setSelectedCategory(item);
                setFormData(prev => ({
                  ...prev,
                  course_category_id: item.id.toString(),
                }));
              }}
              containerStyle={{marginTop: 20}}
              radius={12}
              borderWidth={1}
              borderColor={'grey'}
            />
            {selectedCategory?.is_extra && (
              <View style={{marginTop: hp(2)}}>
                <PrimaryInput
                  title={t('optional_category')}
                  placeholder={t('enter_category')}
                  value={optionalSubject}
                  lableStyle={styles.sessionModeTitle}
                  onChangeText={setOptionalSubject}
                  inputMarginBottom={0}
                />
              </View>
            )}
            {/* <CustomDropDown
          data={courseLevels}
          lable={t('Select_Level')}
          labelStyle={styles.dropdownLabel}
          backgroundColor="white"
          defaultValue={selectedLevel?.name || t('Select_Level')}
          height={50}
          onSelect={item => {
            setSelectedLevel(item);
            setFormData(prev => ({
              ...prev,
              course_level_id: item.id.toString(),
            }));
          }}
          containerStyle={{marginTop: 20}}
          radius={12}
          borderWidth={1}
          borderColor={'grey'}
        /> */}

            <View style={styles.sectionContainer}>
              <Text
                style={[
                  styles.sectionTitle,
                  {
                    marginVertical: 10,
                    textAlign: isRTL ? 'right' : 'left',
                  },
                ]}>
                {t('Select_Session_Type')}
              </Text>
              <View
                style={[
                  styles.row,
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                ]}>
                {classAndSchedules?.data?.map(type => (
                  <TouchableOpacity
                    key={type.id}
                    style={[
                      styles.topButton,
                      {
                        backgroundColor:
                          formData.selectedClassType === type.name
                            ? colors.themeColor
                            : colors.white,
                        borderWidth:
                          formData.selectedClassType === type.name ? 0 : 1,
                        borderColor: 'lightgrey',
                        marginTop: 10,
                      },
                    ]}
                    onPress={() => {
                      setFormData(prev => ({
                        ...prev,
                        selectedClassType: type.name,
                      }));
                    }}>
                    <Text
                      style={[
                        // styles.sessionButtonText,
                        {
                          color:
                            formData.selectedClassType === type.name
                              ? 'white'
                              : 'grey',
                          fontFamily: Fonts.medium,
                        },
                      ]}>
                      {type.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View
              style={[
                styles.checkboxView,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {classAndSchedules?.data
                ?.find(type => type.name === formData.selectedClassType)
                ?.tlm_sessions_types?.map(type => (
                  <View key={type.id} style={{marginLeft: 0, marginTop: 10}}>
                    <CustomCheckbox
                      label={type.name}
                      isSelected={formData.class_sessions.some(
                        session => session.sessions_id === type.id.toString(),
                      )}
                      onSelect={() => {
                        setFormData(prev => {
                          const exists = prev.class_sessions.some(
                            session =>
                              session.sessions_id === type.id.toString(),
                          );

                          const updatedSessions = exists
                            ? prev.class_sessions.filter(
                                session =>
                                  session.sessions_id !== type.id.toString(),
                              )
                            : [
                                ...prev.class_sessions,
                                {sessions_id: type.id.toString()},
                              ];

                          return {
                            ...prev,
                            class_sessions: updatedSessions,
                          };
                        });
                      }}
                    />
                  </View>
                ))}
            </View>

            {selectedClassTypeObj && (
              <PrimaryInput
                title={
                  cummissionOnline > 0 && selectedClassTypeObj.name == 'Online'
                    ? `${t('Price_for')} ${selectedClassTypeObj.name} (${t(
                        'commission_applied',
                      )} ${cummissionOnline}%)`
                    : cummissionF2F > 0 &&
                      selectedClassTypeObj.name == 'Face to Face'
                    ? `${t('Price_for')} ${selectedClassTypeObj.name} ( ${t(
                        'commission_applied',
                      )} ${cummissionF2F}%)`
                    : `${t('Price_for')} ${selectedClassTypeObj.name}`
                }
                keyboardType="numeric"
                containerStyle={{width: '100%', marginTop: 20}}
                placeholder={t('Enter_price')}
                value={
                  classTypePrices[selectedClassTypeObj.id]
                    ? parseFloat(
                        classTypePrices[selectedClassTypeObj.id],
                      ).toString()
                    : ''
                }
                onChangeText={text => {
                  setClassTypePrices(prev => ({
                    ...prev,
                    [selectedClassTypeObj.id]: text,
                  }));
                }}
              />
            )}

            {formData.class_sessions.some(session =>
              [
                online_groupId,
                online_openSessionId,
                f2f_groupId,
                f2f_openSessionId,
              ]
                .filter(Boolean) // Remove undefined values
                .includes(Number(session.sessions_id)),
            ) && (
              <View>
                <PrimaryInput
                  title={t('minStudent')}
                  placeholder={t('minStudent')}
                  value={minStudents}
                  lableStyle={styles.sessionModeTitle}
                  // onChangeText={text => setMinStudents(text.replace(/[^0-9]/g, ''))}
                  onChangeText={text => {
                    const value = text.replace(/[^0-9]/g, '');
                    if (
                      value &&
                      parseInt(value) > 1 &&
                      (!maxStudents || parseInt(value) < parseInt(maxStudents))
                    ) {
                      setMinStudents(value);
                    } else if (!value) {
                      setMinStudents('');
                    }
                  }}
                  keyboardType="numeric"
                />
                <PrimaryInput
                  title={t('maxStudent')}
                  placeholder={t('maxStudent')}
                  lableStyle={styles.sessionModeTitle}
                  value={maxStudents}
                  // onChangeText={text => setMaxStudents(text.replace(/[^0-9]/g, ''))}
                  onChangeText={text => {
                    const value = text.replace(/[^0-9]/g, '');
                    setMaxStudents(value);
                    // if (value && parseInt(value) > parseInt(minStudents)) {
                    //   setMaxStudents(value);
                    // } else if (!value) {
                    //   setMaxStudents('');
                    // }
                  }}
                  keyboardType="numeric"
                />
              </View>
            )}

            <View style={{marginBottom: hp(1)}}>
              <PrimaryButton
                loading={isLoading}
                onPress={handleSubmit}
                title={t('Save')}
                style={styles.saveButton}
              />
            </View>

            <View style={{marginBottom: hp(1)}}>
              <PrimaryButton
                loading={isLoading}
                onPress={handleDelete}
                title={t('Delete')}
                style={styles.deleteButton}
                textStyle={styles.deleteButtonText}
              />
            </View>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EditCourses;
