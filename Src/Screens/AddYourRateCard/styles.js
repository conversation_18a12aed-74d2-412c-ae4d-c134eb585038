import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp} from '../../Helper/ResponsiveDimensions';

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    paddingHorizontal: 10,
  },
  contentContainer: {
    backgroundColor: colors.white,
    flex: 1,
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
  },
  drowpdownLable: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  grade: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  topButton: {
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 0.7,
    borderColor: colors.txtGrey,
    marginHorizontal: 5,
    marginVertical: 10,
  },
  btnTxt: {
    fontSize: responsiveFontSize(12),
    fontFamily: Fonts.semiBold,
    color: colors.themeColor,
  },
  addDetails: {
    fontSize: responsiveFontSize(16),
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  row: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'center',
  },
  checkboxView: {
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  heading: {
    fontSize: fp(1.6),
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  editBtn: {
    backgroundColor: colors.white,
    width: '50%',
    borderWidth: 1,
    borderColor: colors.themeColor,
    marginVertical: 10,
  },
  deleteButton: {
    padding: 10,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: 'red',
    marginBottom: 30,
    width: '100%',
  },
  deleteButtonText: {
    fontSize: 16,
    fontFamily: Fonts.bold,
    color: 'red',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: colors.themeColor,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: fp(1.8),
    fontFamily: Fonts.bold,
    color: colors.red,
    textAlign: 'center',
    // marginBottom: 10,
  },
  retryText: {
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    color: colors.themeColor,
  },
  safeArea: {
    flex: 1,
    backgroundColor: colors.white,
  },
  section: {
    // marginBottom: 20,
  },
  sectionTitle: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
    marginRight: 8,
  },
  sessionModeTitle: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.black,
    marginRight: 8,
  },
  sectionContainer: {},
  saveButton: {
    width: '100%',
    marginBottom: hp(1),
  },
  drowpdownLable: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  btnStyle: {marginVertical: hp(1)},
});

export default styles;
