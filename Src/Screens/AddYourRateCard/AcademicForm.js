import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import styles from './styles';
import CustomDropDown from '../../Components/CustomDropDown';
import {useTranslation} from 'react-i18next';
import colors from '../../Utils/colors';
import CustomCheckbox from '../../Components/CustomCheckbox';
import {PrimaryInput} from '../../Components/Input';
import {PrimaryButton} from '../../Components/CustomButton';
import {showToast} from '../../Components/ToastHelper';
import {
  useAddAcademicRateCardMutation,
  useChooseYourGradeQuery,
  useGetSubjectsTutorQuery,
  useGetClassAndSchedulesQuery,
  useGetPackgeTypesQuery,
  useLazyChooseYourCurriculumsQuery,
} from '../../Api/ApiSlice';
import {useNavigation} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {hp} from '../../Helper/ResponsiveDimensions';
import {useTutorCommissions} from '../../Helper/useGetTutorCommission';
import HelperTextComponent from '../../Components/HelperTipComp';

const AcademicForm = () => {
  const {t, i18n} = useTranslation();
  const navigation = useNavigation();
  const isRTL = i18n.language === 'ar';
  const {rateCardLength} = useSelector(state => state.rateCardSlice);

  // State Variables
  const [curriculum, setCurriculum] = useState({id: 1, name: t('Select')});
  const [optionalSubject, setOptionalSubject] = useState('');
  const [activeBtn, setActiveBtn] = useState(1);
  const [subject, setSubject] = useState({id: null, name: t('selectSubject')});

  const [loading, setLoading] = useState(false);
  // Separate prices for both modes
  const [priceF2F, setPriceF2F] = useState('');
  const [priceOnline, setPriceOnline] = useState('');
  const [priceOpenSession, setPriceOpenSession] = useState('');

  // Separate states for selected sessions
  const [selectedOnlineSessions, setSelectedOnlineSessions] = useState([]);
  const [selectedF2FSessions, setSelectedF2FSessions] = useState([]);
  const [selectOpenSession, setSelectOpenSession] = useState([]);

  const [minStudents, setMinStudents] = useState('');
  const [maxStudents, setMaxStudents] = useState('');
  const [selectedPackages, setSelectedPackages] = useState([]);
  const [selectAddOptional, setSelectAddOptional] = useState(false);

  // New state for group session selection
  const [isGroupSelected, setIsGroupSelected] = useState(false);

  // API Hooks
  const [addAcademicRateCard] = useAddAcademicRateCardMutation();
  const [chooseYourCurriculum, {data: curriculumData}] =
    useLazyChooseYourCurriculumsQuery();

  useEffect(() => {
    chooseYourCurriculum({viewAll: true});
  }, []);

  const {academicOnline, academicFaceToFace} = useTutorCommissions();
  console.log('🚀 ~ AcademicForm ~ academicFaceToFace:', academicFaceToFace);
  console.log('🚀 ~ AcademicForm ~ academicOnline:', academicOnline);

  const {data: gradesData} = useChooseYourGradeQuery();
  const {data: subjectsData} = useGetSubjectsTutorQuery();
  const {data: classAndSchedulesData, refetch} = useGetClassAndSchedulesQuery();
  console.log(
    '🚀 ~ AcademicForm ~ classAndSchedulesData:',
    classAndSchedulesData,
  );
  const {data: packageTypesData} = useGetPackgeTypesQuery();
  const [openHelperText, setOpenHelperText] = useState(false);
  const [openOnlineHelper, setOpenOnlineHelper] = useState(false);
  const [openPackageHelper, setOpenPackegeHelper] = useState(false);
  useEffect(() => {
    refetch();
  }, []);

  // Find the appropriate modes from the API data.
  // Adjust these name checks if the API returns different naming.
  const onlineData = classAndSchedulesData?.data?.find(item =>
    item.value.toLowerCase().includes('online'),
  );
  console.log(
    '🚀 ~ AcademicForm ~ classAndSchedulesData:',
    JSON.stringify(classAndSchedulesData),
  );
  const faceToFaceData = classAndSchedulesData?.data?.find(item =>
    item.value.includes('faceToFace'),
  );
  console.log('🚀 ~ AcademicForm ~ faceToFaceData:', faceToFaceData);

  const online_groupId = onlineData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'group',
  )?.id;

  const online_openSessionId = onlineData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'opensession',
  )?.id;

  const f2f_groupId = faceToFaceData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'group',
  )?.id;

  const f2f_openSessionId = faceToFaceData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'opensession',
  )?.id;

  const openSessionData = classAndSchedulesData?.data?.find(item =>
    item.value.toLowerCase().includes('OpenSession'),
  );

  // Handlers for selecting sessions
  const toggleOnlineSessionSelection = sessionId => {
    setSelectedOnlineSessions(prev => {
      const groupId = onlineData?.tlm_sessions_types.find(
        item => item.value.toLowerCase() === 'group',
      ).id;
      const openSessionId = onlineData.tlm_sessions_types.find(
        item => item.value.toLowerCase() === 'opensession',
      ).id;

      // Toggle the sessionId in the selectedOnlineSessions array
      const newSelected = prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId) // Deselect the session
        : [...prev, sessionId]; // Select the session

      // Check if the newSelected array includes the groupId

      const isGroupSelected =
        newSelected.includes(groupId) || newSelected.includes(openSessionId);
      // Update the isGroupSelected state
      setIsGroupSelected(isGroupSelected);

      return newSelected;
    });
  };

  const toggleF2FSessionSelection = sessionId => {
    setSelectedF2FSessions(prev => {
      const groupId = faceToFaceData.tlm_sessions_types.find(
        item => item.value?.toLowerCase() === 'group',
      ).id;
      const openSessionId = faceToFaceData.tlm_sessions_types.find(
        item => item.value?.toLowerCase() === 'opensession',
      ).id;

      // Toggle the sessionId in the selectedF2FSessions array
      const newSelected = prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId) // Deselect the session
        : [...prev, sessionId]; // Select the session

      // Check if the newSelected array includes the groupId
      const isGroupSelected =
        newSelected.includes(groupId) || newSelected.includes(openSessionId);

      // Update the isGroupSelected state
      setIsGroupSelected(isGroupSelected);

      return newSelected;
    });
  };

  const toggleOpenSessionSelection = sessionId => {
    setSelectOpenSession(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
  };

  const togglePackageSelection = packageId => {
    setSelectedPackages(prev =>
      prev.includes(packageId)
        ? prev.filter(id => id !== packageId)
        : [...prev, packageId],
    );
  };

  const validateForm = () => {
    if (
      selectedOnlineSessions.some(
        id => id === online_groupId || id === online_openSessionId,
      ) ||
      selectedF2FSessions.some(
        id => id === f2f_groupId || id === f2f_openSessionId,
      )
    ) {
      if (!minStudents || !maxStudents) {
        showToast('error', t('please_fill_all_fields'), 'bottom', isRTL);
        return false;
      }
      if (isNaN(minStudents) || isNaN(maxStudents)) {
        showToast('error', t('min_and_max_students_numeric'), 'bottom', isRTL);
        return false;
      }
    }

    if (parseInt(minStudents) >= parseInt(maxStudents)) {
      showToast('error', t('maximumStudentError'), 'bottom', isRTL);

      return false;
    }

    const anyOnlineSelected = selectedOnlineSessions.length > 0;
    const anyF2FSelected = selectedF2FSessions.length > 0;
    const anyOpenSelected = selectOpenSession.length > 0;

    // Ensure at least one session is selected
    if (!anyOnlineSelected && !anyF2FSelected && !anyOpenSelected) {
      showToast(
        'error',
        t('please_select_at_least_one_session_type'),
        'bottom',
        isRTL,
      );
      return false;
    }

    // If online sessions selected, online price required
    if (anyOnlineSelected && !priceOnline) {
      showToast('error', t('provide_online_price'), 'bottom', isRTL);
      return false;
    }

    // If face-to-face sessions selected, face-to-face price required
    if (anyF2FSelected && !priceF2F) {
      showToast('error', t('provide_f2f_price'), 'bottom', isRTL);
      return false;
    }
    if (anyOpenSelected && !priceOpenSession) {
      showToast('error', t('addPriceError'), 'bottom', isRTL);
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    if (
      subject.name === 'Other' ||
      subject.name === 'Others' ||
      subject.name == t('other') ||
      subject.name == t('others')
    ) {
      if (optionalSubject == '') {
        showToast(
          'warning',
          t('Please fill optional subject'),
          'bottom',
          isRTL,
        );
      }
    }

    const classTypeIds = [];
    if (selectedOnlineSessions.length > 0 && onlineData?.id) {
      classTypeIds.push({
        class_type_id: Number(onlineData.id),
        price: parseFloat(priceOnline),
      });
    }

    if (selectedF2FSessions.length > 0 && faceToFaceData?.id) {
      classTypeIds.push({
        class_type_id: Number(faceToFaceData.id),
        price: parseFloat(priceF2F),
      });
    }
    if (selectOpenSession.length > 0 && openSessionData?.id) {
      classTypeIds.push({
        class_type_id: Number(openSessionData.id),
        price: parseFloat(priceOpenSession),
      });
    }

    const classSessions = [
      ...selectedOnlineSessions.map(id => ({sessions_id: id})),
      ...selectedF2FSessions.map(id => ({sessions_id: id})),
      ...selectOpenSession.map(id => ({sessions_id: id})),
    ];

    const payload = {
      min_student_no: minStudents,
      max_student_no: maxStudents,
      curriculum_id: curriculum.id,
      grades_id: activeBtn,
      subject_id: subject.id,
      package_id: selectedPackages.map(String),
      class_type_ids: classTypeIds,
      class_sessions: classSessions,
      optional_subject: optionalSubject,
    };
    console.log('🚀 ~ handleSubmit ~ payload:', payload);

    try {
      setLoading(true);
      await addAcademicRateCard(payload).unwrap();
      showToast(
        'success',
        t('academic_rate_card_added_successfully'),
        'bottom',
        isRTL,
      );
      if (rateCardLength > 0) {
        navigation.navigate('RateCardScreen');
      } else {
        navigation?.navigate('ScheduleTutor');
      }
      console.log(
        'Success:',
        'Academic rate card added successfully',
        'bottom',
        isRTL,
      );
    } catch (error) {
      showToast(
        'error',
        error?.data?.message || t('error_saving_rate_card'),
        'bottom',
        isRTL,
      );
      console.warn('error', error?.data);
    } finally {
      setLoading(false);
    }
  };
  const getObjectById = (array, id) => {
    return array?.find(item => item?.id === id) || null; // Returns null if not found
  };
  const getHelperText = type => {
    if (type == 'Individual') {
      return t('individualHelperTxt');
    } else if (type == 'Group') {
      return t('groupHelperTxt');
    } else {
      return t('publicHelperTxt');
    }
  };
  const getHelperTextPackage = name => {
    console.log('91389182', name);
    if (name == '1 Week Package') {
      return t('oneWeekPackageHelper');
    } else if (name == '2 Week Package') {
      return t('twoWeekPackageHelper');
    } else if (name == '3 Week Package') {
      return t('threeWeeksPackageHelper');
    } else {
      return t('oneMonthPackageHelper');
    }
    // return id;
  };
  console.log('subject name ', subject?.name);
  return (
    <ScrollView showsVerticalScrollIndicator={false} style={{}}>
      {/* Curriculum Dropdown */}
      <CustomDropDown
        lable={t('selectGrade')}
        data={gradesData?.data?.rows || []}
        backgroundColor={colors.txtGrey}
        defaultValue={activeBtn}
        height={40}
        onSelect={item => setActiveBtn(item.id)}
        showTooltip
        helperTxt={t('selectGradeHelper')}
        lableStyle={{marginRight: 8}}
      />
      {!getObjectById(gradesData?.data?.rows, activeBtn)
        ?.is_higher_education && (
        <CustomDropDown
          lable={t('selectCurriculum')}
          data={curriculumData?.data?.rows || []}
          backgroundColor={colors.txtGrey}
          defaultValue={curriculum}
          height={40}
          onSelect={selected => setCurriculum(selected)}
          showTooltip
          helperTxt={t('curriculamHelper')}
          lableStyle={{marginRight: 8}}
        />
      )}

      {/* Grade Selection */}
      {/* <View style={{marginVertical: 10}}>
        <Text style={styles.grade}>{t('selectGrade')}</Text>
        <FlatList
          data={gradesData?.data?.rows || []}
          // horizontal
          showsHorizontalScrollIndicator={false}
          style={{marginVertical: 10}}
          keyExtractor={item => item.id.toString()}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={() => setActiveBtn(item.id)}
              style={[
                styles.topButton,
                {
                  backgroundColor:
                    activeBtn === item.id ? colors.themeColor : colors.white,
                },
              ]}>
              <Text
                style={[
                  styles.btnTxt,
                  {
                    color:
                      activeBtn === item.id ? colors.white : colors.txtGrey1,
                  },
                ]}>
                {item.name}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View> */}

      {/* Session Selection */}
      <View
        style={[
          styles.sectionContainer,
          {alignItems: isRTL ? 'flex-end' : 'flex-start'},
        ]}>
        {/* <Text style={styles.sectionTitle}>{t('select_session')}</Text> */}

        {/* Face to Face Sessions */}
        {faceToFaceData && (
          <View
            style={{
              marginTop: 10,
              // alignItems: isRTL ? 'flex-end' : 'flex-start',
            }}>
            <View style={{flexDirection: isRTL ? 'row-reverse' : 'row'}}>
              <Text
                style={[
                  styles.sessionModeTitle,
                  {textAlign: isRTL ? 'right' : 'left'},
                ]}>
                {`${faceToFaceData.name}`}
              </Text>
              <HelperTextComponent
                helperText={t('f2fHelperTxt')}
                setOpen={setOpenHelperText}
                open={openHelperText}
                borderColor={colors.black}
                iconColor={colors.black}
              />
            </View>
            <View
              style={[
                styles.checkboxView,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {faceToFaceData.tlm_sessions_types.map((type, index) => (
                <View
                  key={type.id}
                  style={{
                    // marginLeft: index % 2 === 1 ? 24 : 0,
                    marginBottom: 10,
                  }}>
                  <CustomCheckbox
                    label={type.name}
                    isSelected={selectedF2FSessions.includes(type.id)}
                    onSelect={() => toggleF2FSessionSelection(type.id)}
                    showTooltip
                    helperTxt={getHelperText(type.name)}
                  />
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Online Sessions */}
        {onlineData && (
          <View style={{marginTop: 10}}>
            <View style={{flexDirection: isRTL ? 'row-reverse' : 'row'}}>
              <Text
                style={[
                  styles.sessionModeTitle,
                  {textAlign: isRTL ? 'right' : 'left'},
                ]}>
                {onlineData.name}
              </Text>
              <HelperTextComponent
                helperText={t('onlineHelper')}
                setOpen={setOpenOnlineHelper}
                open={openOnlineHelper}
                borderColor={colors.black}
                iconColor={colors.black}
              />
            </View>
            <View
              style={[
                styles.checkboxView,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {onlineData.tlm_sessions_types.map((type, index) => (
                <View
                  key={type.id}
                  style={{
                    // marginLeft: index % 2 === 1 ? 24 : 0,
                    marginBottom: 10,
                  }}>
                  <CustomCheckbox
                    label={type.name}
                    isSelected={selectedOnlineSessions.includes(type.id)}
                    onSelect={() => toggleOnlineSessionSelection(type.id)}
                    showTooltip
                    helperTxt={getHelperText(type.name)}
                  />
                </View>
              ))}
            </View>
          </View>
        )}
        {/* Open Sessions */}
        {openSessionData && (
          <View style={{marginTop: 20}}>
            <Text
              style={[
                styles.sessionModeTitle,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {openSessionData.name}
            </Text>
            <View
              style={[
                styles.checkboxView,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {openSessionData?.tlm_sessions_types.map((type, index) => (
                <View
                  key={type.id}
                  style={{
                    marginLeft: index % 2 === 1 ? 24 : 0,
                    marginBottom: 10,
                  }}>
                  <CustomCheckbox
                    label={type.name}
                    isSelected={selectOpenSession.includes(type.id)}
                    onSelect={() => toggleOpenSessionSelection(type.id)}
                  />
                </View>
              ))}
            </View>
          </View>
        )}
      </View>

      {/* Subject Dropdown */}
      {/* <CustomDropDown
       3
      /> */}
      <CustomDropDown
        lable={t('selectSubject')}
        data={subjectsData?.data?.rows}
        backgroundColor={colors.txtGrey}
        defaultValue={activeBtn}
        height={40}
        onSelect={selected => setSubject(selected)}
        showTooltip
        helperTxt={t('selectSubAcademicHelper')}
        lableStyle={{marginRight: 8}}
      />

      {/* Add Optional Subject */}
      {/* <PrimaryButton
        title={`+ ${t('add_optional')}`}
        style={[styles.editBtn, {alignSelf: isRTL ? 'flex-end' : 'flex-start'}]}
        textStyle={styles.btnTxt}
        onPress={() => setSelectAddOptional(!selectAddOptional)}
      /> */}

      {(subject.name === 'Other' ||
        subject.name === 'Others' ||
        subject.name == t('other') ||
        subject.name == t('others')) && (
        <View style={{marginTop: hp(1)}}>
          <PrimaryInput
            title={t('optional_subject')}
            placeholder={t('enter_optional_subject')}
            value={optionalSubject}
            lableStyle={styles.sessionModeTitle}
            onChangeText={setOptionalSubject}
          />
        </View>
      )}

      {/* Price Inputs */}
      {/* Show face to face price input if any F2F sessions selected */}
      {selectedF2FSessions.length > 0 && (
        <PrimaryInput
          title={
            academicFaceToFace?.rate > 0
              ? `${t('price_for_face_to_face')} (${t('commission_applied')} ${
                  academicFaceToFace?.rate
                }%)`
              : t('price_for_face_to_face')
          }
          // title={t('price_for_face_to_face')}
          placeholder={t('180 QAR')}
          value={priceF2F}
          lableStyle={styles.sessionModeTitle}
          onChangeText={text => setPriceF2F(text.replace(/[^0-9]/g, ''))}
          keyboardType="numeric"
          showTooltip
          helperTxt={t('priceF2fHelper')}
        />
      )}

      {/* Show online price input if any online sessions selected */}
      {selectedOnlineSessions.length > 0 && (
        <PrimaryInput
          title={
            academicOnline?.rate > 0
              ? `${t('price_for_online')} (${t('commission_applied')} ${
                  academicOnline?.rate
                }%)`
              : t('price_for_online')
          }
          placeholder={t('150 QAR')}
          value={priceOnline}
          lableStyle={styles.sessionModeTitle}
          onChangeText={text => setPriceOnline(text.replace(/[^0-9]/g, ''))}
          keyboardType="numeric"
          showTooltip
          helperTxt={t('priceOnlineHelper')}
        />
      )}
      {selectOpenSession.length > 0 && (
        <PrimaryInput
          title={t('price_for_open_session')}
          placeholder={t('120 QAR')}
          value={priceOpenSession}
          lableStyle={styles.sessionModeTitle}
          onChangeText={text =>
            setPriceOpenSession(text.replace(/[^0-9]/g, ''))
          }
          keyboardType="numeric"
        />
      )}

      {/* Student Number Inputs */}
      {(selectedOnlineSessions.some(
        id => id === online_groupId || id === online_openSessionId,
      ) ||
        selectedF2FSessions.some(
          id => id === f2f_groupId || id === f2f_openSessionId,
        )) && (
        <View>
          <PrimaryInput
            title={t('minStudent')}
            placeholder={t('minStudent')}
            value={minStudents}
            lableStyle={styles.sessionModeTitle}
            // onChangeText={text => setMinStudents(text.replace(/[^0-9]/g, ''))}
            onChangeText={text => {
              const value = text.replace(/[^0-9]/g, '');
              if (
                value &&
                parseInt(value) > 1 &&
                (!maxStudents || parseInt(value) < parseInt(maxStudents))
              ) {
                setMinStudents(value);
              } else if (!value) {
                setMinStudents('');
              }
            }}
            keyboardType="numeric"
            showTooltip
            helperTxt={t('minStudentHelper')}
          />
          <PrimaryInput
            title={t('maxStudent')}
            placeholder={t('maxStudent')}
            lableStyle={styles.sessionModeTitle}
            value={maxStudents}
            // onChangeText={text => setMaxStudents(text.replace(/[^0-9]/g, ''))}
            onChangeText={text => {
              const value = text.replace(/[^0-9]/g, '');
              setMaxStudents(value);
              // if (value && parseInt(value) > parseInt(minStudents)) {
              //   setMaxStudents(value);
              // } else if (!value) {
              //   setMaxStudents('');
              // }
            }}
            keyboardType="numeric"
            showTooltip
            helperTxt={t('maxStudentHelper')}
          />
        </View>
      )}

      {/* Package Types Selection */}
      <View style={{marginTop: 20}}>
        <View style={{flexDirection: isRTL ? 'row-reverse' : 'row'}}>
          <Text
            style={[
              styles.sectionTitle,
              {textAlign: isRTL ? 'right' : 'left'},
            ]}>
            {t('package_type')}
          </Text>
          <HelperTextComponent
            helperText={t('packageHelperTxt')}
            setOpen={setOpenPackegeHelper}
            open={openPackageHelper}
            borderColor={colors.black}
            iconColor={colors.black}
          />
        </View>
        {packageTypesData?.data?.rows.map(pkg => (
          <CustomCheckbox
            key={pkg.id}
            label={pkg.name}
            isSelected={selectedPackages.includes(pkg.id)}
            onSelect={() => togglePackageSelection(pkg.id)}
            showTooltip
            helperTxt={getHelperTextPackage(pkg.name)}
          />
        ))}
      </View>

      {/* Save Button */}
      <PrimaryButton
        loading={loading}
        onPress={handleSubmit}
        title={t('save')}
        style={{marginVertical: 20, width: '100%'}}
      />
    </ScrollView>
  );
};

export default AcademicForm;
