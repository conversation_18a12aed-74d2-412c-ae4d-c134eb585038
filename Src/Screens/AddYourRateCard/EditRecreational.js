import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  ScrollView,
  Platform,
  SafeAreaView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import CustomDropDown from '../../Components/CustomDropDown';
import colors from '../../Utils/colors';
import CustomCheckbox from '../../Components/CustomCheckbox';
import {PrimaryInput} from '../../Components/Input';
import {PrimaryButton} from '../../Components/CustomButton';
import {showToast} from '../../Components/ToastHelper';
import {useNavigation, useRoute} from '@react-navigation/native';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import styles from './styles';
import {
  useGetClassAndSchedulesQuery,
  useGetPackgeTypesQuery,
  useGettMyExpertiseQuery,
  useGetRecreationalRateCardEditDataQuery,
  useEditRecreationalRateCardMutation,
  useDeleteRecreationalRateCardMutation,
} from '../../Api/ApiSlice';
import {hp} from '../../Helper/ResponsiveDimensions';

const EditRecreational = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigation = useNavigation();
  const route = useRoute();
  const {rateCardId, cummissionOnline, cummissionF2F} = route.params || {
    rateCardId: null,
  };

  // API Hooks
  const {
    data: rateCardData,
    isLoading: isRateCardLoading,
    error: rateCardError,
    refetch,
  } = useGetRecreationalRateCardEditDataQuery(rateCardId, {
    skip: !rateCardId,
  });
  useEffect(() => {
    refetch();
  }, []);

  const {
    data: classAndSchedulesData,
    isLoading: isClassLoading,
    error: classError,
  } = useGetClassAndSchedulesQuery();

  const {
    data: packageTypesData,
    isLoading: isPackageLoading,
    error: packageError,
  } = useGetPackgeTypesQuery();

  const {data: MyExpertiseData} = useGettMyExpertiseQuery();
  console.log('🚀 ~ EditRecreational ~ MyExpertiseData:', MyExpertiseData);

  const [editRecreationalRateCard] = useEditRecreationalRateCardMutation();
  const [deleteRecreationalRateCard, {isLoading: isDeleting}] =
    useDeleteRecreationalRateCardMutation();

  // State management
  const [loading, setLoading] = useState(false);
  const [priceOnline, setPriceOnline] = useState('');
  const [priceFaceToFace, setPriceFaceToFace] = useState('');
  const [selectedOnlineSessions, setSelectedOnlineSessions] = useState([]);
  const [selectedF2FSessions, setSelectedF2FSessions] = useState([]);
  const [selectedPackages, setSelectedPackages] = useState([]);
  const [expertise, setExpertise] = useState(null);
  const [formErrors, setFormErrors] = useState({});
  const [priceOpenSession, setPriceOpenSession] = useState('');
  const [selectOpenSession, setSelectOpenSession] = useState([]);
  const [optionalSubject, setOptionalSubject] = useState('');

  // Identify online and face-to-face data
  const onlineData = classAndSchedulesData?.data?.find(item =>
    item.value.toLowerCase().includes('online'),
  );
  const faceToFaceData = classAndSchedulesData?.data?.find(item =>
    item.value.includes('faceToFace'),
  );
  const openSessionData = classAndSchedulesData?.data?.find(item =>
    item.value.toLowerCase().includes('opensession'),
  );

  //is show min or max student
  const [minStudents, setMinStudents] = useState('');
  const [maxStudents, setMaxStudents] = useState('');

  const online_groupId = onlineData?.tlm_sessions_types?.find(
    item => item?.value?.toLowerCase() === 'group',
  )?.id;
  const online_openSessionId = onlineData?.tlm_sessions_types?.find(
    item => item?.value?.toLowerCase() === 'opensession',
  )?.id;

  const f2f_groupId = faceToFaceData?.tlm_sessions_types?.find(
    item => item?.value?.toLowerCase() === 'group',
  )?.id;
  const f2f_openSessionId = faceToFaceData?.tlm_sessions_types?.find(
    item => item.value?.toLowerCase() === 'opensession',
  )?.id;

  // Pre-populate form data
  useEffect(() => {
    if (rateCardData?.data) {
      const data = rateCardData.data;
      console.log('🚀 ~ useEffect ~ data:', data);

      // Set expertise
      setExpertise(data.expertise_id);
      setMinStudents(data?.min_student_no?.toString());
      setMaxStudents(data?.max_student_no?.toString());
      if (data?.other_expertise) {
        setOptionalSubject(data?.other_expertise);
      }
      // Set prices from tlm_tutor_class_types
      const classTypes = data.tlm_tutor_class_types || [];
      classTypes.forEach(type => {
        if (type.tlm_class_type.id === 1) {
          setPriceOnline(parseFloat(type.price).toString());
        } else if (type.tlm_class_type.id === 2) {
          setPriceFaceToFace(parseFloat(type.price).toString());
        } else if (type.tlm_class_type.id === 3) {
          setPriceOpenSession(parseFloat(type.price).toString());
        }
      });

      // Set selected sessions
      const sessions = data.tlm_tutor_class_sessions || [];
      const onlineSessionIds =
        onlineData?.tlm_sessions_types.map(s => s.id) || [];
      const f2fSessionIds =
        faceToFaceData?.tlm_sessions_types.map(s => s.id) || [];
      const openSessoinIds =
        openSessionData?.tlm_sessions_types.map(s => s.id) || [];

      const onlineSessions = [];
      const f2fSessions = [];
      const openSessions = [];

      sessions.forEach(session => {
        const sessionId = parseInt(session.sessions_id);
        if (onlineSessionIds.includes(sessionId)) {
          onlineSessions.push(sessionId);
        } else if (f2fSessionIds.includes(sessionId)) {
          f2fSessions.push(sessionId);
        } else if (openSessoinIds.includes(sessionId)) {
          openSessions.push(sessionId);
        }
      });

      setSelectedOnlineSessions(onlineSessions);
      setSelectedF2FSessions(f2fSessions);
      setSelectOpenSession(openSessions);

      // Set packages
      const packages = (data.tlm_tutor_rate_card_packages || []).map(pkg =>
        parseInt(pkg.package_id),
      );
      setSelectedPackages(packages);
    }
  }, [rateCardData, onlineData, faceToFaceData, openSessionData]);

  const toggleOnlineSessionSelection = sessionId => {
    setSelectedOnlineSessions(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
    setFormErrors(prev => ({...prev, sessionTypes: null}));
  };

  const toggleF2FSessionSelection = sessionId => {
    setSelectedF2FSessions(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
    setFormErrors(prev => ({...prev, sessionTypes: null}));
  };
  const toggleOpenSessionSelection = sessionId => {
    setSelectOpenSession(prev =>
      prev.includes(sessionId)
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId],
    );
  };
  const togglePackageSelection = packageId => {
    setSelectedPackages(prev =>
      prev.includes(packageId)
        ? prev.filter(id => id !== packageId)
        : [...prev, packageId],
    );
    setFormErrors(prev => ({...prev, packages: null}));
  };

  const validateForm = () => {
    const errors = {};

    if (!expertise) {
      errors.expertise = t('expertise_is_required');
    }

    const anyOnlineSelected = selectedOnlineSessions.length > 0;
    const anyF2FSelected = selectedF2FSessions.length > 0;
    const anyOpenSelected = selectOpenSession.length > 0;

    if (!anyOnlineSelected && !anyF2FSelected && !anyOpenSelected) {
      errors.sessionTypes = t('at_least_one_session_type_is_required');
    }

    if (anyOnlineSelected && !priceOnline) {
      errors.priceOnline = t('online_price_is_required');
    }

    if (anyF2FSelected && !priceFaceToFace) {
      errors.priceFaceToFace = t('face_to_face_price_is_required');
    }
    if (anyOpenSelected && !priceOpenSession) {
      showToast('error', t('addPriceError'), 'bottom', isRTL);
      return false;
    }

    if (selectedPackages.length === 0) {
      errors.packages = t('at_least_one_package_is_required');
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      showToast(
        'error',
        t('please_fill_in_all_required_fields'),
        'bottom',
        isRTL,
      );
      return;
    }

    setLoading(true);
    try {
      const class_type_ids = [];
      if (selectedOnlineSessions.length > 0) {
        class_type_ids.push({
          class_type_id: '1',
          price: parseFloat(priceOnline),
        });
      }
      if (selectedF2FSessions.length > 0) {
        class_type_ids.push({
          class_type_id: '2',
          price: parseFloat(priceFaceToFace),
        });
      }
      if (selectOpenSession.length > 0) {
        class_type_ids.push({
          class_type_id: '3',
          price: parseFloat(priceOpenSession),
        });
      }
      const payload = {
        expertise_id: expertise?.toString(),
        package_id: selectedPackages.map(id => id.toString()),
        class_sessions: [
          ...selectedOnlineSessions.map(id => ({sessions_id: id.toString()})),
          ...selectedF2FSessions.map(id => ({sessions_id: id.toString()})),
          ...selectOpenSession.map(id => ({sessions_id: id.toString()})),
        ],
        class_type_ids,
        min_student_no: minStudents,
        max_student_no: maxStudents,
      };

      await editRecreationalRateCard({id: rateCardId, data: payload}).unwrap();

      showToast(
        'success',
        t('rate_card_updated_successfully'),
        'bottom',
        isRTL,
      );
      navigation.goBack();
    } catch (error) {
      showToast(
        'error',
        error?.data?.message || t('error_updating_rate_card'),
        'bottom',
        isRTL,
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      t('confirm_delete'),
      t('are_you_sure_you_want_to_delete_this_rate_card'),
      [
        {text: t('cancel'), style: 'cancel'},
        {text: t('delete'), style: 'destructive', onPress: confirmDelete},
      ],
      {cancelable: true},
    );
  };

  const confirmDelete = async () => {
    try {
      await deleteRecreationalRateCard(rateCardId).unwrap();
      showToast(
        'success',
        t('rate_card_deleted_successfully'),
        'bottom',
        isRTL,
      );
      navigation.goBack();
    } catch (error) {
      showToast(
        'error',
        error?.data?.message || t('error_deleting_rate_card'),
        'bottom',
        isRTL,
      );
    }
  };

  if (isRateCardLoading || isClassLoading || isPackageLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.themeColor} />
        <Text style={styles.loadingText}>{t('loading')}</Text>
      </View>
    );
  }

  if (rateCardError || classError || packageError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('error_loading_data')}</Text>
      </View>
    );
  }
  const othersId = MyExpertiseData?.data?.rows?.find(
    item => item?.is_extra,
  )?.id;

  return (
    <SafeAreaView style={styles.safeArea}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}

      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('edit_recreational_rate_card')}
      />

      <ScrollView showsVerticalScrollIndicator={false} style={styles.container}>
        <View style={styles.section}>
          <CustomDropDown
            lable={t('select_expertise')}
            data={
              MyExpertiseData?.data?.rows.map(item => ({
                id: item?.id,
                name: item?.name,
                is_extra: item?.is_extra,
              })) || []
            }
            labelStyle={styles.dropdownLabel}
            backgroundColor={colors.txtGrey}
            defaultValue={rateCardData?.data?.tlm_expertise?.name}
            value={expertise}
            height={40}
            onSelect={selectedItem => {
              setExpertise(selectedItem.id);
              setFormErrors(prev => ({...prev, expertise: null}));
            }}
          />
          {formErrors.expertise && (
            <Text style={styles.errorText}>{formErrors.expertise}</Text>
          )}
        </View>

        {expertise == othersId && (
          <View style={{marginTop: hp(1)}}>
            <PrimaryInput
              title={t('optional_subject')}
              placeholder={t('enter_optional_subject')}
              value={optionalSubject}
              lableStyle={styles.sessionModeTitle}
              onChangeText={setOptionalSubject}
            />
          </View>
        )}
        {faceToFaceData && (
          <View
            style={{
              alignItems: isRTL ? 'flex-end' : 'flex-start',
            }}>
            <Text style={styles.sectionTitle}>{faceToFaceData.name}</Text>
            <View
              style={[
                styles.checkboxView,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {faceToFaceData.tlm_sessions_types.map((type, index) => (
                <View
                  key={type.id}
                  style={[
                    styles.checkboxContainer,
                    index % 2 === 1 && {marginLeft: 24},
                  ]}>
                  <CustomCheckbox
                    label={type.name}
                    isSelected={selectedF2FSessions.includes(type.id)}
                    onSelect={() => toggleF2FSessionSelection(type.id)}
                  />
                </View>
              ))}
            </View>
          </View>
        )}

        {onlineData && (
          <View
            style={{
              alignItems: isRTL ? 'flex-end' : 'flex-start',
            }}>
            <Text style={styles.sectionTitle}>{onlineData.name}</Text>
            <View
              style={[
                styles.checkboxView,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {onlineData.tlm_sessions_types.map((type, index) => (
                <View
                  key={type.id}
                  style={[
                    styles.checkboxContainer,
                    index % 2 === 1 && {marginLeft: 24},
                  ]}>
                  <CustomCheckbox
                    label={type.name}
                    isSelected={selectedOnlineSessions.includes(type.id)}
                    onSelect={() => toggleOnlineSessionSelection(type.id)}
                  />
                </View>
              ))}
            </View>
          </View>
        )}
        {openSessionData && (
          <View
            style={{
              marginTop: 20,
              alignItems: isRTL ? 'flex-end' : 'flex-start',
            }}>
            <Text style={styles.sessionModeTitle}>{openSessionData.name}</Text>
            <View
              style={[
                styles.checkboxView,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              {openSessionData?.tlm_sessions_types.map((type, index) => (
                <View
                  key={type.id}
                  style={{
                    marginLeft: index % 2 === 1 ? 24 : 0,
                    marginBottom: 10,
                  }}>
                  <CustomCheckbox
                    label={type.name}
                    isSelected={selectOpenSession.includes(type.id)}
                    onSelect={() => toggleOpenSessionSelection(type.id)}
                  />
                </View>
              ))}
            </View>
          </View>
        )}

        {selectedOnlineSessions.length > 0 && (
          <View style={styles.section}>
            <PrimaryInput
              keyboardType="numeric"
              title={
                cummissionOnline > 0
                  ? `${t('online_price')} (${t(
                      'commission_applied',
                    )} ${cummissionOnline}%)`
                  : t('online_price')
              }
              placeholder={t('enter_price')}
              value={priceOnline}
              onChangeText={text => {
                setPriceOnline(text.replace(/[^0-9.]/g, ''));
                setFormErrors(prev => ({...prev, priceOnline: null}));
              }}
              error={formErrors.priceOnline}
            />
          </View>
        )}

        {selectedF2FSessions.length > 0 && (
          <View style={styles.section}>
            <PrimaryInput
              keyboardType="numeric"
              title={
                cummissionF2F > 0
                  ? `${t('face_to_face_price')} ( ${t(
                      'commission_applied',
                    )}${cummissionF2F}%)`
                  : t('face_to_face_price')
              }
              placeholder={t('enter_price')}
              value={priceFaceToFace}
              onChangeText={text => {
                setPriceFaceToFace(text.replace(/[^0-9.]/g, ''));
                setFormErrors(prev => ({...prev, priceFaceToFace: null}));
              }}
              error={formErrors.priceFaceToFace}
            />
          </View>
        )}
        {(selectedOnlineSessions.some(
          id => id === online_groupId || id === online_openSessionId,
        ) ||
          selectedF2FSessions.some(
            id => id === f2f_groupId || id === f2f_openSessionId,
          )) && (
          <View>
            <PrimaryInput
              title={t('minStudent')}
              placeholder={t('minStudent')}
              value={minStudents}
              lableStyle={styles.sessionModeTitle}
              // onChangeText={text => setMinStudents(text.replace(/[^0-9]/g, ''))}
              onChangeText={text => {
                const value = text.replace(/[^0-9]/g, '');
                if (
                  value &&
                  parseInt(value) > 1 &&
                  (!maxStudents || parseInt(value) < parseInt(maxStudents))
                ) {
                  setMinStudents(value);
                } else if (!value) {
                  setMinStudents('');
                }
              }}
              keyboardType="numeric"
            />
            <PrimaryInput
              title={t('maxStudent')}
              placeholder={t('maxStudent')}
              lableStyle={styles.sessionModeTitle}
              value={maxStudents}
              // onChangeText={text => setMaxStudents(text.replace(/[^0-9]/g, ''))}
              onChangeText={text => {
                const value = text.replace(/[^0-9]/g, '');
                setMaxStudents(value);
                // if (value && parseInt(value) > parseInt(minStudents)) {
                //   setMaxStudents(value);
                // } else if (!value) {
                //   setMaxStudents('');
                // }
              }}
              keyboardType="numeric"
            />
          </View>
        )}

        <View
          style={[
            styles.section,
            {alignItems: isRTL ? 'flex-end' : 'flex-start'},
          ]}>
          <Text style={styles.sectionTitle}>{t('package_type')}</Text>
          {packageTypesData?.data?.rows?.map(pkg => (
            <CustomCheckbox
              key={pkg.id}
              label={pkg.name}
              isSelected={selectedPackages.includes(pkg.id)}
              onSelect={() => togglePackageSelection(pkg.id)}
            />
          ))}
          {formErrors.packages && (
            <Text style={styles.errorText}>{formErrors.packages}</Text>
          )}
        </View>

        <View style={styles.buttonContainer}>
          <PrimaryButton
            loading={loading}
            onPress={handleSubmit}
            title={t('save')}
            style={styles.saveButton}
          />
          <PrimaryButton
            loading={isDeleting}
            onPress={handleDelete}
            title={t('delete')}
            style={styles.deleteButton}
            textStyle={styles.deleteButtonText}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default EditRecreational;
