import {
  View,
  SafeAreaView,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import CustomDropDown from '../../Components/CustomDropDown';
import HeaderWithDopdown from '../../Components/HeaderWithDopdown';
import styles from './styles';
import colors from '../../Utils/colors';
import AcademicForm from './AcademicForm';
import Recreational from './Recreational';
import Courses from './Courses';
import {StatusContainer} from '../../Components/StatusBar';
import icons from '../../Utils/icons';

const AddYourRateCard = ({navigation, route}) => {
  const {id, label} = route?.params;
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [selectCategory, setSelectCategory] = useState({id: id, name: label});

  const categoryMapping = {
    [t('academic')]: 'academic',
    [t('recreational')]: 'recreational',
    [t('courses')]: 'courses',
  };

  const buttonData = [
    {id: 1, name: t('academic')},
    {id: 2, name: t('recreational')},
    {id: 3, name: t('courses')},
  ];

  const selectedCategoryKey = categoryMapping[selectCategory.name];

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <HeaderWithDopdown
        backIcon={isRTL ? icons.rightArrowLarge : icons.backbtn}
        isBackBtn
        title={t('add_rate_card')}
        style={{zIndex: 20}}
        rightContent={
          <CustomDropDown
            width="75%"
            data={buttonData}
            radius={20}
            onSelect={selected => setSelectCategory(selected)}
            defaultValue={selectCategory}
            height={30}
          />
        }
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{flex: 1}}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView
            contentContainerStyle={styles.container}
            keyboardShouldPersistTaps="handled">
            {selectedCategoryKey === 'academic' && <AcademicForm />}
            {selectedCategoryKey === 'recreational' && <Recreational />}
            {selectedCategoryKey === 'courses' && <Courses />}
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default AddYourRateCard;
