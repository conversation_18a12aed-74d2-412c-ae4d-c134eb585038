import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, SafeAreaView, TouchableOpacity, Image} from 'react-native';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import styles from './styles';
import {StatusContainer} from '../../Components/StatusBar';
import {showToast} from '../../Components/ToastHelper';
import icons from '../../Utils/icons';
import WalletCard from './WalletCard';
import {PrimaryInput} from '../../Components/Input';
import {PrimaryButton} from '../../Components/CustomButton';
import userSlice from '../../Features/userSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useSelector} from 'react-redux';
import {
  useAddAmountStudentWalletMutation,
  useGetEarningsQuery,
  useGetSavedPaymentMethodStudentMutation,
  useGetSavedPaymentMethodStudentQuery,
  useGetStudentWalletAmountQuery,
  useLazyGetStudentWalletAmountQuery,
  useRequesPayoutTutorMutation,
} from '../../Api/ApiSlice';
import {PAYMENT_GATEWAY_RETURN_URL} from '../../Utils/constant';
import {useFocusEffect} from '@react-navigation/native';
import TaleemHeader from '../../Components/TaleemHeader/TaleemHeader';

const WalletScreen = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [langType, setLangType] = useState('');
  const [amount, setAmount] = useState('');
  const logoSource =
    langType === 'ar'
      ? icons.logo.welcomeLogoArabic
      : icons.logo.welcomeLogoEng;
  const {user_type} = useSelector(state => state?.auth);

  const getLangType = async () => {
    const storedLangType = await AsyncStorage.getItem('selectedLang');
    return storedLangType ? JSON.parse(storedLangType) : '';
  };
  useEffect(() => {
    const fetchLangType = async () => {
      const type = await getLangType();
      setLangType(type);
    };
    fetchLangType();
  }, []);

  const [getWalletAmoount, {data: studentWallet}] =
    useLazyGetStudentWalletAmountQuery();
  const {data: savedPaymentMethodsRes, refetch: refetchSavedPaymentMethods} =
    useGetSavedPaymentMethodStudentQuery();
  console.log(
    '🚀 ~ WalletScreen ~ savedPaymentMethodsRes:',
    savedPaymentMethodsRes,
  );

  useFocusEffect(
    useCallback(() => {
      getWalletAmoount();
      refetchSavedPaymentMethods();
    }, []),
  );

  const [addAmount, {isLoading: withdrawLoadin}] =
    useAddAmountStudentWalletMutation();

  const handleWithDrawRequest = async () => {
    if (!amount) {
      showToast('error', t('pleaseEnterAmount'), 'bottom', isRTL);
      return;
    }
    navigation.navigate('SavedPaymentMethods', {amount: amount});
    setAmount('');
    return;
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      {/* <View style={styles.headerContainer}>
        <View style={styles.leftHeader}>
          <TouchableOpacity onPress={() => navigation.openDrawer()}>
            <Image source={icons.hamBurger} style={styles.hamburgerIcon} />
          </TouchableOpacity>

          <View style={styles.logoContainer}>
            <Image
              source={logoSource}
              style={styles.logoImage}
              resizeMode="contain"
            />
          </View>
        </View>

        <View style={styles.rightHeader}>
          <TouchableOpacity
            style={styles.notificationContainer}
            onPress={() =>
              showToast('success', t('notifications_unavailable'))
            }>
            <Image
              source={icons.notification}
              style={styles.notificationIcon}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View> */}
      <TaleemHeader />
      <View style={styles.contentContainer}>
        <WalletCard
          amount={studentWallet?.data}
          onRefresh={() => getWalletAmoount()}
        />
        <View style={styles.bottomView}>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image source={icons.walletIcon1} style={styles.walletIcons} />
            <Text style={styles.addMoneyTxt}>{t('addMoney')}</Text>
          </View>
          <PrimaryInput
            keyboardType="default"
            placeholder={'0 QAR'}
            value={amount}
            onChangeText={text => setAmount(text)}
            placeholderTextColor={colors.txtGrey1}
            maxLength={8}
          />
          <PrimaryButton
            style={styles.btn}
            title={t('addMoney')}
            loading={withdrawLoadin}
            onPress={handleWithDrawRequest}
          />
          <TouchableOpacity
            style={[
              styles.greyBtn,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}
            onPress={() => navigation.navigate('Transactions')}>
            <Image source={icons.publish} />
            <Text style={styles.addMoneyTxt}>{t('transactions')}</Text>
          </TouchableOpacity>
          {/* {user_type == '1' && (
            <TouchableOpacity
              onPress={() => navigation.navigate('SavedPaymentMethods')}
              style={[
                styles.greyBtn,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Image source={icons.bookmark} />
              <Text style={styles.addMoneyTxt}>
                {t('saved_payment_method')}
              </Text>
            </TouchableOpacity>
          )} */}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default WalletScreen;
