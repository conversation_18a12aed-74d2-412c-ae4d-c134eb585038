import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize, SCREEN_WIDTH} from '../../Utils/constant';
import {CurrentRenderContext} from '@react-navigation/native';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp} from '../../Helper/ResponsiveDimensions';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
  },
  headerContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    height: hp(7),
    marginBottom: hp(1),
    justifyContent: 'center',
  },
  leftHeader: {
    flex: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  hamburgerIcon: {
    width: fp(2.4),
    height: fp(2.4),
  },
  logoContainer: {
    alignItems: 'center',
    paddingLeft: 5,
  },
  logoImage: {
    width: fp(14), // Keep the width fixed
  },
  rightHeader: {
    flex: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingRight: 10,
    minWidth: 120,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: fp(1.8),
    minWidth: fp(8),
  },
  locationIcon: {
    width: fp(2.8),
    height: fp(2.5),
  },
  locationText: {
    marginHorizontal: 6,
    color: colors.white,
    fontWeight: '500',
    fontSize: 12,
  },
  arrowDownIcon: {
    width: fp(1.2),
    height: fp(1.2),
    alignSelf: 'center',
  },
  notificationContainer: {
    padding: fp(0.5),
  },
  notificationIcon: {
    width: fp(3.5),
    height: fp(3.5),
  },
  contentContainer: {
    backgroundColor: colors.white,
    flex: 1,
    padding: fp(1.6),
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
  },
  walletView: {
    padding: fp(2),
    height: fp(22),
    // flexDirection: 'row',
  },
  title: {
    color: colors.white,
    left: fp(2),
    fontFamily: Fonts.medium,
    fontSize: fp(2),
  },
  refreshView: {
    alignItems: 'center',
  },
  refreshTxt: {
    fontFamily: Fonts.medium,
    color: colors.pencilGray,
    fontSize: fp(1.4),
  },
  refreshImg: {
    height: fp(1.6),
    width: fp(1.6),
    tintColor: colors.pencilGray,
    right: 8,
  },
  amountView: {
    alignItems: 'center',
    marginTop: hp(2),
  },
  amount: {
    fontFamily: Fonts.bold,
    fontSize: fp(4.2),
    color: colors.white,
  },
  currency: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.4),
    color: '#90DFD9',
  },
  walletIcons: {
    tintColor: '#40A39B',
    width: fp(3),
    height: fp(3),
  },
  bottomView: {
    marginVertical: fp(4),
  },
  row: {
    alignItems: 'center',
    marginBottom: fp(2),
  },
  addMoneyTxt: {
    color: colors.black,
    left: 8,
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
  },
  btn: {
    width: '100%',
    height: fp(7),
  },
  greyBtn: {
    height: fp(7),
    alignItems: 'center',
    paddingHorizontal: fp(1.6),
    backgroundColor: colors.whiteFlash,
    marginTop: fp(2.2),
    borderRadius: 10,
  },
});

export default styles;
