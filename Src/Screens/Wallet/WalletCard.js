import {View, Text, Image, TouchableOpacity} from 'react-native';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import styles from './styles';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {fp} from '../../Helper/ResponsiveDimensions';

const WalletCard = ({onRefresh, amount = 0}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <LinearGradient
      style={{borderRadius: 10}}
      start={{
        x: Math.sin((0 * Math.PI) / 180),
        y: -Math.cos((100 * Math.PI) / 180),
      }}
      end={{
        x: Math.sin((50 * Math.PI) / 180),
        y: -Math.cos((200 * Math.PI) / 180),
      }}
      colors={['#40A39B', '#002825']}>
      {/* <View style={styles.walletView}>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
          }}>
          <View style={{flexDirection: 'row'}}>
            <Image source={icons.walletIcon1} />
            <Text style={styles.title}>{t('currentBalance')}</Text>
          </View>

          <TouchableOpacity
            style={[
              styles.refreshView,
              {
                alignSelf: isRTL ? 'flex-start' : 'flex-end',
                flexDirection: isRTL ? 'row-reverse' : 'row',
              },
            ]}
            onPress={onRefresh}>
            <Image source={icons.refresh} style={styles.refreshImg} />
            <Text style={styles.refreshTxt}>{t('refresh')}</Text>
          </TouchableOpacity>
        </View>
       
      </View> */}
      <View style={styles.walletView}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Image
              source={icons.walletIcon1}
              style={{height: fp(4), width: fp(4)}}
            />
            <Text style={styles.title}>{t('currentBalance')}</Text>
          </View>
          <TouchableOpacity
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
            }}
            onPress={onRefresh}>
            <Image source={icons.refresh} style={styles.refreshImg} />
            <Text style={styles.refreshTxt}>{t('refresh')}</Text>
          </TouchableOpacity>

          {/* <TouchableOpacity
            style={[
              styles.refreshView,
              {
                alignSelf: isRTL ? 'flex-start' : 'flex-end',
                flexDirection: isRTL ? 'row-reverse' : 'row',
              },
            ]}
            onPress={onRefresh}>
            
          </TouchableOpacity> */}
        </View>
        <View style={styles.amountView}>
          <Text style={styles.amount}>{amount}</Text>
          <Text style={styles.currency}>{'QAR'}</Text>
        </View>
      </View>
    </LinearGradient>
  );
};

export default WalletCard;
