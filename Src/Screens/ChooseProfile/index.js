import {
  FlatList,
  Image,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {AppHeader} from '../../Components/Header';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import {Fonts} from '../../Utils/Fonts';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import {
  useGetAllConnectedProfilesAfterLoginQuery,
  useLazyGetAllConnectedProfilesAfterLoginQuery,
  useSwitchConnectedProfileMutation,
} from '../../Api/ApiSlice';
import {useDispatch, useSelector} from 'react-redux';
import {setAuthData, setIsLoggedIn} from '../../Features/authSlice';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {FlipOutYRight} from 'react-native-reanimated';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {useNavigation} from '@react-navigation/native';

const ChooseProfile = () => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigation = useNavigation();
  const {isLoggedIn} = useSelector(state => state.auth);

  const {
    data: connectedProfileRes,
    isLoading: profileLoading,
    refetch,
  } = useGetAllConnectedProfilesAfterLoginQuery();
  useEffect(() => {
    refetch();
  }, []);

  const [
    switchConnectedProfileApi,
    {data: switchProfileRes, isLoading: switchProfileLoading},
  ] = useSwitchConnectedProfileMutation();

  const dispatch = useDispatch();
  // async function handleProfilePress(id) {
  //   try {
  //     console.log('🚀 ~ handleProfilePress ~ id:', id);
  //     const response = await switchConnectedProfileApi(id);
  //     console.log(
  //       '🚀 ~ switchConnectedProfileApi ~ response:',
  //       response?.data?.data,
  //     );

  //     const userData = {
  //       token: response?.data?.data?.token,
  //       userId: response?.data?.data?.id,
  //       user_type: response?.data?.data?.user_type,
  //       action_type: response?.data?.data?.action_type,
  //       profile_image: null,
  //       profile_accounts: response?.data?.data?.profile_accounts,
  //     };
  //     await AsyncStorage.setItem('user', JSON.stringify(userData));
  //     console.log('🚀 ~ handleProfilePress ~ userData:', userData);

  //     dispatch(setAuthData(userData));
  //     dispatch(setIsLoggedIn(true));

  //   } catch (error) {
  //     console.error('Error in handleProfilePress:', error);
  //     // You might want to add appropriate error handling here, such as:
  //     // - Showing an error message to the user
  //     // - Setting an error state
  //     // - Logging to an error tracking service
  //   }
  // }

  const handleProfilePress = async id => {
    if (!id) {
      console.error('No profile ID provided');
      return;
    }

    try {
      console.log('Switching profile with ID:', id);
      const response = await switchConnectedProfileApi(id).unwrap();
      const profileData = response?.data;

      if (!profileData) {
        throw new Error('Invalid response data');
      }

      const userData = {
        token: profileData.token,
        userId: profileData.id,
        user_type: profileData.user_type,
        action_type: profileData.action,
        profile_image: null,
        profile_accounts: profileData.profile_accounts || {},
      };

      console.log('Profile switch successful:', userData);
      await AsyncStorage.setItem('user', JSON.stringify(userData));

      dispatch(setAuthData(userData));
      dispatch(setIsLoggedIn(true));
      // navigation.navigate('AuthNavigator'); // Adjust route name as needed
    } catch (error) {
      console.error('Failed to switch profile:', error.message || error);
      // Optional: Toast.show('Failed to switch profile. Please try again.');
    }
  };

  const ProfileView = ({item}) => {
    return (
      <View
        style={applyShadowStyleIos({
          height: fp(10),
          width: fp(10),
          borderRadius: fp(10),
          backgroundColor: colors.white,
          elevation: 2,
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1,
        })}>
        <Image
          // source={{uri: DUMMY_USER_IMG}}
          source={{
            uri: item?.image
              ? `${IMAGE_BASE_URL + item?.image}`
              : `${DUMMY_USER_IMG}`,
          }}
          style={{height: fp(9), width: fp(9), borderRadius: fp(10)}}
        />
      </View>
    );
  };
  const userTypeMap = {
    1: t('student'),
    2: t('parent'),
    3: t('tutor'),
  };

  const FullProfileView = ({item, index}) => {
    console.log('🚀 ~ FullProfileView ~ item:', item);
    return (
      <TouchableOpacity
        style={{
          alignItems: 'center',
          width: wp(30),
          //   backgroundColor: 'red',
          justifyContent: 'center',
          // flex: 1,
        }}
        onPress={() => handleProfilePress(item?.id)}>
        <ProfileView item={item} />
        <Text
          style={{
            marginTop: hp(1),
            fontSize: fp(1.8),
            fontFamily: Fonts.medium,
            color: colors.white,
            textAlign: 'center',
            lineHeight: hp(2),
          }}>
          {item?.name}
        </Text>
        <Text
          style={{
            marginTop: hp(0.2),
            fontSize: fp(1.4),
            fontFamily: Fonts.poppinsRegular,
            color: colors.white,
          }}>
          {userTypeMap[item?.user_type]}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      {/* <AppHeader backIcon={icons.backbtn} isBackBtn title={t('withdraw')} /> */}

      <View style={styles.body}>
        <Text
          style={{
            fontFamily: Fonts.bold,
            alignSelf: 'center',
            fontSize: fp(3),
            color: colors.white,
            marginBottom: hp(2),
          }}>
          {t('choose_profile')}
        </Text>
        <View
          style={{
            marginTop: hp(1),
            flexDirection: 'row',
            width: wp(90),
            alignItems: 'center',
            justifyContent: 'center',
            flexWrap: 'wrap',
            gap: hp(3),
          }}>
          {connectedProfileRes?.data?.list.map((item, index) => {
            return <FullProfileView item={item} />;
          })}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ChooseProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
    marginBottom: fp(2),
  },
  body: {
    flex: 1,
    padding: fp(2),
    alignItems: 'center',
    justifyContent: 'center',
    // width: wp(100),
  },
});
