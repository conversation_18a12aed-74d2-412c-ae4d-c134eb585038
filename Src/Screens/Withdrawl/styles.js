import {StyleSheet} from 'react-native';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {IS_ANDROID, responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: colors.white, marginBottom: fp(2)},
  body: {
    flex: 1,
    padding: fp(2),
  },
  card: {
    padding: fp(2),
    alignItems: 'center',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  balanceTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.bold,
    fontSize: fp(1.6),
  },
  amount: {
    color: colors.darkBlack,
    fontFamily: Fonts.bold,
    fontSize: fp(4.2),
  },
  btn: {
    backgroundColor: colors.lightGreen,
    width: fp(10),
    height: fp(4),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 20,
    paddingHorizontal: 8,
  },
  btn2: {
    width: '48%',
    backgroundColor: colors.white,
    borderColor: colors.themeColor,
    borderWidth: 1,
    height: fp(6),
  },
  btnTxt: {
    color: colors.themeColor,
  },
  transactionTxt: {
    fontFamily: Fonts.semiBold,
    fontSize: fp(1.9),
    color: colors.black,
  },
  itemCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 10,
    backgroundColor: colors.white,
    paddingHorizontal: fp(2),
    paddingVertical: fp(1.5),
  },
  gradientBorder: {
    padding: 1, // Border thickness
    borderRadius: 10,
    marginVertical: fp(1),
  },
  imageView: {
    height: fp(5),
    width: fp(5),
    borderRadius: fp(3),
    alignItems: 'center',
    justifyContent: 'center',
  },
  withdrawl: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.4),
    left: 8,
    color: colors.searchGray,
  },
  listAmount: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
  },
  enterAmount: {
    color: colors.darkBlack,
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
  },
});

export default styles;
