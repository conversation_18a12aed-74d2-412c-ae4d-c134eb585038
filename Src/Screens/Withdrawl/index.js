import {View, Text, Platform, Keyboard} from 'react-native'; // Add Keyboard import
import React, {useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import styles from './styles';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import {useTranslation} from 'react-i18next';
import LinearGradient from 'react-native-linear-gradient';
import {PrimaryInput} from '../../Components/Input';
import {PrimaryButton} from '../../Components/CustomButton';
import {fp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import {useRequesPayoutTutorMutation} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';

const Withdrawal = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const [amount, setAmount] = useState('');
  const isRTL = i18n.language === 'ar';
  const [addRequesPayout] = useRequesPayoutTutorMutation();

  const handlePayoutRequest = async () => {
    if (!amount || !amount.trim()) {
      showToast('error', t('pleaseEnterAmount'), 'bottom', isRTL);
      return;
    }

    const payload = {
      withdrawalAmount: amount,
    };

    try {
      Keyboard.dismiss(); // Dismiss the keyboard before proceeding
      await addRequesPayout(payload).unwrap();
      showToast('success', t('requestSuccess'), 'bottom', isRTL);
      navigation.goBack();
      setAmount('');
    } catch (error) {
      showToast('error', error?.data?.message, 'bottom', isRTL);
      console.error('Error saving meeting point:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('withdraw')} />
      <View style={styles.body}>
        <LinearGradient
          style={{borderRadius: 10}}
          start={{
            x: Math.sin((0 * Math.PI) / 180),
            y: -Math.cos((100 * Math.PI) / 180),
          }}
          end={{
            x: Math.sin((50 * Math.PI) / 180),
            y: -Math.cos((200 * Math.PI) / 180),
          }}
          colors={['#C6FFC9', '#D4EBFF']}>
          <View style={styles.card}>
            <Text style={styles.enterAmount}>
              {t('enter_withdrawal_amount')}
            </Text>
            <PrimaryInput
              keyboardType="numeric"
              placeholder={'0 QAR'}
              value={amount}
              onChangeText={text => setAmount(text)}
              placeholderTextColor={colors.txtGrey1}
              maxLength={8}
              autoFocus={true}
              onSubmitEditing={() => Keyboard.dismiss()} // Dismiss keyboard on submit
              returnKeyType="done" // Show "Done" on the keyboard
              textInputStyle={{
                borderWidth: 0,
                alignSelf: 'center',
                width: '50%',
                fontSize: fp(3),
                height: fp(7),
                fontFamily: Fonts.bold,
                color: colors.black,
                textAlign: 'center',
              }}
              lableStyle={{textAlign: 'center'}}
            />
          </View>
        </LinearGradient>
      </View>
      <PrimaryButton
        onPress={handlePayoutRequest}
        title={`${t('withdraw')} ${amount ? `${amount} QAR` : ''}`}
      />
    </SafeAreaView>
  );
};

export default Withdrawal;
