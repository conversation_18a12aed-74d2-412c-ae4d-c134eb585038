import React, {useState} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  Platform,
  TouchableWithoutFeedback,
  FlatList,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import styles from './styles';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {AppHeader} from '../../Components/Header';
import BookYourTutorCard from '../../Components/Custom_Components/BookYourTutorCard';
import {useTranslation} from 'react-i18next';
import icons from '../../Utils/icons';
import {showToast} from '../../Components/ToastHelper';
import {PrimaryButton} from '../../Components/CustomButton';
import LinearGradient from 'react-native-linear-gradient';
import {useSelector} from 'react-redux';
import {
  convertTo12HourFormat,
  convertToDMY,
  convertToHoursAndMinutes,
  convertToLocal12HourFormat,
} from '../../Helper/DateHelpers/DateHelpers';
import {DUMMY_USER_IMG, PAYMENT_GATEWAY_RETURN_URL} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {
  useGetBookingDetailsQuery,
  useGetTutorBookingDetailsByIdQuery,
  useGetTutorBookingDetailsQuery,
  usePostStudentPaymentMutation,
  usePutTutorConfirmationStatusMutation,
} from '../../Api/ApiSlice';
import {FrameHeight} from 'react-native-agora';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import moment from 'moment';
import {Fonts} from '../../Utils/Fonts';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {openDirections} from '../../Helper/GoogleMapsHelpers';

const BookingDetailsTutor = ({navigation, route}) => {
  const {bookingId} = route?.params;
  console.log('🚀 ~ BookYourTutorConfirmation ~ bookingId:', bookingId);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [expandedDates, setExpandedDates] = useState({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(
    t('skipCash'),
  );

  const {
    data: bookingRes,
    error,
    isLoading,
  } = useGetTutorBookingDetailsQuery(bookingId);

  const [
    postStudentPayment,
    {
      data: studentPaymentData,
      error: bookingError,
      isLoading: studentPaymentLoading,
    },
  ] = usePostStudentPaymentMutation();

  const [
    updateTutorConfirmationStatus,
    {data: res, error: err, isLoading: confirmationLoading},
  ] = usePutTutorConfirmationStatusMutation();

  const bookingData = bookingRes?.data;

  function handleAcceptPress(status) {
    console.log('🚀 ~ handleAcceptPress ~ status:', status);
    //Status = 1 for accept
    //Status = 2 for reject
    const body = {
      status: status,
      booking_id: bookingId,
    };

    updateTutorConfirmationStatus(body)
      .then(response => {
        console.log('🚀 ~ .then ~ response:', response);
        navigation.navigate('Home');
        // showToast('success', response?.data?.message, 'bottom', isRTL);
      })
      .catch(err => {
        console.log('🚀 ~ handleAcceptPress ~ err:', err);
        showToast('error', err?.response?.message, 'bottom', isRTL);
      });
  }

  const toggleExpandDate = index => {
    setExpandedDates(prev => ({
      ...prev,
      [index]: !prev[index],
    }));
  };
  function renderGroupRows({item}) {
    console.log('🚀 ~ renderGroupRows ~ item:', item);

    return (
      <View
        style={{
          borderWidth: 1,
          borderColor: colors.txtGrey,
          borderRadius: 10,
          padding: 10,
          marginVertical: 4,
          backgroundColor: '#fff',
        }}>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
          }}>
          <Text style={styles.labelText}>{t('student_name')} :</Text>
          <Text style={styles.valueText}>
            {capitalizeFirstLetter(item?.tlm_user?.name)}
          </Text>
        </View>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
          }}>
          <Text
            style={[styles.labelText, {textAlign: isRTL ? 'right' : 'left'}]}>
            {t('paymentStatus')} :
          </Text>
          <Text
            style={[
              styles.valueText,
              {
                textAlign: isRTL ? 'right' : 'left',
                color:
                  item?.payment_status == '0'
                    ? colors.orangeLight
                    : item?.payment_status == '1'
                    ? colors.green
                    : colors.lightRed,
              },
            ]}>
            {item?.payment_status == '0' ? 'Pending' : 'Paid'}
          </Text>
        </View>
      </View>
    );
  }

  const result = convertToHoursAndMinutes(
    bookingData?.tlm_booking_schedules?.reduce((total, schedule) => {
      const hours = parseFloat(schedule.tlm_tutor_schedule.hours_duration);
      return total + hours;
    }, 0),
  );

  const handleAddMoneyToWallet = () => {
    navigation?.navigate('Wallet');
  };
  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <View style={styles.headerContainer}>
        <AppHeader
          backIcon={icons.backbtn}
          isBackBtn
          title={t('accept_booking')}
          style={{zIndex: 20}}
        />
      </View>
      <ScrollView contentContainerStyle={{flexGrow: 1, paddingBottom: 20}}>
        {/* Header Section */}

        <TaleemLoader isLoading={confirmationLoading} />
        {/* Package Details */}
        <View style={styles.cardContainer}>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              style={styles.icon}
              resizeMode="contain"
              source={icons.calanderYellow}
            />
            <Text style={styles.labelText}>{t('packageStartingDate')} :</Text>
            <Text
              style={[styles.valueText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {moment(bookingData?.package_start_date).format(
                isRTL ? 'YYYY MMM DD' : 'DD MMM YYYY',
              )}
              {/* {bookingData?.package_start_date?.toLocaleDateString('en-GB')} */}
            </Text>
          </View>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              style={styles.icon}
              resizeMode="contain"
              source={icons.clockYellow}
            />
            <Text style={styles.labelText}>{t('package')} :</Text>
            <Text
              style={[styles.valueText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {bookingData?.tlm_package_type?.name}
            </Text>
          </View>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              style={styles.icon}
              resizeMode="contain"
              source={icons.hatYellow}
            />
            <Text style={styles.labelText}>{t('classMode')} :</Text>
            <Text
              style={[styles.valueText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {bookingData?.tlm_class_type?.name}
            </Text>
          </View>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              style={styles.icon}
              resizeMode="contain"
              source={icons.hatYellow}
            />
            <Text style={styles.labelText}>{t('session')} :</Text>
            <Text
              style={[styles.valueText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {bookingData?.tlm_sessions_type?.name}
            </Text>
          </View>

          {bookingData?.address && (
            <View
              style={[
                styles.row,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Image
                style={styles.icon}
                resizeMode="contain"
                source={icons.locationGray}
                tintColor={'rgba(232,185,74,1)'}
              />
              <Text style={styles.labelText}>{t('meetingPoint')} :</Text>
              <Text
                numberOfLines={3}
                onPress={() =>
                  openDirections(bookingData?.latitude, bookingData?.longitude)
                }
                style={[
                  styles.valueText,
                  {
                    textAlign: isRTL ? 'right' : 'left',
                    width: wp(45),
                    lineHeight: hp(2),
                    textDecorationLine: 'underline',
                  },
                ]}>
                {bookingData?.address}
              </Text>
            </View>
          )}
        </View>

        {/* Enrollment Details */}
        {bookingData?.tlm_sessions_type?.name == 'Group' && (
          <View style={styles.cardContainer}>
            <Text style={styles.sectionTitle}>{t('group_details')}</Text>
            <FlatList
              data={bookingData?.tlm_booking_enrollments}
              renderItem={renderGroupRows}
            />
          </View>
        )}

        {/* Date and Time Selection */}
        <View style={styles.cardContainer}>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              style={styles.icon}
              resizeMode="contain"
              source={icons.clockYellow}
            />
            <Text style={styles.labelText}>{t('time')} :</Text>

            <Text
              style={[styles.valueText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {result}
            </Text>
          </View>

          {bookingData?.tlm_booking_schedules.map((schedule, index) => {
            // Calculate duration in hours

            // Format start and end time
            const formattedTime = isRTL
              ? `${convertToLocal12HourFormat(
                  schedule?.tlm_tutor_schedule?.end_time,
                )} - ${convertToLocal12HourFormat(
                  schedule?.tlm_tutor_schedule?.start_time,
                )}`
              : `${convertToLocal12HourFormat(
                  schedule?.tlm_tutor_schedule?.start_time,
                )} - ${convertToLocal12HourFormat(
                  schedule?.tlm_tutor_schedule?.end_time,
                )}`;

            // Determine display date
            const displayDate = schedule?.date || t('Date');

            return (
              <View
                key={schedule.id || index}
                style={styles.collapsibleContainer}>
                <TouchableWithoutFeedback
                  onPress={() => toggleExpandDate(index)}>
                  <View style={styles.datePicker}>
                    <View
                      style={{
                        flexDirection: isRTL ? 'row-reverse' : 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <Text style={styles.dateText}>
                        {t('date')}:{' '}
                        {moment(displayDate).format(
                          isRTL ? 'YYYY MMM DD' : 'DD MMM YYYY',
                        )}
                      </Text>
                      <View
                        style={[
                          styles.durationContainer,
                          {flexDirection: isRTL ? 'row-reverse' : 'row'},
                        ]}>
                        <LinearGradient
                          colors={['#C6FFC9', '#D4EBFF']}
                          start={{x: 0, y: 0}}
                          end={{x: 1, y: 1}}
                          style={{
                            borderRadius: 20,
                            paddingHorizontal: 2,
                            paddingVertical: 1,
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <Text
                            style={[
                              styles.durationText,
                              {textAlign: isRTL ? 'right' : 'left'},
                            ]}>
                            {convertToHoursAndMinutes(
                              Number(
                                schedule?.tlm_tutor_schedule?.hours_duration,
                              ),
                            )}
                          </Text>
                        </LinearGradient>
                        {/* <Image
                          source={
                            expandedDates[index]
                              ? icons.upArrow
                              : icons.downArrowBlack
                          }
                          style={styles.arrowIcon}
                        /> */}
                      </View>
                    </View>
                    {/* {expandedDates[index] && ( */}
                    <View
                      style={{flexDirection: isRTL ? 'row-reverse' : 'row'}}>
                      <Text style={styles.timeText}>{t('time')} : </Text>
                      <Text style={styles.timeText}> {formattedTime} </Text>
                    </View>

                    {/* )} */}
                  </View>
                </TouchableWithoutFeedback>
              </View>
            );
          })}
        </View>

        {/* Book Now Button */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginHorizontal: wp(6),
          }}>
          <PrimaryButton
            onPress={() => handleAcceptPress('1')} //1 for accept the booking
            title={t('accept')}
            style={{
              backgroundColor: colors.themeColor,
              marginBottom: 15,
              width: wp(40),
            }}
            textStyle={{fontSize: 16, color: colors.white}}
            loading={studentPaymentLoading}
          />
          <PrimaryButton
            onPress={() => handleAcceptPress('2')} //2 for reject the booking
            title={t('reject')}
            style={{
              backgroundColor: 'transparent',
              marginBottom: 15,
              width: wp(40),
              borderWidth: fp(0.1),
              borderColor: colors.blackSkatch,
            }}
            textStyle={{fontSize: 16, color: colors.blackSkatch}}
            loading={studentPaymentLoading}
          />
        </View>

        {/* OTP Modal
        <Modal visible={isModalVisible} transparent animationType="none">
          <TouchableOpacity
            style={styles.modalContainer}
            onPress={() => setIsModalVisible(false)}>
            <View style={styles.modalContent}>
              <View
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: 20,
                }}>
                <Text style={styles.modalTitle}>{t('bookingFailed')}</Text>
                <Image
                  source={icons.cross}
                  style={{
                    height: fp(2),
                    width: fp(2),
                  }}
                />
              </View>
              <Text
                style={{
                  fontSize: fp(2),
                  fontFamily: Fonts.medium,
                  marginBottom: 15,
                  lineHeight: hp(2.4),
                }}>
                {t('lowWalletError')}
              </Text>
              <View
                style={{
                  marginTop: hp(1),
                  alignItems: 'flex-start',
                  justifyContent: 'flex-start',
                }}>
                <PrimaryButton
                  onPress={handleAddMoneyToWallet}
                  title={t('addMoneyToWallet')}
                  style={{
                    backgroundColor: colors.themeColor,
                    marginBottom: 15,
                    width: wp(80),
                    alignSelf: 'center',
                  }}
                  textStyle={{fontSize: 14, color: colors.white}}
                  // loading={studentPaymentLoading}
                />
                <PrimaryButton
                  onPress={handlePayUsingCard}
                  title={t('payUsingCard')}
                  style={{
                    backgroundColor: colors.themeColor,
                    marginBottom: 15,
                    width: wp(80),
                    alignSelf: 'center',
                  }}
                  textStyle={{fontSize: 14, color: colors.white}}
                  // loading={studentPaymentLoading}
                />
              </View>
            </View>
          </TouchableOpacity>
        </Modal> */}
      </ScrollView>
    </SafeAreaView>
  );
};

export default BookingDetailsTutor;
