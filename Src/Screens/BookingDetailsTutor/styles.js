import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {fp, wp} from '../../Helper/ResponsiveDimensions';

const styles = StyleSheet.create({
  headerContainer: {
    backgroundColor: colors.themeColor,
    // paddingBottom: 20,
    paddingHorizontal: 5,
  },
  cardContainer: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 20,
    padding: 20,
    marginHorizontal: 20,
    marginVertical: 10,
    backgroundColor: '#fff',
  },
  row: {
    alignItems: 'center',
    marginVertical: 8,
  },
  icon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  labelText: {
    fontSize: fp(1.6),
    color: colors.searchGray,
    marginRight: 5,
    fontFamily: Fonts.poppinsRegular,
  },
  valueText: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
    width: wp(62),
  },
  sectionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderRadius: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
    marginBottom: 10,
  },
  collapsibleContainer: {
    marginVertical: 5,
  },
  datePicker: {
    flexDirection: 'column',
    alignItems: 'stretch',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    // backgroundColor: '#F9F9F9',
    marginBottom: 5,
  },
  dateText: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
  },
  durationText: {
    fontSize: fp(1.8),
    color: colors.black,
    paddingHorizontal: 14,
    paddingVertical: 4,
    borderRadius: 10,
    textAlignVertical: 'center',
    textAlign: 'center',
    // marginRight: 5,
    fontFamily: Fonts.regular,
  },
  arrowIcon: {
    width: 20,
    height: 20,
    tintColor: colors.black,
    marginLeft: 7,
  },
  timeText: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: colors.black,
    marginTop: 5,
  },
  totalAmountContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  totalAmountText: {
    fontSize: 16,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  totalAmountValue: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  checkboxesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  checkboxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    marginRight: 10,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  checkboxSelected: {
    backgroundColor: colors.themeColor,
    borderColor: colors.themeColor,
  },
  checkMark: {
    color: colors.white,
    fontSize: fp(1.8),
  },
  checkboxLabel: {
    fontSize: fp(1.8),
    color: colors.txtGrey1,
    fontFamily: Fonts.medium,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    marginBottom: 15,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  otpInput: {
    width: wp(70),
    alignSelf: 'center',
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
    marginBottom: 10,
    width: '40%',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    // alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  saveButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
    marginBottom: 10,
    width: '40%',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
});

export default styles;
