import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Image,
  SafeAreaView,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import CountryPicker from 'react-native-country-picker-modal';
import RBSheet from 'react-native-raw-bottom-sheet';
import {
  useGetStudentStaticContentQuery,
  useSendOtpMutation,
  useSignUpMutation,
  useSocialSigninMutation,
} from '../../Api/ApiSlice';
import {PrimaryButton} from '../../Components/CustomButton';
import {AppHeader} from '../../Components/Header';
import {PrimaryInput} from '../../Components/Input';
import GenderSelector from '../../Components/selectGender';
import {SubTitle, Title} from '../../Components/Title';
import {showToast} from '../../Components/ToastHelper';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import styles from './styles';
import {AppLogo, TextLink} from '../../Components/Rest';
import {generateStyledHtmlContent, SCREEN_HEIGHT} from '../../Utils/constant';
import PrimaryDateTimePicker from '../../Components/PrimaryDateTimePicker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {StatusContainer} from '../../Components/StatusBar';
import StudentOrProfessional from '../../Components/StudentOrProfessional';
import {Fonts} from '../../Utils/Fonts';
import MobileNumberInput from '../../Components/MobileNumberInput';
import {useSelector} from 'react-redux';
import {maxLengthsByCountry} from '../../Utils/CountryCodeLength';
import RenderHTML from 'react-native-render-html';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import CustomDatePicker from '../../Components/CustomDatePicker';

const CreateYourAccount = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const {appLocale} = useSelector(state => state?.auth);
  const [isSignupLoading, setIsSignupLoading] = useState(false);
  const {
    mobileNumber = '',
    countryCodeParam = 'QA',
    callingCodeParam = '974',
    body,
  } = route?.params || '';

  const refRBSheet = useRef();
  const [sheetContent, setSheetContent] = useState('');
  const [fullName, setFullName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState(mobileNumber || '');
  const [email, setEmail] = useState('');
  const [selectedGender, setSelectedGender] = useState(null);
  const [studentOrProfessional, setStudentOrProfessional] = useState(null);
  console.log('🚀 ~ CreateYourAccount ~ body:', body);
  const [countryCode, setCountryCode] = useState(countryCodeParam);
  const [callingCode, setCallingCode] = useState(callingCodeParam);

  const [isPickerVisible, setIsPickerVisible] = useState(false);

  const [dob, setDob] = useState(null);

  const [userType, setUserType] = useState(null);

  const [isTermsAccept, setIsTermsAccept] = useState(false);
  const [isPrivacyAccept, setIsPrivacyAccept] = useState(false);
  console.log('userType in signup page 00--00--00--', userType);

  const userTypeStr =
    userType !== null && !isNaN(userType) ? userType.toString() : '';
  console.log(
    'userType in signup page 00--00--00--',
    typeof userTypeStr,
    userTypeStr,
  );

  const [langType, setLangType] = useState('');

  const [signUp, {isLoading}] = useSignUpMutation();
  const [socialSignin] = useSocialSigninMutation(); // Social Sign-In Mutation
  const [sendOtp, {isLoading: isSendOtpLoading}] = useSendOtpMutation();

  useEffect(() => {
    if (body) {
      if (Object.keys(body).length > 0) {
        setFullName(body?.name);
        setEmail(body?.email);
      }
    }
  }, [body]);
  const {
    data: staticDataTnc,
    isLoading: isStaticDataLoading,
    refetch,
  } = useGetStudentStaticContentQuery({
    pageType: 'terms-and-conditions',
    user: userType == 3 ? 'tutor' : userType == '2' ? 'parent' : 'user',
  });
  const {
    data: staticDataPrivacyPolicy,
    isLoading: isstaticDataPrivacyPolicy,
    refetch: refetchStaticData,
  } = useGetStudentStaticContentQuery({
    pageType: 'privacy-policy',
    user: userType == 3 ? 'tutor' : userType == '2' ? 'parent' : 'user',
  });
  useEffect(() => {
    refetch();
    refetchStaticData();
  }, []);
  const handleDateSelect = selectedDate => {
    console.log('🚀 ~ CreateYourAccount ~ selectedDate:', selectedDate);
    if (selectedDate) {
      const dateObject = selectedDate;
      if (!isNaN(dateObject.getTime())) {
        const today = new Date();
        let age = today.getFullYear() - dateObject.getFullYear();

        if (
          today.getMonth() < dateObject.getMonth() ||
          (today.getMonth() === dateObject.getMonth() &&
            today.getDate() < dateObject.getDate())
        ) {
          age--;
        }

        if (age < 18) {
          showToast('error', t('ageValid'), 'bottom', isRTL);
          return;
        }

        // If age is valid, set the date
        const formattedDate = dateObject.toISOString().split('T')[0];
        setDob(formattedDate);
      }
    }
  };

  const getLangType = async () => {
    const storedLangType = await AsyncStorage.getItem('selectedLang');
    return storedLangType ? JSON.parse(storedLangType) : '';
  };

  useEffect(() => {
    const fetchLangType = async () => {
      const type = await getLangType();
      setLangType(type);
    };
    fetchLangType();
  }, []);

  const logoSource =
    appLocale === 'ar' ? icons.taleemLogoSmall : icons.logondtext;

  const getUserType = async () => {
    const storedUserType = await AsyncStorage.getItem('userType');
    return storedUserType ? JSON.parse(storedUserType) : null;
  };

  useEffect(() => {
    const fetchUserType = async () => {
      const type = await getUserType();
      setUserType(type);
    };
    fetchUserType();
  }, []);

  const handleSignUp = async () => {
    setIsSignupLoading(true);

    if (!validateForm()) return;

    const userData = {
      name: fullName,
      email: email,
      mobile_no: phoneNumber,
      gender: selectedGender,
      student_type: studentOrProfessional || '1',
      user_type: userTypeStr,
      country_code: callingCode,
      ...(dob && {dob}),
      language: appLocale ? appLocale : 'en',
      social_id: body?.social_id,
    };

    try {
      const response = await signUp(userData).unwrap();

      console.log('response in signUp Api---', response);

      if (response.status === true) {
        showToast('success', response.message, 'bottom', isRTL);
        setIsSignupLoading(false);
        navigation.navigate('OtpVerificationScreen', {
          token: response.data.token,
          action: 'signup',
          userData: {
            name: fullName,
            email,
            mobile_no: phoneNumber,
            gender: selectedGender,
            country_code: callingCode,
          },
          handleSignUp,
        });
      } else {
        setIsSignupLoading(false);
        showToast('error', response?.message, 'bottom', isRTL);
      }
    } catch (error) {
      console.log('error in signUp Api---', error);
      setIsSignupLoading(false);
      showToast(
        'error',
        error.data?.message || 'Registration failed',
        'bottom',
        isRTL,
      ); //
    }
  };
  // const handleSocialLogin = async () => {
  //   try {
  //     if (!validateForm()) return;

  //     const socialLoginBody = {
  //       social_id: body?.social_id,
  //       name: fullName,
  //       email: email,
  //       mobile_no: phoneNumber,
  //       gender: selectedGender,
  //       student_type: studentOrProfessional || '1',
  //       user_type: userTypeStr,
  //       country_code: callingCode,
  //       ...(dob && {dob}),
  //       language: appLocale ? appLocale : 'en',
  //       login_type: body?.login_type,
  //       social_token: body?.social_token,
  //     };

  //     const response = await sendOtp({
  //       mobileNumber: phoneNumber,
  //       country_code: callingCode,
  //       language: appLocale ? appLocale : 'en',
  //       user_type: userTypeStr,
  //       action: 'signup',
  //     });
  //     console.log(
  //       '🚀 ~ handleSocialLogin ~ response: send otp api',
  //       JSON.stringify(response),
  //     );

  //     const {status, message} = response.data;

  //     if (status === true) {
  //       showToast('success', message);
  //       navigation.navigate('OtpVerificationScreen', {
  //         token: response?.data?.data?.token,
  //         action: 'signup',
  //         userData: {
  //           mobile_no: phoneNumber,
  //           country_code: callingCode,
  //         },
  //         socialLoginBody: socialLoginBody,
  //       });
  //     } else {
  //       showToast('error', message);
  //     }
  //   } catch (error) {
  //     console.error('Social Login Error:', error);
  //     showToast(
  //       'error',
  //       error.message || 'An error occurred during social login',
  //     );
  //   }
  // };

  const isValidFullName = name => {
    // const fullNameRegex = /^[A-Za-z]+(?: [A-Za-z]+)?$/;
    const fullNameRegex = /^[A-Za-z]+(?: [A-Za-z]+){0,3}$/;

    return fullNameRegex.test(name) && name.length <= 30;
  };

  const handleGenderChange = gender => {
    setSelectedGender(gender === 'male' ? '1' : '2');
  };
  const handleStudentOrProfessional = option => {
    setStudentOrProfessional(option === 'student' ? '1' : '2');
  };

  const isValidEmail = email => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateForm = () => {
    if (!fullName.trim()) {
      showToast('error', t('fullName_required'), 'bottom', isRTL);
      setIsSignupLoading(false);
      return false;
    }

    if (!isValidFullName(fullName)) {
      showToast('error', t('invalid_name'), 'bottom', isRTL);
      setIsSignupLoading(false);
      return false;
    }

    if (!phoneNumber.trim()) {
      showToast('error', t('phoneNumber_required'), 'bottom', isRTL);
      setIsSignupLoading(false);
      return false;
    } else if (!/^[0-9]{6,16}$/.test(phoneNumber)) {
      showToast('error', t('invalid_phoneNumber'), 'bottom', isRTL);
      setIsSignupLoading(false);
      return false;
    }

    if (email && !isValidEmail(email)) {
      showToast('error', t('invalid_email'), 'bottom', isRTL);
      setIsSignupLoading(false);
      return false;
    }

    if (!selectedGender) {
      showToast('error', t('gender_required'), 'bottom', isRTL);
      setIsSignupLoading(false);
      return false;
    }

    if (userType === 3) {
      if (!dob) {
        showToast('error', t('dob_required'), 'bottom', isRTL);
        setIsSignupLoading(false);
        return false;
      }
    }

    // if (!isTermsAccept) {
    //   showToast('error', t('error_terms_conditions'));
    //   return false;
    // }
    // if (!isPrivacyAccept) {
    //   showToast('error', t('error_privacy_policy'));
    //   return false;
    // }
    setIsSignupLoading(true);
    return true;
  };

  const openBottomSheet = type => {
    console.log('🚀 ~ openBottomSheet ~ type:', type);
    setSheetContent(type);
    refRBSheet.current.open();
  };

  const openCountryPicker = () => {
    setIsPickerVisible(true);
  };

  function handleTermsAccept(sheetContent) {
    if (sheetContent == 'terms') {
      setIsTermsAccept(true);
    } else if (sheetContent == 'privacy') {
      setIsPrivacyAccept(true);
    }
    refRBSheet.current.close();
  }

  function handleTermsClose(sheetContent) {
    // if (sheetContent == 'terms') {
    //   setIsPrivacyAccept(false);
    // } else if (sheetContent == 'privacy') {
    //   setIsTermsAccept(false);
    // }

    refRBSheet.current.close();
  }
  //   const styledHtmlContent = `
  //   <html>
  //     <head>
  //       <style>
  //         body {
  //           color: #333;
  //           font-family: Arial, sans-serif;
  //           justify-content: ${!staticDataTnc?.data?.content ? 'center' : 'left'}
  //         }
  //         p { color: #555; }
  //         h1, h2, h3, h4, h5, h6 { color: #222; }
  //         strong { color: #000; }
  //         ul { color: #666; }
  //       </style>
  //     </head>
  //     <body style="${
  //       !staticDataTnc?.data?.content
  //         ? 'display: flex; align-items: center; justify-content: center; height: 100vh; text-align: center;'
  //         : 'text-align: left;'
  //     }">
  //       ${staticDataTnc?.data?.content || t('noData')}
  //     </body>
  //   </html>
  // `;
  console.log('🚀 ~ CreateYourAccount ~ staticDataTnc:', staticDataTnc);

  const renderSheetContent = () => {
    if (sheetContent === 'terms') {
      const htmlContent = generateStyledHtmlContent(staticDataTnc, t);
      return (
        // <>
        //   <Title
        //     text={t('termsOfService')}
        //     style={{fontSize: 16, color: colors.darkBlack}}
        //   />
        //   <Title
        //     text={t('welcomeText')}
        //     style={{fontSize: 15, color: colors.themeColor}}
        //   />
        //   <View style={styles.termsContainer}>
        //     <Text style={styles.termServicetxt}>{t('termsContent1')}</Text>
        //     <Text style={[styles.termServicetxt]}>{t('termsContent2')}</Text>
        //     <Text style={styles.termServicetxt}>{t('termsContent3')}</Text>
        //   </View>
        // </>
        <ScrollView contentContainerStyle={{paddingBottom: 20}}>
          <RenderHTML
            contentWidth={'100%'}
            source={{html: htmlContent}}
            baseStyle={{
              color: 'black',
              fontFamily: Fonts.medium,
              direction: isRTL ? 'rtl' : 'ltr',
            }}
            tagsStyles={{
              p: {color: 'gray', direction: isRTL ? 'rtl' : 'ltr'},
              h1: {
                color: 'black',
                fontFamily: fp(1.2),
                direction: isRTL ? 'rtl' : 'ltr',
              },
              h2: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h3: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h4: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h5: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h6: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              strong: {color: 'black'},
              ul: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              li: {color: 'gray', direction: isRTL ? 'rtl' : 'ltr'},
              body: {marginHorizontal: wp(6), direction: isRTL ? 'rtl' : 'ltr'},
            }}
          />
        </ScrollView>
      );
    } else if (sheetContent === 'privacy') {
      const htmlContent = generateStyledHtmlContent(staticDataPrivacyPolicy, t);
      return (
        <ScrollView contentContainerStyle={{paddingBottom: 20}}>
          <RenderHTML
            key={'privacy'}
            contentWidth={'100%'}
            source={{html: htmlContent}}
            baseStyle={{
              color: 'black',
              fontFamily: Fonts.medium,
              direction: isRTL ? 'rtl' : 'ltr',
            }}
            tagsStyles={{
              p: {color: 'gray', direction: isRTL ? 'rtl' : 'ltr'},
              h1: {
                color: 'black',
                fontFamily: fp(1.2),
                direction: isRTL ? 'rtl' : 'ltr',
              },
              h2: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h3: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h4: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h5: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              h6: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              strong: {color: 'black'},
              ul: {color: 'black', direction: isRTL ? 'rtl' : 'ltr'},
              li: {color: 'gray', direction: isRTL ? 'rtl' : 'ltr'},
              body: {marginHorizontal: wp(6), direction: isRTL ? 'rtl' : 'ltr'},
            }}
          />
          {/* <Title
            text={t('privacyPolicy')}
            style={{fontSize: 16, color: colors.darkBlack}}
          />
          <Title
            text={t('welcomeText')}
            style={{fontSize: 15, color: colors.themeColor}}
          />
          <View style={styles.termsContainer}>
            <Text style={styles.termServicetxt}>{t('privacyContent1')}</Text>
            <Text style={styles.termServicetxt}>{t('privacyContent2')}</Text>
            <Text style={styles.termServicetxt}>{t('privacyContent3')}</Text>
          </View> */}
        </ScrollView>
      );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.white} />
      <AppHeader
        isWhite={true}
        backIcon={icons.backIcon}
        isBackBtn
        title={''}
      />

      <AppLogo langType={i18n.language} />
      {/* <Image
          source={logoSource}
          style={styles.logoStyle}
          resizeMode="contain"
        /> */}

      <Title text={t('createAccount')} isRTL={isRTL} />
      <SubTitle text={t('continueLearning')} isRTL={isRTL} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 20}}
        style={styles.mainContainer}>
        <PrimaryInput
          title={t('fullName')}
          keyboardType="default"
          containerStyle={{width: '100%'}}
          placeholder={t('enter_full_name')}
          value={fullName}
          onChangeText={text => setFullName(text)}
          placeholderTextColor={colors.txtGrey1}
          maxLength={30}
          editable={!(body && body.name)}
        />
        {/* <Text style={styles.headerTxt}>{t('phoneNumber')}</Text> */}

        {/* <View style={styles.pinContainer}>
          <TouchableOpacity
            style={styles.phoneCode}
            onPress={openCountryPicker}>
            <CountryPicker
              countryCode={countryCode}
              withCallingCode
              withFlag
              withFilter
              onSelect={country => {
                setCountryCode(country.cca2);
                setCallingCode(country.callingCode[0]);
              }}
              visible={isPickerVisible}
              onClose={() => setIsPickerVisible(false)}
            />
            <Text style={styles.callingCodeText}>+{callingCode}</Text>
          </TouchableOpacity>

          <TextInput
            placeholder={t('enternumber')}
            keyboardType="numeric"
            style={styles.mobileInput}
            value={phoneNumber}
            onChangeText={text => setPhoneNumber(text)}
            maxLength={callingCode == 91 ? 10 : 15}
            placeholderTextColor={colors.greyLight}
          />
        </View> */}

        <MobileNumberInput
          title={`${t('mobile_Txt')}*`}
          value={phoneNumber}
          onChange={setPhoneNumber}
          callingCode={callingCode}
          onCodeChange={setCallingCode}
          onCountryCodeChange={setCountryCode}
          countryCode={countryCode}
          placeholder={t('enter_number_Txt')}
          containerStyle={{width: '100%'}}
          maxLength={maxLengthsByCountry[countryCode]}
          placeholderTextColor={colors.txtGrey1}
          countryCodeProp={countryCodeParam}
          callingCodeProp={callingCodeParam}
        />

        <PrimaryInput
          title={t('email')}
          keyboardType="email-address"
          containerStyle={{width: '100%'}}
          placeholder={t('placeholder_email')}
          value={email}
          onChangeText={text => setEmail(text)}
          placeholderTextColor={colors.txtGrey1}
          editable={!(body && body.email)}
        />

        <GenderSelector onGenderSelect={handleGenderChange} />
        {userType === 1 && (
          <StudentOrProfessional
            onStudentOrProfessional={handleStudentOrProfessional}
          />
        )}

        {userType === 3 && (
          <PrimaryDateTimePicker
            title={`${t('dateOfBirth')}*`}
            date={dob}
            onDateSelect={handleDateSelect}
            containerStyle={{width: '100%'}}
          />
        )}

        <View style={styles.txtContainer}>
          <Text style={styles.signinTxt}>{t('agreeTerms')}</Text>
          <View style={styles.rowContainer}>
            <Text style={styles.signinTxt}> {t('our')} </Text>
            <TouchableOpacity onPress={() => openBottomSheet('terms')}>
              <Text style={[styles.signinTxt, {color: colors.themeColor}]}>
                {t('termsOfService')}
              </Text>
            </TouchableOpacity>
            <Text style={styles.signinTxt}> & </Text>
            <TouchableOpacity>
              <Text
                style={[styles.signinTxt, {color: colors.themeColor}]}
                onPress={() => openBottomSheet('privacy')}>
                {t('privacyPolicy')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <PrimaryButton
          // onPress={() => {
          //   Object.keys(body).length > 0 ? handleSocialLogin() : handleSignUp();
          // }}
          onPress={() => {
            handleSignUp();
          }}
          title={t('signUp')}
          style={{width: '100%'}}
          loading={isSignupLoading}
          disabled={isSignupLoading}
        />
        <TextLink
          onPress={() => navigation.navigate('Login')}
          title={t('alreadyHaveAccount')}
          linkText={t('logIn')}
          style={{marginTop: SCREEN_HEIGHT / 25}}
        />
      </ScrollView>

      <RBSheet
        ref={refRBSheet}
        height={500}
        openDuration={250}
        customStyles={{container: {padding: 5}}}>
        {renderSheetContent()}
        <PrimaryButton
          onPress={() => handleTermsClose(sheetContent)}
          title={t('cancel')}
          // style={{backgroundColor: colors.white}}
          textStyle={{
            fontSize: 16,
            fontFamily: Fonts.regular,
            // color: colors.themeColor,
          }}
          style={{bottom: 20}}
          loading={isLoading}
        />
        {/* 
        <PrimaryButton
          onPress={() => handleTermsAccept(sheetContent)}
          title={t('iAgree')}
          textStyle={{
            fontSize: 16,
            fontFamily: Fonts.regular,
            color: colors.white,
          }}
        /> */}
      </RBSheet>
    </SafeAreaView>
  );
};

export default CreateYourAccount;
