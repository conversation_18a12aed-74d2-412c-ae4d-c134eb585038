import {Dimensions, Platform, StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize, SCREEN_HEIGHT} from '../../Utils/constant';
import {fp, hp} from '../../Helper/ResponsiveDimensions';

const deviceWidth = Dimensions.get('window').width;
const deviceHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  mainContainer: {
    paddingHorizontal: 20,
  },

  logoContainer: {
    alignItems: 'flex-start',
    marginVertical: 10,
    paddingLeft: 20,
  },

  logoStyle: {width: 80, height: 40},

  headerTxt: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    marginBottom: 5,
    color: colors.black,
  },

  button: {
    width: deviceWidth * 0.95,
    marginBottom: 10,
  },
  termServicetxt: {
    marginLeft: 20,
    fontSize: responsiveFontSize(14),
    color: colors.txtGrey1,
    marginBottom: 30,
    // marginTop: 18,
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  termsContainer: {
    marginTop: hp(2),
  },

  signinTxt: {
    fontSize: fp(1.64),
    color: 'gray',
    // textAlign: 'center',
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },

  txtContainer: {alignItems: 'center', marginBottom: 20, marginTop: 10},

  signinTxt1: {
    fontSize: responsiveFontSize(14),
    fontFamily: Fonts.medium,
    textAlign: 'center',
  },

  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  termsStyle: {
    fontSize: 14,
    color: colors.themeColorlight,
    textAlign: 'center',
    fontFamily: Fonts.medium,
  },

  pinContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    marginBottom: 10,
  },
  phoneCode: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    borderWidth: 1,
    paddingHorizontal: 10,
    paddingVertical: Platform.OS == 'ios' ? 4 : 15,
    borderColor: colors.lightgreay,
    borderRadius: 8,
    marginRight: 10,
  },
  callingCodeText: {
    fontSize: responsiveFontSize(14),
    marginLeft: 5,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  mobileInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.lightgreay,
    borderRadius: 8,
    padding: 15,
    color: colors.txtGrey1,
  },
});

export default styles;
