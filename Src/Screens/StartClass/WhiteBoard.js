import {View, Text, SafeAreaView, Alert} from 'react-native';
import React, {useEffect, useState} from 'react';
import {
  WhiteboardView,
  Room,
  RoomPlayer,
  SDK,
  SDKConfig,
} from '@netless/react-native-whiteboard';
import {appIdentifier, getAgoraRoomDetails} from '../../Utils/utils';
import {styles} from './styles';
import {PrimaryButton} from '../../Components/CustomButton';
import {useGetAgoraRoomNetlessQuery} from '../../Api/ApiSlice';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {useSelector} from 'react-redux';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {wp} from '../../Helper/ResponsiveDimensions';

const sdkParams = {
  log: true,
  userCursor: true,
  appIdentifier,
  useMultiViews: true,
};

const WhiteBoard = ({route, navigation}) => {
  const {user_type} = useSelector(state => state?.auth);
  const {whiteBoardToken, userName = 'tutor', whiteBoardUUID} = route?.params;
  // const whiteBoardToken =
  //   'NETLESSROOM_YWs9NHY2TEpRN0JtZUtNYjNEUyZleHBpcmVBdD0xNzQ0MDEyMjI5MjE4Jm5vbmNlPTE3NDQwMDgzMjkyMTgwMCZyb2xlPTAmc2lnPWQ0ZGQ1YWUzNjNlMmM4NTMxMzU2ZGM1ZmU4MmY2N2Y1OGZmM2NjY2I2NWY5YzAwYmZiN2VkMmQzZGYzYWYwOWEmdXVpZD0zNzMxMjFiMDEzN2ExMWYwOTAwYzdiY2Y2MDdmZmE1ZA';
  // const whiteBoardUUID = '373121b0137a11f0900c7bcf607ffa5d';
  // const userName = 'tutor';
  const [activeTool, setActiveTool] = React.useState(
    user_type == '3' ? 'pen' : null, // Set active tool only for userType 3 (tutor)
  );
  const [room, setRoom] = useState(undefined);
  const [isRoomInitialized, setIsRoomInitialized] = useState(false); // Add loading state

  const roomConfig = {
    uuid: whiteBoardUUID,
    uid: userName,
    region: 'in-mum',
    roomToken: whiteBoardToken,
  };

  console.log(
    '🚀 ~ WhiteBoard ~ roomConfig:',
    roomConfig,
    'userType:',
    user_type,
  );

  const joinRoomCallback = React.useCallback(
    (aRoom, aSdk, error) => {
      console.log('🚀 ~ joinRoomCallback ~ error:', error);
      console.log('🚀 ~ joinRoomCallback ~ aSdk:', aSdk);
      console.log('🚀 ~ joinRoomCallback ~ aRoom:', JSON.stringify(aRoom));

      if (error) {
        console.log(error);
        return;
      }

      setRoom(aRoom); // Update room state
      setIsRoomInitialized(true); // Mark room as initialized

      // Reset tool state for userType 1 (viewer)
      if (user_type === '1') {
        aRoom?.setMemberState({
          currentApplianceName: null, // Disable any active tool
        });
      }

      aRoom?.addMagixEventListener('*', event => {
        console.log('Whiteboard Event:', event);
      });
    },
    [user_type],
  ); // Add user_type as a dependency

  const changeTool = async tool => {
    if (!room) {
      Alert.alert('Error', 'Whiteboard room is not initialized.');
      return;
    }

    // Prevent tool changes for userType 1 (viewer)
    if (user_type === '1') {
      console.log('UserType 1 cannot change tools.');
      return;
    }

    console.log('Changing tool to:', tool); // Debug log

    try {
      // Clear the previous state
      await room.setMemberState({
        currentApplianceName: null, // Reset the tool state
      });

      // Switch between Pen and Eraser based on the selected tool
      if (tool === 'pen') {
        await room.setMemberState({
          currentApplianceName: 'pencil', // Use 'pencil' instead of 'pen'
          strokeColor: [0, 0, 0], // Black color
          strokeWidth: 4, // Pen stroke width
        });
        console.log('Pen tool activated'); // Debug log
      } else if (tool === 'eraser') {
        await room.setMemberState({
          currentApplianceName: 'eraser',
          strokeWidth: 8, // Eraser stroke width (adjust as needed)
        });
        console.log('Eraser tool activated'); // Debug log
      }

      setActiveTool(tool); // Update active tool state
      console.log('Active tool updated to:', tool); // Debug log
    } catch (error) {
      console.error('Error changing tool:', error);
    }
  };

  console.log(activeTool, 'active Tool');

  return (
    <SafeAreaView style={{flex: 1}}>
      <AppHeader backIcon={icons.backbtn} isBackBtn title={'WhiteBoard'} />
      <WhiteboardView
        style={styles.whiteboard}
        sdkConfig={sdkParams}
        roomConfig={roomConfig}
        joinRoomCallback={joinRoomCallback}
      />

      {/* Show tool buttons only for userType 3 (tutor) */}
      {user_type === '3' && (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 10,
            gap: wp(1.4),
          }}>
          <PrimaryButton
            title={'Pen'}
            onPress={() => changeTool('pen')}
            style={{width: '45%'}}
            disabled={!isRoomInitialized} // Disable button until room is initialized
          />
          <PrimaryButton
            title={'Eraser'}
            onPress={() => changeTool('eraser')}
            style={{width: '45%'}}
            disabled={!isRoomInitialized} // Disable button until room is initialized
          />
        </View>
      )}
    </SafeAreaView>
  );
};

export default WhiteBoard;
