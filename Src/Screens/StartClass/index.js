import {
  View,
  Text,
  PermissionsAndroid,
  Platform,
  Alert,
  SafeAreaView,
  Image,
  StyleSheet,
  Modal,
  TouchableOpacity,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import {styles} from './styles';
import {useTranslation} from 'react-i18next';
import {PrimaryButton} from '../../Components/CustomButton';
import {getAgoraRoomDetails, appIdentifier} from '../../Utils/utils';
import AgoraUIKit from 'agora-rn-uikit';
import RtcEngine, {
  ChannelProfile,
  ClientRole,
  FrameHeight,
  VideoSourceType,
} from 'react-native-agora';
import {baseUrl, useGenerateAgoraTokenMutation} from '../../Api/ApiSlice';
import {connect, useSelector} from 'react-redux';
import {io} from 'socket.io-client';
import Toast from 'react-native-toast-message';
import {showToast} from '../../Components/ToastHelper';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {responsiveFontSize} from '../../Utils/constant';
import {useSocket} from '../../Helper/SocketHelper/SocketProvider';
import {isEndTimeReached} from '../../Helper/DateHelpers/DateHelpers';
// const socket = io(baseUrl);
const StartClassScreen = ({navigation, route}) => {
  const {socket, emit, on, off} = useSocket();
  const {
    lastItemId = '',
    id = '',
    tutor = '',
    bookingId = '',
    agoraToken,
    whiteBoardToken,
    userName,
    channelName,
    whiteBoardUUID,
    end_time,
  } = route?.params || {};
  console.log('🚀 ~ StartClassScreen ~ end_time:', end_time);
  console.log('🚀 ~ StartClassScreen ~ id:', id);
  const [isCallEnded, setIsCallEnded] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(null);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [timer, setTimer] = useState(10);
  const [roomConfig, setRoomConfig] = React.useState({});
  const [isConnecting, setIsConnecting] = useState(false);
  const [roomToken, setRoomToken] = useState('');
  const [videoCall, setVideoCall] = useState(true);
  const [roomDataWhiteBoard, setRoomDataWhiteBoard] = useState();
  const [rtcEngine, setRtcEngine] = useState(null);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isWhiteBoardModal, setIsWhiteBoardModal] = useState(false);

  // const [tutorWhiteBoardStatus, setTutorWhiteBoardStatus] = useState(false);
  const tutorWhiteBoardStatus = useRef(false);
  // const [connectionData, setConnectionData] = useState();
  const connectionData = {
    appId: 'd47521f39a41483294d6936c01842a1b',
    channel: channelName,
    // channel: 'channel2',
    uid: 0,
    token: agoraToken,
  };
  const {user_type} = useSelector(state => state?.auth);
  const initSocketRoom = () => {
    emit('joinVideoRoom', {room_id: id});
  };

  // const checkEndTime = () => {
  //   if (!end_time) return;

  //   const currentTime = new Date();
  //   const endTime = new Date(end_time);

  //   if (isEndTimeReached(new Date(), end_time)) {
  //     setVideoCall(false);
  //     setIsCallEnded(true);
  //     setTimeRemaining(null);
  //     if (id === lastItemId && user_type == '1') {
  //       showToast('warning', 'Class Duration Ends..');
  //       navigation.navigate('RatingScreen', {
  //         bookingId: bookingId,
  //         tutor,
  //       });
  //     } else {
  //       showToast('warning', 'Class Duration Ends..');
  //       navigation.navigate('My Class');
  //     }
  //     // Navigation logic...
  //   }
  // };

  useEffect(() => {
    // if (socket) {
    // Initialize chat on component mount
    console.log('room initiated');
    initSocketRoom();

    on('error_callback', data => {
      showToast('error', data?.message, 'bottom', isRTL);
    });

    on('tutorWhiteBoardStatus', item => {
      tutorWhiteBoardStatus.current = item?.white_board_status;
      if (user_type == '1' && item?.white_board_status == true) {
        console.log(user_type, tutorWhiteBoardStatus);
        navigation.navigate('WhiteBoard', {
          whiteBoardUUID: whiteBoardUUID,
          whiteBoardToken: whiteBoardToken,
          userName: userName,
        });
      }
      // setTutorWhiteBoardStatus(item?.white_board_status);
      console.log('socket item tutor white baord status', item);
    });
    // Clean up socket listeners on unmount to avoid memory leaks
    // return () => {
    //   socket.off('joinVideoRoom');
    //   socket.off('tutorWhiteBoardStatus');
    //   socket.off('error_callback');
    // };
    // }
  }, []);
  console.log(`🚀 ~ StartClassScreen ~ {}:`, {
    id,
    tutor,
    bookingId,
    agoraToken,
    whiteBoardToken,
    userName,
    channelName,
    whiteBoardUUID,
    end_time,
  });
  const [
    generateAgoraToken,
    {data: agoraRoomData, error: tokenError, isLoading: roomLoading},
  ] = useGenerateAgoraTokenMutation();

  const updateTutorWhiteboardStatus = () => {
    // initChat();
    if (!id) {
      showToast('error', t('something_went_wrong'), 'bottom', isRTL);
      return;
    }
    emit('tutorWhiteBoardStatus', {
      room_id: id,
      white_board_status: true,
    });
  };

  // useEffect(() => {
  //   const roomConfig = {
  //     type: 'rtc', // rtc
  //     uid: 0,
  //     channelName: 'channel1',
  //     role: 'publisher',
  //     expirationTime: '3600',
  //     tokenType: 'uid',
  //   };
  //   generateAgoraToken(roomConfig)
  //     .unwrap()
  //     .then(response => {
  //       console.log('🚀 ~ handleBookTutor ~ response:', response?.data?.token);
  //       setRoomToken(response?.data?.token);
  //       setConnectionData({
  //         appId: 'd47521f39a41483294d6936c01842a1b',
  //         channel: roomConfig?.channelName,
  //         uid: roomConfig?.uid,
  //         token: response?.data?.token,
  //       });
  //       // showToast('success', response?.message);
  //     })

  //     .catch(err => {
  //       console.error('Error calculating price:', err);
  //       showToast('error', 'Booking Failed. Please try again.');
  //     });
  // }, []);

  // useEffect(() => {
  //   if (user_type == '1' && tutorWhiteBoardStatus.current == true) {
  //     console.log(user_type, tutorWhiteBoardStatus);
  //     navigation.navigate('WhiteBoard', {
  //       whiteBoardUUID: whiteBoardUUID,
  //       whiteBoardToken: whiteBoardToken,
  //       userName: userName,
  //     });
  //   }
  // }, [tutorWhiteBoardStatus.current]);

  const rtcCallbacks = {
    EndCall: () => {
      setVideoCall(false);
      if (id === lastItemId && user_type == '1')
        navigation.navigate('RatingScreen', {
          bookingId: bookingId,
          tutor,
        });
      else navigation.navigate('My Class');
    },
    Error: error => {
      Alert.alert('Error', `Code: ${error}`);
      setIsConnecting(false);
    },
    JoinChannelSuccess: () => {
      setIsConnecting(false);
    },
  };
  useEffect(() => {
    const interval = setInterval(() => {
      setTimer(prevTimer => {
        if (prevTimer > 0) {
          return prevTimer - 1;
        }
      });
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // Add useEffect to check time periodically
  // useEffect(() => {
  //   // Check immediately on mount
  //   checkEndTime();

  //   // Set up interval to check every second
  //   const timeCheckInterval = setInterval(() => {
  //     checkEndTime();
  //   }, 1000);

  //   // Cleanup interval on unmount
  //   return () => clearInterval(timeCheckInterval);
  // }, [end_time, isCallEnded]);

  function handleCancelModal() {
    console.log('cancel Modal');
  }
  return (
    <SafeAreaView style={{flex: 1}}>
      {/* Top Button */}
      <View
        style={{
          padding: 10,
          alignItems: 'center',
        }}>
        <PrimaryButton
          title={t('openWhiteBoard')}
          style={styles.button}
          textStyle={styles.btnTxt}
          // onPress={() => {
          //   setVideoCall(false);

          //   isScreenSharing ? stopScreenShare() : startScreenShare();
          // }}
          onPress={() => {
            // setVideoCall(false);
            updateTutorWhiteboardStatus();
            navigation.navigate('WhiteBoard', {
              whiteBoardUUID: whiteBoardUUID,
              whiteBoardToken: whiteBoardToken,
              userName: userName,
            });
          }}
        />
      </View>
      {/* {timeRemaining !== null && (
        <View style={styles.warningContainer}>
          <Text>Call ending in {timeRemaining} seconds</Text>
        </View>
      )} */}
      {/* Video Call */}
      <View style={{flex: 1}}>
        {videoCall && (
          <AgoraUIKit
            connectionData={connectionData}
            // connectionData={tempConnectionData}
            rtcCallbacks={rtcCallbacks}
            rtcProps={{
              ...connectionData,
              enabledPermission: true,
            }}
          />
        )}
      </View>
      {/* {videoCall && (
        <AgoraUIKit
          connectionData={connectionData}
          rtcCallbacks={rtcCallbacks}
          rtcProps={{
            ...connectionData,
            enabledPermission: true,
          }}
        />
      )} */}
      {/* {timer > 0 ? (
        <View style={{alignItems: 'center'}}>
          <Text style={styles.txt}>{t('waitingForStudent')}</Text>
          <Text style={styles.timer}>{timer}</Text>
        </View>
      ) : (
        <>
          {isConnecting && (
            <AgoraUIKit
              connectionData={connectionData}
              rtcCallbacks={rtcCallbacks}
              rtcProps={{
                ...connectionData,
                enabledPermission: true,
              }}
            />
          )}
        </>
      )} */}

      {/* {!timer > 0 && (
        <View style={styles.bottomBtn}>
          <PrimaryButton
            title={'End Class'}
            style={styles.button}
            textStyle={styles.btnTxt}
            onPress={() => setIsConnecting(true)}
          />
        </View>
      )} */}

      <Modal
        animationType="fade"
        transparent={true}
        visible={isWhiteBoardModal}
        onRequestClose={handleCancelModal}>
        <View style={logoutModalStyles.centeredView}>
          <View style={logoutModalStyles.modalView}>
            <Text style={logoutModalStyles.modalTitle}>
              {t('confirmCancel')}
            </Text>

            {/* <Text style={logoutModalStyles.modalText}>{t('reason')}</Text> */}
            {/* <TextInput
              style={logoutModalStyles.input}
              placeholder={t('typeHere')}
              value={reason}
              onChangeText={newText => setReason(newText)}
            /> */}

            <View style={logoutModalStyles.buttonContainer}>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  logoutModalStyles.cancelButton,
                ]}
                onPress={handleCancelModal}>
                <Text style={logoutModalStyles.cancelButtonText}>
                  {t('close')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  logoutModalStyles.logoutButton,
                ]}
                onPress={() => {
                  console.log('asdlkjfa');
                }}>
                <Text style={logoutModalStyles.logoutButtonText}>
                  {t('cancelBooking')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>

    // </>
  );
};

export default StartClassScreen;

const logoutModalStyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    // alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    // alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: 15,
    // textAlign: 'center',
    fontSize: 18,
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  modalText: {
    marginBottom: 8,
    fontFamily: Fonts.medium,
    color: colors.black,
    fontSize: fp(1.6),
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    marginHorizontal: 10,
    minWidth: 100,
  },
  cancelButton: {
    backgroundColor: colors.txtGrey,
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    color: 'black',
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    textAlign: 'center',
  },
  logoutButtonText: {
    color: 'white',
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: 'grey',
    borderWidth: 2,
    width: wp(70),
    paddingHorizontal: 8,
    marginBottom: 16,
    borderRadius: fp(1),
    fontFamily: Fonts.medium,
  },
  text: {
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
});
