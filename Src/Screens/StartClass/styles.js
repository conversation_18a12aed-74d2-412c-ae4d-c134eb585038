import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize} from '../../Utils/constant';
import {fp} from '../../Helper/ResponsiveDimensions';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
    justifyContent: 'center',
    alignItems: 'center',
  },
  txt: {
    fontSize: responsiveFontSize(20),
    color: colors.white,
    fontWeight: 'bold',
  },
  timer: {
    fontSize: responsiveFontSize(48),
    color: colors.white,
    fontWeight: 'bold',
  },
  bottomBtn: {
    position: 'absolute',
    bottom: 20,
    width: '90%',
  },
  button: {
    backgroundColor: colors.white,
  },
  btnTxt: {
    color: colors.themeColor,
    fontSize: fp(1.6),
  },
  whiteboard: {
    aspectRatio: 16.0 / 9.0,
    flexGrow: 1,
  },
});
