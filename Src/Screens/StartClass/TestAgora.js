import React, {useEffect, useState} from 'react';
import {View, Button, StyleSheet, TouchableOpacity, Text} from 'react-native';
import RtcEngine, {
  RenderModeType,
  RtcLocalView,
  RtcRemoteView,
  RtcSurfaceView,
  VideoRenderMode,
  VideoSourceType,
} from 'react-native-agora';
const TestAgora = () => {
  const [engine, setEngine] = useState(null);
  const [remoteUsers, setRemoteUsers] = useState([]);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isCameraFront, setIsCameraFront] = useState(true); // Track camera state

  console.log('RtcLocalView', RtcLocalView);
  useEffect(() => {
    const initAgora = async () => {
      const agoraEngine = await RtcEngine.create(
        'd47521f39a41483294d6936c01842a1b',
      );
      await agoraEngine.enableVideo();
      setEngine(agoraEngine);
      agoraEngine.addListener('UserJoined', uid => {
        setRemoteUsers(prevUsers => [...prevUsers, uid]);
      });
      agoraEngine.addListener('UserOffline', uid => {
        setRemoteUsers(prevUsers => prevUsers.filter(user => user !== uid));
      });
      await agoraEngine.joinChannel('', 'testChannel', null, 0);
    };
    initAgora();
    return () => {
      if (engine) {
        engine.destroy();
      }
    };
  }, []);
  // Start Screen Sharing
  const startScreenShare = async () => {
    try {
      await engine.startScreenCapture(); // Start screen capture
      setIsScreenSharing(true);
    } catch (error) {
      console.error('Error starting screen share:', error);
    }
  };
  // Stop Screen Sharing
  const stopScreenShare = async () => {
    try {
      await engine.stopScreenCapture(); // Stop screen capture
      setIsScreenSharing(false);
    } catch (error) {
      console.error('Error stopping screen share:', error);
    }
  };
  // Mute/Unmute Microphone
  const toggleMute = async () => {
    try {
      await engine.muteLocalAudioStream(!isMuted); // Mute or Unmute
      setIsMuted(!isMuted);
    } catch (error) {
      console.error('Error toggling mute:', error);
    }
  };
  // Switch Camera
  const switchCamera = async () => {
    try {
      await engine.switchCamera(); // Switch between front and back camera
      setIsCameraFront(!isCameraFront);
    } catch (error) {
      console.error('Error switching camera:', error);
    }
  };
  // Leave the channel (disconnect the call)
  const leaveChannel = async () => {
    try {
      await engine.leaveChannel(); // Leave the channel
      setRemoteUsers([]); // Clear remote users
    } catch (error) {
      console.error('Error leaving channel:', error);
    }
  };
  return (
    <View style={styles.container}>
      {/* Local User Video */}
      <RtcSurfaceView
        style={styles.localVideo}
        channelId="testChannel"
        // renderMode={VideoRenderMode.Hidden}
      />
      <RtcSurfaceView
        style={styles.videoView}
        canvas={{
          uid: 0,
          sourceType: VideoSourceType.VideoSourceScreen,
          renderMode: RenderModeType.RenderModeFit,
        }}
      />
      {/* Remote Users Video */}
      <View style={styles.remoteContainer}>
        {remoteUsers.map(uid => (
          <RtcRemoteView.SurfaceView
            key={uid}
            style={styles.remoteVideo}
            uid={uid}
            channelId="testChannel"
            // renderMode={VideoRenderMode.Hidden}
          />
        ))}
      </View>
      {/* Call Control Buttons */}
      <View style={styles.controls}>
        {/* Screen Share Button */}
        <TouchableOpacity
          onPress={isScreenSharing ? stopScreenShare : startScreenShare}
          style={styles.controlButton}>
          <Text>
            {isScreenSharing ? 'Stop Screen Share' : 'Start Screen Share'}
          </Text>
        </TouchableOpacity>
        {/* Mute/Unmute Button */}
        <TouchableOpacity onPress={toggleMute} style={styles.controlButton}>
          <Text>{isMuted ? 'Unmute' : 'Mute'}</Text>
        </TouchableOpacity>
        {/* Switch Camera Button */}
        <TouchableOpacity onPress={switchCamera} style={styles.controlButton}>
          <Text>
            {isCameraFront ? 'Switch to Back Camera' : 'Switch to Front Camera'}
          </Text>
        </TouchableOpacity>
        {/* Leave Call Button */}
        <TouchableOpacity onPress={leaveChannel} style={styles.controlButton}>
          <Text>Leave Call</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  localVideo: {
    flex: 1,
  },
  remoteContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
  },
  remoteVideo: {
    width: 120,
    height: 120,
    margin: 5,
  },
  controls: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
    flexDirection: 'row', // Align controls in a row
    justifyContent: 'space-around', // Space them out evenly
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  controlButton: {
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 5,
    flex: 1,
    alignItems: 'center', // Center the text inside buttons
    justifyContent: 'center',
  },
});
export default TestAgora;
