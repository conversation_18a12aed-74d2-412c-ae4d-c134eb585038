import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  Pressable,
  FlatList,
  Image,
  ActivityIndicator,
  I18nManager,
  ScrollView,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {AppHeader} from '../../Components/Header';
import {showToast} from '../../Components/ToastHelper';
import styles from './styles';
import icons from '../../Utils/icons';
import {SubTitle, Title} from '../../Components/Title';
import colors from '../../Utils/colors';
import {PrimaryButton} from '../../Components/CustomButton';
import {StatusContainer} from '../../Components/StatusBar';
import {
  useChooseYourGradeQuery,
  useStudentAcademicMutation,
} from '../../Api/ApiSlice';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {FrameWidth} from 'react-native-agora';
import {updateBookingFlowType} from '../../Redux/Slices/Student/TutorBookingSlice';
import {useDimensionsChange} from 'react-native-responsive-dimensions';
import {useDispatch} from 'react-redux';

const Chooseyourgrade = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [selectedId, setSelectedId] = useState(null);
  const [selectedButton, setSelectedButton] = useState(null);
  const [expandedContainer, setExpandedContainer] = useState(null);

  const {data: gradeData, error, isLoading} = useChooseYourGradeQuery();

  useEffect(() => {
    console.log('Loading grade:', isLoading);
    console.log('Error grade:', error);
    console.log('Grade Data:', JSON.stringify(gradeData?.data?.rows));
  }, [gradeData, error, isLoading]);

  const handleGradeSelect = useCallback(id => {
    setSelectedId(id);
    setSelectedButton(null);
    setExpandedContainer(id);
  }, []);

  const handleClassSelect = useCallback(index => {
    setSelectedButton(index);
  }, []);

  // const renderItem = useCallback(
  //   ({item, index}) => {
  //     const isSelected = selectedButton === index;
  //     return (
  //       <Pressable
  //         activeOpacity={0.8}
  //         style={[
  //           styles.button,
  //           {
  //             backgroundColor: isSelected ? colors.themeColor : colors.white,
  //             borderColor: isSelected ? colors.white : colors.txtGrey,
  //           },
  //         ]}
  //         onPress={() => handleClassSelect(index)}>
  //         <Text
  //           style={[styles.buttonText, {color: isSelected ? 'white' : 'grey'}]}>
  //           {capitalizeFirstLetter(item?.name)}
  //         </Text>
  //       </Pressable>
  //     );
  //   },
  //   [selectedButton],
  // );
  const [studentAcademic, {isLoading: isPosting}] =
    useStudentAcademicMutation();

  const renderItem = useCallback(
    ({item, index}) => {
      const isSelected = selectedButton === index;
      return (
        <Pressable
          activeOpacity={0.8}
          style={[
            styles.button,
            {
              backgroundColor: isSelected ? colors.themeColor : colors.white,
              borderColor: isSelected ? colors.white : colors.txtGrey,
            },
          ]}
          onPress={() => handleClassSelect(index)}>
          <Text
            style={[styles.buttonText, {color: isSelected ? 'white' : 'grey'}]}>
            {capitalizeFirstLetter(item?.name)}
          </Text>
        </Pressable>
      );
    },
    [selectedButton],
  );
  const dispatch = useDispatch();
  const handleNavigateTutorList = gradeID => {
    const result = studentAcademic({
      grades_id: gradeID,
    })
      .unwrap()
      .then(res => {
        console.log('res');

        dispatch(updateBookingFlowType('academic'));

        navigation.navigate('TutorList');
      });
  };
  const handleContinue = () => {
    if (!selectedId) {
      showToast('error', t('select_grade'), 'bottom', isRTL);
    } else if (selectedButton === null) {
      showToast('error', t('select_class'), 'bottom', isRTL);
    } else {
      const selectedGrade = gradeData?.data?.rows.find(
        grade => grade.id === selectedId,
      );
      console.log('🚀 ~ handleContinue ~ selectedId:', selectedId);
      console.log('🚀 ~ handleContinue ~ selectedGrade:', selectedGrade);
      const selectedClassId = selectedGrade?.tlm_classes[selectedButton]?.id;
      if (selectedGrade?.is_higher_education) {
        handleNavigateTutorList(selectedGrade?.id);
      } else {
        navigation.navigate('ChooseyourCurriculum', {
          classId: selectedClassId,
          gradeId: selectedGrade?.id,
        });
      }
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <View style={{backgroundColor: colors.themeColor}}>
        <AppHeader backIcon={icons.backbtn} style={{}} isBackBtn title={''} />
        <Title
          text={t('choose_grade')}
          style={{
            color: colors.white,
            marginTop: 16,
            textAlign: isRTL ? 'right' : 'left',
          }}
        />
        <SubTitle
          text={t('grade_subtitle')}
          style={{
            color: colors.txtGrey,
            marginTop: 5,
            textAlign: isRTL ? 'right' : 'left',
            width: wp(80),
            lineHeight: hp(2),
            alignSelf: isRTL ? 'flex-end' : 'flex-start',
            marginRight: 20,
          }}
        />
      </View>

      <ScrollView
        style={{flex: 1, backgroundColor: colors.white, marginHorizontal: 6}}>
        {isLoading ? (
          <ActivityIndicator
            size="large"
            color={colors.themeColor}
            style={{marginTop: 20}}
          />
        ) : (
          gradeData?.data?.rows.map(grade => (
            <Pressable
              key={grade.id}
              onPress={() => handleGradeSelect(grade.id)}>
              <View
                style={[
                  styles.cardContainer,
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                ]}>
                <View style={[styles.radioContainer]}>
                  <Image
                    source={
                      selectedId === grade.id ? icons.checked : icons.unchecked
                    }
                    style={styles.radioIcon}
                  />
                </View>
                <Text style={[styles.text]}>{grade.name}</Text>
                <View style={styles.iconContainer}>
                  <Image
                    source={
                      expandedContainer === grade.id
                        ? icons.downchevron
                        : icons.rightArrowGrade
                    }
                    style={{
                      transform: [
                        {
                          rotate:
                            isRTL && expandedContainer !== grade.id
                              ? '-180deg'
                              : '0deg',
                        },
                      ],
                    }}
                  />
                </View>
              </View>
              {/* // Update the FlatList to use a flow layout */}
              {expandedContainer === grade.id && (
                <View style={{}}>
                  <Text
                    style={[
                      styles.title,
                      {marginLeft: isRTL ? 0 : 20, marginRight: isRTL ? 20 : 0},
                    ]}>
                    {t('select_class')}
                  </Text>
                  <View style={styles.containerBtn}>
                    <View
                      style={[
                        styles.flowContainer,
                        {flexDirection: isRTL ? 'row-reverse' : 'row'},
                      ]}>
                      {grade.tlm_classes.map((item, index) => {
                        const isSelected = selectedButton === index;
                        return (
                          <Pressable
                            key={item.id}
                            activeOpacity={0.8}
                            style={[
                              styles.button,
                              {
                                backgroundColor: isSelected
                                  ? colors.themeColor
                                  : colors.white,
                                borderColor: isSelected
                                  ? colors.white
                                  : colors.txtGrey,
                              },
                            ]}
                            onPress={() => handleClassSelect(index)}>
                            <Text
                              style={[
                                styles.buttonText,
                                {color: isSelected ? 'white' : 'grey'},
                              ]}
                              numberOfLines={1} // Prevent text wrapping
                            >
                              {capitalizeFirstLetter(item?.name)}
                            </Text>
                          </Pressable>
                        );
                      })}
                    </View>
                  </View>
                </View>
              )}
            </Pressable>
          ))
        )}
      </ScrollView>
      <PrimaryButton
        onPress={handleContinue}
        title={t('continue')}
        style={{
          backgroundColor: colors.themeColor,
          marginBottom: 20,
        }}
        textStyle={{fontSize: 16, color: colors.white}}
      />
    </SafeAreaView>
  );
};

export default Chooseyourgrade;
