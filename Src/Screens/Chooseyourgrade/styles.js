import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: colors.black,

    marginTop: 5,
  },
  arrowIcon: {
    width: 20,
    height: 20,
    tintColor: colors.black,
    marginLeft: 7,
  },
  containerBtn: {
    padding: 10,
    // paddingHorizontal: 10,
    // marginVertical: 10,
    // width: '100%',
  },
  row: {
    justifyContent: 'space-around',
    marginBottom: 20,
  },

  buttonText: {
    fontSize: 14,
    fontFamily: Fonts.medium, // Use your app's font
    textAlign: 'center',
    flexShrink: 1,
    flexWrap: 'nowrap',
  },

  flatListContent: {
    paddingBottom: hp(3),
  },

  cardContainer: {
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    paddingVertical: 20,
    backgroundColor: colors.white,
    borderRadius: 12,
    marginVertical: 5,
    marginHorizontal: 14,
    elevation: 1,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginTop: 10,
  },
  radioContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioCircle: {
    height: 20,
    width: 20,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: colors.txtGrey1,
    backgroundColor: colors.white,
  },
  radioSelected: {
    backgroundColor: colors.themeColor,
  },
  text: {
    flex: 1,
    textAlign: 'left',
    marginLeft: 14,
    color: colors.darkBlack,
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  flatlistContainer: {marginTop: 15},

  radioIcon: {
    width: 24,
    height: 24,
  },

  containerBtn: {
    paddingHorizontal: 10,
    marginVertical: 10,
  },

  flatListContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
  },

  button: {
    borderRadius: 25, // More rounded corners
    borderWidth: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginBottom: 10,
    marginRight: 10,
    backgroundColor: colors.white,
    elevation: 2, // Add shadow for Android
    shadowColor: '#000', // Add shadow for iOS
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    alignSelf: 'center',
    // minWidth: wp(70),
  },

  flowContainer: {
    justifyContent: 'center',
    flexWrap: 'wrap',
    // gap: 12,
  },
});

export default styles;
