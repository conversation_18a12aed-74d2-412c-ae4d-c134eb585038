import {
  StyleSheet,
  Dimensions,
  Platform,
  TouchableWithoutFeedbackComponent,
} from 'react-native';
import colors from '../../Utils/colors';
import {fp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';

const {width} = Dimensions.get('screen');

const horizontalPadding = width * 0.04;
const categoryButtonMargin = width * 0.025;
const buttonPadding = Platform.OS === 'ios' ? width * 0.045 : width * 0.035;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: horizontalPadding,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerButton: {
    color: colors.themeColor,
    fontSize: 16,
  },
  contentContainer: {
    flex: 1,
  },
  categoriesContainer: {
    width: '40%',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    backgroundColor: colors.offWhite1,
  },
  optionsContainer: {
    flex: 1,
    paddingHorizontal: fp(1),
    backgroundColor: colors.white,
    paddingBottom: fp(8),
    paddingTop: fp(2),
  },
  categoryButton: {
    paddingHorizontal: horizontalPadding,
    margin: categoryButtonMargin,
    borderRadius: 13,
    backgroundColor: colors.white,
    paddingVertical: 14,
    elevation: 1,
  },
  categoryButtonSelected: {
    backgroundColor: colors.themeColor,
  },
  categoryButtonText: {
    fontSize: fp(1.8),
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  categoryButtonTextSelected: {
    color: colors.white,
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
  },
  checkboxContainer: {
    alignItems: 'center',
    marginBottom: width * 0.04,
  },
  checkbox: {
    width: width * 0.05, // 5% of screen width
    height: width * 0.05,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.txtGrey,
    marginRight: width * 0.03, // 3% of screen width
    justifyContent: 'center',
    alignItems: 'center',
    // marginBottom: 10,
  },
  checkboxSelected: {
    backgroundColor: colors.themeColor,
    borderColor: colors.themeColor,
  },
  checkMark: {
    color: '#fff',
    fontSize: fp(1.6), // 3.5% of screen width for scalability
  },
  checkboxLabel: {
    fontSize: fp(1.6),
    // fontSize: width * 0.04, // 4% of screen width
    color: '#333',
    width: '80%',
    fontFamily: Fonts.medium,
  },
  applyButton: {
    backgroundColor: colors.themeColor,
    marginHorizontal: horizontalPadding,
    padding: buttonPadding,
    borderRadius: 8,
    alignItems: 'center',
    position: 'absolute',
    bottom: horizontalPadding,
    left: horizontalPadding,
    right: horizontalPadding,
  },
  applyButtonText: {
    color: colors.white,
    fontSize: width * 0.04, // 4% of screen width
    fontFamily: Fonts.medium,
  },
  scrollContent: {
    flex: 1,
  },
  distance: {
    fontFamily: Fonts.bold,
    color: colors.black,
    fontSize: fp(1.7),
  },
});

export default styles;
