import {
  Safe<PERSON>reaV<PERSON><PERSON>,
  ScrollView,
  View,
  TouchableOpacity,
  Text,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import styles from './styles';
import FilterHeader from '../../Components/FilterHeader';
import {useTranslation} from 'react-i18next';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  useChooseYourCurriculumsQuery,
  useGetSubjectsTutorQuery,
  useLazyChooseYourCurriculumsQuery,
} from '../../Api/ApiSlice';
import {useSelector} from 'react-redux';
import RadiusSlider from '../../Components/RadiusSlider';
import Slider from '@react-native-community/slider';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';

// Filter Category Button Component
const CategoryButton = ({title, isSelected, onPress}) => {
  return (
    <TouchableOpacity
      style={[
        applyShadowStyleIos(styles.categoryButton),
        isSelected && styles.categoryButtonSelected,
      ]}
      onPress={onPress}>
      <Text
        style={[
          styles.categoryButtonText,
          isSelected && styles.categoryButtonTextSelected,
        ]}>
        {capitalizeFirstLetter(title)}
      </Text>
    </TouchableOpacity>
  );
};

// Custom Checkbox component
const CustomCheckbox = ({label, isSelected, onSelect, isRTL}) => {
  return (
    <TouchableOpacity
      style={[
        styles.checkboxContainer,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
      ]}
      onPress={onSelect}>
      <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
        {isSelected && <Text style={styles.checkMark}>✓</Text>}
      </View>
      <Text style={[styles.checkboxLabel, {marginRight: isRTL ? 10 : 0}]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const FilterScreen = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [chooseYourCurriculum, {data: curriculumData}] =
    useLazyChooseYourCurriculumsQuery();
  const {data: subjectsData} = useGetSubjectsTutorQuery();

  useEffect(() => {
    chooseYourCurriculum();
  }, []);
  function handleViewAll() {
    const newViewAllState = !isViewAll;
    setIsViewAll(newViewAllState);
    const params = {};
    if (newViewAllState) {
      params.viewAll = true;
    }
    console.log('API Params:', params);
    chooseYourCurriculum(params);
  }
  const {bookingFlowType} = useSelector(state => state?.tutorBookingSlice);
  const [radiusValue, setRadiusValue] = useState(0); // Ensure initial value is 0
  const [isViewAll, setIsViewAll] = useState(false);

  // Filter data with translations for filter options only
  const filterDataWithIds = {
    language: [
      {id: 1, name: t('englishL')},
      {id: 2, name: t('arabic')},
    ],

    ...(bookingFlowType !== 'recreational' && {
      curriculum: curriculumData?.data?.rows,
      subject: subjectsData?.data?.rows,
    }),
    price: [
      {id: 1, name: t('highestToLowest')},
      {id: 2, name: t('lowestToHighest')},
      // {id: 3, name: t('hundred_plus')},
    ],
    ratings: [
      {id: 1, name: t('highestToLowest')},
      // {id: 2, name: t('four_star')},
      // {id: 3, name: t('three_star')},
      // {id: 4, name: t('two_star')},
      // {id: 5, name: t('one_star')},
    ],

    gender: [
      {id: 1, name: t('male')},
      {id: 2, name: t('female')},
    ],
    distance: [{id: 1, name: '0-20'}],
    classType: [
      {id: 1, name: t('online')},
      {id: 2, name: t('face_to_face')},
    ],
  };

  const [selectedCategory, setSelectedCategory] = useState('language');
  const [selectedFilters, setSelectedFilters] = useState({
    language: [],
    subject: [],
    price: [],
    rating_en: [],
    gender: [],
    distance: [],
    curriculum: [],
    classType: [],
  });
  const saveFilters = async () => {
    try {
      await AsyncStorage.setItem('filters', JSON.stringify(selectedFilters));
    } catch (e) {
      console.error('Failed to save filters:', e);
    }
  };
  // Clear all filters
  const clearFilters = async () => {
    const resetFilters = {
      language: [],
      subject: [],
      price: [],
      rating_en: [],
      gender: [],
      distance: [],
      curriculum: [],
      classType: [],
    };
    setSelectedFilters(resetFilters);

    try {
      await AsyncStorage.removeItem('filters');
    } catch (e) {
      console.error('Failed to remove filters from AsyncStorage:', e);
    }

    navigation.navigate('TutorList', {
      selectedFilters: resetFilters,
    });
  };
  const handleRadiusChange = React.useCallback(value => {
    // Ensure value is a valid number
    const numericValue = Number(value) || 0;
    setRadiusValue(numericValue);
    // Set distance as a single number value
    setSelectedFilters(prev => ({
      ...prev,
      distance: numericValue, // Store as a number
    }));
  }, []);
  useFocusEffect(
    React.useCallback(() => {
      const loadFilters = async () => {
        try {
          const filters = await AsyncStorage.getItem('filters');
          if (filters) {
            const parsedFilters = JSON.parse(filters);
            setSelectedFilters(parsedFilters);
            // Ensure we set a valid number for radiusValue
            setRadiusValue(Number(parsedFilters?.distance) || 0);
          }
        } catch (e) {
          console.error('Failed to load filters:', e);
        }
      };

      loadFilters();
    }, []),
  );

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', saveFilters);

    // Cleanup the listener on unmount
    return unsubscribe;
  }, [navigation, selectedFilters]);
  const handleSelect = (category, itemId) => {
    console.log('🚀 ~ handleSelect ~ itemId:', itemId);

    setSelectedFilters(prev => {
      // Normalize category key to lowercase for consistency
      const normalizedCategory = category.toLowerCase();

      // Check if the category is single-select
      const isSingleSelect =
        normalizedCategory === 'price' || normalizedCategory === 'ratings';

      if (isSingleSelect) {
        // For single-select, set the selected item directly
        return {
          ...prev,
          [normalizedCategory]:
            prev[normalizedCategory]?.[0] === itemId ? [] : [itemId], // Deselect if the same item is selected
        };
      } else {
        // For multi-select, add or remove the item from the array
        const currentSelection = prev[normalizedCategory] || [];
        const isSelected = currentSelection.includes(itemId);

        const updatedSelection = isSelected
          ? currentSelection.filter(id => id !== itemId) // Remove if already selected
          : [...currentSelection, itemId]; // Add if not selected

        return {
          ...prev,
          [normalizedCategory]: updatedSelection,
        };
      }
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />

      {/* Header */}
      <FilterHeader
        onClearFilters={clearFilters}
        title={t('filters_title')}
        showBackButton={true}
        showClearButton={true}
        style={{backgroundColor: colors.themeColor}}
      />

      {/* Main Content */}
      <View
        style={[
          styles.contentContainer,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        {/* Left Side - Categories */}
        <View style={styles.categoriesContainer}>
          {Object.keys(filterDataWithIds).map(category => (
            <CategoryButton
              key={category}
              title={t(category)} // Translation applied here for category names
              isSelected={selectedCategory === category}
              onPress={() => setSelectedCategory(category)}
            />
          ))}
        </View>

        {/* Right Side - Options */}
        <View style={styles.optionsContainer}>
          {selectedCategory === 'curriculum' && (
            <Text
              onPress={handleViewAll}
              style={{
                color: colors.black,
                marginBottom: hp(1),
                textAlign: isRTL ? 'left' : 'right',
                // width: wp(90),
                fontFamily: Fonts.semiBold,
                fontSize: fp(1.8),
                marginLeft: wp(2.6),
                textDecorationLine: 'underline',
              }}>
              {t('view')} {!isViewAll ? t('all') : t('less')}
            </Text>
          )}
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={styles.scrollContent}>
            {filterDataWithIds[selectedCategory]?.map(item => {
              return selectedCategory !== 'distance' ? (
                <View key={item?.id}>
                  <CustomCheckbox
                    label={item?.name}
                    isSelected={
                      Array.isArray(
                        selectedFilters[selectedCategory.toLowerCase()],
                      ) &&
                      selectedFilters[selectedCategory.toLowerCase()]?.includes(
                        item?.id,
                      )
                    } // Check if item is selected
                    onSelect={() => handleSelect(selectedCategory, item?.id)}
                    isRTL={isRTL}
                  />
                </View>
              ) : (
                <View style={{alignItems: 'center'}} key={item?.id}>
                  <Text style={styles.distance}>
                    {`${t('distance')}: ${Math.round(radiusValue)} ${t('km')}`}
                  </Text>
                  <Slider
                    style={{width: fp(25), height: fp(5)}}
                    minimumValue={0}
                    maximumValue={120}
                    minimumTrackTintColor={colors.themeColor}
                    maximumTrackTintColor={colors.greyLight}
                    value={Number(radiusValue) || 0}
                    onValueChange={handleRadiusChange}
                  />
                </View>
              );
            })}
          </ScrollView>
          <TouchableOpacity
            style={styles.applyButton}
            onPress={() => {
              navigation.navigate('TutorList', {
                selectedFilters,
                radiusValue,
              });
            }}>
            <Text style={styles.applyButtonText}>{t('apply')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default FilterScreen;
