import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';
import {fp, hp} from '../../Helper/ResponsiveDimensions';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
  },
  headerContainer: {
    paddingHorizontal: 20,
    height: hp(5),
    marginBottom: hp(1),
    justifyContent: 'center',
  },
  statusBadge: {
    paddingRight: 10,
    borderRadius: 12,
    flex: 0.22,
    alignSelf: 'center',
  },
  gradient: {
    paddingHorizontal: 14,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statusText: {
    color: colors.black,
    fontSize: 13,
    fontFamily: Fonts.medium,
  },
  leftHeader: {
    flex: 0.5,

    alignItems: 'center',
  },
  hamburgerIcon: {
    width: 24,
    height: 24,
  },
  logoContainer: {
    alignItems: 'center',
    paddingLeft: 5,
  },
  logoImage: {
    width: fp(12),
    // height: fp(10),
  },
  rightHeader: {
    flex: 0.5,

    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingRight: 10,
    minWidth: 120,
  },
  locationContainer: {
    alignItems: 'center',
    marginRight: 15,
    minWidth: 80,
  },
  locationIcon: {
    width: fp(1.6),
    // height: 17,
  },
  locationText: {
    marginHorizontal: 6,
    color: colors.white,
    fontFamily: Fonts.poppinsRegular,
    fontSize: fp(1.4),
  },
  arrowDownIcon: {
    width: 12,
    height: 12,
    alignSelf: 'center',
  },
  notificationContainer: {
    padding: 5,
  },
  notificationIcon: {
    width: 20,
    height: 20,
  },
  contentContainer: {
    backgroundColor: colors.white,
    flex: 1,
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.themeColor,
  },
  btnTxt: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '400',
    textAlign: 'center',
    color: colors.white,
  },

  card: {
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 8,
    marginVertical: 8,
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  txtContent: {
    fontSize: 15,
    lineHeight: 18,
    color: colors.black,
    fontFamily: Fonts.bold,
    flex: 0.65,
  },
  content: {
    alignItems: 'center',
    marginVertical: 6,
  },
  innerView: {
    width: '70%',
    marginLeft: 5,
    marginTop: 10,
  },
  txtheader: {
    marginHorizontal: 8,
    fontSize: responsiveFontSize(14),
    fontFamily: Fonts.medium,
    color: colors.searchGray,
  },
  txt: {
    marginLeft: 8,
    fontSize: responsiveFontSize(13),
    color: colors.black,
    fontFamily: Fonts.medium,
  },

  partitionLine: {
    height: 2,
    width: '95%',
    backgroundColor: colors.txtGrey,
    marginVertical: 8,
    alignSelf: 'center',
  },
  bookingTxt: {
    fontSize: fp(2.2),
    color: colors.black,
    fontFamily: Fonts.semiBold,
    // flex: 0.7,
  },
  row: {
    flex: 1,
    marginHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  notDataTxt: {
    color: colors.black,
    textAlign: 'center',
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
  },
  calender: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.offBlack,
  },
  dayTxt: {
    marginLeft: 10,
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.darkBlack,
    alignSelf: 'center',
  },
  enrollmentTxt: {
    fontSize: fp(1.6),
    fontFamily: Fonts.semiBold,
    lineHeight: 18,
    color: colors.black,
  },
});

export default styles;
