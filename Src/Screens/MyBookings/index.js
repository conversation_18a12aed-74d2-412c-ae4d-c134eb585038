import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  FlatList,
  Image,
  Modal,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StatusContainer} from '../../Components/StatusBar';
import TabButton from '../../Components/TabButton';
import {showToast} from '../../Components/ToastHelper';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import styles from './styles';
import {Fonts} from '../../Utils/Fonts';
import {
  useGetMyBookingsTutorQuery,
  useLazyGetBookingDatesForCalendarQuery,
} from '../../Api/ApiSlice';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import DateTimePicker from '@react-native-community/datetimepicker';
import {useSelector} from 'react-redux';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import {convertTo12HourFormat} from '../../Helper/DateHelpers/DateHelpers';
import moment from 'moment';
import {
  getFirstAndLastDates,
  getFormattedBookingDatesWithSlots,
} from '../../Helper/Calendar/FormatAvailableSlotDate';
import TaleemEventCalendar from '../../Components/Calendar/TaleemEventCalendar';

const MyBookings = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [activeBtn, setActiveBtn] = useState(1);
  const [isCalendarLoading, setIsCalendarLoading] = useState(false);
  const [data, setData] = useState([]);
  const [selectedTabTitle, setSelectedTabTitle] = useState(t('academic'));
  const [bookingDate, setBookingDate] = useState(
    new Date().toISOString().split('T')[0],
  );
  const [showPicker, setShowPicker] = useState(false);
  const date = new Date();
  const markedDatesRef = useRef({});
  // const slectDate = new Date(bookingDate).toISOString();

  const {user_type} = useSelector(state => state?.auth);

  const {
    data: tutorsBookingData,
    error,
    isLoading,
  } = useGetMyBookingsTutorQuery({
    date: bookingDate,
    rate_card_type: activeBtn,
  });

  const [
    fetchSchedules,
    {isLoading: isLoadingSchedules, error: getCalendarDataError},
  ] = useLazyGetBookingDatesForCalendarQuery();

  const {appLocale} = useSelector(state => state?.auth);

  useEffect(() => {
    setData(tutorsBookingData?.data?.rows);
  }, [tutorsBookingData, activeBtn, bookingDate]);

  const onChange = (event, selectedDate) => {
    setShowPicker(false);
    if (event.type === 'set' && selectedDate) {
      setBookingDate(selectedDate); // Keep it as a Date object
    }
  };
  const logoSource =
    appLocale === 'ar'
      ? icons.logo.welcomeLogoArabic
      : icons.logo.welcomeLogoEng;

  function handleGetBookingDatesForCalendar(year, month, startDate, endDate) {
    setIsCalendarLoading(true);
    // const studentParams = {
    //   startDate: startDate,
    //   endDate: endDate,
    //   class_type_id: selectedTab == 'Face to Face' ? 2 : 1,
    // };
    const tutorParams = {
      startDate: startDate,
      endDate: endDate,
      rate_card_type: activeBtn,
    };
    const USER_TYPE = user_type == '1' ? 'student' : 'tutor';
    fetchSchedules({params: tutorParams, userType: USER_TYPE})
      .unwrap()
      .then(response => {
        console.log(
          '🚀 ~ handleGetBookingDatesForCalendar ~ response:',
          response?.data,
        );
        // const scheduleDatesRes = response?.data?.schedule_dates;
        const bookingDatesApiRes = response?.data;

        // Format booking dates with yellow dots
        const bookingDatesFormattedData = getFormattedBookingDatesWithSlots(
          bookingDatesApiRes,
          {},
          // {...slotsDatesFormattedData, ...remainingDatesFormattedData},
          'bold',
          colors.darkBlack,
        );

        // Merge all formatted dates
        const allFormattedDates = {
          ...bookingDatesFormattedData,
        };

        // Add selected date styling
        if (bookingDate) {
          console.log('selectedDate 789854684', allFormattedDates[bookingDate]);
          allFormattedDates[bookingDate] = {
            ...allFormattedDates[bookingDate],
            // Ensure the background color is consistent
            // color: colors.themeBackground, // Background color for selected date
            // Merge custom styles without overriding fontWeight
            customStyles: {
              container: {
                backgroundColor: colors.themeBackground,
              },
              text: {
                color:
                  allFormattedDates[bookingDate]?.customStyles?.text?.color, // Text color for selected date
                fontWeight:
                  allFormattedDates[bookingDate]?.customStyles?.text
                    ?.fontWeight || '300',
              },
            },
          };
        }

        markedDatesRef.current = allFormattedDates;
        setIsCalendarLoading(false);
      })

      .catch(err => {
        console.error('Error: getBookingDates', err);
        showToast('error', err?.data?.message, 'bottom', isRTL);
        setIsCalendarLoading(false);
      });
  }

  const handleDateSelect = date => {
    const dateString = date.dateString;
    setBookingDate(dateString);

    const updatedMarkedDates = {...markedDatesRef.current};

    // Reset any previous selected date
    Object.keys(updatedMarkedDates).forEach(dateKey => {
      if (updatedMarkedDates[dateKey]?.selected) {
        delete updatedMarkedDates[dateKey].selected;
        delete updatedMarkedDates[dateKey].color;
        delete updatedMarkedDates[dateKey].textColor;
      }
    });

    // Apply new selected date styles
    updatedMarkedDates[dateString] = {
      ...updatedMarkedDates[dateString],
      selected: true,
    };

    markedDatesRef.current = updatedMarkedDates;
    setShowPicker(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <View
        style={[
          styles.headerContainer,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <View
          style={[
            styles.leftHeader,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Image
              source={isRTL ? icons.rightArrowLarge : icons.backbtn}
              style={[
                isRTL
                  ? {height: 20, width: 20, tintColor: colors.white}
                  : styles.hamburgerIcon,
              ]}
            />
          </TouchableOpacity>

          <View
            style={[
              styles.logoContainer,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              source={logoSource}
              style={[styles.logoImage, {marginRight: isRTL ? 10 : 0}]}
              resizeMode="contain"
            />
          </View>
        </View>

        <View
          style={[
            styles.rightHeader,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          {/* <TouchableOpacity
            activeOpacity={0.8}
            style={[
              styles.locationContainer,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}
            onPress={() =>
              showToast('success', t('coming_soon'), 'bottom', isRTL)
            }>
            <Image
              source={icons.location}
              style={styles.locationIcon}
              resizeMode="contain"
            />

            <Text style={styles.locationText}>{t('Qatar')}</Text>
            <Image
              source={icons.arrowDownWhite}
              style={styles.arrowDownIcon}
              resizeMode="contain"
            />
          </TouchableOpacity> */}

          <TouchableOpacity
            style={styles.notificationContainer}
            onPress={() =>
              showToast(
                'success',
                t('notifications_unavailable'),
                'bottom',
                isRTL,
              )
            }>
            <Image
              source={icons.notification}
              style={styles.notificationIcon}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.contentContainer}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View
            style={[
              styles.row,
              {
                flexDirection: isRTL ? 'row-reverse' : 'row',
              },
            ]}>
            <Text style={styles.bookingTxt}>{t('my_bookings')}</Text>
            <TouchableOpacity
              onPress={() => setShowPicker(!showPicker)}
              activeOpacity={0.8}
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignSelf: 'center',
              }}>
              <Image
                source={icons.calanderYellow}
                style={{height: fp(2.6), width: fp(2.6), alignSelf: 'center'}}
                resizeMode="contain"
              />
              <Text style={styles.dayTxt}>
                {bookingDate === date.toISOString().split('T')[0]
                  ? t('today')
                  : moment(bookingDate).format('DD MMM YYYY')}
              </Text>
              <Image
                source={icons.arrowDown}
                style={{height: 24, width: 24, marginLeft: 5}}
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>
          <FlatList
            data={[
              {id: 1, title: t('academic')},
              {id: 2, title: t('recreational')},
              {id: 3, title: t('courses')},
            ]}
            horizontal
            style={{
              marginVertical: 10,
              flexDirection: isRTL ? 'row-reverse' : 'row',
              marginLeft: wp(2),
            }}
            renderItem={({item}) => (
              <TabButton
                title={item.title}
                isActive={activeBtn === item.id}
                onPress={() => {
                  setActiveBtn(item.id);
                  setSelectedTabTitle(item?.title);
                }}
                textStyle={styles.btnTxt}
              />
            )}
          />

          <FlatList
            data={data}
            keyExtractor={item => item.id.toString()}
            ListEmptyComponent={
              isLoading ? (
                <TaleemLoader isLoading={isLoading} />
              ) : (
                <Text style={styles.notDataTxt}>{t('no_booking_found')}</Text>
              )
            }
            renderItem={({item}) => (
              <TouchableOpacity
                style={styles.card}
                activeOpacity={0.8}
                onPress={() =>
                  navigation.navigate('StudentClassDetails', {
                    item,
                    title: item.title,
                    timing: item.timing,
                    session: item.session,
                    selectedTabTitle,
                  })
                }>
                <View
                  style={{
                    flexDirection: isRTL ? 'row-reverse' : 'row',
                    justifyContent: 'space-between',
                    flex: 1,
                  }}>
                  <Text
                    style={
                      styles.txtContent
                    }>{`${item?.tlm_booking?.class_title}`}</Text>
                  <View style={styles.statusBadge}>
                    <LinearGradient
                      colors={['#C6FFC9', '#D4EBFF']}
                      start={{x: 0.0, y: 0.0}}
                      end={{x: 1.0, y: 0.0}}
                      style={styles.gradient}>
                      <Text style={styles.statusText}>
                        {item?.tlm_booking?.tlm_class_type?.name}
                      </Text>
                    </LinearGradient>
                  </View>
                </View>
                <View
                  style={[
                    styles.innerView,
                    {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
                  ]}>
                  <View
                    style={[
                      styles.content,
                      {flexDirection: isRTL ? 'row-reverse' : 'row'},
                    ]}>
                    <Image
                      source={icons.clockYellow}
                      style={{height: 20, width: 20}}
                      resizeMode="contain"
                    />
                    <Text style={styles.txtheader}>
                      {t('time_label')}
                      <Text style={styles.txt}>{`${convertTo12HourFormat(
                        item?.tlm_tutor_schedule?.start_time,
                      )} - ${convertTo12HourFormat(
                        item?.tlm_tutor_schedule?.end_time,
                      )}`}</Text>
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.content,
                      {
                        flexDirection: isRTL ? 'row-reverse' : 'row',
                      },
                    ]}>
                    <Image
                      source={icons.monthCalendar}
                      style={{height: 22, width: 22}}
                      resizeMode="contain"
                    />

                    <Text style={styles.txtheader}>
                      {t('session_label')} :{' '}
                      <Text style={styles.txt}>
                        {item.tlm_booking?.tlm_sessions_type?.name}
                      </Text>
                    </Text>
                  </View>
                </View>
                <View style={styles.partitionLine} />
                <View
                  style={{
                    marginVertical: 10,
                    flexDirection: isRTL ? 'row-reverse' : 'row',
                  }}>
                  <Image
                    source={
                      item?.tlm_booking?.tlm_booking_enrollments[0]?.tlm_user
                        ?.image
                        ? {
                            uri:
                              IMAGE_BASE_URL +
                              item?.tlm_booking?.tlm_booking_enrollments[0]
                                ?.tlm_user?.image,
                          }
                        : {uri: DUMMY_USER_IMG}
                    }
                    style={{width: 35, height: 35, borderRadius: 10}}
                    resizeMode="contain"
                  />
                  <View style={isRTL ? {marginRight: 8} : {marginLeft: 8}}>
                    <Text
                      style={[
                        styles.enrollmentTxt,
                        {textAlign: isRTL ? 'right' : 'left'},
                      ]}>
                      {
                        item?.tlm_booking?.tlm_booking_enrollments[0]?.tlm_user
                          ?.name
                      }
                    </Text>
                    <Text
                      style={{
                        fontSize: fp(1.4),
                        fontFamily: Fonts.regular,
                        lineHeight: 16,
                        color: colors.searchGray,
                      }}>
                      {`${
                        isRTL
                          ? item?.tlm_booking?.tlm_booking_enrollments[0]
                              ?.tlm_user?.tlm_student_academic_details[0]
                              ?.tlm_grade?.name_ar
                          : item?.tlm_booking?.tlm_booking_enrollments[0]
                              ?.tlm_user?.tlm_student_academic_details[0]
                              ?.tlm_grade?.name
                      } (${
                        item?.tlm_booking?.tlm_booking_enrollments[0]?.tlm_user
                          ?.tlm_student_academic_details[0]?.tlm_class?.name ||
                        ''
                      })`}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
            contentContainerStyle={{paddingHorizontal: 16}}
          />
        </ScrollView>

        {/* {Platform.OS == 'ios' ? (
          <Modal
            transparent
            visible={showPicker}
            style={{alignItems: 'center', justifyContent: 'center'}}>
            <TouchableWithoutFeedback onPress={() => setShowPicker(false)}>
              <View style={styles.calender}>
                <View style={{backgroundColor: colors.white, borderRadius: 10}}>
                  <DateTimePicker
                    value={bookingDate}
                    mode="date"
                    display="inline"
                    onChange={onChange}
                    minimumDate={date}
                  />
                </View>
              </View>
            </TouchableWithoutFeedback>
          </Modal>
        ) : (
          showPicker && (
            <DateTimePicker
              value={bookingDate}
              mode="date"
              display="default"
              onChange={onChange}
              minimumDate={date}
            />
          )
        )} */}
        <Modal
          transparent
          visible={showPicker}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <TouchableWithoutFeedback onPress={() => setShowPicker(false)}>
            <View style={styles.calender}>
              <TaleemEventCalendar
                selectedDate={bookingDate}
                handleDateSelect={handleDateSelect}
                markedDates={markedDatesRef.current}
                isLoading={isCalendarLoading}
                handleOnMonthChange={dateObj => {
                  const {firstDay, lastDay, year, month} = getFirstAndLastDates(
                    dateObj.dateString,
                  );
                  handleGetBookingDatesForCalendar(
                    year,
                    month,
                    firstDay,
                    lastDay,
                  );
                }}
                isShowAllInstructions={false}
              />
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </View>
    </SafeAreaView>
  );
};

export default MyBookings;
