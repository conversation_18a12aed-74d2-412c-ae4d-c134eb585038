import {
  Image,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import colors from '../../Utils/colors';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {jsiConfigureProps} from 'react-native-reanimated/lib/typescript/core';
import {useDispatch, useSelector} from 'react-redux';
import {setAppLocale} from '../../Features/authSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18next from 'i18next';

const SettingsMenu = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const [isLogoutModalVisible, setIsLogoutModalVisible] = useState(false);
  const [isLanguageModalVisible, setIsLanguageModalVisible] = useState(false);
  const {appLocale} = useSelector(state => state?.auth);
  const isRTL = i18n.language === 'ar';

  function handleNavigation({item}) {
    if (item?.route === 'Language') {
      console.log('route is language');
      handleLanguageConfirmation();
    } else {
      navigation.navigate(item?.route);
    }
  }
  const handleCancelLanguageChange = () => {
    // Hide logout confirmation modal
    setIsLanguageModalVisible(false);
  };
  const handleLanguageConfirmation = () => {
    // Show logout confirmation modal
    setIsLanguageModalVisible(true);
  };
  const dispatch = useDispatch();
  const onContinueClick = async appLang => {
    try {
      await i18next.changeLanguage(appLang);
      await AsyncStorage.setItem('selectedLang', JSON.stringify(appLang));
      dispatch(setAppLocale(appLang));
      setTimeout(() => {
        handleCancelLanguageChange();
      }, 1000);
    } catch (error) {
      console.log('🚀 ~ SettingsMenu ~ error:', error);
    }
  };
  const renderItem = item => (
    <TouchableOpacity
      onPress={() => handleNavigation({item})}
      style={applyShadowStyleIos({
        //   borderColor: colors.black,
        //   borderWidth: 1,
        elevation: 1,
        //   height: hp(10),
        width: wp(90),
        backgroundColor: colors.white,
        alignSelf: 'center',
        borderRadius: fp(2),
        padding: fp(2),
        flexDirection: isRTL ? 'row-reverse' : 'row',
        justifyContent: 'space-between',
        marginBottom: hp(1.6),
      })}>
      <Text
        style={{
          fontFamily: Fonts.medium,
          fontSize: fp(1.8),
        }}>
        {item?.label}
      </Text>
      <Image
        style={{transform: [{rotate: isRTL ? '180deg' : '0deg'}]}}
        source={icons.rightArrowGrade}
      />
    </TouchableOpacity>
  );
  const userType = useSelector(state => state.auth.user_type);

  const menuItems = [
    {label: t('notifications'), route: 'NotficationSetting'}, //temp changes 25 Nov
    // {label: t('manage_profile'), route: 'ManageProfile'}, //temp changes 25 Nov
    {label: t('terms_of_service'), route: 'TermsOfService'},
    {label: t('faqs'), route: 'FaqScreeen'},

    // {label: t('rating_feedbacks'), route: 'RantingsAndFeedback'},
    {label: t('contact_us'), route: 'ContactUs'},
    {label: t('deleteAccount'), route: 'DeleteAccount'},
    // {label: t('risedTicket'), route: 'ConcernScreen'},
  ];
  const tutorMenuItems = [
    // {label: t('doc'), route: 'TutorDocuments'},
    {label: t('notifications'), route: 'NotficationSetting'}, //temp changes 25 Nov
    {label: t('change_language'), route: 'Language'},

    // {label: t('manage_profile'), route: 'ManageProfile'}, //temp changes 25 Nov
    {label: t('deleteAccount'), route: 'DeleteAccount'},
    // {label: t('terms_of_service'), route: 'TermsOfService'},
    // {label: t('faqs'), route: 'FaqScreeen'},
    // {label: t('contact_us'), route: 'ContactUs'},
    // {label: t('risedTicket'), route: 'ConcernScreen'},
    // {label: t('change_language'), route: 'Language'},
  ];
  const studentMenuItems = [
    // {label: t('doc'), route: 'TutorDocuments'},
    {label: t('notifications'), route: 'NotficationSetting'}, //temp changes 25 Nov
    {label: t('change_language'), route: 'Language'},
    // {label: t('manage_profile'), route: 'ManageProfile'}, //temp changes 25 Nov
    {label: t('deleteAccount'), route: 'DeleteAccount'},
    // {label: t('terms_of_service'), route: 'TermsOfService'},
    // {label: t('faqs'), route: 'FaqScreeen'},
    // {label: t('contact_us'), route: 'ContactUs'},
    // {label: t('risedTicket'), route: 'ConcernScreen'},
    // {label: t('change_language'), route: 'Language'},
  ];
  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white, zIndex: 2}}>
      <StatusContainer />

      {/* Header */}
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('settings')}
        // style={{backgroundColor: colors.white}}
        // isWhite={true}
      />
      <ScrollView
        contentContainerStyle={{paddingVertical: hp(1), marginTop: hp(0.6)}}>
        {userType == '3'
          ? tutorMenuItems.map((item, index) => renderItem(item))
          : studentMenuItems.map((item, index) => renderItem(item))}
      </ScrollView>

      {/* {Item()} */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={isLanguageModalVisible}
        onRequestClose={handleCancelLanguageChange}>
        <View style={logoutModalStyles.centeredView}>
          <View style={logoutModalStyles.modalView}>
            {/* <Image source={icons.cross} style={{alignSelf: 'flex-end'}} /> */}

            <Text style={logoutModalStyles.modalTitle}>
              {t('selectLanguage')}
            </Text>
            <Text style={logoutModalStyles.modalText}>
              {t('chooseLanguage')}
            </Text>
            <View style={logoutModalStyles.buttonContainer}>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  appLocale == 'ar'
                    ? logoutModalStyles.logoutButton
                    : logoutModalStyles.cancelButton,
                ]}
                onPress={() => onContinueClick('ar')}>
                <Text
                  style={
                    appLocale == 'ar'
                      ? logoutModalStyles.logoutButtonText
                      : logoutModalStyles.cancelButtonText
                  }>
                  عربي
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  appLocale == 'en'
                    ? logoutModalStyles.logoutButton
                    : logoutModalStyles.cancelButton,
                ]}
                onPress={() => onContinueClick('en')}>
                <Text
                  style={
                    appLocale == 'en'
                      ? logoutModalStyles.logoutButtonText
                      : logoutModalStyles.cancelButtonText
                  }>
                  {/* {t('englishL')} */}
                  English
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default SettingsMenu;

const styles = StyleSheet.create({});
const logoutModalStyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    // margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 18,
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  modalText: {
    marginBottom: 15,
    fontFamily: Fonts.medium,
    color: colors.black,
    textAlign: 'center',
    fontSize: fp(1.6),
  },
  buttonContainer: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    marginHorizontal: wp(1),
    minWidth: 100,
  },
  cancelButton: {
    backgroundColor: colors.txtGrey,
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    color: 'black',
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    textAlign: 'center',
  },
  logoutButtonText: {
    color: 'white',
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    textAlign: 'center',
  },
});
