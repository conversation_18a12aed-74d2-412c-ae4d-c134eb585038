import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  TouchableWithoutFeedback,
  StyleSheet,
  Button,
  PanResponder,
} from 'react-native';
import {Path, Svg} from 'react-native-svg';
import io from 'socket.io-client';
import {baseUrl} from '../../Api/ApiSlice';
import {useSocket} from '../../Helper/SocketHelper/SocketProvider';

// const socket = io(baseUrl); // Your server URL

const WhiteBoardScoket = ({isTutor = true}) => {
  const {socket} = useSocket();
  const [points, setPoints] = useState([]);
  const [color, setColor] = useState('#000');
  const [brushSize, setBrushSize] = useState(5);
  const [currentPoints, setCurrentPoints] = useState([]);
  const panRef = useRef(null);

  // Buffer for smooth drawing
  const pointBuffer = useRef([]);

  // Initialize PanResponder
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => isTutor,
      onPanResponderMove: (evt, gestureState) => {
        if (!isTutor) return;
        const {locationX, locationY} = evt.nativeEvent;
        pointBuffer.current.push({x: locationX, y: locationY});
        handleDraw();
      },
      onPanResponderRelease: () => {
        if (!isTutor) return;
        handleDrawEnd();
      },
    }),
  ).current;

  useEffect(() => {
    // Listen for drawing events
    socket.on('drawing', data => {
      setPoints(prev => [...prev, data]);
    });

    // Listen for clear events
    socket.on('clear', () => {
      setPoints([]);
      pointBuffer.current = [];
    });
  }, []);

  const handleDraw = () => {
    if (!isTutor) return;

    // Process the point buffer
    if (pointBuffer.current.length > 0) {
      const points = pointBuffer.current;
      pointBuffer.current = [];

      // Create smooth curves
      const smoothPoints = createSmoothCurve(points);

      // Send the drawing data
      smoothPoints.forEach((point, index) => {
        const data = {
          x: point.x,
          y: point.y,
          prevX: smoothPoints[index - 1]?.x || point.x,
          prevY: smoothPoints[index - 1]?.y || point.y,
          color: color,
          brushSize: brushSize,
        };

        socket.emit('drawing', data);
        setPoints(prev => [...prev, data]);
      });
    }
  };

  const handleDrawEnd = () => {
    if (!isTutor) return;
    pointBuffer.current = [];
  };

  const clearCanvas = () => {
    if (!isTutor) return;
    socket.emit('clear');
    setPoints([]);
    pointBuffer.current = [];
  };

  const createSmoothCurve = points => {
    // Implement a simple curve smoothing algorithm
    // This example uses quadratic interpolation
    const smoothPoints = [];
    for (let i = 0; i < points.length; i++) {
      const prevPoint = i > 0 ? points[i - 1] : points[i];
      const nextPoint = i < points.length - 1 ? points[i + 1] : points[i];
      const curvePoint = {
        x: (prevPoint.x + 2 * points[i].x + nextPoint.x) / 4,
        y: (prevPoint.y + 2 * points[i].y + nextPoint.y) / 4,
      };
      smoothPoints.push(curvePoint);
    }
    return smoothPoints;
  };

  return (
    <View style={styles.container}>
      <TouchableWithoutFeedback {...panResponder}>
        <Svg style={styles.svg}>
          {points.map((point, index) => (
            <Path
              key={index}
              d={`M ${point.prevX} ${point.prevY} Q ${point.x} ${point.y} ${point.x} ${point.y}`}
              stroke={point.color}
              strokeWidth={point.brushSize}
              fill="none"
            />
          ))}
        </Svg>
      </TouchableWithoutFeedback>
      {isTutor && (
        <View style={styles.controls}>
          <View style={styles.clearButton}>
            <Button title="Clear" onPress={clearCanvas} />
          </View>
          <View style={styles.colorPicker}>
            <Button title="Change Color" onPress={() => setColor('#ff0000')} />
            <Button
              title="Change Brush Size"
              onPress={() => setBrushSize(10)}
            />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  svg: {
    width: '100%',
    height: '80%',
    backgroundColor: '#fff',
  },
  controls: {
    position: 'absolute',
    bottom: 20,
  },
  clearButton: {
    marginBottom: 10,
  },
});

export default WhiteBoardScoket;
