import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {fp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  body: {
    flex: 1,
    backgroundColor: colors.white,
    padding: 16,
  },
  card: {
    borderWidth: 1,
    borderRadius: 8,
    borderColor: colors.txtGrey,

    marginVertical: 10,
    padding: 20,
  },
  ratingView: {
    marginVertical: fp(1),
    alignItems: 'center',
  },
  ratingTxt: {
    fontFamily: Fonts.medium,
    fontSize: responsiveFontSize(14),
    color: colors.darkBlack,
  },
  input: {
    height: fp(15),
    backgroundColor: colors.txtGrey,
    borderWidth: 1,
    borderRadius: 10,
    borderColor: '#E2E2E2',
    padding: 8,
    marginVertical: fp(2),
    textAlignVertical: 'top',
  },
});

export default styles;
