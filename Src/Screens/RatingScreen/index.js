import {
  View,
  Text,
  SafeAreaView,
  Image,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import React, {useState} from 'react';
import styles from './styles';
import colors from '../../Utils/colors';
import {StatusContainer} from '../../Components/StatusBar';
import icons from '../../Utils/icons';
import {AppHeader} from '../../Components/Header';
import {useTranslation} from 'react-i18next';
import TutorDetails from '../../Components/MyClass/TutorDetails';
import {Rating, AirbnbRating} from 'react-native-ratings';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {PrimaryButton} from '../../Components/CustomButton';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import {useStudentTutorReviewMutation} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';
import {useDispatch} from 'react-redux';

const RatingScreen = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [review, setReview] = useState('');
  const [rating, setRating] = useState('');
  console.log('route=======', route?.params);
  const {bookingId, tutor} = route?.params || {};
  const ratingCompleted = rating => {
    console.log('Rating is: ' + rating);
    setRating(rating);
  };

  const [addRating, {isLoading}] = useStudentTutorReviewMutation();

  const handleSubmit = async () => {
    if (!review) {
      showToast('error', t('pleaseAddReview'), 'bottom', isRTL);
      return;
    } else if (rating < 1) {
      showToast('error', t('add1Star'), 'bottom', isRTL);
      return;
    }
    try {
      let data = {
        tutor_user_id: tutor?.id,
        booking_id: bookingId,
        review: review,
        rating: rating,
      };
      const response = await addRating(data);

      if (response?.data?.status) {
        showToast('success', response?.data?.message, 'bottom', isRTL);
        navigation.navigate('HomeScreen');
      } else {
        showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      }
    } catch (error) {
      console.log('review===', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader
        backIcon={icons.backbtn}
        title={t('rateYourTeacher')}
        style={{zIndex: 20}}
      />
      <View style={styles.body}>
        <View style={styles.card}>
          <View
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TutorDetails
              imageUri={
                tutor?.image
                  ? {uri: IMAGE_BASE_URL + tutor?.image}
                  : {uri: DUMMY_USER_IMG}
              }
              name={tutor?.name}
              occupation={tutor?.tlm_tutor_profile?.occupation}
            />
            <TouchableOpacity style={styles.messageBtn}>
              <Image source={icons.messageIcon} />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.ratingView}>
          <Text style={styles.ratingTxt}>{t('rateYourTutor')}</Text>
          <View style={{marginVertical: 10}}>
            <Rating
              ratingCount={5}
              imageSize={fp(4)}
              showRating={false}
              onFinishRating={ratingCompleted}
              starContainerStyle={{}}
              startingValue={0}
            />
          </View>
        </View>
        <TextInput
          multiline
          value={review}
          placeholder={t('placeHolderMessage')}
          placeholderTextColor={colors.txtGrey2}
          onChangeText={text => setReview(text)}
          style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
        />
      </View>
      <PrimaryButton
        title={t('Ignore')}
        style={{marginVertical: hp(1)}}
        onPress={() => navigation.navigate('Home')}
      />
      <PrimaryButton
        loading={isLoading}
        title={t('submit')}
        onPress={handleSubmit}
        style={{marginVertical: hp(1)}}
      />
    </SafeAreaView>
  );
};

export default RatingScreen;
