import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
    // marginTop: hp(8),
  },
  logoContainer: {
    // alignItems: 'flex-start',
    // marginVertical: 40,
    // marginLeft: 25,
    // position: 'absolute',
    marginTop: -hp(1),
  },
  logo: {
    alignSelf: 'center',
    width: fp(20),
    // position: 'absolute',
    zIndex: 1,
    // backgroundColor: 'red',
    // height: fp(18),
  },
  title: {
    fontSize: fp(3.4),
    fontFamily: Fonts.bold,
    textAlign: 'center',
    color: colors.black,
    marginVertical: hp(1),
  },
  subTitle: {
    fontSize: fp(2),
    fontFamily: Fonts.medium,
    textAlign: 'center',

    color: colors.txtGrey1,
    // marginVertical: hp(1),
  },
  button: {
    // backgroundColor: colors.themeColorlight,
    // width: '100%',
    // padding: 22,
    height: hp(6),
    borderRadius: 10,
    marginBottom: 14,
    alignItems: 'center',
    // marginVertical: hp(1),
  },
});

export default styles;
