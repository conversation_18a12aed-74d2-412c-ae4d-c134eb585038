import AsyncStorage from '@react-native-async-storage/async-storage';
import React from 'react';
import {Image, SafeAreaView, Text, View} from 'react-native';
import {PrimaryButton} from '../../Components/CustomButton';
import i18next from '../../Config/i18next';
import icons from '../../Utils/icons';
import styles from './styles';
import {useDispatch} from 'react-redux';
import {setAppLocale} from '../../Features/authSlice';
import {hp, wp} from '../../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';

const SelectLanguage = ({navigation}) => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const onContinueClick = async appLang => {
    await i18next.changeLanguage(appLang);
    await AsyncStorage.setItem('selectedLang', JSON.stringify(appLang));
    dispatch(setAppLocale(appLang));
    setTimeout(() => {
      navigation.navigate('WelcomeScreen');
    }, 1000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Image
        // source={icons.logo.logoAtSelectLang}
        source={icons.logo?.generalLogoEng}
        style={styles.logo}
        resizeMode="contain"
      />
      <View style={styles.logoContainer}>
        <Text style={styles.title}>{t('selectLanguage')}</Text>
        <Text style={styles.subTitle}>{t('chooseLanguage')}</Text>

        <View style={{marginTop: hp(4), width: wp(100)}}>
          <PrimaryButton
            title="Arabic"
            onPress={() => onContinueClick('ar')}
            style={styles.button}
          />
          <PrimaryButton
            title="English"
            onPress={() => onContinueClick('en')}
            style={styles.button}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SelectLanguage;
