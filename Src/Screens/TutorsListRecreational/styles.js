import { I18nManager, StyleSheet } from 'react-native';
import colors from '../../Utils/colors';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 20,
    color: colors.black,
    fontWeight: 'bold',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    width: '100%',
  },
  teacherCardContainer: {
    width: '48%',
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.themeColor,
    height: 50,
    width: '100%',
  },
  headerButtonContainer: {
    width: '13%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButton: {
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerIcon: {
    height: 18,
    width: 18,
    tintColor: colors.white,
  },
  searchContainer: {
    flex: 1,
  },
  searchWrapper: {
    flexDirection: 'row',
    width: '87%',
    height: '100%',
    paddingRight: 10,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 10,
    height: 35,
    marginLeft: 10,
    paddingHorizontal: 10,
  },
  searchIcon: {
    width: 18,
    height: 18,
    marginRight: 8,
  },
  searchText: {
    flex: 1,
    fontSize: 14,
    color: colors.searchGray,
    padding: 0,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.offWhite1,
    borderRadius: 20,
    height: 25,
    marginLeft: 10,
    paddingHorizontal: 8,
  },
  locationIcon: {
    width: 14,
    height: 14,
  },
  locationText: {
    marginHorizontal: 8,
    color: colors.black,
    fontSize: 12,
  },
  downArrowIcon: {
    width: 14,
    height: 14,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterButtonText: {
    fontSize: 16,
    color: colors.white,
  },
});

export default styles;
