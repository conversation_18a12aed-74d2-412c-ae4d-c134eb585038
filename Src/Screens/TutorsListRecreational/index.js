import {
  Image,
  Pressable,
  SafeAreaView,
  Text,
  View,
  I18nManager,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import styles from './styles';
import icons from '../../Utils/icons';
import TeacherCard from '../../Components/Custom_Components/TeacherCard';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import {FilterButton} from '../../Components/FilterButton';
import {useGetTutorsRecreationalQuery} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import TutorListHeader from '../../Components/TutorListHeader';

// const TutorListHeader = ({onBackPress, navigation}) => {
//   const {t} = useTranslation();

//   return (
//     <View style={styles.header}>
//       <View style={styles.headerButtonContainer}>
//         <Pressable onPress={onBackPress} style={styles.headerButton}>
//           <Image source={icons.backbtn} style={styles.headerIcon} />
//         </Pressable>
//       </View>
//       <Pressable
//         style={styles.searchContainer}
//         onPress={() => navigation.navigate('Search')}>
//         <View style={styles.searchWrapper}>
//           <View style={styles.searchInputContainer}>
//             <Image
//               style={styles.searchIcon}
//               source={icons.searchIcon}
//               resizeMode="contain"
//             />
//             <Text style={styles.searchText}>{t('searchPlaceholder')}</Text>
//             <View style={styles.locationContainer}>
//               <Image
//                 source={icons.locationBlack}
//                 style={styles.locationIcon}
//                 resizeMode="contain"
//               />
//               <Text style={styles.locationText}>{t('locationLabel')}</Text>
//               <Image
//                 source={icons.downArrowBlack}
//                 style={styles.downArrowIcon}
//                 resizeMode="contain"
//               />
//             </View>
//           </View>
//         </View>
//       </Pressable>
//     </View>
//   );
// };

const TutorsListRecreational = ({navigation, route}) => {
  const {t} = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [tutorList, setTutorList] = useState();
  const {
    expertise_id,
    gender,
    grades_id,
    curriculum_id,
    ratingsFrom,
    ratingsTo,
    priceFrom,
    priceTo,
  } = route.params || {};

  const {
    data: tutorData,
    error,
    isLoading,
  } = useGetTutorsRecreationalQuery({
    expertise_id,
    gender,
    grades_id,
    curriculum_id,
    ratingsFrom,
    ratingsTo,
    priceFrom,
    priceTo,
    name: searchQuery,
  });

  useEffect(() => {
    setTutorList(tutorData?.data?.tutorRateCards?.rows);
  }, [tutorData, searchQuery]);

  useEffect(() => {
    console.log('Tutor API Data:', tutorData);
    console.log('Loading Status:', isLoading);
    console.log('Error:', error);
  }, [tutorData, isLoading, error]);

  const expertiseDataName = tutorData?.data?.expertise_data?.name;

  const handleTeacherClick = teacher => {
    navigation.navigate('TutorsDetails', {
      id: teacher.id,
    });
  };

  const renderTeacherCard = ({item: teacher}) => {
    const subject =
      teacher.tlm_tutor_profile?.expertise?.[0]?.tlm_expertise?.name ||
      t('subjectPlaceholder');
    return (
      <View style={styles.teacherCardContainer}>
        <TeacherCard
          key={teacher.id}
          name={teacher.name}
          subject={subject}
          price={teacher.price.toString()}
          rating={teacher.tlm_tutor_profile?.ratings}
          imageUrl={
            teacher.image
              ? {uri: `${IMAGE_BASE_URL}${teacher.image}`}
              : icons.teacherImage1
          }
          isTopTutor={teacher.isTopTutor || false}
          onPress={() => handleTeacherClick(teacher)}
        />
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <TutorListHeader
        onBackPress={() => navigation.navigate('Home')}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />
      <View style={styles.content}>
        <Text
          style={[
            styles.title,
            {textAlign: I18nManager.isRTL ? 'right' : 'left'},
          ]}>
          {`${expertiseDataName || ''} ${t('tutorsTitle', {
            count: tutorData?.data?.tutorRateCards?.count || 0,
          })}`}
        </Text>
        {isLoading ? (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color={colors.themeColor} />
          </View>
        ) : tutorData?.data?.tutorRateCards?.rows?.length > 0 ? (
          <FlatList
            data={tutorList}
            renderItem={renderTeacherCard}
            keyExtractor={teacher => teacher.id.toString()}
            numColumns={2}
            contentContainerStyle={styles.gridContainer}
            columnWrapperStyle={{justifyContent: 'space-between'}}
          />
        ) : (
          <Text style={styles.noDataText}>{t('noTutorsFound')}</Text>
        )}
        <FilterButton
          onPress={() => navigation.navigate('FilterScreen')}
          title={t('filter')}
          textStyle={styles.filterButtonText}
        />
      </View>
    </SafeAreaView>
  );
};

export default TutorsListRecreational;
