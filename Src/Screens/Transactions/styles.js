import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize, SCREEN_WIDTH} from '../../Utils/constant';
import {CurrentRenderContext} from '@react-navigation/native';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

export const styles = StyleSheet.create({
  container: {flex: 1},
  dayTxt: {
    marginLeft: 10,
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    color: colors.darkBlack,
    alignSelf: 'center',
  },
  calender: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.offBlack,
  },
  gradientBorder: {
    padding: 1, // Border thickness
    borderRadius: 10,
    marginVertical: fp(1),
  },
  imageView: {
    height: fp(5),
    width: fp(5),
    borderRadius: fp(3),
    alignItems: 'center',
    justifyContent: 'center',
  },
  withdrawl: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    left: 8,
    color: colors.searchGray,
    lineHeight: hp(2),
  },
  listAmount: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    lineHeight: hp(2),
  },
  calender: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  itemCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 10,
    backgroundColor: colors.white,
    paddingHorizontal: fp(2),
    paddingVertical: fp(1.5),
  },
  saperator: {
    borderWidth: fp(0.06),
    width: wp(100),
    borderColor: colors.lightgreay,
  },
});
