import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  Image,
  Modal,
  TouchableWithoutFeedback,
  Platform,
  FlatList,
  Pressable,
  Alert, // Added Alert import
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {styles} from './styles';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import {useTranslation} from 'react-i18next';
import {
  useGetStudentWalletHistoryQuery,
  useLazyGetStudentWalletHistoryQuery,
} from '../../Api/ApiSlice';
import moment from 'moment';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import DateTimePicker from '@react-native-community/datetimepicker';
import {Fonts} from '../../Utils/Fonts';
import CompleteYourProfilePageOne from '../CompleteYourProfile/CompleteYourProfilePageOne';
import RNFS from 'react-native-fs';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';

const Transactions = ({navigation}) => {
  const {t} = useTranslation();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showPicker, SetShowPicker] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false); // Added loading state

  const [triggerGetTrasations, {data: walletHistory, isLoading}] =
    useLazyGetStudentWalletHistoryQuery();

  useEffect(() => {
    console.log('walletHistory===', JSON.stringify(walletHistory?.data?.rows));
  }, [walletHistory]);

  const handleDateSelect = (event, date) => {
    SetShowPicker(false);
    if (event.type === 'set' && date) {
      setSelectedDate(date);
    }
  };

  const handleGetTrasationsApi = async () => {
    await triggerGetTrasations({
      date: moment(selectedDate).format('YYYY-MM-DD'),
      page: 0,
    });
  };

  useEffect(() => {
    handleGetTrasationsApi();
  }, [selectedDate]);

  const handlePress = useCallback(
    link => {
      navigation.navigate('DocViewer', {
        doc: {uri: link, type: 'application/pdf'},
      });
    },
    [navigation],
  );

  const downloadPDF = async invoice_link => {
    try {
      // Wrap the entire function in try-catch
      setIsDownloading(true); // Set loading state to true

      if (!invoice_link) {
        throw new Error('No invoice link provided');
      }

      const fileUrl = `${IMAGE_BASE_URL}${invoice_link}`;
      const fileName = `downloaded_document_${Date.now()}.pdf`; // Added timestamp to make filename unique
      // const downloadDest = `${RNFS.DownloadDirectoryPath}/${fileName}`;
      let downloadDest;
      if (Platform.OS === 'ios') {
        // For iOS, use DocumentDirectoryPath
        downloadDest = `${RNFS.DocumentDirectoryPath}/${fileName}`;
      } else {
        // For Android, use DownloadDirectoryPath
        downloadDest = `${RNFS.DownloadDirectoryPath}/${fileName}`;
        // Ensure the Downloads directory exists on Android
        await RNFS.mkdir(RNFS.DownloadDirectoryPath);
      }

      const options = {
        fromUrl: fileUrl,
        toFile: downloadDest,
        background: true,
      };

      try {
        const downloadResult = await RNFS.downloadFile(options).promise;

        if (downloadResult.statusCode === 200) {
          Alert.alert('Success', `PDF saved to: ${downloadDest}`);
        } else {
          throw new Error(
            'Download failed with status: ' + downloadResult.statusCode,
          );
        }
      } catch (downloadError) {
        console.log('Download Operation Error:', downloadError);
        Alert.alert('Error', 'Failed to download the PDF');
      }
    } catch (error) {
      console.log('Download Error:', error);
      Alert.alert('Error', error.message || 'Download failed');
    } finally {
      setIsDownloading(false); // Reset loading state
    }
  };

  const renderItem = ({item, index}) => {
    return (
      <View>
        <View style={styles.itemCard}>
          <Pressable
            style={{flexDirection: 'row', alignItems: 'center'}}
            onPress={() => handlePress(item?.invoice_link)}>
            <View
              style={[
                styles.imageView,
                {
                  backgroundColor:
                    item?.type == 'debit' ? colors.lightRed : 'lightblue',
                },
              ]}>
              <Text
                style={[
                  {
                    fontFamily: Fonts.medium,
                    fontSize: fp(1.8),
                    color: colors.lightBlack,
                  },
                ]}>
                {item?.type == 'debit' ? 'W' : 'P'}
              </Text>
            </View>
            <View>
              <Text style={styles.withdrawl}>{item?.description}</Text>
              <Text
                style={{
                  fontFamily: Fonts.medium,
                  fontSize: fp(1.4),
                  left: 8,
                  color: colors.searchGray,
                  lineHeight: hp(2),
                }}>
                {moment(item?.createdAt).format('DD MMM, YYYY, hh:mm A')}
              </Text>
            </View>
          </Pressable>

          <Pressable onPress={() => handlePress(item?.invoice_link)}>
            <Text
              style={[
                styles.listAmount,
                {
                  color:
                    item?.transaction_type == 'debit'
                      ? colors.red
                      : colors.green,
                },
              ]}>
              {item?.transaction_type == 'debit'
                ? `-${item.amount}`
                : `+${item.amount}`}
            </Text>
            <Text style={styles.withdrawl}>{item.currency}</Text>
          </Pressable>
          {item?.invoice_link ? (
            <TouchableOpacity
              onPress={() => downloadPDF(item?.invoice_link)}
              disabled={isDownloading} // Disable button while downloading
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                opacity: isDownloading ? 0.5 : 1, // Visual feedback for disabled state
              }}>
              {/* {isDownloading ? (
              <Text
                style={{
                  fontSize: fp(1.4),
                  fontFamily: Fonts.medium,
                  color: colors.txtGrey2,
                }}>
                Downloading...
              </Text>
            ) : ( */}
              <>
                <Image
                  source={icons.invoiceDownload}
                  style={{height: fp(3), width: fp(3)}}
                  tintColor={colors.themeBackgroundTwo}
                />
                <Text
                  style={{
                    fontSize: fp(1.4),
                    marginTop: hp(0.4),
                    fontFamily: Fonts.medium,
                    color: colors.txtGrey2,
                  }}>
                  PDF
                </Text>
              </>
              {/* )} */}
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={{height: fp(3), width: fp(3)}}
              // onPress={() => downloadPDF(item?.invoice_link)}
              // disabled={isDownloading} // Disable button while downloading
              // style={{
              //   alignItems: 'center',
              //   justifyContent: 'center',
              //   opacity: isDownloading ? 0.5 : 1, // Visual feedback for disabled state
              // }}
            >
              {/* {isDownloading ? (
              <Text
                style={{
                  fontSize: fp(1.4),
                  fontFamily: Fonts.medium,
                  color: colors.txtGrey2,
                }}>
                Downloading...
              </Text>
            ) : ( */}
              {/* <>
                <Image
                  source={icons.invoiceDownload}
                  style={{height: fp(3), width: fp(3)}}
                  tintColor={colors.themeBackgroundTwo}
                />
                <Text
                  style={{
                    fontSize: fp(1.4),
                    marginTop: hp(0.4),
                    fontFamily: Fonts.medium,
                    color: colors.txtGrey2,
                  }}>
                  PDF
                </Text>
              </> */}
              {/* )} */}
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer />
      <AppHeader
        title={t('transactions')}
        backIcon={icons.backbtn}
        isBackBtn
        righContent={
          <TouchableOpacity
            onPress={() => SetShowPicker(!showPicker)}
            activeOpacity={0.8}
            style={{
              flexDirection: 'row',
              alignSelf: 'flex-end',
              justifyContent: 'space-between',
              backgroundColor: colors.lightGreen,
              borderRadius: wp(4),
              alignItems: 'center',
              padding: hp(0.6),
            }}>
            <Image
              source={icons.calanderYellow}
              style={{
                tintColor: colors.black,
                height: hp(2.2),
                width: wp(4),
                left: 5,
              }}
            />
            <View style={{flexDirection: 'row', alignSelf: 'center'}}>
              <Text style={styles.dayTxt}>
                {selectedDate
                  ? moment(selectedDate).isSame(moment(), 'day')
                    ? t('today')
                    : moment(selectedDate).format('DD MMM YYYY')
                  : ''}
              </Text>
              <Image
                source={icons.arrowDown}
                style={{height: 24, width: 24, marginLeft: 5}}
                resizeMode="contain"
              />
            </View>
          </TouchableOpacity>
        }
      />
      <View style={{flex: 1, backgroundColor: colors.white}}>
        <TaleemLoader isLoading={isDownloading} />
        <FlatList
          data={walletHistory?.data?.rows}
          renderItem={renderItem}
          contentContainerStyle={{marginTop: fp(1)}}
          ItemSeparatorComponent={<View style={styles.saperator} />}
          ListEmptyComponent={
            <View>
              <Text
                style={{
                  textAlign: 'center',
                  color: colors.black,
                  fontFamily: Fonts.medium,
                  marginTop: hp(1),
                }}>
                {t('noData')}
              </Text>
            </View>
          }
        />
        {Platform.OS == 'ios' ? (
          <Modal
            transparent
            visible={showPicker}
            style={{alignItems: 'center', justifyContent: 'center'}}>
            <TouchableWithoutFeedback onPress={() => SetShowPicker(false)}>
              <View style={styles.calender}>
                <View style={{backgroundColor: colors.white, borderRadius: 10}}>
                  <DateTimePicker
                    value={selectedDate}
                    mode="date"
                    display="inline"
                    onChange={handleDateSelect}
                    maximumDate={new Date()}
                    style={{width: wp(95), height: hp(45)}}
                  />
                </View>
              </View>
            </TouchableWithoutFeedback>
          </Modal>
        ) : (
          showPicker && (
            <DateTimePicker
              value={selectedDate}
              mode="date"
              display="default"
              onChange={handleDateSelect}
              maximumDate={new Date()}
            />
          )
        )}
      </View>
    </SafeAreaView>
  );
};

export default Transactions;
