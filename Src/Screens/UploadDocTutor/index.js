import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';

const UploadDocTutor = () => {
  const {t} = useTranslation();
  return (
    <View style={styles.container}>
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('chooseCategory')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default UploadDocTutor;
