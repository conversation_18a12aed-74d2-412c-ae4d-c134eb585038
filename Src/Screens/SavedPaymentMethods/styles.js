import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize, SCREEN_WIDTH} from '../../Utils/constant';
import {CurrentRenderContext} from '@react-navigation/native';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  body: {
    flex: 1,
    paddingHorizontal: wp(5),
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(4),
    marginBottom: hp(2),
    borderRadius: wp(2),
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.lightGrey,
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  selectedCard: {
    borderColor: colors.themeColor,
    borderWidth: 1.5,
  },
  radioContainer: {
    marginRight: wp(4),
  },
  radioOuter: {
    height: wp(6),
    width: wp(6),
    borderRadius: wp(3),
    borderWidth: 2,
    borderColor: colors.grey,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioInner: {
    height: wp(3),
    width: wp(3),
    borderRadius: wp(1.5),
    backgroundColor: colors.themeColor,
  },
  cardIcon: {
    height: hp(4),
    width: wp(8),
    marginRight: wp(4),
  },
  cardDetails: {
    flex: 1,
  },
  cardNumber: {
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    color: colors.black,
    marginBottom: hp(0.5),
  },
  cardType: {
    fontSize: fp(1.6),
    fontFamily: Fonts.regular,
    color: colors.txtGrey1,
  },
  listContainer: {
    paddingBottom: hp(2),
    marginTop: hp(2),
  },
  emptyText: {
    textAlign: 'center',
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    color: colors.txtGrey1,
    marginTop: hp(2),
  },
  buttonContainer: {
    marginTop: hp(2),
    marginBottom: hp(4),
    gap: hp(1.5),
  },
  buttonText: {
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
  },
  secondaryButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.themeColor,
  },
  secondaryButtonText: {
    color: colors.themeColor,
  },
});
