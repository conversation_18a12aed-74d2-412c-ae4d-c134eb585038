import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {styles} from './styles';
import {useTranslation} from 'react-i18next';
import {
  useAddAmountStudentWalletMutation,
  useGetStudentPaymentMethodsQuery,
} from '../../Api/ApiSlice';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {PrimaryButton} from '../../Components/CustomButton';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import {PAYMENT_GATEWAY_RETURN_URL} from '../../Utils/constant';

const SavedPaymentMethods = ({navigation, route}) => {
  const {amount} = route?.params;
  const {t} = useTranslation();
  const [selectedMethod, setSelectedMethod] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const {
    data: paymentMethods,
    isLoading: getMethodsLoading,
    refetch: refetchMethods,
  } = useGetStudentPaymentMethodsQuery();
  useEffect(() => {
    refetchMethods();
  }, []);

  console.log('🚀 ~ SavedPaymentMethods ~ selectedMethod:', selectedMethod);
  const [addAmount, {isLoading: withdrawLoadin}] =
    useAddAmountStudentWalletMutation();
  useEffect(() => {
    // Set the first payment method as selected by default
    if (paymentMethods?.data?.length > 0 && !selectedMethod) {
      setSelectedMethod(paymentMethods.data[0]);
    }
  }, [paymentMethods]);

  const handlePayNow = async () => {
    setIsLoading(true);
    console.log('pay now clicked');
    try {
      const response = await addAmount({
        amount: amount,
        returnUrl: PAYMENT_GATEWAY_RETURN_URL,
        tokenId: selectedMethod?.token_id,
      });
      if (response?.data?.status) {
        // setAmount('');
        // showToast('success', response?.data?.message);
        setIsLoading(false);
        navigation.navigate('PaymentScreen', {url: response?.data?.data?.url});
      } else {
        // setAmount('');
        setIsLoading(false);
        showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      }
    } catch (e) {
      setIsLoading(false);
      showToast('error', 'amount error', 'bottom', isRTL);
      console.log('withdraw errror', e);
    }
  };
  const handlePayNowWithNewPayment = async () => {
    try {
      const response = await addAmount({
        amount: amount,
        returnUrl: PAYMENT_GATEWAY_RETURN_URL,
      });
      if (response?.data?.status) {
        // setAmount('');
        // showToast('success', response?.data?.message);
        navigation.navigate('PaymentScreen', {url: response?.data?.data?.url});
      } else {
        // setAmount('');
        showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      }
    } catch (e) {
      showToast('error', 'amount error', 'bottom', isRTL);
      console.log('withdraw errror', e);
    }
  };

  const renderItem = ({item}) => {
    console.log('🚀 ~ renderItem ~ item:', item);
    const isSelected = selectedMethod?.id === item.id;
    const cardIcon =
      item.card_type?.toLowerCase() === 'visa' ? icons.visa : icons.mastercard;

    return (
      <TouchableOpacity
        style={[styles.card, isSelected && styles.selectedCard]}
        onPress={() => setSelectedMethod(item)}
        activeOpacity={0.7}>
        <View style={styles.radioContainer}>
          <View
            style={[
              styles.radioOuter,
              isSelected && {borderColor: colors.themeColor},
            ]}>
            {isSelected && <View style={styles.radioInner} />}
          </View>
        </View>

        <Image source={cardIcon} style={styles.cardIcon} resizeMode="contain" />

        <View style={styles.cardDetails}>
          <Text style={styles.cardNumber}> {item?.card_number || '1234'}</Text>
          <Text style={styles.cardType}>{item.card_type || 'Credit Card'}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer />
      <AppHeader
        title={t('savedPaymentMethods')}
        backIcon={icons.backbtn}
        isBackBtn
      />
      <TaleemLoader isLoading={isLoading} />

      <View style={styles.body}>
        <FlatList
          data={paymentMethods?.data}
          renderItem={renderItem}
          keyExtractor={item => item.id?.toString()}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={
            <Text style={styles.emptyText}>{t('no_payment_methods')}</Text>
          }
        />

        <View style={styles.buttonContainer}>
          {paymentMethods?.data?.length > 0 && (
            <PrimaryButton
              title={t('pay_now')}
              onPress={() => {
                if (selectedMethod) {
                  handlePayNow();
                  // Handle payment with selected method
                } else {
                  showToast(
                    'error',
                    t('pleaseSelectPaymentMethod'),
                    'bottom',
                    isRTL,
                  );
                  console.log('Please select a payment method');
                }
              }}
              disabled={!selectedMethod}
              textStyle={styles.buttonText}
            />
          )}

          <PrimaryButton
            title={t('pay_with_new_payment')}
            onPress={() => handlePayNowWithNewPayment()}
            textStyle={[styles.buttonText, {color: colors.themeBackground}]}
            style={styles.secondaryButton}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SavedPaymentMethods;
