import React, {useState} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  Platform,
  TouchableWithoutFeedback,
  FlatList,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import styles from './styles';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {AppHeader} from '../../Components/Header';
import BookYourTutorCard from '../../Components/Custom_Components/BookYourTutorCard';
import {useTranslation} from 'react-i18next';
import icons from '../../Utils/icons';
import {showToast} from '../../Components/ToastHelper';
import {PrimaryButton} from '../../Components/CustomButton';
import LinearGradient from 'react-native-linear-gradient';
import {useSelector} from 'react-redux';
import {
  convertTo12HourFormat,
  convertToDMY,
  convertToHoursAndMinutes,
  convertToLocal12HourFormat,
} from '../../Helper/DateHelpers/DateHelpers';
import {DUMMY_USER_IMG, PAYMENT_GATEWAY_RETURN_URL} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {
  useGetBookingDetailsQuery,
  usePostStudentPaymentMutation,
} from '../../Api/ApiSlice';
import {FrameHeight} from 'react-native-agora';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import moment from 'moment';
import {Fonts} from '../../Utils/Fonts';
import {openDirections} from '../../Helper/GoogleMapsHelpers';

const BookYourTutorConfirmation = ({navigation, route}) => {
  const {bookingId} = route?.params;
  console.log('🚀 ~ BookYourTutorConfirmation ~ bookingId:', bookingId);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [expandedDates, setExpandedDates] = useState({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(
    t('skipCash'),
  );

  const {
    data: bookingRes,
    error,
    isLoading,
  } = useGetBookingDetailsQuery(bookingId);

  const [
    postStudentPayment,
    {
      data: studentPaymentData,
      error: bookingError,
      isLoading: studentPaymentLoading,
    },
  ] = usePostStudentPaymentMutation();

  const bookingData = bookingRes?.data;
  console.log(
    '🚀 ~ BookYourTutorConfirmation ~ bookingData:',
    JSON.stringify(bookingData),
  );
  const handleAvailability = selectedPaymentMethodParam => {
    console.log(
      '🚀 ~ BookYourTutorConfirmation ~ selectedPaymentMethod:',
      selectedPaymentMethodParam,
    );
    const paymentJson = {
      booking_id: bookingId,
      amount: bookingData?.amount * bookingData?.tlm_booking_enrollments.length,
      returnUrl: PAYMENT_GATEWAY_RETURN_URL,
      payment_method:
        selectedPaymentMethodParam == t('skipCash') ? 'skipCash' : 'wallet', // skipcash, wallet
    };
    console.log('🚀 ~ handleAvailability ~ paymentJson:', paymentJson);

    postStudentPayment(paymentJson)
      .unwrap()
      .then(response => {
        console.log('🚀 ~ handleBookTutor ~ response:', response);
        // navigation.navigate('PaymentScreen', {url: response?.data?.url});
        if (response?.data?.url) {
          navigation.navigate('PaymentScreen', {url: response?.data?.url});
          // showToast('success', response?.message);
        } else if (response?.data?.wallet_status === 'sufficient') {
          console.log(
            '🚀 ~ BookYourTutorConfirmation ~ response?.data?.wallet_status:',
            response?.data?.wallet_status,
          );
          navigation.navigate('My Class');
          showToast('success', response?.message, 'bottom', isRTL);
        }

        // showToast('success', response?.message);
      })

      .catch(err => {
        console.error('Error calculating price:', err);
        if (err?.data?.data?.wallet_status === 'insufficient') {
          setIsModalVisible(true);
        }
        if (err?.status == 409) {
          // case of No email Present
          showToast('error', err?.data?.message, 'bottom', isRTL);
        } else {
          showToast('error', err?.data?.message, 'bottom', isRTL);
        }
      });
  };

  const handlePayUsingCard = () => {
    setIsModalVisible(false);
    setSelectedPaymentMethod(t('skipCash'));
    handleAvailability(t('skipCash'));
  };
  const imageUrl = bookingData?.tutor?.image
    ? {uri: `${IMAGE_BASE_URL}${bookingData?.tutor?.image}`}
    : {uri: DUMMY_USER_IMG};

  const toggleExpandDate = index => {
    setExpandedDates(prev => ({
      ...prev,
      [index]: !prev[index],
    }));
  };
  function renderGroupRows({item}) {
    console.log(item?.payment_status, 'item?.payment_status');
    return (
      <View
        style={{
          borderWidth: 1,
          borderColor: colors.txtGrey,
          borderRadius: 10,
          padding: 10,
          marginVertical: 4,
          backgroundColor: '#fff',
        }}>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
          }}>
          <Text style={styles.labelText}>{t('student_name')} :</Text>
          <Text style={styles.valueText}>
            {capitalizeFirstLetter(item?.tlm_user?.name)}
          </Text>
        </View>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
          }}>
          <Text
            style={[styles.labelText, {textAlign: isRTL ? 'right' : 'left'}]}>
            {t('paymentStatus')} :
          </Text>
          <Text
            style={[
              styles.valueText,
              {
                textAlign: isRTL ? 'right' : 'left',
                color:
                  item?.payment_status == '0'
                    ? colors.orangeLight
                    : item?.payment_status == '1'
                    ? colors.green
                    : colors.lightRed,
              },
            ]}>
            {item?.payment_status == '0' ? 'Pending' : 'Paid'}
          </Text>
        </View>
      </View>
    );
  }

  const CustomCheckbox = ({label, isSelected, onSelect, isRTL}) => (
    <TouchableOpacity
      style={[
        styles.checkboxWrapper,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
      ]}
      onPress={onSelect}>
      <View
        style={[
          styles.checkbox,
          {marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0},
          isSelected && styles.checkboxSelected,
        ]}>
        {isSelected && <Text style={styles.checkMark}>✓</Text>}
      </View>
      <Text style={styles.checkboxLabel}>{label}</Text>
    </TouchableOpacity>
  );

  const result = convertToHoursAndMinutes(
    bookingData?.tlm_booking_schedules?.reduce((total, schedule) => {
      const hours = parseFloat(schedule.tlm_tutor_schedule.hours_duration);
      return total + hours;
    }, 0),
  );

  const handleAddMoneyToWallet = () => {
    navigation?.navigate('Wallet');
  };
  const trimmedAddress = bookingData?.tutor?.address?.trim(); // Remove leading/trailing spaces
  const words = trimmedAddress?.split(/[\s,،-]+/);
  const lastWord = words ? words[words?.length - 1] : '';
  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <ScrollView contentContainerStyle={{flexGrow: 1, paddingBottom: 20}}>
        {/* Header Section */}
        <View style={styles.headerContainer}>
          <AppHeader
            backIcon={icons.backbtn}
            isBackBtn
            title={t('book_tutor_title')}
            style={{zIndex: 20}}
          />
          <View style={{marginLeft: 0, alignSelf: 'center'}}>
            <BookYourTutorCard
              name={bookingData?.tutor?.name}
              title={bookingData?.tutor?.tlm_tutor_profile?.occupation}
              location={lastWord || ''}
              rating={bookingData?.tutor?.tlm_tutor_profile?.ratings}
              reviews={
                bookingData?.tutor?.tlm_tutor_profile?.total_ratings || 0
              }
              expertise={bookingData?.tutor?.tlm_tutor_profile?.tlm_tutor_expertises?.map(
                item => item?.tlm_expertise?.name,
              )}
              imgUrl={imageUrl}
              experience={bookingData?.tutor?.tlm_tutor_profile?.experience}
            />
          </View>
        </View>

        {/* Package Details */}
        <View style={styles.cardContainer}>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              style={styles.icon}
              resizeMode="contain"
              source={icons.calanderYellow}
            />
            <Text style={styles.labelText}>{t('packageStartingDate')} :</Text>
            <Text
              style={[styles.valueText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {moment(bookingData?.package_start_date).format('DD MMM YYYY')}
              {/* {bookingData?.package_start_date?.toLocaleDateString('en-GB')} */}
            </Text>
          </View>
          {bookingData?.tlm_package_type?.name && (
            <View
              style={[
                styles.row,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Image
                style={styles.icon}
                resizeMode="contain"
                source={icons.clockYellow}
              />
              <Text
                style={[
                  styles.labelText,
                  {textAlign: isRTL ? 'right' : 'left'},
                ]}>
                {t('package')} :
              </Text>
              <Text
                style={[
                  styles.valueText,
                  {textAlign: isRTL ? 'right' : 'left'},
                ]}>
                {bookingData?.tlm_package_type?.name}
              </Text>
            </View>
          )}

          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              style={styles.icon}
              resizeMode="contain"
              source={icons.hatYellow}
            />
            <Text
              style={[styles.labelText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {t('classMode')} :
            </Text>
            <Text
              style={[styles.valueText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {bookingData?.tlm_class_type?.name}
            </Text>
          </View>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              style={styles.icon}
              resizeMode="contain"
              source={icons.hatYellow}
            />
            <Text
              style={[styles.labelText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {t('session')} :
            </Text>
            <Text
              style={[styles.valueText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {bookingData?.tlm_sessions_type?.name}
            </Text>
          </View>
          {bookingData?.address && (
            <View
              style={[
                styles.row,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Image
                style={styles.icon}
                resizeMode="contain"
                source={icons.locationGray}
                tintColor={'rgba(232,185,74,1)'}
              />
              <Text
                style={[
                  styles.labelText,
                  {textAlign: isRTL ? 'right' : 'left'},
                ]}>
                {t('meetingPoint')} :
              </Text>
              <Text
                numberOfLines={3}
                onPress={() =>
                  openDirections(bookingData?.latitude, bookingData?.longitude)
                }
                style={[
                  styles.valueText,
                  {
                    textAlign: isRTL ? 'right' : 'left',
                    width: wp(45),
                    lineHeight: hp(2),
                    textDecorationLine: 'underline',
                  },
                ]}>
                {bookingData?.address}
              </Text>
            </View>
          )}
        </View>

        {/* Enrollment Details */}
        {bookingData?.tlm_sessions_type?.name == 'Group' && (
          <View style={styles.cardContainer}>
            <Text
              style={[
                styles.sectionTitle,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {t('group_details')}
            </Text>
            <FlatList
              data={bookingData?.tlm_booking_enrollments}
              renderItem={renderGroupRows}
            />
          </View>
        )}

        {/* Date and Time Selection */}
        <View style={styles.cardContainer}>
          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              style={styles.icon}
              resizeMode="contain"
              source={icons.clockYellow}
            />
            <Text
              style={[styles.labelText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {t('time')} :
            </Text>

            <Text
              style={[styles.valueText, {textAlign: isRTL ? 'right' : 'left'}]}>
              {result}
            </Text>
          </View>

          {bookingData?.tlm_booking_schedules?.map((schedule, index) => {
            // Calculate duration in hours

            // Format start and end time
            const formattedTime = `${convertToLocal12HourFormat(
              schedule?.tlm_tutor_schedule?.start_time,
            )} - ${convertToLocal12HourFormat(
              schedule?.tlm_tutor_schedule?.end_time,
            )}`;

            // Determine display date
            const displayDate = schedule?.date || t('Date');

            return (
              <View
                key={schedule.id || index}
                style={styles.collapsibleContainer}>
                <TouchableWithoutFeedback
                  onPress={() => toggleExpandDate(index)}>
                  <View style={styles.datePicker}>
                    <View
                      style={{
                        flexDirection: isRTL ? 'row-reverse' : 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <Text
                        style={[
                          styles.dateText,
                          {textAlign: isRTL ? 'right' : 'left'},
                        ]}>
                        {t('date')}: {moment(displayDate).format('DD MMM YYYY')}
                      </Text>
                      <View
                        style={[
                          styles.durationContainer,
                          {flexDirection: isRTL ? 'row-reverse' : 'row'},
                        ]}>
                        <LinearGradient
                          colors={['#C6FFC9', '#D4EBFF']}
                          start={{x: 0, y: 0}}
                          end={{x: 1, y: 1}}
                          style={{
                            borderRadius: 20,
                            paddingHorizontal: 2,
                            paddingVertical: 1,
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <Text style={styles.durationText}>
                            {convertToHoursAndMinutes(
                              Number(
                                schedule?.tlm_tutor_schedule?.hours_duration,
                              ),
                            )}
                          </Text>
                        </LinearGradient>
                        {/* <Image
                          source={
                            expandedDates[index]
                              ? icons.upArrow
                              : icons.downArrowBlack
                          }
                          style={styles.arrowIcon}
                        /> */}
                      </View>
                    </View>
                    {/* {expandedDates[index] && ( */}
                    <Text
                      style={[
                        styles.timeText,
                        {textAlign: isRTL ? 'right' : 'left'},
                      ]}>
                      {t('time')}: {formattedTime}
                    </Text>
                    {/* )} */}
                  </View>
                </TouchableWithoutFeedback>
              </View>
            );
          })}
        </View>

        {/* Payment Method */}
        <View style={styles.sectionContainer}>
          <Text
            style={[
              styles.sectionTitle,
              {textAlign: isRTL ? 'right' : 'left'},
            ]}>
            {t('selectPaymentMethod')}
          </Text>
          <CustomCheckbox
            label={t('skipCash')}
            isSelected={selectedPaymentMethod === t('skipCash')}
            onSelect={() => setSelectedPaymentMethod(t('skipCash'))}
            isRTL={isRTL}
          />
          <CustomCheckbox
            label={t('wallet')}
            isSelected={selectedPaymentMethod === t('wallet')}
            onSelect={() => setSelectedPaymentMethod(t('wallet'))}
            isRTL={isRTL}
          />
        </View>

        {/* Total Amount */}
        <View
          style={[
            styles.totalAmountContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Text style={styles.totalAmountText}>{t('totalAmount')}</Text>
          <Text style={styles.totalAmountValue}>
            {' '}
            {bookingData?.amount
              ? `${bookingData?.amount} ${t('currency')}`
              : '---'}
          </Text>
        </View>

        {/* Book Now Button */}
        <PrimaryButton
          onPress={() => handleAvailability(selectedPaymentMethod)}
          title={t('bookNow')}
          style={{backgroundColor: colors.themeColor, marginBottom: 15}}
          textStyle={{fontSize: 16, color: colors.white}}
          loading={studentPaymentLoading}
        />

        {/* OTP Modal */}
        <Modal visible={isModalVisible} transparent animationType="none">
          <TouchableOpacity
            style={styles.modalContainer}
            onPress={() => setIsModalVisible(false)}>
            <View style={styles.modalContent}>
              <View
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: 20,
                }}>
                <Text style={styles.modalTitle}>{t('bookingFailed')}</Text>
                <Image
                  source={icons.cross}
                  style={{
                    height: fp(2),
                    width: fp(2),
                  }}
                />
              </View>
              <Text
                style={{
                  fontSize: fp(2),
                  fontFamily: Fonts.medium,
                  marginBottom: 15,
                  lineHeight: hp(2.4),
                }}>
                {t('lowWalletError')}
              </Text>
              <View
                style={{
                  marginTop: hp(1),
                  alignItems: 'flex-start',
                  justifyContent: 'flex-start',
                }}>
                <PrimaryButton
                  onPress={handleAddMoneyToWallet}
                  title={t('addMoneyToWallet')}
                  style={{
                    backgroundColor: colors.themeColor,
                    marginBottom: 15,
                    width: wp(80),
                    alignSelf: 'center',
                  }}
                  textStyle={{fontSize: 14, color: colors.white}}
                  // loading={studentPaymentLoading}
                />
                <PrimaryButton
                  onPress={handlePayUsingCard}
                  title={t('payUsingCard')}
                  style={{
                    backgroundColor: colors.themeColor,
                    marginBottom: 15,
                    width: wp(80),
                    alignSelf: 'center',
                  }}
                  textStyle={{fontSize: 14, color: colors.white}}
                  // loading={studentPaymentLoading}
                />
              </View>
            </View>
          </TouchableOpacity>
        </Modal>
      </ScrollView>
    </SafeAreaView>
  );
};

export default BookYourTutorConfirmation;
