import {View, Text, Image, TouchableOpacity} from 'react-native';
import React, {useRef, useState} from 'react';
import styles from './styles';
import MapView, {Marker} from 'react-native-maps';
import {useSelector} from 'react-redux';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import icons from '../../Utils/icons';
import {AppHeader} from '../../Components/Header';
import colors from '../../Utils/colors';
import {fp, wp} from '../../Helper/ResponsiveDimensions';
import TutorDetails from '../../Components/MyClass/TutorDetails';
import {PrimaryButton} from '../../Components/CustomButton';
import {useTranslation} from 'react-i18next';

const OfflineScreen = ({navigation}) => {
  const {meetingPoint} = useSelector(state => state?.tutorBookingSlice);
  const {t} = useTranslation();
  const [state, setState] = useState({
    selectedPoint: 'your',
    address: meetingPoint,
    region: null,
    suggestions: [],
    selectedLocation: null,
    loading: false,
  });

  const mapRef = useRef(null);
  return (
    <View style={styles.container}>
      <View style={{flex: 1}}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Image
              source={icons.backbtn}
              style={{tintColor: colors.black, height: 30, width: 30}}
            />
          </TouchableOpacity>
          <Text style={styles.title}>{t('classDetails')}</Text>
        </View>
        <MapView
          style={styles.map}
          initialRegion={state.region}
          showsUserLocation
          ref={mapRef}
          // onRegionChange={region => {
          //   mapRef.current?.animateToRegion(region, ANIMATION_DURATION);
          // }}
        >
          <Marker
            coordinate={{
              latitude: 25.3548,
              longitude: 51.1839,
            }}
            image={{
              uri: `${IMAGE_BASE_URL}document/1734345316053-TaleemMapPin.png`,
            }}
          />
        </MapView>
      </View>
      <View style={styles.bottomView}>
        <View style={styles.cardRow}>
          <Image
            source={icons.locationGray}
            style={{
              tintColor: colors.themeColor,
              height: fp(3),
              width: fp(3),
            }}
          />
          <View style={styles.meetingView}>
            <Text style={styles.meetingTxt}>{t('tutorMeetingPoint')}</Text>
          </View>
        </View>
        <View style={styles.card}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TutorDetails
              imageUri={icons.dummyTeacher}
              name={'Daniela Chikitani'}
              occupation={'Math’s Teacher in University'}
            />
            <TouchableOpacity
              onPress={() => navigation.navigate('ChatScreen')}
              style={styles.messageBtn}>
              <Image source={icons.messageIcon} />
            </TouchableOpacity>
          </View>
        </View>
        <View style={{marginTop: 20}}>
          <Text style={[styles.meetingTxt, {color: '#737373'}]}>
            {
              'Location : Street No. 16, Gate No. 196 Industrial Area, Doha, Qatar'
            }
          </Text>
          <PrimaryButton
            title={'Navigate'}
            style={{marginTop: 10, width: '100%'}}
            onPress={() => navigation.navigate('StartClass')}
          />
        </View>
      </View>
    </View>
  );
};

export default OfflineScreen;
