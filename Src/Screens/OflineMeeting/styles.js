import {StyleSheet} from 'react-native';
import {responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import {fp, hp} from '../../Helper/ResponsiveDimensions';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  header: {
    padding: 20,
    zIndex: 999,
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: responsiveFontSize(16),
    left: 8,
    fontFamily: Fonts.bold,
    color: colors.darkBlack,
    fontWeight: '700',
  },
  bottomView: {
    backgroundColor: colors.white,
    zIndex: 999,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    borderWidth: 1,
    borderColor: colors.lightGrey,
    height: '35%',
    padding: 10,
  },
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  packageTxt: {
    color: colors.txtGrey1,
    fontFamily: Fonts.regular,
    fontSize: responsiveFontSize(12),
    left: 8,
  },
  meetingView: {
    backgroundColor: colors.lightGreen,
    padding: 8,
    height: fp(4),
    borderRadius: 30,
    marginLeft: 10,
  },
  meetingTxt: {
    fontFamily: Fonts.regular,
    fontSize: responsiveFontSize(12),
    color: colors.txtGrey2,
  },
  card: {
    borderWidth: 1,
    borderRadius: 8,
    borderColor: colors.txtGrey,
    marginVertical: 10,
    padding: 20,
  },
  messageBtn: {
    backgroundColor: colors.themeColor,
    borderRadius: 30,
    padding: 8,
    height: 30,
    width: 30,
  },
});

export default styles;
