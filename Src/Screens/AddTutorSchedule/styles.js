// styles.js
import {Dimensions, Platform, StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';
const {height} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
    // paddingHorizontal: Platform.OS === 'android' ? 0 : 10,
  },
  monthTitle: {
    textAlign: 'center',
    color: '#fff',
    fontSize: fp(1.6),
    marginVertical: 10,
    fontFamily: Fonts.bold,
  },
  drowpdownLable: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    marginBottom: 10,
  },
  arrow: {
    height: 10,
    width: 10,
    tintColor: colors.black,
    marginHorizontal: 5,
  },
  dateItem: {
    width: 40,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 3,
  },
  gradientWrapper: {
    borderRadius: 10,
  },
  selectedDate: {
    borderRadius: 10,
  },
  dateText: {
    color: colors.black,
    fontSize: responsiveFontSize(14),
    fontFamily: Fonts.regular,
  },
  selectedDateText: {
    color: '#333',
    fontFamily: Fonts.bold,
  },
  bottomContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingTop: 20,
    width: '100%',
    paddingBottom: 20,
  },
  tabContainer: {
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  tabButton: {
    paddingVertical: 7,
    paddingHorizontal: 20,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  activeTabButton: {
    backgroundColor: colors.themeColor,
  },
  tabButtonText: {
    fontSize: 14,
    fontFamily: Fonts.regular,
    color: colors.black,
  },
  activeTabButtonText: {
    color: '#fff',
  },
  tabContentText: {
    fontSize: 16,
    color: colors.black,
    textAlign: 'center',
    marginVertical: 10,
  },
  saveButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    width: '100%',
    marginTop: 20,
    height: hp(2),
    bottom: 10,
  },

  sectionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: Fonts.semiBold,
    color: colors.txtBlack,
    marginBottom: 8,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: Fonts.bold,
  },
  tabBtn: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 5,
    width: '45%',
  },
  tabTxt: {
    fontSize: responsiveFontSize(14),
    fontFamily: Fonts.bold,
  },
  addKey: {
    color: '#fff',
    fontFamily: Fonts.regular,
    fontSize: 16,
  },
  noData: {
    fontFamily: Fonts.bold,
    textAlign: 'center',
    fontSize: responsiveFontSize(16),
    color: colors.grey,
  },
  addBtn: {
    marginTop: 20,
    backgroundColor: colors.themeColor,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  dayTxt: {
    marginLeft: 10,
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.darkBlack,
    alignSelf: 'center',
  },
  calender: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.offBlack,
  },
  // activeTabBtn: {
  //   backgroundColor: colors.themeColor,
  // },

  slope: {
    // position: 'absolute',
    // bottom: -0, // Position the slope below the button
    // left: 0,
    // right: 0,
    // height: 20,
    height: 10,
    width: 100,
    backgroundColor: colors.themeColor,
    transform: [{skewX: '45deg'}], // Creates the sloped effect
  },
});

export default styles;
