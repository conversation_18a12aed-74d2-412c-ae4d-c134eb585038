import React, {useState, useEffect, useRef} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  Modal,
  Alert,
  TouchableWithoutFeedback,
  Platform,
} from 'react-native';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import LinearGradient from 'react-native-linear-gradient';
import styles from './styles';
import ScheduleCard from '../ScheduleTutor/ScheduleCard';
import {useTranslation} from 'react-i18next';
import TaleemDatePicker from '../../Components/Custom_Components/TaleemDatePicker';
import AddScheduleCard from '../ScheduleTutor/AddScheduleCard';
import {
  useAddTutorScheduleApiMutation,
  useDeleteTutorScheduleMutation,
  useLazyGetScheduleDatesForCalendarTutorQuery,
  useLazyGetTutorSchedulesQuery,
} from '../../Api/ApiSlice';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import CustomDropDown from '../../Components/CustomDropDown';
import {showToast} from '../../Components/ToastHelper';
import {RECURRENCE_TYPE_DATA} from '../../Utils/constant';
import {PrimaryButton} from '../../Components/CustomButton';
import {Fonts} from '../../Utils/Fonts';

import DateTimePicker from '@react-native-community/datetimepicker';
import moment from 'moment';
import TutorAddSlotModal from '../../Components/Custom_Modal/TutorAddSlotModal';
import TaleemEventCalendar from '../../Components/Calendar/TaleemEventCalendar';
import {
  getFirstAndLastDates,
  getFormattedBookingDatesWithSlots,
  getFormattedDatesWithSlots,
  getRemainingDatesFormatted,
} from '../../Helper/Calendar/FormatAvailableSlotDate';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {convertToUTC} from '../../Helper/DateHelpers/DateHelpers';

const AddTutorSchedule = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showAddSlotModal, setShowAddSlotModal] = useState(false);
  const [showCustomModal, setShowCustomModal] = useState(false);
  const [dates, setDates] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [recurrenceType, setRecurrenceType] = useState({
    name: 'Daily',
    value: [0, 1, 2, 3, 4, 5, 6],
  });
  const [isCalendarLoading, setIsCalendarLoading] = useState(false);
  const [customSelectedDays, setCustomSelectedDays] = useState([]);
  // const [selectedTab, setSelectedTab] = useState([]);
  const [selectedTab, setSelectedTab] = useState('1');
  const [selectedTabApi, setSelectedTabApi] = useState([]);
  const flatListRef = useRef(null);
  const [fromTime, setFromTime] = useState('');
  const [toTime, setToTime] = useState('');
  const [showPicker, SetShowPicker] = useState(false);
  const [UTCFromTime, setUTCFromTime] = useState('');
  const [UTCToTime, setUTCToTime] = useState('');
  const [selectedClassType, setSelectedClassType] = useState(
    selectedTab == '1'
      ? {name: 'Online', value: 1}
      : {name: 'Face to Face', value: 2},
  );

  const handleGetSchedulesApi = async () => {
    await triggerGetSchedules({
      date: new Date(selectedDate).toISOString().split('T')[0],
      class_type_id: selectedTab,
    });
  };
  useEffect(() => {
    handleGetSchedulesApi();
  }, [selectedDate, selectedTab]);

  const handleFromTimeChange = time => {
    setFromTime(
      `${new Date(time).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false, // Use 24-hour format
      })}:00`,
    );
    setUTCFromTime(convertToUTC(time));
  };

  const handleToTimeChange = time => {
    setToTime(
      `${new Date(time).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false, // Use 24-hour format
      })}:00`,
    );
    setUTCToTime(convertToUTC(time));
  };

  const [triggerGetSchedules, {data: tutorScheduleData, error, isLoading}] =
    useLazyGetTutorSchedulesQuery();

  const handleSheduleData = async () => {
    await triggerGetSchedules({
      date: new Date(selectedDate).toISOString().split('T')[0],
      class_type_id: selectedTab,
    });
  };

  useEffect(() => {
    handleSheduleData();
  }, [selectedDate, selectedTab]);

  const [addTutorScheduleApi, {isLoading: scheduleLoading}] =
    useAddTutorScheduleApiMutation();

  const addScheduleApi = async () => {
    console.log('🚀 ~ addScheduleApi ~ addScheduleApi:');
    if (fromTime > toTime) {
      showToast('error', t('startTimeErrorMessage'), 'bottom', isRTL);
      return;
    }
    if (!fromTime || !toTime) {
      return showToast('error', t('timeValidationError'), 'bottom', isRTL);
    }

    try {
      const payload = {
        start_time: UTCFromTime,
        end_time: UTCToTime,
        // start_time: fromTime,
        // end_time: toTime,
        recurrence_type: 'weekly',
        // month_dates:
        //   recurrenceType?.name === 'Monthly'
        //     ? [selectedDate?.getDate().toString()]
        //     : [],
        days_of_week:
          // recurrenceType?.name === 'Weekly'
          //   ? [selectedDate.getDay().toString()]
          //   : recurrenceType?.name === 'Daily'
          //   ? ['0', '1', '2', '3', '4', '5', '6']
          //   : recurrenceType?.name === 'Weekend'
          //   ? ['6', '0']
          //   : recurrenceType?.name === 'Week Days'
          //   ? ['1', '2', '3', '4', '5']
          //   : recurrenceType?.name?.toLowerCase() == 'custom'
          //   ? //SelectedDays
          customSelectedDays.map(String),
        // : [],
        class_type_id: selectedTabApi,
        start_date: new Date(selectedDate).toISOString().split('T')[0],
        tag: customSelectedDays.length == 7 ? 'Daily' : 'Weekly',
      };
      console.log('🚀 ~ addScheduleApi ~ payload:', payload);

      const response = await addTutorScheduleApi(payload);
      if (response?.data?.status) {
        await handleSheduleData();
        showToast('success', response?.data?.message, 'bottom', isRTL);
        triggerGetSchedules({
          date: new Date(selectedDate).toISOString().split('T')[0],
          class_type_id: selectedTab,
        });
        setShowAddSlotModal(false);
      } else {
        console.log(
          'response?.error?.data?.message',
          response?.error?.data?.message,
        );
        showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      }
    } catch (error) {
      console.error('Error in addScheduleApi:', error);

      // Show a toast with the error message
      showToast('error', response?.data?.message, 'bottom', isRTL);
    }
  };
  useEffect(() => {
    const now = new Date();
    const daysInMonth = Array.from(
      {length: new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate()},
      (_, i) => {
        const date = new Date(now.getFullYear(), now.getMonth(), i + 1);
        return new Date(
          Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()),
        );
      },
    );
    setDates(daysInMonth);

    const todayIndex = daysInMonth.findIndex(
      d => d.toISOString().split('T')[0] === now.toISOString().split('T')[0],
    );
    setCurrentIndex(todayIndex);

    setTimeout(() => {
      try {
        flatListRef.current?.scrollToIndex({
          animated: true,
          index: todayIndex,
          viewPosition: 0.5,
        });
      } catch (error) {
        console.error('Scroll to index failed', error);
      }
    }, 0);
  }, []);

  const handleDateSelect = date => {
    const dateString = date.dateString;
    setSelectedDate(dateString);

    const updatedMarkedDates = {...markedDatesRef.current};

    // Reset any previous selected date
    Object.keys(updatedMarkedDates).forEach(dateKey => {
      if (updatedMarkedDates[dateKey]?.selected) {
        delete updatedMarkedDates[dateKey].selected;
        delete updatedMarkedDates[dateKey].color;
        delete updatedMarkedDates[dateKey].textColor;
      }
    });

    // Apply new selected date styles
    updatedMarkedDates[dateString] = {
      ...updatedMarkedDates[dateString],
      selected: true,
    };

    markedDatesRef.current = updatedMarkedDates;
    SetShowPicker(false);
  };

  // const [updateTutorSchedule, {isLoading: updateScheduleLoading}] =
  //   useUpdateTutorScheduleMutation();
  const [deleteTutorSchedule, {isLoading: deleteLoading}] =
    useDeleteTutorScheduleMutation();
  const [showNestedTimePicker, setShowNestedTimePicker] = useState(false);
  const handleDelete = async (id, type) => {
    try {
      const body = {id: id, type: type};
      const response = await deleteTutorSchedule(body);
      if (response?.data?.status) {
        await handleSheduleData();
        showToast('success', response?.data?.message, 'bottom', isRTL);
      } else {
        showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      }
    } catch (error) {
      showToast('error', response?.error?.data?.message, 'bottom', isRTL);
      console.log('error', error);
    }
  };
  function selectedDaysCallback(params) {
    setCustomSelectedDays(params);
    console.log('🚀 ~ AddTutorSchedule ~ params:', params);
  }
  const [
    fetchSchedules,
    {data: scheduleCalendarData, isLoading: isLoadingSchedules},
  ] = useLazyGetScheduleDatesForCalendarTutorQuery();
  function handleGetScheduleDataForCalendar(year, month, startDate, endDate) {
    setIsCalendarLoading(true);
    const params = {
      startDate: startDate,
      endDate: endDate,
      class_type_id: selectedTab,
    };

    fetchSchedules(params)
      .unwrap()
      .then(response => {
        const scheduleDatesRes = response?.data?.schedule_dates;
        const bookingDatesApiRes = response?.data?.booking_dates;

        // Format remaining dates with grey color
        const remainingDatesFormattedData = getRemainingDatesFormatted(
          scheduleDatesRes,
          bookingDatesApiRes,
          year,
          month,
          '400',
          colors.darkGrey,
        );

        // Format scheduled dates with bold and black
        const slotsDatesFormattedData = getFormattedDatesWithSlots(
          scheduleDatesRes,
          bookingDatesApiRes,
          'bold',
          colors.darkBlack,
        );

        // Format booking dates with yellow dots
        const bookingDatesFormattedData = getFormattedBookingDatesWithSlots(
          bookingDatesApiRes,
          {...slotsDatesFormattedData, ...remainingDatesFormattedData},
          'bold',
          colors.darkBlack,
        );

        // Merge all formatted dates
        const allFormattedDates = {
          ...remainingDatesFormattedData,
          ...slotsDatesFormattedData,
          ...bookingDatesFormattedData,
        };

        // Add selected date styling
        if (selectedDate) {
          console.log(
            'selectedDate 789854684',
            allFormattedDates[selectedDate],
          );
          allFormattedDates[selectedDate] = {
            ...allFormattedDates[selectedDate],
            // Ensure the background color is consistent
            // color: colors.themeBackground, // Background color for selected date
            // Merge custom styles without overriding fontWeight
            customStyles: {
              container: {
                backgroundColor: colors.themeBackground,
              },
              text: {
                color:
                  allFormattedDates[selectedDate]?.customStyles?.text?.color, // Text color for selected date
                fontWeight:
                  allFormattedDates[selectedDate]?.customStyles?.text
                    ?.fontWeight || '300',
              },
            },
          };
        }

        markedDatesRef.current = allFormattedDates;
        setIsCalendarLoading(false);
      })
      .catch(err => {
        console.error('Error:', err);
        showToast('error', err?.data?.message, 'bottom', isRTL);
        setIsCalendarLoading(false);
      });
  }
  const markedDatesRef = useRef({});

  function handleRealTimeToTimeValidations(item) {
    console.log('🚀 ~ handleRealTimeToTimeValidations ~ item:', item);
    if (true) {
      setShowNestedTimePicker(false);
      Alert.alert('Please select from time');
    }
  }

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: 'white'}}>
      <StatusContainer color={colors.themeColor} />
      <View style={styles.container}>
        <AppHeader
          backIcon={icons.backbtn}
          isBackBtn
          title={t('AddYourSchedule')}
        />
        <View
          style={[
            styles.tabContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <TouchableOpacity
            onPress={() => setSelectedTab('1')}
            style={[
              styles.tabBtn,
              // selectedTab === 1 && styles.activeTabBtn, // Apply active style
            ]}>
            <Text
              style={[
                styles.tabTxt,
                {color: selectedTab == 1 ? colors.white : colors.black},
              ]}>
              {t('online')}
            </Text>
            {/* {selectedTab == 1 && <View style={styles.slope} />} */}
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setSelectedTab('2')}
            style={styles.tabBtn}>
            <Text
              style={[
                styles.tabTxt,
                {color: selectedTab == 2 ? colors.white : colors.black},
              ]}>
              {t('face_to_face')}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.bottomContainer}>
          <TouchableOpacity
            onPress={() => SetShowPicker(!showPicker)}
            activeOpacity={0.8}
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              // alignSelf: 'center',
              marginBottom: hp(1),
              justifyContent: 'space-between',
            }}>
            <Text style={styles.dayTxt}>{t('selectDate')}</Text>
            <View
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignSelf: 'center',
              }}>
              <Image
                source={icons.calanderYellow}
                style={{height: fp(2.6), width: fp(2.6), alignSelf: 'center'}}
                resizeMode="contain"
              />
              <Text style={styles.dayTxt}>
                {selectedDate
                  ? moment(selectedDate).isSame(moment(), 'day')
                    ? t('today')
                    : moment(selectedDate).format('DD MMM YYYY')
                  : ''}
              </Text>
              <Image
                source={icons.arrowDown}
                style={{height: 24, width: 24, marginLeft: 5}}
                resizeMode="contain"
              />
            </View>
          </TouchableOpacity>

          {/* {Platform.OS == 'ios' ? (
            <Modal
              transparent
              visible={showPicker}
              style={{alignItems: 'center', justifyContent: 'center'}}>
              <TouchableWithoutFeedback onPress={() => SetShowPicker(false)}>
                <View style={styles.calender}>
                  <View
                    style={{backgroundColor: colors.white, borderRadius: 10}}>
                    <DateTimePicker
                      value={selectedDate}
                      mode="date"
                      display="inline"
                      onChange={handleDateSelect}
                      minimumDate={new Date()}
                    />
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Modal>
          ) : (
            showPicker && (
              <DateTimePicker
                value={selectedDate}
                mode="date"
                display="default"
                onChange={handleDateSelect}
                minimumDate={new Date()}
              />
            )
          )} */}

          <Modal
            transparent
            visible={showPicker}
            style={{
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <TouchableWithoutFeedback onPress={() => SetShowPicker(false)}>
              <View style={styles.calender}>
                <TaleemEventCalendar
                  selectedDate={selectedDate}
                  handleDateSelect={handleDateSelect}
                  markedDates={markedDatesRef.current}
                  isLoading={isCalendarLoading}
                  handleOnMonthChange={dateObj => {
                    const {firstDay, lastDay, year, month} =
                      getFirstAndLastDates(dateObj.dateString);
                    handleGetScheduleDataForCalendar(
                      year,
                      month,
                      firstDay,
                      lastDay,
                    );
                  }}
                  isShowAllInstructions={false}
                />
              </View>
            </TouchableWithoutFeedback>
          </Modal>
          <TaleemLoader isLoading={isLoadingSchedules || isLoading} />
          <FlatList
            data={tutorScheduleData?.data?.rows}
            ListEmptyComponent={
              <View
                style={{
                  alignSelf: 'center',
                  justifyContent: 'center',
                  flex: 1,
                  marginTop: hp(2),
                }}>
                <Text
                  style={{
                    color: colors.grey,
                    fontFamily: Fonts.medium,
                  }}>
                  {t('noSchedules')}
                </Text>
              </View>
            }
            renderItem={({item}) => (
              <AddScheduleCard
                item={item}
                selectedDate={selectedDate}
                onCrossPress={() => {
                  Alert.alert(t('areYouSure'), t('deleteSlotMsg'), [
                    {
                      text: t('cancel'),
                      onPress: () => console.log('Cancel Pressed'),
                      style: 'cancel',
                    },
                    {
                      text: t('all'),
                      onPress: () => handleDelete(item?.id, 'recurring'),
                    },
                    {
                      // text: `${dayNames[selectedDate?.getDay()]}`,
                      text: t('ok'),
                      onPress: () => handleDelete(item?.id, 'single'),
                    },
                  ]);
                }}
                availablity={selectedTab.toString()}
              />
            )}
            keyExtractor={item => item.id.toString()}
          />
          <PrimaryButton
            onPress={() => setShowAddSlotModal(true)}
            title={t('add_new_schedule')}
            style={{backgroundColor: colors.themeColor}}
            textStyle={{fontSize: 16, color: colors.white}}
          />
        </View>

        <TutorAddSlotModal
          showAddSlotModal={showAddSlotModal}
          setShowAddSlotModal={setShowAddSlotModal}
          handleFromTimeChange={handleFromTimeChange}
          handleToTimeChange={handleToTimeChange}
          heading={t('addSlot')}
          // recurrenceType={recurrenceType}
          // setRecurrenceType={setRecurrenceType}
          selectedTab={selectedTabApi}
          setSelectedTab={setSelectedTabApi}
          handleAddButton={addScheduleApi}
          selectedDaysCallback={selectedDaysCallback}
          handleRealTimeToChange={handleRealTimeToTimeValidations}
          showDatePicker={showNestedTimePicker}
          setShowDatePicker={setShowNestedTimePicker}
        />
      </View>
    </SafeAreaView>
  );
};

export default AddTutorSchedule;
