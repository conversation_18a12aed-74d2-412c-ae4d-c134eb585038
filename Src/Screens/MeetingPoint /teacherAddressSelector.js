import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Image} from 'react-native';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';
import {
  updateMeetingPoint,
  updateMeetingTutorPoint,
} from '../../Redux/Slices/Student/TutorBookingSlice';

const TeacherAddressSelector = React.memo(
  ({tutorMeetingPointAddressData = [], onSelectTutorMeetingPoint}) => {
    const {t, i18n} = useTranslation();
    const isRTL = i18n.language === 'ar';
    const dispatch = useDispatch();

    const [packages, setPackages] = useState([]);

    // Memoize the initial package data to prevent unnecessary re-computation
    const initializedPackages = useMemo(() => {
      if (
        tutorMeetingPointAddressData &&
        Array.isArray(tutorMeetingPointAddressData)
      ) {
        return tutorMeetingPointAddressData.map(pkg => ({
          ...pkg,
          expanded: false,
        }));
      }
      return [];
    }, [tutorMeetingPointAddressData]);

    useEffect(() => {
      // Only update state if the data has actually changed
      setPackages(prevPackages => {
        if (
          JSON.stringify(prevPackages) !== JSON.stringify(initializedPackages)
        ) {
          return initializedPackages;
        }
        return prevPackages;
      });
    }, [initializedPackages]);

    // Memoize the toggle function to prevent unnecessary recreations
    const toggleExpand = useCallback(
      id => {
        setPackages(prevPackages => {
          const updatedPackages = prevPackages.map(pkg => ({
            ...pkg,
            expanded: pkg?.id === id ? !pkg?.expanded : false,
          }));

          // Find the selected package
          const selectedPackage = updatedPackages.find(
            pkg => pkg.id === id && pkg.expanded,
          );

          // Only dispatch and call callback if a package was actually selected
          if (selectedPackage) {
            dispatch(updateMeetingTutorPoint(selectedPackage));
            onSelectTutorMeetingPoint?.(selectedPackage);
          }

          return updatedPackages;
        });
      },
      [dispatch, onSelectTutorMeetingPoint],
    );

    return (
      <View style={styles.container}>
        <Text style={styles.heading}>{t('availableMeetingPoint')}</Text>
        {packages.length > 0 ? (
          packages.map(pkg => (
            <View
              key={pkg?.id}
              style={[
                styles.packageWrapper,
                {
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  alignItems: isRTL ? 'flex-end' : 'flex-start',
                },
              ]}>
              <View
                style={[
                  styles.radioContainer,
                  {paddingRight: isRTL ? 0 : 10, paddingLeft: isRTL ? 10 : 0},
                ]}>
                <View
                  style={[styles.radio, pkg.expanded && styles.radioSelected]}>
                  {pkg?.expanded && <View style={styles.radioInner} />}
                </View>
              </View>
              <View style={styles.packageContainerWrapper}>
                <TouchableOpacity
                  style={styles.packageContainer}
                  onPress={() => toggleExpand(pkg?.id)}
                  activeOpacity={0.7}>
                  <Image
                    source={icons.locationGray}
                    resizeMode="contain"
                    style={{height: 30, width: 30}}
                  />
                  <View style={styles.packageInfo}>
                    <Text style={styles.packageTitle}>
                      {pkg?.address + ', ' + pkg?.city + ', ' + pkg?.country}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ))
        ) : (
          <Text>{t('noAddressesAvailable')}</Text>
        )}
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  heading: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#000',
  },
  packageWrapper: {
    marginBottom: 12,
  },
  radioContainer: {
    alignSelf: 'center',
  },
  radio: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#666',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioSelected: {
    borderColor: colors.themeColor,
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.themeColor,
  },
  packageContainerWrapper: {
    flex: 1,
  },
  packageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    gap: 10,
  },
  packageInfo: {
    flex: 1,
  },
  packageTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: 'black',
  },
});

export default TeacherAddressSelector;
