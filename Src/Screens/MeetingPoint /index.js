import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Image,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  PermissionsAndroid,
  FlatList,
  SafeAreaView,
  Alert,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {useDispatch, useSelector} from 'react-redux';
import MapView, {Marker} from 'react-native-maps';
import Geolocation from 'react-native-geolocation-service';
import {Linking} from 'react-native';

import {AppHeader} from '../../Components/Header';
import {StatusContainer} from '../../Components/StatusBar';
import {PrimaryButton} from '../../Components/CustomButton';
import TeacherAddressSelector from './teacherAddressSelector';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {
  getReverseGeoCodeGoogleApi,
  GOOGLE_PLACE_API_KEY,
} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {updateMeetingPoint} from '../../Redux/Slices/Student/TutorBookingSlice';
import {useLazyGetTutorMeetingPointAddressQuery} from '../../Api/ApiSlice';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import icons from '../../Utils/icons';
import {tutorBookingJson} from '../../Api/Model/TutorBookingModel';
import CustomCheckbox from '../../Components/CustomCheckbox';

const ANIMATION_DURATION = 1000;

const SelectClassTypeCard = React.memo(
  ({color, image, title, isSelected, onPress, isRTL}) => (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.classTypeCard,
        {
          backgroundColor: color,
          borderColor: isSelected ? colors.themeColor : colors.lightGrey,
          alignItems: isRTL ? 'flex-end' : 'flex-start',
        },
      ]}>
      <Image source={image} style={styles.cardImage} />
      <Text
        style={[
          styles.cardTitle,
          {color: isSelected ? '#fff' : colors.txtGrey1},
        ]}>
        {title}
      </Text>
    </TouchableOpacity>
  ),
);

const MeetingPoint = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dispatch = useDispatch();
  const {is_tutor_available} = route.params;
  const {tutorData} = useSelector(state => state?.tutorBookingSlice);

  const [isSaveAddress, setIsSaveAddress] = useState(false);
  const [showSavedAddress, setShowSavedAddress] = useState(false);
  const [savedAddresses, setSavedAddresses] = useState([]);
  const [state, setState] = useState({
    selectedPoint: is_tutor_available ? 'your' : 'tutor',
    address: '',
    region: null,
    suggestions: [],
    selectedLocation: null,
    loading: false,
  });

  const mapRef = useRef(null);
  const scrollViewRef = useRef(null);

  const [
    getTutorMeetingPointAddress,
    {data: tutorMeetingPointAddressData, isLoading},
  ] = useLazyGetTutorMeetingPointAddressQuery();

  const requestLocationPermission = async () => {
    try {
      if (Platform.OS === 'ios') {
        const status = await Geolocation.requestAuthorization('whenInUse');
        return status === 'granted';
      } else {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      }
    } catch (err) {
      console.warn('Permission request error:', err);
      return false;
    }
  };

  const fetchAddressFromCoordinates = useCallback(
    async (latitude, longitude) => {
      try {
        const url = getReverseGeoCodeGoogleApi(latitude, longitude);
        const response = await fetch(url);
        const data = await response.json();
        if (data.status === 'OK' && data.results.length > 0) {
          const formattedAddress = data.results[0].formatted_address;
          const cityComponent =
            data.results[0].address_components.find(comp =>
              comp.types.includes('locality'),
            ) ||
            data.results[0].address_components.find(comp =>
              comp.types.includes('administrative_area_level_2'),
            );
          const countryComponent = data.results[0].address_components.find(
            comp => comp.types.includes('country'),
          );
          const city = cityComponent ? cityComponent.long_name : 'NA';
          const country = countryComponent ? countryComponent.long_name : 'NA';
          setState(prev => ({
            ...prev,
            address: formattedAddress,
            selectedLocation: {lat: latitude, lng: longitude, city, country},
            suggestions: [], // Clear suggestions after dragging
          }));
          return formattedAddress;
        } else {
          console.error('Reverse geocoding failed:', data.status);
          Alert.alert(t('error'), t('failedToRetrieveAddress'));
          return null;
        }
      } catch (error) {
        console.error('Error fetching address from coordinates:', error);
        Alert.alert(t('error'), t('failedToRetrieveAddress'));
        return null;
      }
    },
    [t],
  );

  const handleDragEnd = useCallback(
    async event => {
      const {latitude, longitude} = event.nativeEvent.coordinate;
      const newRegion = {
        latitude,
        longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
      setState(prev => ({
        ...prev,
        region: newRegion,
        selectedLocation: {lat: latitude, lng: longitude},
      }));
      mapRef.current?.animateToRegion(newRegion, ANIMATION_DURATION);
      await fetchAddressFromCoordinates(latitude, longitude);
    },
    [fetchAddressFromCoordinates],
  );

  const initializeLocation = useCallback(async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      console.log('Location permission denied');
      return;
    }

    Geolocation.getCurrentPosition(
      position => {
        const {latitude, longitude} = position.coords;
        setState(prev => {
          if (
            prev.region?.latitude === latitude &&
            prev.region?.longitude === longitude
          ) {
            return prev;
          }
          const newRegion = {
            latitude,
            longitude,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          };
          fetchAddressFromCoordinates(latitude, longitude); // Set initial address
          return {
            ...prev,
            region: newRegion,
            selectedLocation: {lat: latitude, lng: longitude},
          };
        });
      },
      error => {
        console.error('Geolocation error:', error);
        if (Platform.OS === 'ios') {
          Alert.alert(
            'Location Error',
            'Please enable location services in Settings to use this feature',
            [
              {text: 'Open Settings', onPress: () => Linking.openSettings()},
              {text: 'Cancel', style: 'cancel'},
            ],
          );
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
        distanceFilter: 10,
      },
    );
  }, [fetchAddressFromCoordinates]);

  useEffect(() => {
    initializeLocation();
    if (tutorData?.profileData?.id) {
      getTutorMeetingPointAddress({
        type: 2,
        tutor_user_id: tutorData?.profileData?.id,
      });
    }

    return () => {
      Geolocation.stopObserving();
    };
  }, [initializeLocation, tutorData?.profileData?.id]);

  const handleGetSavedAddress = useCallback(() => {
    getTutorMeetingPointAddress({
      type: 1, // for student's saved addresses
      tutor_user_id: tutorData?.profileData?.id,
    }).then(res => {
      setSavedAddresses(res?.data?.data || []);
    });
  }, [tutorData?.profileData?.id, getTutorMeetingPointAddress]);

  const handleAddressChange = useCallback(async text => {
    setState(prev => ({...prev, address: text}));
    if (text.length > 2) {
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${text}&key=${GOOGLE_PLACE_API_KEY}&language=en`,
        );
        const data = await response.json();
        setState(prev => ({
          ...prev,
          suggestions: data.status === 'OK' ? data.predictions : [],
        }));
      } catch (error) {
        console.error('Error fetching suggestions:', error);
        setState(prev => ({...prev, suggestions: []}));
      }
    } else {
      setState(prev => ({...prev, suggestions: []}));
    }
  }, []);

  const handleSuggestionPress = useCallback(async suggestion => {
    try {
      setState(prev => ({...prev, loading: true}));
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/details/json?place_id=${suggestion?.place_id}&key=${GOOGLE_PLACE_API_KEY}`,
      );
      const data = await response.json();

      if (data.status === 'OK') {
        const {lat, lng} = data.result.geometry.location;
        const cityComponent =
          data.result.address_components.find(comp =>
            comp.types.includes('locality'),
          ) ||
          data.result.address_components.find(comp =>
            comp.types.includes('administrative_area_level_2'),
          );
        const countryComponent = data.result.address_components.find(comp =>
          comp.types.includes('country'),
        );
        const city = cityComponent ? cityComponent.long_name : 'NA';
        const country = countryComponent ? countryComponent.long_name : 'NA';

        const newRegion = {
          latitude: lat,
          longitude: lng,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        };

        setState(prev => ({
          ...prev,
          address: suggestion.description,
          suggestions: [],
          region: newRegion,
          selectedLocation: {lat, lng, city, country},
          loading: false,
        }));

        mapRef.current?.animateToRegion(newRegion, ANIMATION_DURATION);
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
      setState(prev => ({...prev, loading: false}));
    }
  }, []);

  const handleCurrentLocation = useCallback(async () => {
    try {
      setState(prev => ({...prev, loading: true}));

      const hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        console.log('Location permission denied');
        setState(prev => ({...prev, loading: false}));
        return;
      }

      Geolocation.getCurrentPosition(
        async position => {
          const {latitude, longitude} = position.coords;
          const newRegion = {
            latitude,
            longitude,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          };

          setState(prev => ({
            ...prev,
            region: newRegion,
            selectedLocation: {lat: latitude, lng: longitude},
          }));

          mapRef.current?.animateToRegion(newRegion, ANIMATION_DURATION);
          await fetchAddressFromCoordinates(latitude, longitude);
          setState(prev => ({...prev, loading: false}));
        },
        error => {
          console.error('Geolocation error:', error);
          setState(prev => ({...prev, loading: false}));
          if (Platform.OS === 'ios') {
            Alert.alert(
              'Location Error',
              'Please enable location services in Settings to use this feature',
              [
                {text: 'Open Settings', onPress: () => Linking.openSettings()},
                {text: 'Cancel', style: 'cancel'},
              ],
            );
          } else {
            Alert.alert(t('error'), t('failedToGetLocation'));
          }
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
          distanceFilter: 10,
        },
      );
    } catch (error) {
      console.error('Error getting current location:', error);
      setState(prev => ({...prev, loading: false}));
    }
  }, [fetchAddressFromCoordinates, t]);

  const handleTutorMeetingPointSelection = useCallback(selectedPoint => {
    setState(prev => ({
      ...prev,
      selectedPoint: 'tutor',
      address: selectedPoint?.address,
      region: {
        latitude: selectedPoint?.latitude,
        longitude: selectedPoint?.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      },
      selectedLocation: {
        lat: selectedPoint?.latitude,
        lng: selectedPoint?.longitude,
      },
    }));
  }, []);

  const handleSave = useCallback(() => {
    dispatch(updateMeetingPoint(state.address));
    tutorBookingJson.meeting_point_preferred =
      state.selectedPoint === 'your' ? 'student' : 'tutor';
    tutorBookingJson.latitude = state?.region?.latitude;
    tutorBookingJson.longitude = state?.region?.longitude;
    tutorBookingJson.address = state?.address;
    tutorBookingJson.save_address = isSaveAddress;
    navigation.goBack();
  }, [
    state.address,
    state.selectedPoint,
    state.region,
    isSaveAddress,
    dispatch,
    navigation,
  ]);

  const renderSuggestionItem = useCallback(
    ({item}) => (
      <TouchableOpacity
        style={styles.suggestionItem}
        onPress={() => handleSuggestionPress(item)}>
        <Text style={styles.suggestionText}>{item.description}</Text>
      </TouchableOpacity>
    ),
    [handleSuggestionPress],
  );

  const handleSavedAddressPress = useCallback(item => {
    const lat = item?.latitude;
    const lng = item?.longitude;
    const newRegion = {
      latitude: lat,
      longitude: lng,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    };

    setState(prev => ({
      ...prev,
      address: item.address,
      suggestions: [],
      region: newRegion,
      selectedLocation: {lat, lng},
      loading: false,
    }));
    mapRef.current?.animateToRegion(newRegion, ANIMATION_DURATION);
  }, []);

  const renderSavedAddressItem = useCallback(
    ({item}) => (
      <TouchableOpacity
        style={styles.suggestionItem}
        onPress={() => handleSavedAddressPress(item)}>
        <Text style={styles.suggestionText}>{item.address}</Text>
      </TouchableOpacity>
    ),
    [handleSavedAddressPress],
  );

  const handleOnSuggestionsPress = useCallback(() => {
    navigation?.navigate('SuggestedLocList');
  }, [navigation]);

  return (
    <SafeAreaView style={styles.container}>
      <TaleemLoader isLoading={isLoading || state.loading} />
      <StatusContainer color={colors.themeColor} />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('meeting_point')}
        isHome={false}
      />

      <View style={styles.mapBackground}>
        <View style={styles.mapContainer}>
          {state.region && (
            <MapView
              style={styles.map}
              initialRegion={state.region}
              region={state.region}
              showsUserLocation
              ref={mapRef}>
              <Marker
                coordinate={{
                  latitude: state.region.latitude,
                  longitude: state.region.longitude,
                }}
                image={{
                  uri: `${IMAGE_BASE_URL}document/1734345316053-TaleemMapPin.png`,
                }}
                draggable
                onDragEnd={handleDragEnd}
              />
            </MapView>
          )}
        </View>
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="always">
          <View style={styles.bottomSheet}>
            <Text style={styles.sectionTitle}>{t('meeting_point')}</Text>

            <View style={styles.buttonContainer}>
              {is_tutor_available && (
                <SelectClassTypeCard
                  color={
                    state.selectedPoint === 'your' ? colors.themeColor : '#fff'
                  }
                  image={icons.meetingPointLocation}
                  title={t('yourMeetingPoint')}
                  isSelected={state.selectedPoint === 'your'}
                  onPress={() =>
                    setState(prev => ({...prev, selectedPoint: 'your'}))
                  }
                  isRTL={isRTL}
                />
              )}
              <SelectClassTypeCard
                color={
                  state.selectedPoint === 'tutor' ? colors.themeColor : '#fff'
                }
                image={icons.meetingPointTutor}
                title={t('tutorMeeting')}
                isSelected={state.selectedPoint === 'tutor'}
                onPress={() =>
                  setState(prev => ({...prev, selectedPoint: 'tutor'}))
                }
                isRTL={isRTL}
              />
              <SelectClassTypeCard
                color={
                  state.selectedPoint === 'suggested'
                    ? colors.themeColor
                    : '#fff'
                }
                image={icons.meetingPointTutor}
                title={t('suggestedMeeting')}
                isSelected={state.selectedPoint === 'suggested'}
                onPress={handleOnSuggestionsPress}
                isRTL={isRTL}
              />
            </View>

            {state.selectedPoint === 'your' ? (
              <View style={styles.sectionContainer}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginBottom: hp(0.4),
                  }}>
                  <Text
                    style={{
                      fontSize: 16,
                      color: 'black',
                      fontFamily: Fonts.bold,
                      alignSelf: 'center',
                    }}>
                    {showSavedAddress
                      ? t('Select Address')
                      : t('Enter address')}
                  </Text>
                  <PrimaryButton
                    title={
                      showSavedAddress
                        ? t('Enter Address')
                        : t('Saved Addresses')
                    }
                    onPress={() => {
                      setShowSavedAddress(prev => !prev);
                      if (!showSavedAddress) handleGetSavedAddress();
                    }}
                    style={{width: wp(40), height: fp(4)}}
                    textStyle={{
                      fontSize: fp(1.6),
                      fontFamily: Fonts.semiBold,
                    }}
                  />
                </View>

                {showSavedAddress ? (
                  <FlatList
                    nestedScrollEnabled
                    data={savedAddresses}
                    keyExtractor={item => item.id.toString()}
                    renderItem={renderSavedAddressItem}
                    style={styles.suggestionsList}
                  />
                ) : (
                  <>
                    <View style={styles.addressInput}>
                      <Image
                        source={icons.locationGray}
                        resizeMode="contain"
                        style={styles.locationIcon}
                      />
                      <TextInput
                        style={styles.textInput}
                        placeholder={t('enterAddress')}
                        placeholderTextColor={colors.txtGrey3}
                        value={state.address}
                        onChangeText={handleAddressChange}
                        returnKeyType="done"
                      />
                    </View>

                    {state.suggestions.length > 0 && (
                      <FlatList
                        nestedScrollEnabled
                        data={state.suggestions}
                        keyExtractor={item => item.place_id}
                        renderItem={renderSuggestionItem}
                        style={styles.suggestionsList}
                      />
                    )}

                    <TouchableOpacity
                      style={[
                        styles.locationButton,
                        {
                          flexDirection: isRTL ? 'row-reverse' : 'row',
                          alignSelf: isRTL ? 'flex-end' : 'flex-start',
                        },
                      ]}
                      onPress={handleCurrentLocation}>
                      <Text style={styles.locationButtonText}>
                        {t('useCurrentLocation')}
                      </Text>
                      <Image
                        source={icons.MyLocation}
                        resizeMode="contain"
                        style={styles.locationIcon}
                      />
                    </TouchableOpacity>

                    <CustomCheckbox
                      label={t('Save Address')}
                      isSelected={isSaveAddress}
                      onSelect={() => setIsSaveAddress(prev => !prev)}
                    />
                  </>
                )}
              </View>
            ) : (
              <TeacherAddressSelector
                tutorMeetingPointAddressData={
                  tutorMeetingPointAddressData?.data || []
                }
                onSelectTutorMeetingPoint={handleTutorMeetingPointSelection}
              />
            )}

            <PrimaryButton
              onPress={handleSave}
              title={t('save')}
              style={styles.saveButton}
              textStyle={styles.saveButtonText}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mapBackground: {
    flex: 1,
  },
  mapContainer: {
    ...StyleSheet.absoluteFillObject,
    height: hp(60),
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    marginBottom: hp(10),
  },
  bottomSheet: {
    width: '100%',
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  classTypeCard: {
    width: wp(28),
    borderRadius: 15,
    padding: '2%',
    alignItems: 'flex-start',
    borderWidth: 1,
  },
  cardImage: {
    height: 35,
    width: 35,
    marginBottom: 7,
  },
  cardTitle: {
    fontSize: 14,
    fontFamily: Fonts.bold,
    lineHeight: hp(2),
  },
  sectionTitle: {
    fontSize: 16,
    marginBottom: 10,
    color: 'black',
    fontFamily: Fonts.bold,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 20,
    gap: wp(2),
  },
  addressInput: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: Platform.OS === 'android' ? 5 : 15,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationIcon: {
    height: 24,
    width: 24,
  },
  textInput: {
    marginLeft: 10,
    color: colors.txtGrey3,
    width: '90%',
    fontFamily: Fonts.medium,
  },
  suggestionItem: {
    padding: 10,
    borderBottomWidth: 0.4,
    borderBottomColor: colors.txtGrey,
  },
  suggestionText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  suggestionsList: {
    backgroundColor: colors.white,
    borderColor: colors.txtGrey,
    borderWidth: 1,
    borderRadius: 8,
    maxHeight: 150,
    marginBottom: 10,
  },
  locationButton: {
    backgroundColor: colors.white,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    alignItems: 'center',
    marginBottom: 8,
    width: '60%',
    borderWidth: 1,
    borderColor: colors.txtGrey,
    elevation: 2,
    justifyContent: 'space-around',
  },
  locationButtonText: {
    color: colors.txtGrey3,
    fontFamily: Fonts.medium,
  },
  saveButton: {
    backgroundColor: colors.themeColor,
    marginBottom: 15,
    width: '100%',
  },
  saveButtonText: {
    fontSize: 16,
    color: colors.white,
    fontFamily: Fonts.bold,
  },
  sectionContainer: {
    marginBottom: 20,
  },
});

export default React.memo(MeetingPoint);
