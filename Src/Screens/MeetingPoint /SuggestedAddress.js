import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Image} from 'react-native';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';
import {
  updateMeetingPoint,
  updateMeetingTutorPoint,
} from '../../Redux/Slices/Student/TutorBookingSlice';
import {tutorBookingJson} from '../../Api/Model/TutorBookingModel';
import {useNavigation} from '@react-navigation/native';

const SuggestedAddress = ({
  tutorMeetingPointAddressData = [],
  onSelectTutorMeetingPoint,
}) => {
  console.log(
    '🚀 ~ tutorMeetingPointAddressData:',
    tutorMeetingPointAddressData,
  );
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const disptach = useDispatch();
  const navigation = useNavigation();

  const toggleExpand = async id => {
    const updatedPackages = packages?.map(pkg => ({
      ...pkg,
      expanded: pkg?.id === id ? !pkg?.expanded : false,
    }));
    console.log(
      '🚀 ~ TeacherAddressSelector ~ updatedPackages:',
      updatedPackages,
    );
    const obj = updatedPackages[0];
    setPackages(updatedPackages);
    disptach(updateMeetingTutorPoint(obj));

    const selectedPackage = updatedPackages.find(
      pkg => pkg.id === id && pkg.expanded,
    );
    if (selectedPackage) {
      onSelectTutorMeetingPoint(selectedPackage); // Call the callback with the selected package
    }
  };
  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{t('suggestedMeeting')}</Text>

      {tutorMeetingPointAddressData?.map(pkg => {
        return (
          <View
            key={pkg?.id}
            style={[
              styles.packageWrapper,
              {
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: isRTL ? 'flex-end' : 'flex-start',
              },
            ]}>
            <View
              style={[
                styles.radioContainer,
                {paddingRight: isRTL ? 0 : 10, paddingLeft: isRTL ? 10 : 0},
              ]}>
              <View
                style={[styles.radio, pkg.expanded && styles.radioSelected]}>
                {pkg?.expanded && <View style={styles.radioInner} />}
              </View>
            </View>

            <View style={styles.packageContainerWrapper}>
              <TouchableOpacity
                style={styles.packageContainer}
                onPress={() =>
                  navigation.navigate('SudgestedLoc', {id: pkg?.id})
                }
                activeOpacity={0.7}>
                <Image
                  source={icons.locationGray}
                  resizeMode="contain"
                  style={{height: 30, width: 30}}
                />
                <View style={styles.packageInfo}>
                  <Text style={styles.packageTitle}>
                    {pkg?.address}
                    {/* {t(pkg?.address) +
                      ', ' +
                      t(pkg?.city) +
                      ', ' +
                      t(pkg?.country)} */}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  heading: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#000',
  },
  packageWrapper: {
    marginBottom: 12,
  },
  radioContainer: {
    alignSelf: 'center',

    // marginRight: 15,
    // marginTop: 15,
  },
  radio: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#666',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioSelected: {
    borderColor: colors.themeColor,
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.themeColor,
  },
  packageContainerWrapper: {
    flex: 1,
  },
  packageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    //   backgroundColor : 'red',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    gap: 10,
  },
  packageInfo: {
    flex: 1,
  },
  packageTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000',
    marginBottom: 4,
  },
  duration: {
    fontSize: 16,
    color: '#666',
  },
  durationDate: {
    color: '#000',
  },
  instructionsContainer: {
    paddingVertical: 12,
    paddingHorizontal: 5,
  },
  instructionsHeading: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
  },
  instructions: {
    fontSize: 14,
    color: colors.txtGrey1,
    marginTop: 4,
  },
  arrow: {
    width: 24,
    height: 24,
  },
});

export default SuggestedAddress;
