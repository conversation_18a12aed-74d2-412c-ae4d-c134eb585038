import React, {useState} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Modal,
  FlatList,
  Image,
  Dimensions,
  BackHandler,
} from 'react-native';
import CountryPicker from 'react-native-country-picker-modal';
import colors from '../../Utils/colors';
import {StatusContainer} from '../../Components/StatusBar';
import FormHeader from '../../Components/FormHeader';
import CustomButton from '../../Components/Custom_Components/customButton';
import LinearGradient from 'react-native-linear-gradient';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {
  useGetExpertiseQuery,
  useGetGradesQuery,
  useGetQualificationsQuery,
  useUpdateTutorProfileMutation,
} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';
import {useFocusEffect} from '@react-navigation/native';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {PrimaryButton} from '../../Components/CustomButton';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {responsiveFontSize} from '../../Utils/constant';
import CustomDropDown from '../../Components/CustomDropDown';
import MultiSelectDropdown from '../../Components/MultiSelectComp';

const CompleteYourProfilePageOne = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [occupation, setOccupation] = useState('');
  const [nationality, setNationality] = useState('');
  const [bio, setBio] = useState('');
  const [experience, setExperience] = useState('');
  const [selectedGrades, setSelectedGrades] = useState([]);
  const [selectedExpertise, setSelectedExpertise] = useState([]);
  const [otherGrade, setOtherGrade] = useState('');
  const [countryCode, setCountryCode] = useState('QA');
  const [pickerVisible, setPickerVisible] = useState(false);
  const [otherExpertise, setOtherExpertise] = useState('');
  const [selectedQualification, setSelectedQualification] = useState({});
  const [otherQualification, setOtherQualification] = useState('');
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => {
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
      };
    }, []),
  );

  const {
    data: qualificationsData,
    error: qualificationsError,
    isLoading: isQualificationsLoading,
  } = useGetQualificationsQuery();

  const haddleSkip = () => {
    navigation.navigate('CompleteYourProfilePageTwo');
  };

  const qualificationOptions =
    qualificationsData?.data?.rows.map(qualification => ({
      label: qualification.name,
      value: qualification.value,
      id: qualification.id,
    })) || [];

  const {
    data: gradesData,
    error: gradesError,
    isLoading: isGradesLoading,
  } = useGetGradesQuery();

  const handleGradeSelect = gradeId => {
    if (selectedGrades.includes(gradeId)) {
      setSelectedGrades(selectedGrades.filter(id => id !== gradeId));
    } else {
      setSelectedGrades([...selectedGrades, gradeId]);
    }
  };

  const {data: expertiseData} = useGetExpertiseQuery();
  const expertiseOptions =
    expertiseData?.data?.rows.map(expertise => ({
      label: expertise.name,
      value: expertise.name,
    })) || [];

  const handleExpertiseSelect = expertise => {
    if (!selectedExpertise.includes(expertise)) {
      setSelectedExpertise([...selectedExpertise, expertise]);
    }
  };

  const handleRemoveExpertise = expertise => {
    setSelectedExpertise(selectedExpertise.filter(item => item !== expertise));
  };
  const [updateTutorProfile, {isLoading}] = useUpdateTutorProfileMutation();

  const validateFields = () => {
    if (!selectedQualification?.id) {
      showToast('error', t('selectQualification'), 'bottom', isRTL);
      return false;
    }
    if (!occupation.trim()) {
      showToast('error', t('enterOccupation'), 'bottom', isRTL);
      return false;
    } else if (occupation.match(/[0-9]/)) {
      showToast('error', t('errorOccupation'), 'bottom', isRTL);
      return false;
    }
    if (!nationality) {
      showToast('error', t('enterNationality'), 'bottom', isRTL);
      return false;
    }
    if (!experience.trim()) {
      showToast('error', t('enterExperience'), 'bottom', isRTL);
      return false;
    } else if (!experience.match(/^[0-9]+$/)) {
      showToast('error', t('experience_valid'), 'bottom', isRTL);
      return false;
    }
    // if (selectedGrades.length === 0) {
    //   showToast('error', t('select_grade'), 'bottom', isRTL);
    //   return false;
    // }
    // if (selectedExpertise.length === 0) {
    //   showToast('error', t('selectExpertise'), 'bottom', isRTL);
    //   return false;
    // }
    // if (selectedGrades.includes(6) && !otherGrade.trim()) {
    //   console.log('🚀 ~ validateFields ~ selectedGrades:', selectedGrades);
    //   showToast('error', t('other_grade'), 'bottom', isRTL);
    //   return false;
    // }
    if (typeof experience == 'number') {
      console.log(experience, 'experience');
      showToast('error', t('experience_valid'), 'bottom', isRTL);
      return false;
    }
    return true;
  };

  // const handleExperienceChange = text => {
  //     setExperience(text);
  // };

  const handleSubmit = async () => {
    if (!validateFields()) {
      return;
    }

    const profileData = {
      qualification: [
        {
          id: selectedQualification?.id,
          name: selectedQualification?.value,
        },
      ],
      occupation: occupation,
      nationality: nationality,
      experience: experience,
      bio: bio,
      // grades: selectedGrades.map(gradeId => {
      //   const grade = gradesData?.data?.rows.find(
      //     grade => grade.id === gradeId,
      //   );
      //   return {
      //     id: grade?.id,
      //     name: grade?.name,
      //   };
      // }),
      // other_grades: otherGrade || '',
      // expertise: selectedExpertise.map(expertiseName => {
      //   const expertise = expertiseData?.data?.rows.find(
      //     expert => expert.name === expertiseName?.label,
      //   );
      //   return {
      //     id: expertise?.id,
      //     name: expertise?.name,
      //   };
      // }),
      other_expertise: otherExpertise || '',
      other_qualification: otherQualification || '',
      academic_document: '',
      address_proof: '',
      id_photo: '',
      profile_completion_step: '1',
    };
    console.log('🚀 ~ handleSubmit ~ profileData:', profileData);
    try {
      const response = await updateTutorProfile(profileData);
      console.log(
        '🚀 ~ handleSubmit ~ profileData:',
        JSON.stringify(profileData),
      );
      console.log('Form submitted successfully', JSON.stringify(response));
      if (response?.error) {
        console.error('Error in response:', response.error);
        showToast(
          'error',
          capitalizeFirstLetter(response.error.data?.message) ||
            'Something went wrong',
          'bottom',
          isRTL,
        );
      } else {
        console.log('Form submitted successfully', JSON.stringify(response));
        showToast('success', response?.data?.message, 'bottom', isRTL);
        navigation.navigate('CompleteYourProfilePageTwo');
      }
    } catch (error) {
      console.error('Error submitting form', error);
    }
  };
  console.log(
    '🚀 ~ CompleteYourProfilePageOne ~ selectedQualification:',
    selectedQualification,
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.white} />
      {/* <StatusContainer color={colors.themeColor} /> */}
      <FormHeader
        title={t('completeProfile')}
        titleStyle={{color: colors.black}}
        showBackButton={false}
        showSkipButton={true}
        onSkip={haddleSkip}
        style={{backgroundColor: colors.white}}
        buttonStyle={{color: colors.black}}
      />
      <View style={styles.progressBar}>
        <View style={styles.progressBarFilled}></View>
        <View style={styles.progressBarUnfilled}></View>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {/* <CustomPicker
          label="selectQualification"
          options={qualificationOptions}
          selectedValue={selectedQualification?.name}
          onValueChange={selectedValue => {
            const selectedQual = qualificationsData?.data?.rows.find(
              qualification => qualification.name === selectedValue,
            );
            setSelectedQualification(selectedQual || {});
          }}
        /> */}
        <CustomDropDown
          lable={t('qualifications')}
          data={qualificationOptions.map(({label, ...rest}) => ({
            name: label,
            ...rest,
          }))}
          isSearch={true}
          lableStyle={styles.drowpdownLable}
          backgroundColor={colors.txtGrey}
          height={40}
          onSelect={selectedValue => {
            console.log(
              '🚀 ~ CompleteYourProfilePageOne ~ selectedValue:',
              selectedValue,
            );

            const selectedQual = qualificationsData?.data?.rows.find(
              qualification => qualification.value === selectedValue.value,
            );

            setSelectedQualification(selectedQual || {});
          }}
        />
        {(selectedQualification.name == 'Other' ||
          selectedQualification.value == 'Other' ||
          selectedQualification.name == 'آخر') && (
          <TextInput
            style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
            placeholder={t('enterQualification')}
            placeholderTextColor={colors.txtGrey1}
            value={otherQualification}
            autoFocus
            onChangeText={text => {
              const sanitizedText = text.replace(/[^A-Z a-z]/g, '');
              if (sanitizedText.length <= 16) {
                setOtherQualification(sanitizedText);
              }
            }}
          />
        )}

        <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('occupation')}
        </Text>
        <TextInput
          style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
          placeholder={t('enterOccupation')}
          placeholderTextColor={colors.txtGrey1}
          value={occupation}
          onChangeText={text => {
            const sanitizedText = text.replace(/[^A-Z a-z]/g, '');
            if (sanitizedText.length <= 16) {
              setOccupation(sanitizedText);
            }
          }}
        />

        {/* Nationality */}
        <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('nationality')}
        </Text>
        <TouchableOpacity
          style={{
            borderWidth: 1,
            borderColor: colors.txtGrey,
            borderRadius: 10,
            padding: 12,
            marginBottom: 16,
          }}
          onPress={() => setPickerVisible(true)}>
          <Text
            style={{
              color: nationality ? colors.black : colors.txtGrey1,
              fontFamily: Fonts.regular,
              textAlign: isRTL ? 'right' : 'left',
            }}>
            {nationality || t('enterNationality')}
          </Text>
        </TouchableOpacity>
        <CountryPicker
          countryCode={countryCode}
          withFilter
          withFlag
          onSelect={country => {
            setCountryCode(country.cca2);
            setNationality(country.name);
          }}
          withEmoji={false}
          visible={pickerVisible}
          onClose={() => setPickerVisible(false)}
          renderFlagButton={() => null}
        />

        {/* Nationality */}

        <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('Bio')}
        </Text>
        <TextInput
          placeholder={t('enterYourBio')}
          placeholderTextColor={colors.txtGrey1}
          value={bio}
          maxLength={500}
          multiline
          onChangeText={text => {
            setBio(text);
          }}
          style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
        />
        <Text
          style={{
            textAlign: isRTL ? 'left' : 'right',
            color: 'grey',
            fontSize: fp(1.2),
            marginTop: -hp(1.3),
            fontFamily: Fonts.medium,
          }}>
          {bio.length}/500
        </Text>

        <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {t('experience')}
        </Text>
        <TextInput
          style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
          placeholder={t('enterExperience')}
          placeholderTextColor={colors.txtGrey1}
          keyboardType="numeric"
          value={experience}
          onChangeText={text => {
            const sanitizedText = text.replace(/[^0-9]/g, ''); // Allow only numbers
            setExperience(sanitizedText);
          }}
          maxLength={2}
        />

        {/* <Text style={styles.label}>{t('tutoringGrades')}</Text>
        <View style={styles.checkboxesContainer}>
          {gradesData?.data?.rows.map(grade => (
            <CustomCheckbox
              key={grade.id}
              label={t(grade.name)}
              isSelected={selectedGrades.includes(grade.id)}
              onSelect={() => handleGradeSelect(grade.id)}
            />
          ))}
        </View>

        {gradesData?.data?.rows.some(grade => grade.name === 'Others') &&
          selectedGrades.includes(6) && (
            <TextInput
              style={[
                styles.inputOthers,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}
              placeholder={t('writeYourOwn')}
              placeholderTextColor={colors.txtGrey1}
              value={otherGrade}
              onChangeText={setOtherGrade}
              multiline={true}
              numberOfLines={4}
              textAlignVertical="top"
            />
          )} */}

        {/* <Text style={styles.label}>{t('expertiseIn')}</Text>
        <CustomPicker
          label="selectExpertise"
          options={expertiseOptions}
          selectedValue=""
          onValueChange={handleExpertiseSelect}
        /> */}

        {/* <MultiSelectDropdown
          label={t('expertiseIn')}
          options={expertiseOptions}
          value={selectedExpertise}
          onChange={setSelectedExpertise}
        /> */}
        {/* {selectedExpertise.some(item => item.value === 'Others') && (
          <TextInput
            style={styles.input}
            placeholder={'Enter expertise'}
            placeholderTextColor={colors.txtGrey1}
            value={otherExpertise}
            autoFocus
            onChangeText={setOtherExpertise}
          />
        )}

        <View style={styles.selectedExpertiseContainer}>
          {selectedExpertise.map((expertise, index) => {
            if (expertise.value === 'Others') {
              return null; // Don't render anything for "Other" in this section
            }
            if (otherExpertise != '') {
            }
            return (
              <LinearGradient
                colors={['#C6FFC9', '#D4EBFF']}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 1}}
                style={{
                  borderRadius: 20,
                  paddingHorizontal: 2,
                  paddingVertical: 1,
                  marginRight: 20,
                  marginBottom: 10,
                }}
                key={index}>
                <View style={styles.expertiseTag}>
                  <Text style={styles.expertiseText}>{expertise.label}</Text>
                  <TouchableOpacity
                    onPress={() => handleRemoveExpertise(expertise)}>
                    <Image
                      style={styles.removeImage}
                      resizeMode="center"
                      source={icons.cross}
                    />
                  </TouchableOpacity>
                </View>
              </LinearGradient>
            );
          })}
        </View> */}
      </ScrollView>

      <PrimaryButton
        style={styles.continueButton}
        title={t('continue')}
        onPress={handleSubmit}
        textStyle={styles.continueText}
        loading={isLoading}
      />
    </SafeAreaView>
  );
};

export default CompleteYourProfilePageOne;

const CustomPicker = ({
  label,
  options,
  selectedValue,
  onValueChange,
  borderColor = colors.txtGrey,
}) => {
  const {t} = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);

  const windowWidth = Dimensions.get('window').width;

  return (
    <View style={styles.containerPicker}>
      <TouchableOpacity
        style={[styles.pickerInput, {borderColor}]}
        onPress={() => setModalVisible(true)}
        accessible={true}
        accessibilityLabel={label}>
        <Text style={styles.pickerText}>{selectedValue || t(label)}</Text>
        <Image source={icons.downchevron} style={styles.dropdownIcon} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}>
        <TouchableOpacity
          style={styles.modalOverlay}
          onPress={() => setModalVisible(false)}
        />
        <View style={[styles.modalContainer, {width: windowWidth * 0.85}]}>
          <FlatList
            data={options}
            keyExtractor={item => item.value}
            renderItem={({item}) => (
              <TouchableOpacity
                style={styles.modalOption}
                onPress={() => {
                  onValueChange(item.value);
                  setModalVisible(false);
                }}
                accessible={true}
                accessibilityLabel={item.label}>
                <Text style={styles.modalText}>{t(item.label)}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>
    </View>
  );
};
const CustomCheckbox = ({label, isSelected, onSelect}) => (
  <TouchableOpacity
    activeOpacity={0.8}
    style={styles.checkboxWrapper}
    onPress={onSelect}>
    <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
      {isSelected && <Text style={styles.checkMark}>✓</Text>}
    </View>
    <Text style={styles.checkboxLabel}>{label}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    padding: 16,
  },
  label: {
    fontSize: 16,
    color: colors.black,
    marginBottom: 8,
    fontFamily: Fonts.medium,
  },

  containerPicker: {
    marginBottom: 16,
    alignItems: 'center',
  },

  pickerInput: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    width: '100%',
    maxWidth: 400,
  },

  pickerText: {
    fontSize: 14,
    color: colors.txtGrey1,
    flex: 1,
    fontFamily: Fonts.medium,
  },
  dropdownIcon: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  modalContainer: {
    top: '40%',
    left: '7.5%',
    backgroundColor: colors.white,
    borderRadius: 10,
    zIndex: 1,
  },
  modalOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.txtGrey2,
  },
  modalText: {
    fontSize: responsiveFontSize(16),
    color: colors.lightBlack,
    alignSelf: 'center',
    fontFamily: Fonts.medium,
  },

  input: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 10,
    padding: 12,
    marginBottom: 16,
    color: colors.black,
    fontFamily: Fonts.medium,
  },

  inputOthers: {
    height: 80,
    padding: 10,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    backgroundColor: colors.white,
    fontSize: 16,
    color: colors.black,
    marginBottom: 20,
    textAlignVertical: 'top',
  },

  checkboxesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  checkboxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    marginRight: 10,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  checkboxSelected: {
    backgroundColor: colors.themeColor,
    borderColor: colors.themeColor,
  },
  checkMark: {
    color: colors.white,
  },
  checkboxLabel: {
    fontSize: 14,
    color: colors.txtGrey1,
    fontFamily: Fonts.medium,
  },
  selectedExpertiseContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  expertiseTag: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
    margin: 1,
  },
  expertiseText: {
    color: colors.black,
    fontSize: 14,
    fontFamily: Fonts.medium,
  },
  removeImage: {
    height: 20,
    width: 20,
  },
  continueButton: {
    backgroundColor: colors.themeColor,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: fp(1.5),
    width: '90%',
  },
  continueText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
  progressBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  progressBarFilled: {
    flex: 1,
    height: 5,
    backgroundColor: colors.themeColor,
    borderRadius: 5,
    marginRight: 3,
  },
  progressBarUnfilled: {
    flex: 1,
    height: 5,
    backgroundColor: colors.txtGrey,
    borderRadius: 5,
  },
  drowpdownLable: {
    fontSize: fp(2),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
});
