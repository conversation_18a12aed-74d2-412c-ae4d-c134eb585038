import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import CustomButton from '../../Components/Custom_Components/customButton';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import {useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';
import {responsiveFontSize} from '../../Utils/constant';
import {Fonts} from '../../Utils/Fonts';
import {showToast} from '../../Components/ToastHelper';

const AllSetPage = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dispatch = useDispatch();
  const handleStartJourney = () => {
    showToast('success', t('sendForVerification'), 'bottom', isRTL);
    navigation?.navigate('Login');
    // navigation.navigate('TutorApprovalPending');
    // navigation.reset({
    //   index: 0,
    //   routes: [{name: 'TabTutorScreen'}],
    // });
  };

  const handleNeedHelp = () => {
    // Handle the Need Help action here
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <View style={styles.contentContainer}>
        <View style={styles.iconContainer}>
          <Image
            resizeMode="contain"
            source={icons.doneIcon}
            style={styles.image}
          />
        </View>

        <View style={styles.textContainer}>
          <Text style={styles.heading}>{t('allSet')}</Text>
          <Text style={styles.subText}>{t('welcomeMessage')}</Text>
        </View>
      </View>

      <View style={styles.buttonContainer}>
        {/* <TouchableOpacity
          style={styles.needHelpButton}
          onPress={handleNeedHelp}>
          <Text style={styles.needHelpText}>{t('needHelp')}</Text>
        </TouchableOpacity> */}
        <CustomButton
          style={styles.startJourneyButton}
          title={t('startJourney')}
          onPress={handleStartJourney}
          textStyle={styles.startJourneyText}
        />
      </View>
    </SafeAreaView>
  );
};

export default AllSetPage;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
    padding: 20,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 40,
  },
  image: {
    width: 180,
    height: 180,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  heading: {
    fontSize: responsiveFontSize(24),
    fontFamily: Fonts.bold,
    color: colors.white,
    marginBottom: 10,
  },
  subText: {
    fontSize: responsiveFontSize(16),
    color: colors.white,
    textAlign: 'center',
    paddingHorizontal: 20,
    fontFamily: Fonts.regular,
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  needHelpButton: {
    borderWidth: 1,
    borderColor: colors.white,
    borderRadius: 8,
    paddingVertical: 14,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  needHelpText: {
    color: colors.white,
    fontSize: responsiveFontSize(16),
    fontFamily: Fonts.regular,
  },
  startJourneyButton: {
    backgroundColor: colors.white,
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    width: '100%',
  },
  startJourneyText: {
    color: colors.themeColor,
    fontSize: responsiveFontSize(16),
    fontFamily: Fonts.regular,
  },
});
