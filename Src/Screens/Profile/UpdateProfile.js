import React, {useState, useEffect, useCallback} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  ScrollView,
  Modal,
} from 'react-native';
import {
  useUpdateStudentProfileMutation,
  useProfileDetailsQuery,
  useUploadFileProfileMutation,
  useChooseYourGradeQuery,
  useChooseYourCurriculumsQuery,
  useUpdateTutorProfileMutation,
  useLazyChooseYourCurriculumsQuery,
  useSubCategoryQuery,
  useGetAllConnectedProfilesAfterLoginQuery, // Added for class data
} from '../../Api/ApiSlice';
import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
import {useFocusEffect} from '@react-navigation/native';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import {showToast} from '../../Components/ToastHelper';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {useDispatch, useSelector} from 'react-redux';
import styles from './styles';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {setAuthData, setUserProfileImage} from '../../Features/authSlice';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import MobileNumberInput from '../../Components/MobileNumberInput';
import {maxLengthsByCountry} from '../../Utils/CountryCodeLength';
import {callingCodeToCountryCode} from '../../Utils/CountryCodeByCallingCode';
import {Fonts} from '../../Utils/Fonts';

const UpdateProfile = ({navigation}) => {
  const userType = useSelector(state => state.auth.user_type);
  const {t, i18n} = useTranslation();
  const [name, setName] = useState('');
  const [mobileNo, setMobileNo] = useState('');
  const [email, setEmail] = useState('');
  const [address, setAddress] = useState('');
  const [grade, setGrade] = useState('');
  const [curriculum, setCurriculum] = useState('');
  const [classId, setClassId] = useState(''); // Added for class selection
  const [studentType, setStudentType] = useState('');
  const [countryCode, setCountryCode] = useState('');
  const [isGradeDropdownOpen, setGradeDropdownOpen] = useState(false);
  const [isCurriculumDropdownOpen, setCurriculumDropdownOpen] = useState(false);
  const [isClassDropdownOpen, setClassDropdownOpen] = useState(false); // Added for class dropdown
  const [isStudentTypeDropdownOpen, setStudentTypeDropdownOpen] =
    useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [isImagePickerVisible, setImagePickerVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const isRTL = i18n.language === 'ar';
  const dispatch = useDispatch();

  const studentTypes = [
    {id: 1, type: 'Student', label: t('student')},
    {id: 2, type: 'Professional', label: t('professional')},
  ];

  const {data: studentData, refetch: refetchStudentData} =
    useProfileDetailsQuery();
  const {data: gradesData, refetch: refetchGradesData} =
    useChooseYourGradeQuery();
  console.log('🚀 ~ UpdateProfile ~ gradesData:', gradesData?.data?.rows);
  const [chooseYourCurriculum, {data: curriculumsData}] =
    useLazyChooseYourCurriculumsQuery();

  const [updateProfile, {isLoading: isUpdating}] =
    useUpdateStudentProfileMutation();
  const [updateTutorProfile, {isLoading: isUpdatingTutor}] =
    useUpdateTutorProfileMutation();
  const [uploadFileProfile] = useUploadFileProfileMutation();

  const refetchDataOnFocus = useCallback(() => {
    refetchStudentData();
    refetchGradesData();
    chooseYourCurriculum({viewAll: true});
  }, [refetchStudentData, chooseYourCurriculum]);

  useFocusEffect(refetchDataOnFocus);

  useEffect(() => {
    if (studentData?.data) {
      const {
        name,
        mobile_no,
        email,
        address,
        tlm_student_academic_details,
        image,
        country_code,
        student_type,
      } = studentData.data;

      const academicDetails = tlm_student_academic_details?.[0] || {};
      console.log(
        '🚀 ~ useEffect ~ academicDetails:',
        JSON.stringify(academicDetails),
      );
      setName(name || '');
      setMobileNo(mobile_no || '');
      setCountryCode(studentData.data.country_code || '974');
      setEmail(email || '');
      setAddress(address || '');
      setGrade(academicDetails.tlm_grade || '');
      setCurriculum(academicDetails.tlm_curriculum?.id?.toString() || '');
      setClassId(academicDetails.tlm_class?.id?.toString() || ''); // Added for class
      setStudentType(student_type?.toString() || '');

      if (image) {
        setProfileImage(image);
      }
    }
  }, [studentData]);

  console.log(grade, 'grade at first');

  const validateName = name => {
    const nameRegex = /^[A-Za-z\s]{0,20}$/;
    return nameRegex.test(name);
  };

  const handleNameChange = text => {
    if (validateName(text)) {
      setName(text);
    } else {
      showToast(
        'error',
        'Name should be 1-16 letters without numbers, special characters, or emojis.',
        'bottom',
        isRTL,
      );
    }
  };

  const handleAddressChange = text => {
    if (text.length <= 70) {
      setAddress(text);
    } else {
      showToast('error', t('addressMaxLength'), 'bottom', isRTL);
    }
  };

  const handleStudentTypeSelect = type => {
    setStudentType(type.id.toString());
    setStudentTypeDropdownOpen(false);
    if (type.id !== 1) {
      setGrade('');
      setCurriculum('');
      setClassId(''); // Reset class when not a student
    }
  };
  console.log(classId, 'classsiD----->');

  const handleSaveProfile = async () => {
    if (!name) {
      showToast('error', t('fillAllFields'), 'bottom', isRTL);
      return;
    }

    let requestBody = {
      name,
      ...(mobileNo && {mobile_no: mobileNo}),
      ...(email && {email}),
      ...(address && {address}),
      image: profileImage,
    };

    try {
      let response;

      if (userType === '1') {
        requestBody = {
          ...requestBody,
          user_type: '1',
          student_type: studentType || null,
          ...(grade && {grades_id: parseInt(grade?.id)}),
          ...(grade?.name !== 'Higher Education' &&
            curriculum && {curriculum_id: parseInt(curriculum)}),
          ...(classId && {class_id: parseInt(classId)}), // Added class_id to request body
        };

        response = await updateProfile(requestBody).unwrap();
      } else if (userType === '3') {
        response = await updateTutorProfile(requestBody).unwrap();
      }

      dispatch(setUserProfileImage(profileImage));
      showToast(
        'success',
        response.message || t('profileUpdated'),
        'bottom',
        isRTL,
      );
      navigation.navigate('Home');
    } catch (error) {
      console.error('Profile update error:', error);
      showToast(
        'error',
        error.data?.message || t('updateProfileError'),
        'bottom',
        isRTL,
      );
    }
  };

  const handleSelectImage = () => {
    setImagePickerVisible(true);
  };

  const handleImageResponse = async response => {
    if (response.didCancel) {
      return;
    }

    if (response.errorCode || response.errorMessage) {
      console.error(
        'Image selection error:',
        response.errorCode,
        response.errorMessage,
      );
      showToast(
        'error',
        response.errorMessage || t('failedImageSelection'),
        'bottom',
        isRTL,
      );
      return;
    }

    if (response?.assets && response.assets.length > 0) {
      const selectedImage = response.assets[0];

      if (!selectedImage.uri || !selectedImage.type) {
        showToast('error', t('invalidImageData'), 'bottom', isRTL);
        return;
      }

      let success = false;
      let retryCount = 0;
      const maxRetries = 3;

      try {
        setLoading(true);

        while (!success && retryCount < maxRetries) {
          try {
            const uploadResponse = await uploadFileProfile({
              uri: selectedImage.uri,
              type: selectedImage.type,
              fileName: selectedImage.fileName || 'profile.jpg',
            }).unwrap();

            if (uploadResponse?.data?.path) {
              setProfileImage(uploadResponse.data.path);
              success = true;
              showToast('success', t('imageUploadSuccess'), 'bottom', isRTL);
            } else {
              retryCount++;
              console.log(`Retrying upload... Attempt ${retryCount}`);
            }
          } catch (uploadError) {
            console.error(
              'Image upload error on attempt',
              retryCount,
              ':',
              uploadError,
            );
            retryCount++;
          }
        }

        if (!success) {
          showToast('error', t('failedImageupload'), 'bottom', isRTL);
        }
      } catch (error) {
        console.error('Unexpected error during image upload:', error);
        showToast('error', t('unExpectedError'), 'bottom', isRTL);
      } finally {
        setLoading(false);
      }
    } else if (response.errorCode) {
      console.error('Image selection error:', response.errorCode);
      showToast('error', t('failedImageSelection'), 'bottom', isRTL);
      setLoading(false);
    }
  };

  const openImagePicker = () => {
    launchImageLibrary({mediaType: 'photo', quality: 0.5}, handleImageResponse);
    setImagePickerVisible(false);
  };

  const openCamera = () => {
    launchCamera({mediaType: 'photo', quality: 0.5}, handleImageResponse);
    setImagePickerVisible(false);
  };

  const labelStyle = isRTL
    ? [styles.label, {textAlign: 'right'}]
    : styles.label;

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('editProfile')} />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        <View style={styles.profileContainer}>
          {loading ? (
            <ActivityIndicator size={'large'} />
          ) : (
            <Image
              source={
                profileImage
                  ? {uri: `${IMAGE_BASE_URL}${profileImage}`}
                  : {uri: DUMMY_USER_IMG}
              }
              style={styles.profileImage}
            />
          )}
          <TouchableOpacity
            onPress={handleSelectImage}
            style={styles.editIconContainer}>
            <Image source={icons.addImgEditProfile} style={styles.editIcon} />
          </TouchableOpacity>
        </View>

        <View style={styles.formContainer}>
          <Text style={labelStyle}>{t('enterName')}</Text>
          <TextInput
            style={[styles.input, {textAlign: isRTL ? 'right' : 'left'}]}
            placeholder={t('enterName')}
            value={name}
            onChangeText={handleNameChange}
            placeholderTextColor="#cccccc"
          />

          <TouchableOpacity
            style={{
              alignSelf: isRTL ? 'flex-start' : 'flex-end',
              height: hp(2),
              zIndex: 1,
              marginBottom: -hp(2),
            }}
            onPress={() =>
              navigation.navigate('ChangePhoneNumber', {
                mobileNo: mobileNo,
                countryCode: countryCode,
              })
            }>
            <Image source={icons.pencil} style={[styles.icon]} />
          </TouchableOpacity>
          <MobileNumberInput
            title={`${t('mobile_Txt')}`}
            value={mobileNo}
            placeholder={t('enter_number_Txt')}
            callingCodeProp={studentData?.data?.country_code}
            countryCodeProp={
              callingCodeToCountryCode[Number(studentData?.data?.country_code)]
            }
            placeholderTextColor={colors.txtGrey1}
            width={wp(90)}
            editable={false}
          />

          <View
            style={[
              styles.clickableRow,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Text style={labelStyle}>{t('Email')}</Text>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('ChangeEmail', {email: email})
              }>
              <Image source={icons.pencil} style={styles.icon} />
            </TouchableOpacity>
          </View>
          <TextInput
            style={styles.inputPH}
            value={email}
            editable={false}
            placeholderTextColor="#cccccc"
          />

          <View
            style={[
              styles.clickableRow,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Text style={labelStyle}>{t('address')}</Text>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('StudentAddress', {
                  address: {
                    fullAddress: address,
                    building: studentData.data?.building,
                    street: studentData?.data?.street,
                    zone: studentData?.data?.zone,
                  },
                })
              }>
              <Image source={icons.pencil} style={styles.icon} />
            </TouchableOpacity>
          </View>
          <TextInput
            style={[styles.inputPH, {lineHeight: hp(2)}]}
            value={address}
            editable={false}
            placeholderTextColor="#cccccc"
            multiline
          />

          <View>
            <Text style={labelStyle}>{t('Zone')}</Text>
            <TextInput
              style={[styles.inputPH, {lineHeight: hp(2)}]}
              value={studentData?.data?.zone}
              editable={false}
              placeholderTextColor="#cccccc"
              multiline
            />
          </View>

          <View>
            <Text style={labelStyle}>{t('Street')}</Text>
            <TextInput
              style={[styles.inputPH, {lineHeight: hp(2)}]}
              value={studentData?.data?.street}
              editable={false}
              placeholderTextColor="#cccccc"
              multiline
            />
          </View>

          <View>
            <Text style={labelStyle}>{t('buildingNumber')}</Text>
            <TextInput
              style={[styles.inputPH, {lineHeight: hp(2)}]}
              value={studentData.data?.building}
              editable={false}
              placeholderTextColor="#cccccc"
              multiline
            />
          </View>

          {userType === '1' && (
            <View>
              <Text style={labelStyle}>{t('selectStudentType')}</Text>
              <TouchableOpacity
                style={styles.dropdown}
                onPress={() =>
                  setStudentTypeDropdownOpen(!isStudentTypeDropdownOpen)
                }>
                <Text style={{color: colors.black, fontFamily: Fonts.medium}}>
                  {studentType
                    ? studentTypes.find(
                        item => item.id.toString() === studentType,
                      )?.label
                    : t('selectStudentType')}
                </Text>
                <Image
                  source={
                    isStudentTypeDropdownOpen ? icons.upArrow : icons.downArrow
                  }
                  style={styles.arrow}
                />
              </TouchableOpacity>
              {isStudentTypeDropdownOpen && (
                <View style={styles.dropdownList}>
                  {studentTypes.map(item => (
                    <TouchableOpacity
                      key={item.id}
                      style={styles.dropdownItem}
                      onPress={() => handleStudentTypeSelect(item)}>
                      <Text
                        style={{color: colors.black, fontFamily: Fonts.medium}}>
                        {item.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          )}

          {userType === '1' && studentType === '1' && (
            <>
              <View>
                <Text style={labelStyle}>{t('selectGrade')}</Text>
                <TouchableOpacity
                  style={styles.dropdown}
                  onPress={() => {
                    setGradeDropdownOpen(!isGradeDropdownOpen);
                  }}>
                  <Text style={{color: colors.black, fontFamily: Fonts.medium}}>
                    {grade
                      ? gradesData?.data?.rows?.find(
                          item => item.id.toString() === grade?.id?.toString(),
                        )?.name
                      : t('selectGrade')}
                  </Text>
                  <Image
                    source={
                      isGradeDropdownOpen ? icons.upArrow : icons.downArrow
                    }
                    style={styles.arrow}
                  />
                </TouchableOpacity>
                {isGradeDropdownOpen && (
                  <ScrollView
                    style={styles.dropdownList}
                    nestedScrollEnabled={true}
                    showsVerticalScrollIndicator={true}
                    contentContainerStyle={styles.dropdownScrollContent}>
                    {gradesData?.data?.rows?.map(item => (
                      <TouchableOpacity
                        key={item.id}
                        style={styles.dropdownItem}
                        onPress={() => {
                          setGrade(item);
                          setClassId(''); // Reset class when grade changes
                          setGradeDropdownOpen(false);
                        }}>
                        <Text
                          style={{
                            color: colors.black,
                            fontFamily: Fonts.medium,
                          }}>
                          {item.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                )}
              </View>

              {/* Class Dropdown */}
              <View>
                <Text style={labelStyle}>{t('selectClass')}</Text>
                <TouchableOpacity
                  style={styles.dropdown}
                  onPress={() => setClassDropdownOpen(!isClassDropdownOpen)}>
                  <Text style={{color: colors.black, fontFamily: Fonts.medium}}>
                    {grade
                      ? grade?.tlm_classes?.find(
                          item => item.id.toString() === classId,
                        )?.name
                      : t('selectClass')}
                  </Text>
                  <Image
                    source={
                      isClassDropdownOpen ? icons.upArrow : icons.downArrow
                    }
                    style={styles.arrow}
                  />
                </TouchableOpacity>
                {isClassDropdownOpen && (
                  <ScrollView style={styles.dropdownList} nestedScrollEnabled>
                    {!grade ? (
                      <Text
                        style={{color: colors.black, fontFamily: Fonts.medium}}>
                        {t('selectGradeFirst')}
                      </Text>
                    ) : (
                      grade.tlm_classes?.map(item => (
                        <TouchableOpacity
                          key={item.id}
                          style={styles.dropdownItem}
                          onPress={() => {
                            setClassId(item.id.toString());
                            setClassDropdownOpen(false);
                          }}>
                          <Text
                            style={{
                              color: colors.black,
                              fontFamily: Fonts.medium,
                            }}>
                            {item.name}
                          </Text>
                        </TouchableOpacity>
                      ))
                    )}
                  </ScrollView>
                )}
              </View>

              {!grade?.is_higher_education && (
                <View>
                  <Text style={labelStyle}>{t('curriculum')}</Text>
                  <TouchableOpacity
                    style={styles.dropdown}
                    onPress={() => {
                      setCurriculumDropdownOpen(!isCurriculumDropdownOpen);
                    }}>
                    <Text
                      style={{color: colors.black, fontFamily: Fonts.medium}}>
                      {curriculum
                        ? curriculumsData?.data?.rows?.find(
                            item => item.id.toString() === curriculum,
                          )?.name
                        : studentData?.data?.tlm_student_academic_details[0]
                            ?.tlm_curriculum
                        ? studentData?.data?.tlm_student_academic_details[0]
                            ?.tlm_curriculum?.name
                        : t('selectCurriculum')}
                    </Text>
                    <Image
                      source={
                        isCurriculumDropdownOpen
                          ? icons.upArrow
                          : icons.downArrow
                      }
                      style={styles.arrow}
                    />
                  </TouchableOpacity>
                  {isCurriculumDropdownOpen && (
                    <ScrollView style={styles.dropdownList} nestedScrollEnabled>
                      {curriculumsData?.data?.rows?.map(item => (
                        <TouchableOpacity
                          key={item.id}
                          style={styles.dropdownItem}
                          onPress={() => {
                            setCurriculum(item.id.toString());
                            setCurriculumDropdownOpen(false);
                          }}>
                          <Text style={{color: colors.black}}>{item.name}</Text>
                        </TouchableOpacity>
                      ))}
                    </ScrollView>
                  )}
                </View>
              )}
            </>
          )}
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.saveButton} onPress={handleSaveProfile}>
          {isUpdating ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.saveButtonText}>{t('saveProfile')}</Text>
          )}
        </TouchableOpacity>
      </View>

      <Modal animationType="slide" transparent visible={isImagePickerVisible}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{t('selectImageSource')}</Text>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={openImagePicker}>
              <Text style={styles.modalButtonText}>
                {t('chooseFromGallery')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setImagePickerVisible(false)}>
              <Text style={[styles.modalButtonText, {color: colors.red}]}>
                {t('cancel')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default UpdateProfile;
