import React, {useCallback} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  ActivityIndicator,
  Modal,
  Dimensions,
  StyleSheet,
  Platform,
} from 'react-native';
import {
  useGetStudentDetailQuery,
  useGetStudentProfileForTutorQuery,
  useProfileDetailsQuery,
} from '../../Api/ApiSlice';
import {useFocusEffect} from '@react-navigation/native';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {AppHeader} from '../../Components/Header';
import LinearGradient from 'react-native-linear-gradient';
import {useSelector} from 'react-redux';
import {responsiveFontSize} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {Fonts} from '../../Utils/Fonts';
import {useTranslation} from 'react-i18next';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {fp} from '../../Helper/ResponsiveDimensions';

const {height, width} = Dimensions.get('window');

const StudentProfileForTutor = ({navigation, route}) => {
  const {studentId} = route?.params;
  console.log('🚀 ~ StudentProfileForTutor ~ studentId:', studentId);
  const {t} = useTranslation();
  const {
    data: studentData,
    isLoading,
    refetch: refetchStudentData,
    error,
  } = useGetStudentProfileForTutorQuery(studentId);

  const userType = useSelector(state => state.auth.user_type);

  const refetchDataOnFocus = useCallback(() => {
    refetchStudentData();
  }, [refetchStudentData]);

  useFocusEffect(
    useCallback(() => {
      refetchDataOnFocus();
      return () => {};
    }, [refetchDataOnFocus]),
  );
  console.log('🚀 ~ StudentProfileForTutor ~ studentData:', studentData);
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Error loading profile</Text>
      </View>
    );
  }

  const student = studentData?.data;

  const formatAddress = address =>
    address && address.length > 35 ? `${address.slice(0, 35)}...` : address;

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('profile')} />

      <View style={styles.profileContainer}>
        <Image
          source={
            student?.image
              ? {uri: `${IMAGE_BASE_URL}${student.image}`}
              : icons.profileImage1
          }
          style={styles.profileImage}
        />

        <Text style={styles.profileName}>
          {capitalizeFirstLetter(student?.name) || 'N/A'}
        </Text>
        {userType === '1' && (
          <View style={styles.statusBadge}>
            <LinearGradient
              colors={['#C6FFC9', '#D4EBFF']}
              style={styles.gradient}>
              <Text style={styles.statusText}>{t('student')}</Text>
            </LinearGradient>
          </View>
        )}
      </View>

      <View style={styles.infoCard}>
        {isLoading ? (
          <ActivityIndicator
            size="large"
            color={colors.themeColor}
            style={styles.loadingIndicator}
          />
        ) : (
          <>
            <InfoRow
              icon={icons.phone}
              label={t('mobile_Txt')}
              value={student?.mobile_no || 'N/A'}
            />
            <InfoRow
              icon={icons.email}
              label={t('Email')}
              value={student?.email || 'N/A'}
            />
            <InfoRow
              icon={icons.homeBlack}
              label={t('address')}
              value={formatAddress(student?.address || t('addYourAddress'))}
            />
          </>
        )}
        <>
          <InfoRow
            icon={icons.hatBlack}
            label={t('grade')}
            value={
              studentData?.data?.tlm_student_academic_details[0]?.tlm_grade
                ?.name || 'N/A'
            }
          />
          <InfoRow
            icon={icons.hatBlack}
            label={t('curriculum')}
            value={
              studentData?.data?.tlm_student_academic_details[0]?.tlm_curriculum
                ?.name || 'N/A'
            }
          />
        </>
      </View>
    </SafeAreaView>
  );
};

const InfoRow = ({icon, label, value}) => (
  <View style={styles.infoRow}>
    <Image source={icon} style={styles.icon} />
    <View>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.value}>{value}</Text>
    </View>
  </View>
);

export default StudentProfileForTutor;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  profileContainer: {
    alignItems: 'center',
    padding: 10,
  },
  profileImage: {
    width: width * 0.35,
    height: width * 0.35,
    borderRadius: width * 0.2,
    borderWidth: 2,
    borderColor: colors.txtGrey,
    backgroundColor: colors.txtGrey,
  },
  profileName: {
    fontSize: responsiveFontSize(18),
    fontFamily: Fonts.bold,
    color: colors.black,
    marginTop: 10,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 5,
  },
  statusText: {
    color: colors.black,
    fontSize: responsiveFontSize(12),
    fontFamily: Fonts.bold,
  },
  infoCard: {
    backgroundColor: colors.white,
    marginHorizontal: 20,
    marginTop: 20,
    padding: 15,
    borderRadius: 10,
    borderColor: '#D3D3D3',
    borderWidth: 1,
  },
  loadingIndicator: {
    alignSelf: 'center',
    paddingVertical: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  icon: {
    width: 24,
    height: 24,
    marginRight: 15,
  },
  label: {
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
  },
  value: {
    fontSize: fp(1.6),
    color: colors.black,
    marginTop: 2,
    fontFamily: Fonts.medium,
  },
  buttonGroupContainer: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  meetingPreferenceContainer: {
    backgroundColor: colors.txtGrey,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  qualificationContainer: {
    backgroundColor: colors.txtGrey,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 14,
    color: colors.black,
  },
  buttonGroupContainer: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  meetingPreferenceContainer: {
    backgroundColor: colors.txtGrey,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  qualificationContainer: {
    backgroundColor: colors.txtGrey,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 14,
    color: colors.black,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'android' ? '5%' : '5%',
    width: '100%',
    paddingHorizontal: 20,
  },
  editButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
