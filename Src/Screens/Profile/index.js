import React, {useCallback, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  ActivityIndicator,
  Modal,
  Dimensions,
  StyleSheet,
  Platform,
  FlatList,
  ScrollView,
} from 'react-native';
import {
  useGetConnectProfilesUserQuery,
  useGetStudentDetailQuery,
  useProfileDetailsQuery,
} from '../../Api/ApiSlice';
import {useFocusEffect} from '@react-navigation/native';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {AppHeader} from '../../Components/Header';
import LinearGradient from 'react-native-linear-gradient';
import {useSelector} from 'react-redux';
import {DUMMY_USER_IMG, responsiveFontSize} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {Fonts} from '../../Utils/Fonts';
import {useTranslation} from 'react-i18next';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import {PrimaryButton} from '../../Components/CustomButton';

const {height, width} = Dimensions.get('window');

const ProfilePage = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const {
    data: studentData,
    isLoading,
    refetch: refetchStudentData,
    error,
  } = useProfileDetailsQuery();

  console.log('🚀 ~ ProfilePage ~ error:', error);

  const userType = useSelector(state => state.auth.user_type);

  const refetchDataOnFocus = useCallback(() => {
    refetchStudentData();
  }, [refetchStudentData]);

  useFocusEffect(
    useCallback(() => {
      refetchDataOnFocus();
      return () => {};
    }, [refetchDataOnFocus]),
  );

  if (error) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <Text style={styles.errorText}>{t('errorLoadingProfile')}</Text>
      </View>
    );
  }

  const student = studentData?.data;
  console.log('🚀 ~ ProfilePage ~ Student:', student);
  const {data: connectedProfile, isLoading: loadingConnectedProfile} =
    useGetConnectProfilesUserQuery();

  useEffect(() => {
    console.log('connectedProfile======', JSON.stringify(connectedProfile));
  }, [connectedProfile]);

  const formatAddress = address =>
    address && address.length > 35 ? `${address.slice(0, 150)}...` : address;

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('myProfile')} />
      <ScrollView contentContainerStyle={{paddingBottom: hp(6)}}>
        <View style={styles.profileContainer}>
          <Image
            source={
              student?.image
                ? {uri: `${IMAGE_BASE_URL}${student.image}`}
                : {uri: DUMMY_USER_IMG}
            }
            style={styles.profileImage}
          />

          <Text style={styles.profileName}>
            {capitalizeFirstLetter(student?.name) || 'N/A'}
          </Text>
          {userType === '1' && (
            <View style={styles.statusBadge}>
              <LinearGradient
                colors={['#C6FFC9', '#D4EBFF']}
                style={styles.gradient}>
                <Text style={styles.statusText}>{t('student')}</Text>
              </LinearGradient>
            </View>
          )}
        </View>

        <View style={applyShadowStyleIos(styles.infoCard)}>
          {isLoading ? (
            <ActivityIndicator
              size="large"
              color={colors.themeColor}
              style={styles.loadingIndicator}
            />
          ) : (
            <>
              <InfoRow
                icon={icons.phone}
                label={t('mobile_Txt')}
                value={
                  '+' + student?.country_code + ' ' + student?.mobile_no ||
                  'N/A'
                }
              />
              <InfoRow
                icon={icons.email}
                label={t('Email')}
                value={student?.email || 'N/A'}
              />
              <InfoRow
                icon={icons.homeBlack}
                label={t('address')}
                // value={formatAddress(student?.address || t('addYourAddress'))}
                value={student?.address || t('addYourAddress')}
              />
              {student?.zone && (
                <InfoRow
                  icon={icons.homeBlack}
                  label={t('Zone')}
                  // value={formatAddress(student?.address || t('addYourAddress'))}
                  value={student?.zone || t('addYourAddress')}
                />
              )}
              {student?.street && (
                <InfoRow
                  icon={icons.homeBlack}
                  label={t('Street')}
                  // value={formatAddress(student?.address || t('addYourAddress'))}
                  value={student?.street || t('addYourAddress')}
                />
              )}
              {student?.building && (
                <InfoRow
                  icon={icons.homeBlack}
                  label={t('Building Number')}
                  // value={formatAddress(student?.address || t('addYourAddress'))}
                  value={student?.building || t('addYourAddress')}
                />
              )}

              {userType === '1' && student?.student_type == '1' && (
                <>
                  <InfoRow
                    icon={icons.hatBlack}
                    label={t('grade')}
                    value={
                      studentData?.data?.tlm_student_academic_details[0]
                        ?.tlm_grade?.name || 'N/A'
                    }
                  />
                  <InfoRow
                    icon={icons.hatBlack}
                    label={t('curriculum')}
                    value={
                      studentData?.data?.tlm_student_academic_details[0]
                        ?.tlm_curriculum?.name || 'N/A'
                    }
                  />
                </>
              )}
            </>
          )}
        </View>

        <View style={styles.buttonGroupContainer}>
          {/* {userType == '3' && (
            <TouchableOpacity
              style={styles.meetingPreferenceContainer}
              onPress={() => navigation.navigate('MeetingPreference')}>
              <Text style={styles.buttonText}>
                {t('EditMeetingPreference')}
              </Text>
            </TouchableOpacity>
          )} */}

          {/* {userType == '3' && (
            <TouchableOpacity
              style={styles.qualificationContainer}
              onPress={() =>
                navigation.navigate('QualificationsAndOtherPreferences')
              }>
              <Text style={styles.buttonText}>
                {t('EditqualificationsPreference')}
              </Text>
            </TouchableOpacity>
          )} */}
        </View>
        {/* {userType == '2' && (
          <View style={styles.buttonGroupContainer}>
            <Text style={styles.manageKid}>{t('manageKids')}</Text>
            <FlatList
              data={connectedProfile?.data?.list}
              ListEmptyComponent={
                <Text style={{textAlign: 'center'}}>{t('noData')}</Text>
              }
              renderItem={({item, index}) => {
                return (
                  <View style={styles.row}>
                    <Image
                      source={
                        item?.user?.image
                          ? {uri: IMAGE_BASE_URL + item?.user?.image}
                          : {uri: DUMMY_USER_IMG}
                      }
                      style={styles.img}
                    />
                    <View style={{left: wp(2)}}>
                      <Text style={styles.name}>{item?.user?.name}</Text>
                      <Text
                        style={
                          styles.grade
                        }>{`${item?.user?.tlm_student_academic_details[0]?.tlm_grade?.name}`}</Text>
                    </View>
                  </View>
                );
              }}
            />
          </View>
        )} */}
      </ScrollView>

      <View style={styles.buttonContainer}>
        {userType == '3' && (
          <PrimaryButton
            title={t('document')}
            style={{width: wp(90)}}
            textStyle={{
              fontSize: fp(1.8),
              fontFamily: Fonts.semiBold,
            }}
            onPress={() => navigation.navigate('TutorDocuments')}
          />
        )}
        {userType == '3' && (
          <PrimaryButton
            title={t('EditqualificationsPreference')}
            style={{width: wp(90)}}
            textStyle={{
              fontSize: fp(1.8),
              fontFamily: Fonts.semiBold,
            }}
            onPress={() =>
              navigation.navigate('QualificationsAndOtherPreferences')
            }
          />
        )}
        <PrimaryButton
          title={t('editProfile')}
          style={{width: wp(90)}}
          textStyle={{
            fontSize: fp(1.8),
            fontFamily: Fonts.semiBold,
          }}
          onPress={() => navigation.navigate('UpdateProfile')}
        />
      </View>
    </SafeAreaView>
  );
};

const InfoRow = ({icon, label, value}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <View
      style={[styles.infoRow, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
      <Image source={icon} style={styles.icon} />
      <View>
        <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
          {label}
        </Text>
        <Text style={[styles.value, {textAlign: isRTL ? 'right' : 'left'}]}>
          {value}
        </Text>
      </View>
    </View>
  );
};

export default ProfilePage;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  profileContainer: {
    alignItems: 'center',
    padding: 8,
  },
  profileImage: {
    width: width * 0.35,
    height: width * 0.35,
    borderRadius: width * 0.2,
    borderWidth: 2,
    borderColor: colors.txtGrey,
    backgroundColor: colors.txtGrey,
  },
  profileName: {
    fontSize: responsiveFontSize(18),
    fontFamily: Fonts.bold,
    color: colors.black,
    marginTop: 10,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 5,
  },
  statusText: {
    color: colors.black,
    fontSize: responsiveFontSize(12),
    fontFamily: Fonts.bold,
  },
  infoCard: {
    backgroundColor: colors.white,
    marginHorizontal: 20,
    marginTop: 20,
    padding: 15,
    borderRadius: 10,
    borderColor: '#D3D3D3',
    borderWidth: 0.1,
    elevation: 2,
  },
  loadingIndicator: {
    alignSelf: 'center',
    paddingVertical: 20,
  },
  infoRow: {
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  icon: {
    width: 24,
    height: 24,
    marginRight: 15,
  },
  label: {
    fontSize: responsiveFontSize(14),
    fontFamily: Fonts.semiBold,
    color: colors.black,
  },
  value: {
    fontSize: responsiveFontSize(14),
    color: colors.black,
    marginTop: 2,
    fontFamily: Fonts.medium,
    width: wp(70),
    lineHeight: hp(1.8),
  },
  buttonGroupContainer: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  meetingPreferenceContainer: {
    backgroundColor: colors.txtGrey,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  qualificationContainer: {
    backgroundColor: colors.txtGrey,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  buttonGroupContainer: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  meetingPreferenceContainer: {
    backgroundColor: colors.txtGrey,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  qualificationContainer: {
    backgroundColor: colors.txtGrey,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'android' ? '5%' : '5%',
    width: '100%',
    paddingHorizontal: 20,
    gap: hp(1),
  },
  editButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
  gradient: {
    marginVertical: 10,
    backgroundColor: colors.white,
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 14,
    // width: width * 0.9,
    alignSelf: 'center',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: fp(2),
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: hp(1),
  },
  img: {
    height: fp(5),
    width: fp(5),
    borderRadius: fp(3),
  },
  name: {
    fontSize: fp(1.6),
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  grade: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.4),
    color: colors.lightGrey,
  },
  manageKid: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.4),
    color: colors.black,
  },
});
