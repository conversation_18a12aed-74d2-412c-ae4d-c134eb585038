import {Dimensions, StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';
import {fp} from '../../Helper/ResponsiveDimensions';
const {height, width} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  profileContainer: {
    alignItems: 'center',
    marginTop: 20,
    position: 'relative',
    width: 100,
    alignSelf: 'center',
  },
  profileImage: {
    width: width * 0.35,
    height: width * 0.35,
    borderRadius: width * 0.2,
    borderWidth: 2,
    borderColor: colors.txtGrey,
    overflow: 'hidden',
    backgroundColor: colors.txtGrey,
  },
  editIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: -3,
    borderRadius: 15,
    padding: 3,
  },
  editIcon: {
    width: 24,
    height: 24,
  },
  formContainer: {
    paddingTop: 20,
  },
  label: {
    fontSize: responsiveFontSize(14),
    color: colors.black,
    marginBottom: 5,
    fontFamily: Fonts.semiBold,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    padding: 10,
    fontSize: responsiveFontSize(14),
    marginBottom: 15,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  inputPH: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    padding: 10,
    fontSize: 14,
    marginBottom: 15,
    color: colors.black,

    fontFamily: Fonts.medium,
  },
  dropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    padding: 10,
    marginBottom: 15,
  },
  dropdownList: {
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 8,
    marginTop: -10,
    marginBottom: 15,
    backgroundColor: '#fff',
    maxHeight: fp(22),
  },
  dropdownItem: {
    padding: 10,
  },
  arrow: {
    width: 16,
    height: 16,
  },
  saveButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  saveButtonText: {
    color: colors.white,
    fontSize: responsiveFontSize(16),
    fontFamily: Fonts.bold,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.black,
    marginBottom: 15,
  },
  modalButton: {
    width: '100%',
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 5,
    marginVertical: 5,
    backgroundColor: colors.themeColor,
  },
  cancelButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.red,
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 40 : 20,
    width: '100%',
    paddingHorizontal: 20,
  },
  clickableRow: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  icon: {
    height: 20,
    width: 20,
    tintColor: colors.themeColor,
  },
  dropdownScrollContent: {
    paddingBottom: 5,
  },
});

export default styles;
