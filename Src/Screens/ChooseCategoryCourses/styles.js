import {Dimensions, StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp} from '../../Helper/ResponsiveDimensions';

const deviceWidth = Dimensions.get('window').width;
const deviceHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
  titletext: {
    fontSize: fp(2),
    color: colors.darkBlack,
    fontWeight: '700',
    alignSelf: 'center',
  },
  recentSearchButton: {
    backgroundColor: colors.white,
    borderColor: colors.themeColor,
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginBottom: 8,
  },
  recentSearchButtonText: {
    fontSize: 14,
    color: colors.themeColor,
  },
  card: {
    // backgroundColor: colors.lightGradient,
    // backgroundColor: 'red',
    // borderRadius: 10,
    // alignItems: 'center',
    // justifyContent: 'center',
    // width: '47%',
    // aspectRatio: 1,
    // marginBottom: 16,
    // padding: 10,
  },
  img: {
    height: fp(3.2),
    width: fp(3.2),
    marginVertical: hp(1),
  },
  txt: {
    fontSize: fp(2),
    fontFamily: Fonts.medium,
    color: colors.white,
    width: '100%',
    alignSelf: 'flex-start',
  },
  searchContainer: {
    alignItems: 'center',
    backgroundColor: colors.offWhite1,
    borderRadius: 10,
    paddingHorizontal: 12,
    height: 40,
  },
  searchInput: {
    flex: 1,
    fontSize: fp(1.8),
    color: colors.searchGray,
    fontFamily: Fonts.regular,
  },
});

export default styles;
