import {
  SafeAreaView,
  View,
  TextInput,
  Image,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {AppHeader} from '../../Components/Header';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {StatusContainer} from '../../Components/StatusBar';
import {useTranslation} from 'react-i18next';
import LinearGradient from 'react-native-linear-gradient';
import {useCourseSubCategoriesQuery} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import styles from './styles';
import {SvgUri} from 'react-native-svg';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

const ChooseCategoryCourses = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [search, setSearch] = useState('');
  const {
    data: CourseSubCategorData,
    error,
    isLoading,
  } = useCourseSubCategoriesQuery(2);
  const [filterData, setFilterData] = useState([]);
  useEffect(() => {
    setFilterData(CourseSubCategorData?.data?.rows);
    console.log(
      '🚀 ~ useEffect ~ CourseSubCategorData?.data?.rows:',
      CourseSubCategorData?.data?.rows,
    );
  }, [CourseSubCategorData]);
  useEffect(() => {
    // Filter data when `search` changes
    if (search.trim() === '') {
      setFilterData(CourseSubCategorData?.data?.rows); // Reset to original data if search is empty
    } else {
      const filtered = CourseSubCategorData?.data?.rows.filter(item =>
        item.name.toLowerCase().includes(search.toLowerCase()),
      );
      setFilterData(filtered);
    }
  }, [search]);

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
      <StatusContainer color={colors.white} />

      {/* Header */}
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('chooseCategory')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />

      {/* Search Bar */}
      <View style={{padding: 16}}>
        <View
          style={[
            styles.searchContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Image
            resizeMode="contain"
            style={{height: 20, width: 20}}
            source={icons.searchIcon}
          />
          <TextInput
            placeholder={t('searchPlaceholder')}
            style={[styles.searchInput, {marginRight: isRTL ? 10 : 0}]}
            placeholderTextColor={colors.txtGrey1}
            value={search}
            onChangeText={txt => setSearch(txt)}
          />
        </View>
      </View>

      {/* Category Section */}
      <View style={{flex: 1, padding: 16}}>
        {isLoading ? (
          <ActivityIndicator
            size="large"
            color={colors.themeColor}
            style={{marginTop: 20}}
          />
        ) : error || !CourseSubCategorData ? (
          <Text style={{textAlign: 'center', marginTop: 20}}>
            {t('categoryError')}
          </Text>
        ) : (
          <FlatList
            data={filterData}
            numColumns={2}
            keyExtractor={item => item.id.toString()}
            columnWrapperStyle={{
              justifyContent: 'space-between',
              flexDirection: isRTL ? 'row-reverse' : 'row',
            }}
            renderItem={({item}) => (
              <CategoryCard
                icon={{uri: `${IMAGE_BASE_URL}${item.image}`}}
                categoryName={item.name}
                navigation={navigation}
                course_category_id={item.id}
                isRTL={isRTL}
              />
            )}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const CategoryCard = ({
  categoryName,
  icon,
  navigation,
  course_category_id,
  isRTL,
}) => {
  console.log('🚀 ~ CategoryCard ~ icon:', icon);
  return (
    <TouchableOpacity
      onPress={() =>
        navigation.navigate('TutorsListCourses', {course_category_id})
      }
      style={{marginBottom: hp(2.2)}}>
      <LinearGradient
        colors={['#40A39B', '#40A39B']}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}
        style={{
          borderRadius: 10,
          width: wp(45),
          padding: fp(2),
          maxHeight: hp(30),
        }}>
        <View style={styles.card}>
          {/* <SvgUri
           width={fp(4)}
            height={fp(4)}
            stroke={'white'}
            style={styles.img}
            color={colors.white}
            uri={icon.uri}
          />  */}
          <Image
            source={icon}
            resizeMode="contain"
            style={[styles.img, {alignSelf: isRTL ? 'flex-end' : 'flex-start'}]}
            tintColor={'white'}
          />
          <Text style={[styles.txt, {textAlign: isRTL ? 'right' : 'left'}]}>
            {categoryName}
          </Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default ChooseCategoryCourses;
