import {
  Image,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import colors from '../../Utils/colors';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {jsiConfigureProps} from 'react-native-reanimated/lib/typescript/core';
import {useDispatch, useSelector} from 'react-redux';
import {setAppLocale} from '../../Features/authSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18next from 'i18next';

const AboutMenu = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const {appLocale} = useSelector(state => state?.auth);

  function handleNavigation({item}) {
    if (item?.route === 'Language') {
      console.log('route is language');
      handleLanguageConfirmation();
    } else {
      navigation.navigate(item?.route);
    }
  }

  const renderItem = item => (
    <TouchableOpacity
      onPress={() => handleNavigation({item})}
      style={applyShadowStyleIos({
        //   borderColor: colors.black,
        //   borderWidth: 1,
        elevation: 1,
        //   height: hp(10),
        width: wp(90),
        backgroundColor: colors.white,
        alignSelf: 'center',
        borderRadius: fp(2),
        padding: fp(2),
        flexDirection: isRTL ? 'row-reverse' : 'row',
        justifyContent: 'space-between',
        marginBottom: hp(1.6),
      })}>
      <Text style={{fontFamily: Fonts.medium, fontSize: fp(1.8)}}>
        {item?.label}
      </Text>
      <Image
        source={icons.rightArrowGrade}
        style={{transform: isRTL ? [{rotateY: '180deg'}] : []}}
      />
    </TouchableOpacity>
  );

  const aboutMenu = [
    {label: t('terms_of_service'), route: 'TermsOfService'},
    {label: t('privacyPolicy'), route: 'PrivacyPolicyScreen'},
  ];
  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.white, zIndex: 2}}>
      <StatusContainer color={colors.themeBackground2} />

      {/* Header */}
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('about')} />
      <ScrollView
        contentContainerStyle={{paddingVertical: hp(1), marginTop: hp(0.6)}}>
        {aboutMenu.map((item, index) => renderItem(item))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default AboutMenu;

const styles = StyleSheet.create({});
const logoutModalStyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    // margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 18,
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  modalText: {
    marginBottom: 15,
    fontFamily: Fonts.medium,
    color: colors.black,
    textAlign: 'center',
    fontSize: fp(1.6),
  },
  buttonContainer: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    marginHorizontal: wp(1),
    minWidth: 100,
  },
  cancelButton: {
    backgroundColor: colors.txtGrey,
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    color: 'black',
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    textAlign: 'center',
  },
  logoutButtonText: {
    color: 'white',
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    textAlign: 'center',
  },
});
