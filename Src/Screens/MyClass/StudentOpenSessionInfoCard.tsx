import React from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

import icons from '../../Utils/icons';
import {Fonts} from '../../Utils/Fonts';
import {DUMMY_USER_IMG, responsiveFontSize} from '../../Utils/constant';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {
  convertTo12HourFormat,
  convertToDMY,
  convertToLocal12HourFormat,
} from '../../Helper/DateHelpers/DateHelpers';
import GradBadge from '../../Components/GradBadge/GradBadge';
import TutorDetails from '../../Components/MyClass/TutorDetails';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';

const StudentOpenSessionInfoCard = ({item, onCardPress}) => {
  console.log('902837463589', item);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const navigation = useNavigation();
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={onCardPress}
      style={styles.container}>
      <View
        style={[
          styles.innerHeader,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <Text style={styles.titleTxt}>
          {/* Algebra for Primary Students (Grade - 6th) */}
          {`${item?.tlm_open_session?.title}`}
        </Text>
        {/* <GradBadge
          text={item?.tlm_open_session?.tlm_user?.tlm_tutor_profile?.expertise}
        /> */}
      </View>

      <View
        style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Image
          source={icons.clockYellow}
          style={{height: fp(3), width: fp(3), tintColor: '#EBBE49'}}
          resizeMode="contain"
        />
        <Text style={styles.timeTxt}>
          {t('time')} :{' '}
          <Text style={styles.innerTxt}>
            {`${convertToLocal12HourFormat(
              item?.tlm_tutor_schedule?.start_time,
            )} - ${convertToLocal12HourFormat(
              item?.tlm_tutor_schedule?.end_time,
            )}`}
          </Text>
        </Text>
      </View>
      <View
        style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Image
          source={icons.calendarWhite}
          style={{height: fp(3), width: fp(3), tintColor: '#EBBE49'}}
          resizeMode="contain"
        />
        <Text style={styles.timeTxt}>
          {t('session')} :{' '}
          <Text style={styles.innerTxt}>
            {item?.tlm_open_session?.tlm_class_type?.name}
          </Text>
        </Text>
      </View>
      <View style={styles.saperator} />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <TutorDetails
          imageUri={
            item?.tlm_open_session?.tlm_user?.image
              ? {
                  uri: IMAGE_BASE_URL + item?.tlm_open_session?.tlm_user?.image,
                }
              : {uri: DUMMY_USER_IMG}
          }
          name={item?.tlm_open_session?.tlm_user?.name}
          occupation={
            item?.tlm_open_session?.tlm_user?.tlm_tutor_profile?.expertise
          }
          rightContent={
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('ChatScreen', {
                  item: item?.tlm_open_session?.tlm_user,
                })
              }
              style={styles.messageBtn}>
              <Image
                source={icons.messageIcon}
                style={{alignSelf: 'center', height: fp(2), width: fp(2)}}
              />
            </TouchableOpacity>
          }
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.lightgreay,
    padding: fp(2),
    width: wp(95),
    borderRadius: fp(1.4),
    alignSelf: 'center',
    marginTop: hp(3),
  },
  innerHeader: {
    justifyContent: 'space-between',
    width: wp(60),
    alignItems: 'center',
  },
  messageBtn: {
    backgroundColor: colors.themeColor,
    borderRadius: 30,
    padding: 8,
    height: fp(4.6),
    width: fp(4.6),
    alignSelf: 'center',
    justifyContent: 'center',
  },
  timeTxt: {
    fontFamily: Fonts.poppinsRegular,
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    left: 10,
  },
  innerTxt: {
    fontFamily: Fonts.poppinsRegular,
    fontSize: fp(1.6),
    color: colors.darkBlack,
  },
  row: {
    marginTop: hp(1.5),
    alignItems: 'center',
  },
  saperator: {
    borderWidth: fp(0.06),
    borderColor: colors.txtGrey,
    marginVertical: hp(2),
  },
  titleTxt: {
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
    color: colors.black,
    width: fp(30),
  },
});

export default StudentOpenSessionInfoCard;
