import {
  View,
  Text,
  SafeAreaView,
  Image,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {useRoute} from '@react-navigation/native';
import colors from '../../Utils/colors';

import TutorDetails from '../../Components/MyClass/TutorDetails';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {PrimaryButton} from '../../Components/CustomButton';
import {
  useGetBookedOpenSessionsDetailsQuery,
  useGetBookedOpenSessionsDetailsTutorQuery,
  useGetOpenSessionDetailsByIdQuery,
  useGetTutorOpenSessionDetailsByIdQuery,
  useOpenSessionPaymentMutation,
} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {convertToDMY} from '../../Helper/DateHelpers/DateHelpers';
import {Fonts} from '../../Utils/Fonts';

import {
  DUMMY_USER_IMG,
  PAYMENT_GATEWAY_RETURN_URL,
  staticPaymentDetails,
} from '../../Utils/constant';
import {showToast} from '../../Components/ToastHelper';
import PaymentDetails from '../StudentClassDetails/PaymentDetails';
import Schedule from '../StudentClassDetails/Schedule';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import moment from 'moment';
import {openDirections} from '../../Helper/GoogleMapsHelpers';
import {StatusContainer} from '../../Components/StatusBar';
import {useSelector} from 'react-redux';

const ViewEnrolledOpenSessionDetails = ({navigation}) => {
  const route = useRoute();
  const {openSessionId, openSessionIdTutor} = route?.params;
  console.log(
    '🚀 ~ ViewEnrolledOpenSessionDetails ~ openSessionId:',
    openSessionId,
  );
  console.log(
    '🚀 ~ ViewEnrolledOpenSessionDetails ~ openSessionIdTutor:',
    openSessionIdTutor,
  );
  const userType = useSelector(state => state.auth.user_type);

  const {t} = useTranslation();
  const [instructions, setInstructions] = useState('');
  const [openSessionAmount, setOpenSessionAmount] = useState('');

  const {
    data: OpenSessionDetails,
    isLoading,
    refetch,
  } = useGetBookedOpenSessionsDetailsQuery(openSessionId);

  // const {
  //   data: OpenSessionDetailsTutor,
  //   isLoadingTutor,
  //   refetch: refetchTutor,
  // } = useGetBookedOpenSessionsDetailsTutorQuery(openSessionIdTutor);
  const {
    data: OpenSessionDetailsTutor,
    refetch: refetchOpenSessionDetails,
    isLoading: isLoadingTutor,
  } = useGetTutorOpenSessionDetailsByIdQuery(openSessionId);
  console.log(
    '🚀 ~ ViewEnrolledOpenSessionDetails ~ OpenSessionDetailsTutor:',
    OpenSessionDetailsTutor,
  );

  useEffect(() => {
    refetch();
    refetchOpenSessionDetails();
  }, []);

  const OpenSessionData =
    OpenSessionDetails?.data || OpenSessionDetailsTutor?.data;

  useEffect(() => {
    if (OpenSessionDetails || OpenSessionDetailsTutor)
      setOpenSessionAmount(
        OpenSessionData?.tlm_tutor_rate_card_academic?.tlm_tutor_class_types
          ?.price,
      );
  }, [OpenSessionDetails, OpenSessionDetailsTutor]);
  console.log(
    '🚀 ~ ViewEnrolledOpenSessionDetails ~ OpenSessionDetails:',
    JSON.stringify(OpenSessionDetails || OpenSessionDetailsTutor),
  );
  const [
    openSessionPaymentApi,
    {data: paymentApiRes, isLoading: PaymentApiLoading},
  ] = useOpenSessionPaymentMutation();

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(
    t('skipCash'),
  );
  const [selectedSlots, setSelectedSlots] = useState([]); // State to store data from the child

  // Callback function to receive data from the child
  const handleGetSlots = data => {
    console.log('🚀 ~ ViewOpenSessionDetails ~ data:', data);
    setSelectedSlots(data);
  };
  console.log(
    '🚀 ~ ViewEnrolledOpenSessionDetails ~ OpenSessionDetailsTutor?.data?.address:',
    OpenSessionData?.address,
  );

  function calcualteOpenSessionAmount() {
    const initialPrice =
      OpenSessionData?.tlm_tutor_rate_card_academic?.tlm_tutor_class_types[0]
        ?.price;
    console.log(
      '🚀 ~ calcualteOpenSessionAmount ~ initialPrice:',
      initialPrice,
    );

    const calculatedPrice = Number(initialPrice) * selectedSlots.length;
    console.log(
      '🚀 ~ calcualteOpenSessionAmount ~ calculatedPrice:',
      calculatedPrice,
    );
    setOpenSessionAmount(calculatedPrice);
  }

  useEffect(() => {
    calcualteOpenSessionAmount();
  }, [selectedSlots]);

  function handleBookOpenSession() {
    navigation.navigate('StartClassScreen');
  }

  function handleNavigateToLocPress() {
    openDirections(
      OpenSessionDetails?.data?.latitude ||
        OpenSessionDetailsTutor?.data?.latitude,
      OpenSessionDetails?.data?.longitude ||
        OpenSessionDetailsTutor?.data?.longitude,
    );
  }
  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('open_session')} />
      <TaleemLoader isLoading={isLoading} />
      <View style={{backgroundColor: colors.themeColor}}>
        <View style={styles.header}>
          <Text style={styles.headigTxt}>{OpenSessionData?.title}</Text>
        </View>
        <View style={styles.viewS}>
          <Image source={icons.academicIcon} />
          <Text style={styles.txtCategory}>{`Category : ${
            OpenSessionData?.rate_card_type == '1'
              ? 'Academic'
              : OpenSessionData?.rate_card_type == '2'
              ? 'Recreational'
              : 'Course'
          }`}</Text>
        </View>
        <View style={styles.viewS}>
          <Image
            source={icons.calanderYellow}
            style={{tintColor: colors.white}}
          />
          <Text style={styles.txtCategory}>{`${t('session')} : ${t(
            t('open'),
          )}`}</Text>
        </View>
      </View>
      <ScrollView style={styles.innerContainer}>
        <View style={styles.card}>
          {OpenSessionData?.start_date && (
            <View style={styles.cardRow}>
              <Image
                source={icons.calanderYellow}
                style={{height: fp(3), width: fp(3)}}
              />
              <Text style={styles.packageTxt}>
                {'Date : '}
                <Text style={styles.date}>
                  {moment(OpenSessionData?.start_date).format('DD MMM YYYY')} -{' '}
                  {moment(OpenSessionData?.end_date).format('DD MMM YYYY')}
                </Text>
              </Text>
            </View>
          )}

          <View style={[styles.cardRow, {marginTop: 10}]}>
            <Image
              source={icons.hatYellow}
              style={{
                height: fp(2.8),
                width: fp(2.8),
                alignSelf: 'flex-start',
              }}
            />
            <Text
              style={[styles.packageTxt, {width: wp(70), lineHeight: hp(2)}]}>
              {'Description :'}
              <Text style={styles.date}> {OpenSessionData?.description}</Text>
            </Text>
          </View>
          <View style={styles.cardBody}>
            <View style={[styles.box]}>
              <Text style={styles.package}>{'No. of sessions'}</Text>
              <Text style={styles.details}>
                {OpenSessionData?.tlm_open_session_schedules?.length}
              </Text>
            </View>
            <View style={[styles.box, {borderLeftWidth: 1}]}>
              <Text style={styles.package}>{'Class Type'}</Text>
              <Text style={styles.details}>
                {OpenSessionData?.tlm_class_type?.name ||
                  OpenSessionDetailsTutor?.data?.tlm_class_type?.name}
              </Text>
            </View>
          </View>
        </View>
        {/* Schedule */}
        {}
        <View style={{height: hp(22)}}>
          <Schedule
            data={OpenSessionData?.tlm_open_session_schedules}
            details={OpenSessionData}
            type={'openSession'}
          />
        </View>
        {/* Payment Details */}
        <View style={{marginTop: hp(2)}}>
          <PaymentDetails
            data={OpenSessionData?.PaymentDetails || staticPaymentDetails}
          />
        </View>
        {userType != '3' && (
          <>
            <Text style={[styles.blackTxt, {left: 16, marginTop: hp(4)}]}>
              {t('tutorDetails')}
            </Text>
            <View style={styles.card}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <TutorDetails
                  imageUri={{
                    uri:
                      OpenSessionDetails?.tlm_user?.image ||
                      OpenSessionDetailsTutor?.tlm_user?.image
                        ? IMAGE_BASE_URL +
                          (OpenSessionDetails?.tlm_user?.image ||
                            OpenSessionDetailsTutor?.tlm_user?.image)
                        : DUMMY_USER_IMG,
                  }}
                  name={OpenSessionData?.tlm_user?.name}
                  occupation={
                    OpenSessionData?.tlm_user?.tlm_tutor_profile?.expertise
                  }
                />
                <View style={{alignSelf: 'center'}}>
                  <PrimaryButton
                    onPress={() =>
                      navigation.navigate('ChatScreen', {
                        item: OpenSessionData?.tlm_user,
                      })
                    }
                    title={t('message_tutor')}
                    style={{
                      backgroundColor: 'transparent',
                      width: wp(28),
                      borderWidth: fp(0.1),
                      borderColor: colors.themeColor,
                    }}
                    textStyle={{fontSize: fp(1.6), color: colors.themeColor}}
                    loading={false}
                  />
                </View>
              </View>
            </View>
          </>
        )}

        {OpenSessionData?.address &&
          (OpenSessionDetails?.data?.tlm_class_type?.name == 'Face to Face' ||
            OpenSessionDetailsTutor?.data?.tlm_class_type?.name ===
              'Face to Face') && (
            <View>
              <Text style={[styles.blackTxt, {left: 16}]}>
                {t('meetingPoint')}
              </Text>
              <View style={[styles.card, {padding: fp(2)}]}>
                <View style={[styles.cardRow, {flexDirection: 'row'}]}>
                  <Image
                    source={icons.locationGray}
                    style={{
                      tintColor: colors.themeColor,
                      height: fp(3),
                      width: fp(3),
                    }}
                  />
                  <View style={styles.meetingView}>
                    <Text
                      style={[
                        styles.meetingTxt,
                        {textAlign: 'center', textAlignVertical: 'center'},
                      ]}>
                      {t('meetingPoint')}
                    </Text>
                  </View>
                </View>
                <View
                  style={[
                    styles.cardRow,
                    {
                      marginTop: hp(1),
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                    },
                  ]}>
                  <Text
                    style={[
                      styles.meetingTxt,
                      {
                        color: '#737373',
                        width: wp(60),
                        fontSize: fp(1.8),
                        lineHeight: hp(2),
                      },
                    ]}>
                    Location :{' '}
                    {OpenSessionDetails?.data?.address ||
                      OpenSessionDetailsTutor?.data?.address}
                  </Text>
                  <Pressable onPress={handleNavigateToLocPress}>
                    <Image
                      source={icons.locationArrow}
                      style={{height: hp(4), width: hp(4)}}
                    />
                  </Pressable>
                </View>
              </View>
            </View>
          )}
      </ScrollView>
    </SafeAreaView>
  );
};
const CustomCheckbox = ({label, isSelected, onSelect}) => (
  <TouchableOpacity style={styles.checkboxWrapper} onPress={onSelect}>
    <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
      {isSelected && <Text style={styles.checkMark}>✓</Text>}
    </View>
    <Text style={styles.checkboxLabel}>{label}</Text>
  </TouchableOpacity>
);
export default ViewEnrolledOpenSessionDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  headigTxt: {
    color: colors.white,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  txtCategory: {
    left: 8,
    fontFamily: Fonts.regular,
    color: colors.white,
    fontFamily: Fonts.medium,
  },
  viewS: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginVertical: 8,
  },
  innerContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  card: {
    borderWidth: 1,
    borderRadius: 8,
    borderColor: colors.txtGrey,
    marginHorizontal: 16,
    marginVertical: 10,
    padding: 20,
    backgroundColor: colors.white,
    elevation: 1,
  },
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    // justifyContent: 'center',
  },
  packageTxt: {
    color: colors.txtGrey1,
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    left: 8,
  },
  date: {
    color: colors.darkBlack,
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
  },
  cardBody: {
    flexDirection: 'row',
    marginTop: hp(2),
    // justifyContent: 'space-between',
  },
  package: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: '#878787',
  },
  details: {
    color: colors.black,
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    marginTop: 8,
  },
  box: {
    width: '48%',
    padding: 10,

    borderLeftColor: colors.txtGrey,
  },
  blackTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  sectionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  paymentSectionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderRadius: 15,
    marginBottom: 15,
  },

  instructionsInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 15,
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    height: fp(6),
    fontFamily: Fonts.regular,
    marginTop: fp(1.5),
  },
  messageBtn: {
    backgroundColor: colors.themeColor,
    borderRadius: 30,
    padding: 8,
    height: 30,
    width: 30,
  },
  containerDetails: {
    flex: 1,
    backgroundColor: colors.white,
  },
  cardGradient: {
    marginHorizontal: 16,
    borderRadius: 12,
    padding: 16,
    marginVertical: 10,
  },
  innerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: wp(85),
  },
  timeTxt: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    left: 10,
  },
  innerTxt: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
    color: colors.darkBlack,
    left: 8,
  },
  row: {
    flexDirection: 'row',
    marginTop: hp(1.5),
    alignItems: 'center',
  },
  mediumTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
  },
  bigTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  meetingView: {
    backgroundColor: colors.lightGreen,
    padding: 8,
    height: fp(4),
    borderRadius: 30,
    marginLeft: 10,
  },
  meetingTxt: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
    color: colors.txtGrey2,
  },
  totalAmountContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  totalAmountText: {
    fontSize: fp(2),
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  totalAmountValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.black,
  },
  checkboxesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  checkboxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    marginRight: 10,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  checkboxSelected: {
    backgroundColor: colors.themeColor,
    borderColor: colors.themeColor,
  },
  checkMark: {
    color: colors.white,
    fontSize: fp(1.8),
  },
  checkboxLabel: {
    fontSize: fp(1.8),
    color: colors.txtGrey1,
    fontFamily: Fonts.medium,
  },

  sectionTitle: {
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
    marginBottom: 10,
  },
});
