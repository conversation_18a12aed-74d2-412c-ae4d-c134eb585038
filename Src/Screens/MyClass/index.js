import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  Image,
  I18nManager,
  FlatList,
  Modal,
  TouchableWithoutFeedback,
  Platform,
  ActivityIndicator,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import styles from './styles';
import icons from '../../Utils/icons';
import {showToast} from '../../Components/ToastHelper';
import {StatusContainer} from '../../Components/StatusBar';
import {
  bookingsData,
  CLASS_TYPES_ARRAY,
  SESSION_TYPES_ARRAY,
} from '../../Utils/constant';
import CustomDropDown from '../../Components/CustomDropDown';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import ClassInfoCard from '../../Components/MyClass/ClassInfoCard';
import DateTimePicker from '@react-native-community/datetimepicker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  useGetBookedOpenSessionsQuery,
  useGetStudentBookingListQuery,
  useGetTutorBookingListQuery,
  useLazyGetAllBookingQuery,
  useLazyGetBookingDatesForCalendarQuery,
} from '../../Api/ApiSlice';
import {useSelector} from 'react-redux';
import {convertToDMY} from '../../Helper/DateHelpers/DateHelpers';
import moment from 'moment';
import SessionInfoCard from '../BookOpenSession/SessionInfoCard';
import StudentOpenSessionInfoCard from './StudentOpenSessionInfoCard';
import {globalStyles} from '../../Utils/globalStyles';
import TaleemHeader from '../../Components/TaleemHeader/TaleemHeader';
import TaleemEventCalendar from '../../Components/Calendar/TaleemEventCalendar';
import {
  getFirstAndLastDates,
  getFormattedBookingDatesWithSlots,
} from '../../Helper/Calendar/FormatAvailableSlotDate';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';

const MyClassScreen = ({navigation}) => {
  const tempBookingDataArr = [
    {
      type: 'booking',
      id: 1,
      booking_id: 1,
      start_time: '14:45:26',
      class_link: null,
      date: '2025-04-21',
      status: '1',
      tlm_tutor_schedule: {
        id: 100,
        start_time: '14:45:26',
        end_time: '15:45:26',
      },
      tlm_booking: {
        id: 1,
        class_title: 'Zoology class for Primary School students',
        tutor_rc_academic_id: 1,
        tutor_rc_recreational_id: null,
        tutor_rc_courses_id: null,
        tutor_approval_status: '0',
        tutor: {
          id: 1,
          name: 'Suraj Kumar',
          image: null,
          tlm_tutor_profile: {
            id: 1,
            grades: null,
            qualification: 'Bachelor’s Degree',
            expertise: null,
          },
        },
        tlm_booking_enrollments: [
          {
            id: 1,
            tlm_user: {
              id: 5,
              name: 'Amit',
            },
          },
        ],
        tlm_sessions_type: {
          id: 1,
          name: 'Individual',
          name_ar: 'فردي',
        },
        tlm_class_type: {
          id: 1,
          name: 'Online',
          name_ar: 'متصل',
        },
      },
    },
    {
      type: 'booking',
      id: 2,
      booking_id: 1,
      start_time: '15:45:26',
      class_link: null,
      date: '2025-04-21',
      status: '1',
      tlm_tutor_schedule: {
        id: 107,
        start_time: '15:45:26',
        end_time: '16:45:26',
      },
      tlm_booking: {
        id: 1,
        class_title: 'Zoology class for Primary School students',
        tutor_rc_academic_id: 1,
        tutor_rc_recreational_id: null,
        tutor_rc_courses_id: null,
        tutor_approval_status: '0',
        tutor: {
          id: 1,
          name: 'Suraj Kumar',
          image: null,
          tlm_tutor_profile: {
            id: 1,
            grades: null,
            qualification: 'Bachelor’s Degree',
            expertise: null,
          },
        },
        tlm_booking_enrollments: [
          {
            id: 1,
            tlm_user: {
              id: 5,
              name: 'Amit',
            },
          },
        ],
        tlm_sessions_type: {
          id: 1,
          name: 'Individual',
          name_ar: 'فردي',
        },
        tlm_class_type: {
          id: 1,
          name: 'Online',
          name_ar: 'متصل',
        },
      },
    },
    {
      type: 'booking',
      id: 3,
      booking_id: 1,
      start_time: '16:45:26',
      class_link: null,
      date: '2025-04-21',
      status: '1',
      tlm_tutor_schedule: {
        id: 114,
        start_time: '16:45:26',
        end_time: '17:45:26',
      },
      tlm_booking: {
        id: 1,
        class_title: 'Zoology class for Primary School students',
        tutor_rc_academic_id: 1,
        tutor_rc_recreational_id: null,
        tutor_rc_courses_id: null,
        tutor_approval_status: '0',
        tutor: {
          id: 1,
          name: 'Suraj Kumar',
          image: null,
          tlm_tutor_profile: {
            id: 1,
            grades: null,
            qualification: 'Bachelor’s Degree',
            expertise: null,
          },
        },
        tlm_booking_enrollments: [
          {
            id: 1,
            tlm_user: {
              id: 5,
              name: 'Amit',
            },
          },
        ],
        tlm_sessions_type: {
          id: 1,
          name: 'Individual',
          name_ar: 'فردي',
        },
        tlm_class_type: {
          id: 1,
          name: 'Online',
          name_ar: 'متصل',
        },
      },
    },
  ];
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const logoSource =
    langType === 'ar'
      ? icons.logo.welcomeLogoArabic
      : icons.logo.welcomeLogoEng;

  const [langType, setLangType] = useState('');
  const [selectedTab, setSelectedTab] = useState('All');
  const [selectedClassTab, setSelectedClassTab] = useState('Academic');
  const [selectedDateType, setSelectedDateType] = useState('');
  const [showPicker, setShowPicker] = useState(false);
  const [bookingData, setBookingData] = useState([]);
  const [tempBookingData, setTempBookingData] = useState(tempBookingDataArr);

  const [isLoading, setIsLoading] = useState(false);
  const [bookingDate, setBookingDate] = useState(null);
  const [isCalendarLoading, setIsCalendarLoading] = useState(false);
  const date = new Date();
  const markedDatesRef = useRef({});
  const {user_type} = useSelector(state => state?.auth);

  const getLangType = async () => {
    const storedLangType = await AsyncStorage.getItem('selectedLang');
    return storedLangType ? JSON.parse(storedLangType) : '';
  };

  const [fetchAllBookings] = useLazyGetAllBookingQuery({
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  useFocusEffect(
    useCallback(() => {
      setBookingData([]); // Clear bookingData on focus
      fetchBookings();
    }, []),
  );

  const {data: OpenSessionData, refetch: refetchOpenSessionData} =
    useGetBookedOpenSessionsQuery({
      date: bookingDate,
    });
  const [fetchSchedules] = useLazyGetBookingDatesForCalendarQuery({
    refetchOnFocus: true,
  });

  console.log('🚀 ~ MyClassScreen ~ bookingData:', JSON.stringify(bookingData));
  // Refresh all data when screen comes into focus

  const fetchBookings = useCallback(
    async (date = null, classTypeId = null) => {
      setIsLoading(true);
      setBookingData([]); // Clear bookingData before fetching
      try {
        const params = {
          userType: user_type == '3' ? 'tutor' : 'student',
        };

        if (date) {
          params.date = date;
        }

        if (selectedTab === 'Face to Face') {
          params.class_type_id = '2';
        } else if (selectedTab === 'Online') {
          params.class_type_id = '1';
        }

        if (classTypeId) {
          params.class_type_id = classTypeId;
        }

        const response = await fetchAllBookings(params).unwrap();
        console.log('🚀 ~ .then ~ res:', response?.data?.rows);
        if (response?.data?.rows) {
          setBookingData(response?.data?.rows || []);
        }
      } catch (error) {
        console.error('Error fetching bookings:', error);
        // Add proper error handling here
        showToast('error', 'Failed to fetch bookings', 'bottom', isRTL);
        setBookingData([]); // Clear bookingData on error
      } finally {
        setIsLoading(false);
      }
    },
    [user_type, selectedTab, fetchAllBookings, showToast, isRTL],
  );

  //   useCallback(() => {
  //     const refreshData = async () => {
  //       try {
  //         // Refresh language setting

  //         // Reset date filter
  //         setBookingDate(null);

  //         // Fetch bookings
  //         await fetchBookings();

  //         // Only refetch if already fetched once
  //         if (OpenSessionData) {
  //           await refetchOpenSessionData();
  //         }

  //         // Refresh calendar data
  //         const {firstDay, lastDay, year, month} = getFirstAndLastDates(null);
  //         await handleGetBookingDatesForCalendar(
  //           year,
  //           month,
  //           firstDay,
  //           lastDay,
  //         );
  //       } catch (error) {
  //         console.error('Error refreshing data:', error);
  //       }
  //     };

  //     refreshData();
  //   }, [fetchBookings, refetchOpenSessionData]),
  // );

  // useFocusEffect(
  //   useCallback(() => {
  //     let isActive = true;

  //     const refreshData = async () => {
  //       try {
  //         // Refresh language setting

  //         // Reset date filter if necessary
  //         setBookingDate(null);

  //         // Fetch bookings
  //         await fetchBookings();

  //         // Refetch open session data if it exists
  //         if (isActive && OpenSessionData) {
  //           await refetchOpenSessionData();
  //         }

  //         // Refresh calendar data
  //         const {firstDay, lastDay, year, month} = getFirstAndLastDates(null);
  //         if (isActive) {
  //           await handleGetBookingDatesForCalendar(
  //             year,
  //             month,
  //             firstDay,
  //             lastDay,
  //           );
  //         }
  //       } catch (error) {
  //         if (isActive) {
  //           console.error('Error refreshing data:', error);
  //           showToast('error', 'Failed to refresh data', 'bottom', isRTL);
  //         }
  //       }
  //     };

  //     refreshData();

  //     return () => {
  //       isActive = false; // Cleanup flag
  //     };
  //   }, [fetchBookings, refetchOpenSessionData]),
  // );
  //   useCallback(() => {
  //     let isActive = true; // Flag to prevent race conditions

  //     const refreshData = async () => {
  //       setIsLoading(true); // Set loading state
  //       try {
  //         // Reset date filter
  //         setBookingDate(null);

  //         // Fetch bookings
  //         await fetchBookings();
  //         // Refetch open session data
  //         if (isActive) {
  //           await refetchOpenSessionData(); // Remove unnecessary check
  //         }

  //         // Refresh calendar data
  //         const {firstDay, lastDay, year, month} =
  //           getFirstAndLastDates(bookingDate);
  //         if (isActive) {
  //           await handleGetBookingDatesForCalendar(
  //             year,
  //             month,
  //             firstDay,
  //             lastDay,
  //           );
  //         }
  //       } catch (error) {
  //         if (isActive) {
  //           console.error('Error refreshing data:', error);
  //           showToast('error', 'Failed to refresh data', 'bottom', isRTL);
  //         }
  //       } finally {
  //         if (isActive) {
  //           setIsLoading(false); // Reset loading state
  //         }
  //       }
  //     };

  //     refreshData();

  //     // Cleanup to prevent updates if the screen is unfocused
  //     return () => {
  //       isActive = false;
  //     };
  //   }, [
  //     fetchBookings,
  //     refetchOpenSessionData,
  //     bookingDate,
  //     isRTL,
  //     handleGetBookingDatesForCalendar,
  //   ]),
  // );

  const handleDateSelect = date => {
    const dateString = date.dateString;
    setBookingDate(dateString);
    fetchBookings(dateString);
    refetchOpenSessionData();

    const updatedMarkedDates = {...markedDatesRef.current};
    Object.keys(updatedMarkedDates).forEach(dateKey => {
      if (updatedMarkedDates[dateKey]?.selected) {
        delete updatedMarkedDates[dateKey].selected;
        delete updatedMarkedDates[dateKey].color;
        delete updatedMarkedDates[dateKey].textColor;
      }
    });

    updatedMarkedDates[dateString] = {
      ...updatedMarkedDates[dateString],
      selected: true,
    };

    markedDatesRef.current = updatedMarkedDates;
    setShowPicker(false);
  };

  const handlePress = tab => {
    setSelectedTab(tab);
    setBookingData([]); // Clear bookingData when changing tabs
    // Wrap this in a setTimeout to ensure state update completes first
    setTimeout(() => {
      if (tab === 'All') {
        fetchBookings(bookingDate);
      } else if (tab === 'Online') {
        fetchBookings(bookingDate, '1');
      } else if (tab === 'Face to Face') {
        fetchBookings(bookingDate, '2');
      }

      if (OpenSessionData) {
        refetchOpenSessionData();
      }
    }, 0);
  };

  const handleGetBookingDatesForCalendar = useCallback(
    async (year, month, startDate, endDate) => {
      setIsCalendarLoading(true);
      try {
        const studentParams = {
          startDate: startDate,
          endDate: endDate,
          class_type_id: selectedTab == 'Face to Face' ? 2 : 1,
        };
        const tutorParams = {
          startDate: startDate,
          endDate: endDate,
          rate_card_type:
            selectedClassTab == 'Academic'
              ? 1
              : selectedClassTab == 'Recreational'
              ? 2
              : 3,
        };
        const USER_TYPE = user_type == '3' ? 'tutor' : 'student';
        const params = user_type == '3' ? tutorParams : studentParams;

        const response = await fetchSchedules({
          params: params,
          userType: USER_TYPE,
        }).unwrap();
        const bookingDatesApiRes = response?.data;
        const bookingDatesFormattedData = getFormattedBookingDatesWithSlots(
          bookingDatesApiRes,
          {},
          'bold',
          colors.darkBlack,
        );

        const allFormattedDates = {
          ...bookingDatesFormattedData,
        };

        if (bookingDate) {
          allFormattedDates[bookingDate] = {
            ...allFormattedDates[bookingDate],
            customStyles: {
              container: {
                backgroundColor: colors.themeBackground,
              },
              text: {
                color:
                  allFormattedDates[bookingDate]?.customStyles?.text?.color,
                fontWeight:
                  allFormattedDates[bookingDate]?.customStyles?.text
                    ?.fontWeight || '300',
              },
            },
          };
        }

        markedDatesRef.current = allFormattedDates;
      } catch (err) {
        console.error('Error: getBookingDates', err);
        showToast('error', err?.data?.message, 'bottom', isRTL);
      } finally {
        setIsCalendarLoading(false);
      }
    },
    [user_type, selectedTab, selectedClassTab, bookingDate],
  );

  // const dataToRender =
  //   selectedTab === 'Open Session' ? OpenSessionData?.data?.rows : bookingData;

  const renderItem = ({item}) => {
    console.log('🚀 ~ renderItem ~ item:', item);
    return (
      <ClassInfoCard
        item={item}
        onCardPress={() => {
          if (item?.type === 'openSession') {
            navigation.navigate('ViewEnrolledOpenSessionDetails', {
              openSessionId: item?.tlm_booking?.id,
              openSessionIdTutor: item?.id,
            });
          } else {
            navigation.navigate('StudentClassDetails', {item});
          }
        }}
      />
    );
  };

  const getDateDisplayText = () => {
    if (!bookingDate) {
      return t('select_date');
    }
    return bookingDate === date.toISOString().split('T')[0]
      ? t('today')
      : moment(bookingDate).format('DD MMM YYYY');
  };
  const isFocus = useIsFocused();
  useEffect(() => {
    console.log('🚀 ~ useEffect ~ bookingData:', bookingData);
  }, [bookingData, selectedTab, bookingDate, isFocus]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <TaleemHeader />

      <View style={styles.contentContainer}>
        <TaleemLoader isLoading={isLoading} />
        <View
          style={[
            styles.header,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Text style={styles.quickActionsTitle}>{t('myclass')}</Text>
          <TouchableOpacity
            onPress={() => setShowPicker(!showPicker)}
            activeOpacity={0.8}
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignSelf: 'center',
            }}>
            <Image
              source={icons.calanderYellow}
              style={{height: fp(2.6), width: fp(2.6), alignSelf: 'center'}}
              resizeMode="contain"
            />
            <Text style={styles.dayTxt}>{getDateDisplayText()}</Text>
            <Image
              source={icons.arrowDown}
              style={{height: 24, width: 24, marginLeft: 5}}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
        <View
          style={[
            styles.buttonGroup,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <View
            style={[
              styles.buttonRow,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            {SESSION_TYPES_ARRAY.map(tab => (
              <TouchableOpacity
                key={tab}
                style={[
                  applyShadowStyleIos(styles.button),
                  selectedTab === tab
                    ? {
                        backgroundColor: colors.themeColor,
                        borderColor: colors.themeColor,
                      }
                    : {backgroundColor: colors.white, borderColor: '#ccc'},
                ]}
                onPress={() => handlePress(tab)}>
                <Text
                  style={[
                    styles.buttonText,
                    selectedTab === tab
                      ? {color: colors.white}
                      : {color: colors.lightGrey},
                  ]}>
                  {tab == 'Face to Face'
                    ? t('face_to_face')
                    : tab == 'Open Session'
                    ? t('open_session')
                    : t(tab?.toLowerCase())}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        {!isLoading ? (
          bookingData?.length > 0 ? (
            <FlatList
              data={bookingData}
              contentContainerStyle={{paddingBottom: hp(10)}}
              showsVerticalScrollIndicator={false}
              keyExtractor={(item, index) => `${item?.id || index}`} // Ensure unique keys
              renderItem={item => {
                return renderItem(item);
              }}
            />
          ) : (
            <View>
              <Text style={[styles.dayTxt, {textAlign: 'center'}]}>
                {t('no_class_found')}
              </Text>
            </View>
          )
        ) : null}
      </View>

      <Modal
        transparent
        visible={showPicker}
        style={{
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <TouchableWithoutFeedback onPress={() => setShowPicker(false)}>
          <View style={styles.calender}>
            <TaleemEventCalendar
              selectedDate={bookingDate}
              handleDateSelect={handleDateSelect}
              markedDates={markedDatesRef.current}
              isLoading={isCalendarLoading}
              handleOnMonthChange={dateObj => {
                const {firstDay, lastDay, year, month} = getFirstAndLastDates(
                  dateObj.dateString,
                );
                handleGetBookingDatesForCalendar(
                  year,
                  month,
                  firstDay,
                  lastDay,
                );
              }}
              isShowAllInstructions={false}
            />
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </SafeAreaView>
  );
};

export default MyClassScreen;
