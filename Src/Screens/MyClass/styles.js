import {StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {responsiveFontSize, SCREEN_WIDTH} from '../../Utils/constant';
import {CurrentRenderContext} from '@react-navigation/native';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
  },
  headerContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    height: hp(7),
    marginBottom: hp(1),
    justifyContent: 'center',
  },
  leftHeader: {
    flex: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  hamburgerIcon: {
    width: fp(2.4),
    height: fp(2.4),
  },
  logoContainer: {
    alignItems: 'center',
    paddingLeft: 5,
  },
  logoImage: {
    width: fp(12), // Keep the width fixed
    height: fp(6),
  },
  rightHeader: {
    flex: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingRight: 10,
    minWidth: 120,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: fp(1.8),
    minWidth: fp(8),
  },
  locationIcon: {
    width: fp(2.8),
    height: fp(2.5),
  },
  locationText: {
    marginHorizontal: 6,
    color: colors.white,
    fontWeight: '500',
    fontSize: 12,
  },
  arrowDownIcon: {
    width: fp(1.2),
    height: fp(1.2),
    alignSelf: 'center',
  },
  notificationContainer: {
    padding: fp(0.5),
  },
  notificationIcon: {
    width: fp(3.5),
    height: fp(3.5),
  },
  contentContainer: {
    backgroundColor: colors.white,
    flex: 1,
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
  },
  viewContent: {
    marginBottom: 50,
  },
  quickActionsTitle: {
    fontSize: fp(2.2),
    color: colors.black,
    fontFamily: Fonts.semiBold,
    // marginTop: 10,
  },
  buttonGroup: {
    justifyContent: 'flex-start',
    marginVertical: fp(2),
    marginHorizontal: fp(1.2),
  },
  button: {
    borderRadius: fp(2),
    paddingVertical: fp(0.6),
    paddingHorizontal: fp(1.8),
    marginHorizontal: fp(0.4),
    marginBottom: fp(1),
    elevation: 2,
  },
  buttonText: {
    fontSize: fp(1.6),
    fontFamily: Fonts.poppinsRegular,
    color: colors.greyLight,
  },
  buttonRow: {
    width: '100%',
    flexWrap: 'wrap',
  },
  bioText: {
    fontSize: fp(1.7),
    color: colors.black,
    marginLeft: fp(0.4),
    textAlignVertical: 'center', // Ensure vertical alignment within the text box
  },
  header: {
    alignItems: 'center',
    justifyContent: 'space-between',

    marginTop: hp(2),
    marginHorizontal: fp(1.8),
  },
  clsType: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  dayTxt: {
    marginLeft: 10,
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.darkBlack,
    alignSelf: 'center',
  },
  calender: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.offBlack,
    // borderRadius: fp(4),
  },
});

export default styles;
