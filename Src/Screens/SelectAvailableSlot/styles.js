import {Dimensions, Platform, StyleSheet} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {fp} from '../../Helper/ResponsiveDimensions';
const {height} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
    paddingHorizontal: Platform.OS === 'android' ? 0 : 10,
  },
  checkboxesContainer: {
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  monthTitle: {
    textAlign: 'center',
    color: '#fff',
    fontSize: 16,
    marginVertical: 10,
    fontFamily: Fonts.bold,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    paddingHorizontal: 10,
    marginBottom: 10,
  },
  arrow: {
    height: 10,
    width: 10,
    tintColor: '#fff',
    marginHorizontal: 5,
  },
  dateItem: {
    width: 55,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 3,
  },
  gradientWrapper: {
    borderRadius: 10,
  },
  selectedDate: {
    borderRadius: 10,
  },
  dateText: {
    color: '#fff',
    fontSize: 13,
    fontFamily: Fonts.medium,
  },
  selectedDateText: {
    color: '#333',
    fontFamily: Fonts.bold,
  },
  bottomContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingTop: 20,
    width: '100%',
    height: height * 0.77,
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    color: colors.black,
    marginBottom: 10,
    fontFamily: Fonts.bold,
  },
  slotsList: {
    maxHeight: 250,
  },
  hoursContainer: {
    alignItems: 'center',
    borderColor: '#e0e0e0',
    borderWidth: 1,
    borderRadius: 8,
    padding: 10,
    marginTop: 5,
  },
  clockIcon: {
    height: 20,
    width: 20,
    tintColor: '#555',
    marginRight: 8,
  },
  hoursText: {
    fontSize: 16,
    color: '#555',
  },
  saveButton: {
    backgroundColor: colors.themeColor,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    width: '100%',
    marginTop: 20,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: '#555',
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
    backgroundColor: '#FFF',
  },
  checkboxSelected: {
    backgroundColor: '#40A39B',
  },
  checkMark: {
    color: '#fff',
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#555',
  },
  calender: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.offBlack,
    // borderRadius: fp(4),
  },
  dayTxt: {
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.white,
    alignSelf: 'center',
  },
});

export default styles;
