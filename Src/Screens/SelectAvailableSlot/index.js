import React, {useState, useEffect, useRef} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Modal,
  TouchableWithoutFeedback,
  Alert,
} from 'react-native';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import TimeSlot from './TimeSlot';
import LinearGradient from 'react-native-linear-gradient';
import styles from './styles';
import {useTranslation} from 'react-i18next';
import {
  useLazyGetTutorSchedulesForCalendarQuery,
  useLazyGetTutorSchedulesForStudentQuery,
  useLazyGetTutorSchedulesInRangeQuery,
} from '../../Api/ApiSlice';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import DateTimePicker from '@react-native-community/datetimepicker';
import CustomCheckbox from '../../Components/Custom_Checkbox /CustomCheckbox';
import {useDispatch, useSelector} from 'react-redux';
import {
  updateSelectedSlotDate,
  updateSlot,
} from '../../Redux/Slices/Student/TutorBookingSlice';
import {
  updateBookingSchedules,
  updateGreySlots,
  updateIsScheduleAtSimilarTime,
  updateSelectedSlots,
} from '../../Redux/Slices/Student/SlotSlice';
import {showToast} from '../../Components/ToastHelper';
import {useRoute} from '@react-navigation/native';
import moment from 'moment';
import {Calendar} from 'react-native-calendars';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import TaleemEventCalendar from '../../Components/Calendar/TaleemEventCalendar';
import {
  getFirstAndLastDates,
  getFormattedBookingDatesWithSlots,
  getFormattedDatesWithSlots,
  getRemainingDatesFormatted,
  lastDateOfMonth,
} from '../../Helper/Calendar/FormatAvailableSlotDate';
import {
  convertDecimalHoursToHoursAndMinutes,
  convertTo12HourFormatAvailableSlot,
  getCurrentUTCTime,
} from '../../Helper/DateHelpers/DateHelpers';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';

const SelectAvailableSlot = ({navigation}) => {
  const dispatch = useDispatch();
  const route = useRoute();
  const {t, i18n} = useTranslation();
  const {rateCardId, tutor, type} = route?.params || {};
  console.log('route.params==', route?.params);
  const tutorData = useSelector(state => state?.tutorBookingSlice?.tutorData);
  console.log('🚀 ~ SelectAvailableSlot ~ tutorData:', tutorData);
  const {bookingFlowType, courseRateCardId, classType, packageStartDate} =
    useSelector(state => state?.tutorBookingSlice);
  console.log('🚀 ~ SelectAvailableSlot ~ classType:', classType);
  const {isScheduleAtSimilarTime, selectedSlots, total_days, bookingSchedules} =
    useSelector(state => state.slotSlice);
  // console.log('🚀 ~ SelectAvailableSlot ~ selectedSlots:', selectedSlots);

  const [selectedDate, setSelectedDate] = useState(
    packageStartDate
      ? new Date(packageStartDate).toISOString().split('T')[0]
      : new Date().toISOString().split('T')[0],
  );
  console.log('🚀 ~ SelectAvailableSlot ~ selectedDate:', selectedDate);
  const [isScheduleLoading, setIsScheduleLoading] = useState(false);
  const [showPicker, SetShowPicker] = useState(true);
  const [isCalendarLoading, setIsCalendarLoading] = useState(false);
  const selectedPackageData = useSelector(
    state => state?.tutorBookingSlice?.package,
  );
  const minHours = selectedPackageData?.tlm_package_type?.min_hour;
  console.log('🚀 ~ SelectAvailableSlot ~ minHours:', minHours);
  const maxHour = selectedPackageData?.tlm_package_type?.max_hour;
  console.log('🚀 ~ SelectAvailableSlot ~ maxHour:', maxHour);

  const [dates, setDates] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [localSelectedSlots, setLocalSelectedSlots] = useState([]); // Local state for selected slots
  const flatListRef = useRef(null);
  const markedDatesRef = useRef({});
  const [tutorScheduleData, setTutorScheduleData] = useState();
  const [getTutorSchedulesForStudent, {isLoading}] =
    useLazyGetTutorSchedulesForStudentQuery();
  const [fetchSchedules, {data, isLoading: isLoadingSchedules, error}] =
    useLazyGetTutorSchedulesForCalendarQuery();
  const [fetchSchedulesInRange] = useLazyGetTutorSchedulesInRangeQuery();
  const checkboxData = [
    {key: 'schedule_at_similar_time', label: 'scheduleAtSimilarTimes'},
    {key: 'available_date', label: 'availableDatesAndHours'},
  ];
  const [isGeneralLoading, setIsGeneralLoading] = useState(false);

  const [selectedCheckboxes, setSelectedCheckboxes] = useState({
    schedule_at_similar_time: false,
    available_date: false,
  });

  useEffect(() => {
    // Get the slots for the selected date from Redux
    const dateSlots = selectedSlots[selectedDate] || [];
    setLocalSelectedSlots(dateSlots); // Sync local state with Redux
  }, [selectedDate, selectedSlots]);
  console.log(
    '🚀 ~ useEffect ~ courseRateCardId:',
    courseRateCardId,
    rateCardId,
    classType?.tlm_class_type?.id,
  );

  useEffect(() => {
    if (bookingFlowType === 'courses') {
      setIsScheduleLoading(true);
      getTutorSchedulesForStudent({
        // id: courseRateCardId || rateCardId,
        id: tutorData?.profileData?.id || tutorData?.id,
        date: new Date(selectedDate).toISOString().split('T')[0],
        class_type_id: classType?.tlm_class_type?.id || classType?.id,
        currentTime: getCurrentUTCTime().toString(),
      })
        .unwrap()
        .then(response => {
          setIsScheduleLoading(false);
          setTutorScheduleData(response);
        })
        .catch(err => {
          setIsScheduleLoading(false);
          console.error('Error fetching schedules:', err);
        });
    } else if (bookingFlowType == 'recreational') {
      console.log(
        '🚀 ~ useEffect ~ tutorData?.profileData?.id:',
        tutorData?.profileData?.id,
      );

      setIsScheduleLoading(true);
      getTutorSchedulesForStudent({
        id: tutorData?.profileData?.user_id || tutor?.id || tutorData?.id,
        date: new Date(selectedDate).toISOString().split('T')[0],
        class_type_id: classType?.tlm_class_type?.id || classType?.id,
        currentTime: getCurrentUTCTime().toString(),
      })
        .unwrap()
        .then(response => {
          setIsScheduleLoading(false);
          console.log(
            '🚀 ~ useEffect ~ response: schedules tutor',
            JSON.stringify(response),
          );
          setTutorScheduleData(response);
        })

        .catch(err => {
          setIsScheduleLoading(false);
          console.error('Error fetching schedules:', err);
        });
    } else {
      console.log(
        '🚀 ~ useEffect ~ tutorData?.profileData?.id:',
        tutorData?.profileData?.id,
      );

      setIsScheduleLoading(true);
      getTutorSchedulesForStudent({
        id: tutorData?.profileData?.id || tutor?.id || tutorData?.id,
        date: new Date(selectedDate).toISOString().split('T')[0],
        class_type_id: classType?.tlm_class_type?.id || classType?.id,
        currentTime: getCurrentUTCTime().toString(),
      })
        .unwrap()
        .then(response => {
          setIsScheduleLoading(false);
          console.log(
            '🚀 ~ useEffect ~ response: schedules tutor',
            JSON.stringify(response),
          );
          setTutorScheduleData(response);
        })

        .catch(err => {
          setIsScheduleLoading(false);
          console.error('Error fetching schedules:', err);
        });
    }
  }, [selectedDate, bookingFlowType]);

  useEffect(() => {
    const fetchSchedules = async () => {
      console.log(tutorData?.tlm_user, 'tutorData?.courseData?.tlm_user?.id');
      setIsScheduleLoading(true);
      const startDate = new Date(packageStartDate);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + total_days - 1);
      const params = {
        id:
          tutorData?.profileData?.user_id ||
          tutor?.id ||
          tutorData?.profileData?.id ||
          tutorData?.id,
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0],
        class_type_id: classType?.tlm_class_type?.id || classType?.id,
      };
      console.log('🚀 ~ fetchSchedules ~ params:', params);
      try {
        const response = await getTutorSchedulesForStudent(params).unwrap();
        setTutorScheduleData(response);
      } catch (err) {
        console.error('Error fetching schedules:', err);
      } finally {
        setIsScheduleLoading(false);
      }
    };
    fetchSchedules();
  }, [packageStartDate, total_days]);

  function handleGetScheduleDataForCalendar(year, month, startDate, endDate) {
    setIsCalendarLoading(true);
    const params = {
      id:
        bookingFlowType == 'academic'
          ? tutorData?.profileData?.id || tutor?.id
          : bookingFlowType == 'recreational'
          ? tutorData?.profileData?.user_id || tutor?.id
          : tutorData?.profileData?.id || tutorData?.id,
      class_type_id: classType?.class_type_id || classType?.id,
      startDate: startDate,
      endDate: endDate,
      currentTime: getCurrentUTCTime().toString(),
    };

    fetchSchedules(params)
      .unwrap()
      .then(response => {
        const scheduleDatesRes = response?.data?.schedule_dates;
        const bookingDatesApiRes = response?.data?.booking_dates;

        // Format remaining dates with grey color
        const remainingDatesFormattedData = getRemainingDatesFormatted(
          scheduleDatesRes,
          bookingDatesApiRes,
          year,
          month,
          '400',
          colors.darkGrey,
        );

        // Format scheduled dates with bold and black
        const slotsDatesFormattedData = getFormattedDatesWithSlots(
          scheduleDatesRes,
          bookingDatesApiRes,
          'bold',
          colors.darkBlack,
        );

        // Format booking dates with yellow dots
        const bookingDatesFormattedData = getFormattedBookingDatesWithSlots(
          bookingDatesApiRes,
          {...slotsDatesFormattedData, ...remainingDatesFormattedData},
          'bold',
          colors.darkBlack,
        );

        // Merge all formatted dates
        const allFormattedDates = {
          ...remainingDatesFormattedData,
          ...slotsDatesFormattedData,
          ...bookingDatesFormattedData,
        };

        // Add selected date styling
        if (selectedDate) {
          console.log(
            'selectedDate 789854684',
            allFormattedDates[selectedDate],
          );
          allFormattedDates[selectedDate] = {
            ...allFormattedDates[selectedDate],
            // Ensure the background color is consistent
            // color: colors.themeBackground, // Background color for selected date
            // Merge custom styles without overriding fontWeight
            customStyles: {
              container: {
                backgroundColor: colors.themeBackground,
              },
              text: {
                color:
                  allFormattedDates[selectedDate]?.customStyles?.text?.color, // Text color for selected date
                fontWeight:
                  allFormattedDates[selectedDate]?.customStyles?.text
                    ?.fontWeight || '300',
              },
            },
          };
        }

        markedDatesRef.current = allFormattedDates;
        setIsCalendarLoading(false);
      })
      .catch(err => {
        console.error('Error:', err);
        showToast('error', err?.data?.message, 'bottom', isRTL);
        setIsCalendarLoading(false);
      });
  }
  const isRTL = i18n.language === 'ar';

  //Get Tutor schedules for date range

  useEffect(() => {
    const tempDate = selectedDate;
    // Example usage:
    const {firstDay, lastDay, year, month} = getFirstAndLastDates(tempDate);
    console.log('Last day of the month:', firstDay, lastDay, year, month);
    handleGetScheduleDataForCalendar(
      year,
      month,
      firstDay,
      lastDay,
      // FirstAndLastDateObj.firstDate,
      // FirstAndLastDateObj.lastDate,
    );
  }, [selectedDate]);

  useEffect(() => {
    const now = new Date();
    const daysInMonth = Array.from(
      {length: new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate()},
      (_, i) => {
        const date = new Date(now.getFullYear(), now.getMonth(), i + 1);
        return new Date(
          Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()),
        );
      },
    );
    setDates(daysInMonth);

    const todayIndex = daysInMonth.findIndex(
      d => d.toISOString().split('T')[0] === now.toISOString().split('T')[0],
    );
    setCurrentIndex(todayIndex);

    setTimeout(() => {
      try {
        flatListRef.current?.scrollToIndex({
          animated: true,
          index: todayIndex,
          viewPosition: 0.5,
        });
      } catch (error) {
        console.error('Scroll to index failed', error);
      }
    }, 0);
  }, []);
  const handleSlotSelect = async slot => {
    console.log('🚀 ~ SelectAvailableSlot ~ slot:', slot);

    setLocalSelectedSlots(prevSelectedSlots => {
      const isAlreadySelected = prevSelectedSlots.some(
        selectedSlot => selectedSlot.id === slot.id,
      );
      console.log(
        '🚀 ~ SelectAvailableSlot ~ isAlreadySelected:',
        isAlreadySelected,
      );

      let updatedSlots = isAlreadySelected
        ? prevSelectedSlots.filter(selectedSlot => selectedSlot.id !== slot.id)
        : [...prevSelectedSlots, slot];

      console.log('🚀 ~ SelectAvailableSlot ~ updatedSlots:', updatedSlots);

      // if (selectedCheckboxes.schedule_at_similar_time) {
      //   handleSimilarTimeScheduling();
      // } else {
      dispatch(
        updateSelectedSlots({
          date: selectedDate,
          slots: updatedSlots,
        }),
      );
      // }
      return updatedSlots;
    });
  };

  const handleSimilarTimeScheduling = async isEnabled => {
    setIsGeneralLoading(true); // Start loader
    const selectedHours = getSelectedHours(); // Get current selected hours
    console.log(
      '🚀 ~ handleSimilarTimeScheduling ~ selectedHours:',
      selectedHours,
    );

    // Check if minimum hours are already fulfilled
    if (selectedHours >= minHours) {
      showToast(
        'info',
        `Minimum required hours (${minHours}) already fulfilled. No additional slots needed.`,
        'bottom',
        isRTL,
      );
      setIsGeneralLoading(false); // Stop loader
      return; // Exit the function if minimum hours are already met
    }

    const currentDate = new Date(); // Today's date
    const startDate = new Date(packageStartDate);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + total_days - 1); // Limit to total_days

    // Ensure start date is at least tomorrow
    const tomorrow = new Date(currentDate);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const effectiveStartDate = startDate < tomorrow ? tomorrow : startDate;

    const startDateApi = effectiveStartDate.toISOString().split('T')[0];
    const endDateApi = endDate.toISOString().split('T')[0];
    const classTypeId = classType?.class_type_id || classType?.id;
    const tutorId = tutorData?.profileData?.id || tutor?.id;
    const recreationaTutorId = tutorData?.profileData?.user_id;
    console.log(
      '🚀 ~ SelectAvailableSlot ~ recreationaTutorId:',
      recreationaTutorId,
    );

    const body = {
      startDate: startDateApi,
      endDate: endDateApi,
      class_type_id: classTypeId,
      id:
        bookingFlowType == 'academic'
          ? tutorId
          : bookingFlowType == 'recreational'
          ? recreationaTutorId
          : recreationaTutorId,
    };
    console.log('🚀 ~ handleSimilarTimeScheduling ~ body:', body);

    try {
      const res = await fetchSchedulesInRange(body);
      const scheduleResforRange = res?.data?.data?.availableSlots;

      let updatedSlotsByDate = {...selectedSlots}; // Copy existing selected slots
      let totalProposedHours = selectedHours; // Track total hours including proposed slots

      // Filter future dates only within total_days
      Object.keys(scheduleResforRange)
        .filter(date => new Date(date) >= tomorrow && new Date(date) <= endDate) // Only future dates within range
        .forEach(date => {
          const events = scheduleResforRange[date];

          // Find matching slots for the current date
          const matchingSlots = events.filter(event =>
            localSelectedSlots.some(
              selected =>
                selected.start_time === event.start_time &&
                selected.end_time === event.end_time,
            ),
          );

          // Calculate hours for the matching slots
          const hoursForDate = matchingSlots.reduce(
            (sum, slot) => sum + parseFloat(slot.hours_duration),
            0,
          );

          // Check if adding these slots exceeds the minimum hours
          if (totalProposedHours + hoursForDate <= minHours) {
            totalProposedHours += hoursForDate;
            if (matchingSlots.length > 0) {
              updatedSlotsByDate[date] = matchingSlots; // Add to the slots object
            }
          } else if (totalProposedHours < minHours) {
            // If we haven't reached minHours yet, add only what's needed
            let remainingHoursNeeded = minHours - totalProposedHours;
            const limitedSlots = [];
            for (const slot of matchingSlots) {
              const slotHours = parseFloat(slot.hours_duration);
              if (remainingHoursNeeded >= slotHours) {
                limitedSlots.push(slot);
                remainingHoursNeeded -= slotHours;
                totalProposedHours += slotHours;
              } else {
                break; // Stop adding slots once we reach or exceed minHours
              }
            }
            if (limitedSlots.length > 0) {
              updatedSlotsByDate[date] = limitedSlots;
            }
          }
        });

      // If total hours still don't meet the minimum, notify the user
      if (totalProposedHours < minHours) {
        showToast(
          'warning',
          `Could not schedule enough slots to meet the minimum required hours (${minHours}). Currently scheduled: ${convertDecimalHoursToHoursAndMinutes(
            totalProposedHours,
          )}.`,
          'bottom',
          isRTL,
        );
      }

      // Update Redux with the new slots
      Object.keys(updatedSlotsByDate).forEach(date => {
        dispatch(
          updateSelectedSlots({
            date: date,
            slots: updatedSlotsByDate[date],
          }),
        );
      });
    } catch (err) {
      console.error('error in fetch schedule in range', err);
      showToast(
        'error',
        'Failed to fetch schedules. Please try again.',
        'bottom',
        isRTL,
      );
    } finally {
      setIsGeneralLoading(false); // Stop loader
    }
  };

  const handleScheduleAvailableHours = async isEnabled => {
    setIsGeneralLoading(true); // Start loader
    const selectedHours = getSelectedHours(); // Get current selected hours
    console.log(
      '🚀 ~ handleScheduleAvailableHours ~ selectedHours:',
      selectedHours,
    );

    // Check if minimum hours are already fulfilled
    if (selectedHours >= minHours) {
      showToast(
        'info',
        `Minimum required hours (${minHours}) already fulfilled. No additional slots needed.`,
        'bottom',
        isRTL,
      );
      setIsGeneralLoading(false); // Stop loader
      return; // Exit if minimum hours are already met
    }

    const currentDate = new Date(); // Today's date
    const startDate = new Date(packageStartDate);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + total_days - 1); // Limit to total_days

    // Ensure start date is at least tomorrow
    const tomorrow = new Date(currentDate);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const effectiveStartDate = startDate < tomorrow ? tomorrow : startDate;

    const startDateApi = effectiveStartDate.toISOString().split('T')[0];
    const endDateApi = endDate.toISOString().split('T')[0];
    const classTypeId = classType?.class_type_id || classType?.id;
    const tutorId = tutorData?.profileData?.id || tutor?.id;
    const recreationaTutorId = tutorData?.profileData?.user_id;
    const body = {
      startDate: startDateApi,
      endDate: endDateApi,
      class_type_id: classTypeId,
      id:
        bookingFlowType == 'academic'
          ? tutorId
          : bookingFlowType == 'recreational'
          ? recreationaTutorId
          : recreationaTutorId,
    };
    console.log('🚀 ~ handleScheduleAvailableHours ~ body:', body);

    try {
      const res = await fetchSchedulesInRange(body);
      const scheduleResforRange = res?.data?.data?.availableSlots;

      let updatedSlotsByDate = {...selectedSlots}; // Copy existing selected slots
      let totalScheduledHours = selectedHours; // Track total hours including new slots
      const remainingHoursNeeded = minHours - selectedHours; // Hours still needed

      // Filter future dates only within total_days
      Object.keys(scheduleResforRange)
        .filter(date => new Date(date) >= tomorrow && new Date(date) <= endDate) // Only future dates within range
        .forEach(date => {
          if (totalScheduledHours >= minHours) return; // Stop if we've reached minHours

          const events = scheduleResforRange[date]; // All available slots for this date

          // Filter out slots already selected to avoid duplicates
          const availableSlots = events.filter(
            slot => !updatedSlotsByDate[date]?.some(s => s.id === slot.id),
          );

          let slotsToAdd = [];
          for (const slot of availableSlots) {
            const slotHours = parseFloat(slot.hours_duration);
            if (totalScheduledHours + slotHours <= minHours) {
              slotsToAdd.push(slot);
              totalScheduledHours += slotHours;
            } else if (totalScheduledHours < minHours) {
              // If adding this slot exceeds minHours, check if we can fit it partially
              const remaining = minHours - totalScheduledHours;
              if (remaining > 0) {
                slotsToAdd.push(slot); // Add it anyway, we'll handle excess in validation
                totalScheduledHours += slotHours;
                break;
              }
            }
            if (totalScheduledHours >= minHours) break; // Stop once minHours is reached
          }

          if (slotsToAdd.length > 0) {
            updatedSlotsByDate[date] = [
              ...(updatedSlotsByDate[date] || []),
              ...slotsToAdd,
            ];
          }
        });

      // If total hours still don't meet the minimum, notify the user
      if (totalScheduledHours < minHours) {
        showToast(
          'warning',
          `Could not schedule enough slots to meet the minimum required hours (${minHours}). Currently scheduled: ${convertDecimalHoursToHoursAndMinutes(
            totalScheduledHours,
          )}.`,
          'bottom',
          isRTL,
        );
      } else {
        showToast(
          'success',
          `Successfully scheduled ${convertDecimalHoursToHoursAndMinutes(
            totalScheduledHours,
          )} to meet the minimum requirement of ${minHours} hours.`,
          'bottom',
          isRTL,
        );
      }

      // Update Redux with the new slots
      Object.keys(updatedSlotsByDate).forEach(date => {
        dispatch(
          updateSelectedSlots({
            date: date,
            slots: updatedSlotsByDate[date],
          }),
        );
      });
    } catch (err) {
      console.error('error in fetch schedule in range', err);
      showToast(
        'error',
        'Failed to fetch schedules. Please try again.',
        'bottom',
        isRTL,
      );
    } finally {
      setIsGeneralLoading(false); // Stop loader - this was incorrectly using setIsScheduleLoading
    }
  };

  const handleDateSelect = date => {
    const dateString = date.dateString;
    setSelectedDate(dateString);

    const updatedMarkedDates = {...markedDatesRef.current};

    // Reset any previous selected date
    Object.keys(updatedMarkedDates).forEach(dateKey => {
      if (updatedMarkedDates[dateKey]?.selected) {
        delete updatedMarkedDates[dateKey].selected;
        delete updatedMarkedDates[dateKey].color;
        delete updatedMarkedDates[dateKey].textColor;
      }
    });

    // Apply new selected date styles
    updatedMarkedDates[dateString] = {
      ...updatedMarkedDates[dateString],
      selected: true,
      color: colors.purple,
      textColor: colors.white,
    };

    markedDatesRef.current = updatedMarkedDates;
    SetShowPicker(false);
  };

  const handleCheckboxChange = key => {
    setSelectedCheckboxes(prevState => {
      const newValue = !prevState[key]; // Toggle the current state of the checkbox
      console.log(newValue, 'asdfqw');

      // Reset the other checkbox to false
      const updatedState = {
        schedule_at_similar_time: false,
        available_date: false,
        [key]: newValue, // Set the current checkbox to its new value
      };

      // Call different functions based on the key and its new value
      if (key === 'schedule_at_similar_time') {
        handleSimilarTimeScheduling(newValue);
      } else if (key === 'available_date') {
        handleScheduleAvailableHours(newValue);
      }

      // Return the updated state
      return updatedState;
    });
  };

  useEffect(() => {
    const dateSlots = selectedSlots[selectedDate] || [];
    setLocalSelectedSlots(dateSlots);
  }, []);

  const getSelectedHours = () => {
    // Check if the slot object has data
    if (selectedSlots && Object.keys(selectedSlots).length > 0) {
      // console.log('🚀 ~ getSelectedHours ~ selectedSlots:', selectedSlots);

      // Calculate total hours across all dates
      const totalHours = Object.values(selectedSlots)
        .flat() // Flatten the arrays of slots from all dates
        .reduce((sum, slot) => {
          return sum + parseFloat(slot?.hours_duration);
        }, 0);
      return totalHours;
    } else {
      return 0;
    }
  };

  const handleSave = () => {
    const selectedHours = getSelectedHours();
    console.log('🚀 ~ handleSave ~ selectedHours:', selectedHours);
    if (selectedHours < minHours) {
      showToast(
        'error',
        `Please select at least ${minHours} hours. Currently selected: ${convertDecimalHoursToHoursAndMinutes(
          selectedHours,
        )}.`,
        'bottom',
        isRTL,
      );
    } else if (selectedHours > maxHour) {
      showToast(
        'error',
        `Please select at most ${maxHour} hours. Currently selected: ${convertDecimalHoursToHoursAndMinutes(
          selectedHours,
        )} hours.`,
        'bottom',
        isRTL,
      );
    } else if (type === 'rebook' && (selectedHours > 1 || selectedHours < 1)) {
      showToast('error', t('hourMessage'), 'bottom', isRTL);
    } else {
      dispatch(updateSlot(localSelectedSlots));
      dispatch(updateSelectedSlotDate(selectedDate));
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader
        backIcon={icons.backbtn}
        isBackBtn
        title={t('SelectAvailableSlot')}
      />
      <TaleemLoader isLoading={isGeneralLoading} />
      <TouchableOpacity
        onPress={() => SetShowPicker(!showPicker)}
        activeOpacity={0.8}
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          marginBottom: hp(1),
          justifyContent: 'space-between',
          marginHorizontal: wp(6),
          marginTop: hp(2),
        }}>
        <Text
          style={{
            fontSize: fp(1.8),
            color: colors.white,
            fontFamily: Fonts.bold,
          }}>
          {t('select_date')}
        </Text>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignSelf: 'center',
            alignSelf: 'center',
          }}>
          <Image
            source={icons.calanderYellow}
            style={{
              height: fp(2.6),
              width: fp(2.6),
              alignSelf: 'center',
              justifyContent: 'center',
            }}
            resizeMode="contain"
          />
          <Text
            style={[
              styles.dayTxt,
              {marginLeft: isRTL ? 0 : 10, marginRight: isRTL ? 10 : 0},
            ]}>
            {selectedDate
              ? moment(selectedDate).isSame(moment(), 'day')
                ? t('today')
                : moment(selectedDate).format('DD MMM YYYY')
              : ''}
          </Text>
          <Image
            source={icons.arrowDown}
            style={{height: 24, width: 24, marginLeft: 5}}
            resizeMode="contain"
            tintColor={colors.offWhite}
          />
        </View>
      </TouchableOpacity>

      <Modal
        transparent
        visible={showPicker}
        style={{alignItems: 'center', justifyContent: 'center'}}>
        <TouchableWithoutFeedback onPress={() => SetShowPicker(false)}>
          <View style={styles.calender}>
            <TaleemEventCalendar
              selectedDate={selectedDate} // "2025-02-01"
              handleDateSelect={handleDateSelect}
              markedDates={markedDatesRef.current}
              isLoading={isCalendarLoading}
              handleOnMonthChange={dateObj => {
                const {firstDay, lastDay, year, month} = getFirstAndLastDates(
                  dateObj.dateString,
                );
                handleGetScheduleDataForCalendar(
                  year,
                  month,
                  firstDay,
                  lastDay,
                );
              }}
              minDate={new Date(packageStartDate).toISOString().split('T')[0]}
            />
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      <View style={styles.bottomContainer}>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('availableSlots')}</Text>
          {isLoading || isScheduleLoading ? (
            <ActivityIndicator size={'large'} color={colors.themeBackground} />
          ) : (
            <FlatList
              data={tutorScheduleData?.data?.tutorSchedule?.rows}
              renderItem={({item}) => (
                <TimeSlot
                  item={item}
                  selected={localSelectedSlots.some(
                    slot =>
                      slot.start_time === item.start_time &&
                      slot.end_time === item.end_time,
                  )}
                  selectedSlots={localSelectedSlots}
                  currentDate={selectedDate}
                  onSelect={() => handleSlotSelect(item)}
                />
              )}
              keyExtractor={item => item.id.toString()}
              showsVerticalScrollIndicator={false}
              style={styles.slotsList}
              ListEmptyComponent={
                <View style={{justifyContent: 'center', alignItems: 'center'}}>
                  <Text
                    style={{
                      fontFamily: Fonts.semiBold,
                      fontSize: fp(1.8),
                      color: colors.black,
                    }}>
                    {t('noSlots')}
                  </Text>
                </View>
              }
            />
          )}
        </View>

        {/* <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('availableHours')}</Text>
          <View
            style={[
              styles.hoursContainer,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image source={icons.timeSlotClock} style={styles.clockIcon} />
            <Text style={[styles.hoursText, {marginRight: isRTL ? 10 : 0}]}>
              {tutorScheduleData?.data?.available_hours}
            </Text>
          </View>
        </View> */}
        {bookingFlowType !== 'courses' && (
          <View
            style={[
              styles.checkboxesContainer,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            {checkboxData.map(item => (
              <CustomCheckbox
                isRtl={isRTL}
                key={item.key}
                label={t(item.label)} // Translated label
                isSelected={selectedCheckboxes[item.key]}
                onSelect={() => handleCheckboxChange(item.key)}
              />
            ))}
          </View>
        )}

        <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
          <Text style={styles.saveButtonText}>{t('save')}</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};
export default SelectAvailableSlot;
