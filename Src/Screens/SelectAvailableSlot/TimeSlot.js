import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import {
  convertTo12HourFormat,
  convertToLocal12HourFormat,
  convertUTCToLocal,
} from '../../Helper/DateHelpers/DateHelpers';
import {Fonts} from '../../Utils/Fonts';
import {fp} from '../../Helper/ResponsiveDimensions';

const TimeSlot = ({
  item,
  selected,
  onSelect,
  selectedSlots = [],
  currentDate,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  // Check if this time slot is selected
  const isTimeSlotSelected = () => {
    return (
      selected ||
      selectedSlots.some(
        slot =>
          slot.start_time === item.start_time &&
          slot.end_time === item.end_time,
      )
    );
  };

  // Check if the slot is booked
  const isSlotBooked = () => {
    return item.booked || false;
  };

  const getSlotStyle = () => {
    if (isTimeSlotSelected()) return styles.selectedSlot;
    if (isSlotBooked()) return styles.bookedSlot;
    return styles.defaultSlot;
  };

  const getTextStyle = () => {
    if (isTimeSlotSelected()) return styles.selectedText;
    if (isSlotBooked()) return styles.bookedText;
    return styles.defaultText;
  };

  const handlePress = () => {
    if (!isSlotBooked()) {
      onSelect(item);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.slotContainer,
        getSlotStyle(),
        isRTL && {flexDirection: 'row-reverse'},
      ]}
      onPress={handlePress}
      disabled={isSlotBooked()}>
      <View style={styles.radioContainer}>
        <View
          style={[
            styles.radio,
            isTimeSlotSelected() && styles.radioSelected,
            isSlotBooked() && styles.radioBooked,
          ]}>
          {isTimeSlotSelected() && <View style={styles.radioInner} />}
        </View>
      </View>

      <View
        style={[
          styles.timeContainer,
          isRTL && {marginRight: 12, marginLeft: 0},
        ]}>
        <Text style={getTextStyle()}>
          {isRTL
            ? `${convertToLocal12HourFormat(
                item.end_time.toString(),
              )} - ${convertToLocal12HourFormat(item.start_time.toString())}`
            : `${convertToLocal12HourFormat(
                item.start_time.toString(),
              )} - ${convertToLocal12HourFormat(item.end_time.toString())}`}
        </Text>
      </View>

      {isSlotBooked() && (
        <LinearGradient
          colors={['#C6FFC9', '#D4EBFF']}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
          style={[
            styles.bookedLabel,
            isRTL && {marginRight: 12, marginLeft: 0},
          ]}>
          <Text style={styles.bookedLabelText}>{t('booked')}</Text>
        </LinearGradient>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  slotContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: fp(1.2),
    borderRadius: 10,
    borderWidth: 1,
    marginVertical: 8,
    marginHorizontal: 8,
    height: fp(5),
  },
  defaultSlot: {
    backgroundColor: colors.white,
    borderColor: colors.lightGrey,
  },
  selectedSlot: {
    backgroundColor: colors.themeColor,
    borderColor: colors.themeColor,
  },
  bookedSlot: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
  },
  timeContainer: {
    flex: 1,
    marginLeft: 12,
  },
  defaultText: {
    fontSize: fp(2),
    fontFamily: Fonts.regular,
    color: colors.black,
  },
  selectedText: {
    fontSize: fp(2),
    color: colors.white,
    fontFamily: Fonts.medium,
  },
  bookedText: {
    color: '#9E9E9E',
  },
  radioContainer: {
    width: 24,
  },
  radio: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.grey,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioSelected: {
    borderColor: colors.white,
  },
  radioBooked: {
    borderColor: '#E0E0E0',
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.white,
  },
  bookedLabel: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginLeft: 12,
  },
  bookedLabelText: {
    fontSize: fp(1.8),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
});

export default TimeSlot;
