import React from 'react';
import {View, FlatList, StyleSheet, Text} from 'react-native';
import AccordianOpenSession from '../../Components/AccordianOpenSession';
import {Fonts} from '../../Utils/Fonts';
import {fp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';

const ScheduleOpenSession = ({data, maxStudents}) => {
  console.log('🚀 ~ ScheduleOpenSession ~ maxStudents:', maxStudents);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{t('availableSlots')}</Text>
      <FlatList
        data={data}
        nestedScrollEnabled
        keyExtractor={item => item.id.toString()}
        showsVerticalScrollIndicator={false}
        renderItem={({item}) => (
          <AccordianOpenSession
            item={item}
            disable={item?.enrollmentCount == maxStudents}
            isRTL={isRTL}
          />
        )}
      />
    </View>
  );
};

export default ScheduleOpenSession;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: wp(4),
    backgroundColor: 'white',
  },
  heading: {
    fontFamily: Fonts.bold,
    fontSize: fp(2),
    color: colors.darkBlack,
    marginBottom: 10,
  },
});
