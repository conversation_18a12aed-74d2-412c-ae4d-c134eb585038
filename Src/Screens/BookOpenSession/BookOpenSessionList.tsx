import {
  ActivityIndicator,
  FlatList,
  Image,
  Modal,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import colors from '../../Utils/colors';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {useGetOpenSessionListQuery} from '../../Api/ApiSlice';
import SessionInfoCard from './SessionInfoCard';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import moment from 'moment';
import TaleemEventCalendar from '../../Components/Calendar/TaleemEventCalendar';
import {showToast} from '../../Components/ToastHelper';

const BookOpenSessionList = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [showPicker, setShowPicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const {
    data: OpenSessionData,
    isLoading,
    refetch,
  } = useGetOpenSessionListQuery();

  console.log(
    '🚀 ~ BookOpenSessionList ~ OpenSessionData:',
    JSON.stringify(OpenSessionData),
  );

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    refetch();
  }, []);

  const filteredData = OpenSessionData?.data?.rows?.filter(item => {
    if (!item) return false;

    const searchLower = searchQuery.toLowerCase();

    // If no date is selected (null), show all sessions
    if (!selectedDate) {
      // Apply only search filter when no date is selected
      if (!searchQuery) return true;

      return (
        item?.title?.toLowerCase().includes(searchLower) ||
        item?.tlm_user?.name?.toLowerCase().includes(searchLower) ||
        item?.tlm_tutor_rate_card_academic?.tlm_subject?.name
          ?.toLowerCase()
          .includes(searchLower) ||
        item?.tlm_tutor_rate_card_recreational?.tlm_expertise?.name
          ?.toLowerCase()
          .includes(searchLower) ||
        item?.tlm_class_type?.name?.toLowerCase().includes(searchLower)
      );
    }

    // When date is selected, apply both date and search filters
    const itemDate = moment(item.start_date).format('YYYY-MM-DD');
    const dateMatches =
      itemDate === selectedDate ||
      item?.tlm_open_session_schedules?.some(
        schedule => moment(schedule.date).format('YYYY-MM-DD') === selectedDate,
      );

    if (!dateMatches) return false;

    if (!searchQuery) return true;

    return (
      item?.title?.toLowerCase().includes(searchLower) ||
      item?.tlm_user?.name?.toLowerCase().includes(searchLower) ||
      item?.tlm_tutor_rate_card_academic?.tlm_subject?.name
        ?.toLowerCase()
        .includes(searchLower) ||
      item?.tlm_tutor_rate_card_recreational?.tlm_expertise?.name
        ?.toLowerCase()
        .includes(searchLower) ||
      item?.tlm_class_type?.name?.toLowerCase().includes(searchLower)
    );
  });

  const handleDateSelect = date => {
    const dateString = date?.dateString;
    setSelectedDate(dateString);
    setShowPicker(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('open_session')} />

      {/* Search Bar */}
      <View
        style={{
          flexDirection: 'row',
          marginTop: hp(2),
          width: wp(90),
          marginHorizontal: wp(3),
        }}>
        <View
          style={applyShadowStyleIos({
            flexDirection: isRTL ? 'row-reverse' : 'row',
          })}>
          <View
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
              backgroundColor: colors.white,
              borderRadius: 10,
              height: 35,
              marginRight: isRTL ? 10 : 0,
              paddingHorizontal: 10,
              elevation: 2,
              width: wp(62),
            }}>
            <Image
              style={{
                width: 18,
                height: 18,
                marginRight: isRTL ? 0 : 8,
                marginLeft: isRTL ? 8 : 0,
              }}
              tintColor={colors.darkGrey}
              source={icons.searchIcon}
              resizeMode="contain"
            />
            <TextInput
              style={{
                fontSize: 14,
                color: colors.searchGray,
                padding: 0,
                textAlign: isRTL ? 'right' : 'left',
                fontFamily: Fonts.medium,
                width: wp(70),
              }}
              placeholder={t('searchPlaceholder') || 'Search'}
              placeholderTextColor={colors.searchGray}
              value={searchQuery}
              onChangeText={text => setSearchQuery(text)}
            />
          </View>
        </View>
        <TouchableOpacity
          onPress={() => setShowPicker(!showPicker)}
          activeOpacity={0.8}
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignSelf: 'center',
          }}>
          <Image
            source={icons.calanderYellow}
            style={{
              height: fp(2.6),
              width: fp(2.6),
              alignSelf: 'center',
              marginLeft: wp(1),
            }}
            resizeMode="contain"
          />
          <Text style={[styles.dayTxt, {marginLeft: 2}]}>
            {selectedDate
              ? moment(selectedDate).format('DD MMM YYYY')
              : t('select_date') || 'Select Date'}
          </Text>
          <Image
            source={icons.arrowDown}
            style={{height: 24, width: 24}}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>

      {/* FlatList to Display Filtered Data */}
      <FlatList
        data={filteredData}
        nestedScrollEnabled
        renderItem={({item}) => {
          return (
            <SessionInfoCard
              item={item}
              onCardPress={() => {
                console.log('item', item);
                if (item?.is_enrollment_active == 1) {
                  navigation.navigate('ViewOpenSessionDetails', {
                    openSessionId: item?.id,
                  });
                } else {
                  showToast('error', 'Enrollment is now closed.');
                }
              }}
            />
          );
        }}
        ListEmptyComponent={
          isLoading ? (
            <ActivityIndicator size={'large'} />
          ) : (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: hp(2),
              }}>
              <Text style={[styles.dayTxt, {textAlign: 'center'}]}>
                {t('no_session_found')}!
              </Text>
            </View>
          )
        }
      />

      <Modal
        transparent
        visible={showPicker}
        style={{
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <TouchableWithoutFeedback onPress={() => setShowPicker(false)}>
          <View style={styles.calender}>
            <TaleemEventCalendar
              selectedDate={selectedDate}
              handleDateSelect={handleDateSelect}
              isShowAllInstructions={false}
              markedDates={{
                [selectedDate]: {
                  selected: true,
                  disableTouchEvent: true,
                  selectedDotColor: 'orange',
                },
              }}
              isYellowDotInstruction={false}
            />
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </SafeAreaView>
  );
};

export default BookOpenSessionList;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: fp(2),
  },
  header: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  headigTxt: {
    color: colors.white,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  viewS: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginVertical: 8,
  },
  txtCategory: {
    left: 8,
    color: colors.white,
    fontFamily: Fonts.medium,
  },
  dayTxt: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.darkBlack,
    alignSelf: 'center',
  },
  quickActionsTitle: {
    fontSize: fp(2.2),
    color: colors.black,
    fontFamily: Fonts.semiBold,
  },
  calender: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.offBlack,
  },
});
