import {
  View,
  Text,
  SafeAreaView,
  Image,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {useRoute} from '@react-navigation/native';
import colors from '../../Utils/colors';

import TutorDetails from '../../Components/MyClass/TutorDetails';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {PrimaryButton} from '../../Components/CustomButton';
import {
  useGetOpenSessionDetailsByIdQuery,
  useLazyGetStudentWalletAmountQuery,
  useOpenSessionPaymentMutation,
} from '../../Api/ApiSlice';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {
  convertToDMY,
  isOpenSessionSlotDisabled,
} from '../../Helper/DateHelpers/DateHelpers';
import {Fonts} from '../../Utils/Fonts';
import ScheduleOpenSession from './ScheduleOpenSession';
import {DUMMY_USER_IMG, PAYMENT_GATEWAY_RETURN_URL} from '../../Utils/constant';
import {showToast} from '../../Components/ToastHelper';
import moment from 'moment';
import {openDirections} from '../../Helper/GoogleMapsHelpers';

const ViewOpenSessionDetails = ({navigation}) => {
  const route = useRoute();
  const {openSessionId} = route?.params;
  const {t, i18n} = useTranslation();
  const [instructions, setInstructions] = useState('');
  const [openSessionAmount, setOpenSessionAmount] = useState('');
  const [isPaymentLoading, setIsPaymentLoading] = useState(false);
  const [slotAmount, setSlotAmount] = useState('');
  const isRTL = i18n.language === 'ar';
  const {data: OpenSessionDetails, isLoading} =
    useGetOpenSessionDetailsByIdQuery(openSessionId);
  const OpenSessionData = OpenSessionDetails?.data;
  const [
    openSessionAmountWithoutCommission,
    setOpenSessionAmountWithoutCommission,
  ] = useState('');
  const [sessionsCount, setSessionsCount] = useState('');
  const [availableSlots, setAvailableSlots] = useState();

  useEffect(() => {
    if (OpenSessionData) {
      // Filter available slots (future/current date & not expired)
      const availableSlots = OpenSessionData?.tlm_open_session_schedules.filter(
        slot => {
          const isFutureOrCurrentDate = moment(slot.date).isSameOrAfter(
            moment().format('YYYY-MM-DD'),
          );
          const isSlotAvailable = !isOpenSessionSlotDisabled(
            slot.date,
            slot.tlm_tutor_schedule?.start_time,
          );
          return isFutureOrCurrentDate && isSlotAvailable;
        },
      );

      // Determine which rate card to use based on type (1, 2, or 3)
      const rateCardType = OpenSessionData?.rate_card_type; // 1, 2, or 3

      let initialPrice = 0;
      let pricewoCommission = 0;

      if (rateCardType === '1') {
        // Academic Rate Card
        initialPrice =
          OpenSessionData?.tlm_tutor_rate_card_academic?.[0]
            ?.tlm_tutor_class_types?.[0]?.commission_price || 0;
        pricewoCommission =
          OpenSessionData?.tlm_tutor_rate_card_academic?.[0]
            ?.tlm_tutor_class_types?.[0]?.price || 0;
      } else if (rateCardType === '2') {
        // Recreational Rate Card
        initialPrice =
          OpenSessionData?.tlm_tutor_rate_card_recreational?.[0]
            ?.tlm_tutor_class_types?.[0]?.commission_price || 0;
        pricewoCommission =
          OpenSessionData?.tlm_tutor_rate_card_recreational?.[0]
            ?.tlm_tutor_class_types?.[0]?.price || 0;
      } else if (rateCardType === '3') {
        // "All Courses" Rate Card (use the first available)
        // Fallback to whichever exists
        initialPrice =
          OpenSessionData?.tlm_tutor_rate_card_all_course?.[0]
            ?.tlm_tutor_class_types?.[0]?.commission_price || 0;
        pricewoCommission =
          OpenSessionData?.tlm_tutor_rate_card_all_course?.[0]
            ?.tlm_tutor_class_types?.[0]?.price || 0;
      }

      // Calculate total amount (with and without commission)
      const calculatedPrice = Number(initialPrice) * availableSlots.length;
      setAvailableSlots(availableSlots);
      const calculatePriceWoCommission =
        Number(pricewoCommission) * availableSlots.length;
      setSlotAmount(initialPrice.toString());
      // Update both states
      setOpenSessionAmount(calculatedPrice);
      setOpenSessionAmountWithoutCommission(calculatePriceWoCommission);
      setSessionsCount(availableSlots.length);
    }
  }, [OpenSessionData]);

  const [openSessionPaymentApi, {isLoading: PaymentApiLoading}] =
    useOpenSessionPaymentMutation();
  const [getWalletAmount, {data: studentWallet}] =
    useLazyGetStudentWalletAmountQuery();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(
    t('skipCash'),
  );

  useEffect(() => {
    getWalletAmount();
  }, []);

  function handleBookOpenSession() {
    setIsPaymentLoading(true);
    // const availableSlots = OpenSessionData?.tlm_open_session_schedules
    //   .filter(slot =>
    //     moment(slot.date).isSameOrAfter(moment().format('YYYY-MM-DD')),
    //   )
    //   .map(slot => slot.id);

    if (availableSlots?.length === 0) {
      showToast('error', t('noAvailableSlots'), 'bottom', isRTL);
      setIsPaymentLoading(false);
      return;
    }

    if (
      selectedPaymentMethod === t('wallet') &&
      (studentWallet?.data == 0 || studentWallet?.data < openSessionAmount)
    ) {
      navigation.navigate('Wallet');
      showToast('info', t('insufficientBalance'), 'bottom', isRTL);
      setIsPaymentLoading(false);
      return;
    }

    const body = {
      open_session_id: openSessionId,
      amount: openSessionAmount,
      open_session_schedule_ids: availableSlots.map(item => item.id),
      payment_method:
        selectedPaymentMethod === t('skipCash') ? 'skipCash' : 'wallet',
      returnUrl: PAYMENT_GATEWAY_RETURN_URL,
      tutor_earned_amount: openSessionAmountWithoutCommission,
      payment_breakdown: {
        quantity: availableSlots?.length,
        slot_amount: slotAmount,
        total: openSessionAmount,
        discount_percentage: '0',
        discounted_amount: '0',
        grand_total: openSessionAmount,
      },
    };

    openSessionPaymentApi(body)
      .unwrap()
      .then(response => {
        setIsPaymentLoading(false);
        if (response?.data?.url) {
          navigation.navigate('PaymentScreen', {url: response?.data?.url});
        } else {
          navigation.navigate('My Class');
          showToast('success', response?.message, 'bottom', isRTL);
        }
      })
      .catch(err => {
        setIsPaymentLoading(false);
        if (err?.status == 409) {
          Alert.alert('Alert', err?.data?.message);
        } else {
          showToast('error', err?.data?.message, 'bottom', isRTL);
        }
      });
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusContainer color={colors.themeColor} />
      <AppHeader backIcon={icons.backbtn} isBackBtn title={t('open_session')} />
      <View style={{backgroundColor: colors.themeColor}}>
        <View style={[styles.header]}>
          <Text style={[styles.headigTxt, {textAlign: 'left'}]}>
            {OpenSessionData?.title}
          </Text>
        </View>
        <View
          style={[
            styles.viewS,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Image source={icons.academicIcon} />
          <Text style={styles.txtCategory}>{`${t('category_label')} : ${
            OpenSessionData?.rate_card_type == '1'
              ? t('academic')
              : OpenSessionData?.rate_card_type == '2'
              ? t('recreational')
              : t('courses')
          }`}</Text>
        </View>
        <View
          style={[
            styles.viewS,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Image
            source={icons.calanderYellow}
            style={{tintColor: colors.white}}
          />
          <Text style={styles.txtCategory}>{`${t('session')} : ${t(
            t('open'),
          )}`}</Text>
        </View>
      </View>
      {isLoading ? (
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator size={'large'} />
        </View>
      ) : (
        <ScrollView nestedScrollEnabled style={styles.innerContainer}>
          <View style={styles.card}>
            <View
              style={[
                styles.cardRow,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Image
                source={icons.calanderYellow}
                style={{height: fp(2.6), width: fp(2.6)}}
              />
              <Text style={styles.packageTxt}>
                {`${t('date')} : `}
                <Text style={styles.date}>
                  {moment(OpenSessionData?.start_date).format('DD MMM YYYY')} -{' '}
                  {moment(OpenSessionData?.end_date).format('DD MMM YYYY')}
                </Text>
              </Text>
            </View>
            <View
              style={[
                styles.cardRow,
                {
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  marginTop: hp(1),
                },
              ]}>
              <Image
                source={icons.hatYellow}
                style={{
                  height: fp(2.6),
                  width: fp(2.6),
                  alignSelf: 'flex-start',
                }}
              />
              <Text
                style={[styles.packageTxt, {width: wp(70), lineHeight: hp(2)}]}>
                {'Description : '}
                <Text style={styles.date}>{OpenSessionData?.description}</Text>
              </Text>
            </View>

            {/* <View
              style={[
                styles.cardRow,
                {
                  marginTop: 10,
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  width: wp(75),
                },
              ]}>
              <Image source={icons.language} />
              <Text style={styles.packageTxt}>{t('meetingPoint')} :</Text>
              <Text style={[styles.date, {lineHeight: hp(3), width: wp(40)}]}>
                {' '}
                {OpenSessionData?.address}
              </Text>
            </View> */}
            <View
              style={[
                styles.cardBody,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <View style={[styles.box]}>
                <Text style={styles.package}>{t('numberOfSessions')}</Text>
                <Text
                  style={[
                    styles.details,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {sessionsCount}
                </Text>
              </View>
              <View
                style={[
                  styles.box,
                  {
                    borderLeftWidth: isRTL ? 0 : 1,
                    borderRightWidth: isRTL ? 1 : 0,
                  },
                ]}>
                <Text style={styles.package}>{t('class_type')}</Text>
                <Text
                  style={[
                    styles.details,
                    {textAlign: isRTL ? 'right' : 'left'},
                  ]}>
                  {OpenSessionData?.tlm_class_type?.name}
                </Text>
              </View>
            </View>
          </View>
          {/* Schedule */}

          <View style={{maxHeight: hp(40), height: hp(30), minHeight: hp(10)}}>
            <ScheduleOpenSession
              maxStudents={OpenSessionData?.maxStudents}
              data={OpenSessionData?.tlm_open_session_schedules}
              // handleGetSlots={handleGetSlots}
            />
          </View>
          {/* Payment Details */}
          {/* <PaymentDetails /> */}
          {/* Additional Instructions */}
          {/* {studentBookingDetails?.data?.instructions && (
          <View style={styles.sectionContainer}>
            <Text style={styles.blackTxt}>{t('instructions')}</Text>
            <TextInput
              editable={false}
              placeholder={t('instructions_placeholder')}
              style={styles.instructionsInput}
              placeholderTextColor={colors.searchGray}
              onChangeText={txt => setInstructions(txt)}
              value={studentBookingDetails?.data?.instructions}
            />
          </View>
        )} */}
          <Text
            style={[
              styles.blackTxt,
              {
                left: isRTL ? 0 : 16,
                marginRight: isRTL ? 16 : 0,
                marginTop: hp(2),
              },
            ]}>
            {t('tutorDetails')}
          </Text>
          <View style={styles.card}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <TutorDetails
                imageUri={{
                  uri: OpenSessionData?.tlm_user?.image
                    ? IMAGE_BASE_URL + OpenSessionData?.tlm_user?.image
                    : DUMMY_USER_IMG,
                }}
                name={OpenSessionData?.tlm_user?.name}
                occupation={
                  OpenSessionData?.tlm_user?.tlm_tutor_profile?.expertise
                }
              />
            </View>

            {/* <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                // justifyContent: 'space-between',
              }}>
              <TutorDetails
                imageUri={{
                  uri: OpenSessionData?.tlm_user?.image
                    ? IMAGE_BASE_URL + OpenSessionData?.tlm_user?.image
                    : DUMMY_USER_IMG,
                }}
                name={OpenSessionData?.tlm_user?.name}
                occupation={
                  OpenSessionData?.tlm_user?.tlm_tutor_profile?.expertise
                }
              />
              <TouchableOpacity
                onPress={() =>
                  navigation.navigate('ChatScreen', {
                    item: OpenSessionData?.tlm_user,
                  })
                }
                style={styles.messageBtn}>
                <Image source={icons.messageIcon} />
              </TouchableOpacity>
            </View> */}
          </View>

          {OpenSessionData?.address && (
            <>
              <Text
                style={[
                  styles.blackTxt,
                  {
                    left: isRTL ? 0 : 16,
                    marginRight: isRTL ? 16 : 0,
                    marginTop: hp(2),
                  },
                ]}>
                {t('meetingPoint')}
              </Text>
              <View style={styles.card}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <TutorDetails
                    imageUri={''}
                    name={OpenSessionData?.address}
                    occupation={''}
                    nameStyles={{
                      fontSize: fp(1.8),
                      fontFamily: Fonts.medium,
                      width: wp(70),
                    }}
                    onCardPress={() =>
                      openDirections(
                        OpenSessionData?.latitude,
                        OpenSessionData?.longitude,
                      )
                    }
                    rightContent={
                      <Image
                        source={icons?.redirectArrow}
                        style={{
                          height: fp(4.8),
                          width: fp(4.8),
                          // resizeMode: 'contain',
                        }}
                      />
                    }
                  />
                </View>
              </View>
            </>
          )}

          {/* Payment Method */}
          <View style={styles.paymentSectionContainer}>
            <Text style={styles.sectionTitle}>{t('selectPaymentMethod')}</Text>
            <CustomCheckbox
              label={t('skipCash')}
              isSelected={selectedPaymentMethod === t('skipCash')}
              onSelect={() => setSelectedPaymentMethod(t('skipCash'))}
              isRtl={isRTL}
            />
            {/* <CustomCheckbox
            label={t('applePay')}
            isSelected={selectedPaymentMethod === t('applePay')}
            onSelect={() => setSelectedPaymentMethod(t('applePay'))}
          />
          <CustomCheckbox
            label={t('googlePay')}
            isSelected={selectedPaymentMethod === t('googlePay')}
            onSelect={() => setSelectedPaymentMethod(t('googlePay'))}
          /> */}
            <CustomCheckbox
              label={t('wallet')}
              isSelected={selectedPaymentMethod === t('wallet')}
              onSelect={() => setSelectedPaymentMethod(t('wallet'))}
              isRtl={isRTL}
            />
          </View>

          {/* Total Amount */}
          <View
            style={[
              styles.totalAmountContainer,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Text style={styles.totalAmountText}>{t('totalAmount')}</Text>
            <Text style={styles.totalAmountValue}>
              {openSessionAmount
                ? `${openSessionAmount} ${t('currency')}`
                : '---'}
            </Text>
          </View>
          <View style={{marginBottom: hp(2)}}>
            <PrimaryButton
              title={t('book_now')}
              onPress={handleBookOpenSession}
              loading={isPaymentLoading}
            />
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};
const CustomCheckbox = ({label, isSelected, onSelect, isRtl}) => (
  <TouchableOpacity
    style={[
      styles.checkboxWrapper,
      {flexDirection: isRtl ? 'row-reverse' : 'row'},
    ]}
    onPress={onSelect}>
    <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
      {isSelected && <Text style={styles.checkMark}>✓</Text>}
    </View>
    <Text style={[styles.checkboxLabel, {marginRight: isRtl ? 10 : 0}]}>
      {label}
    </Text>
  </TouchableOpacity>
);
export default ViewOpenSessionDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  headigTxt: {
    color: colors.white,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  txtCategory: {
    left: 8,
    color: colors.white,
    fontFamily: Fonts.medium,
  },
  viewS: {
    alignItems: 'center',
    marginHorizontal: 16,
    marginVertical: 8,
  },
  innerContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  card: {
    borderWidth: 1,
    borderRadius: 8,
    borderColor: colors.txtGrey,
    marginHorizontal: 16,
    marginVertical: 10,
    padding: 10,
    backgroundColor: colors.white,
    elevation: 1,
  },
  cardRow: {
    alignItems: 'center',
  },
  packageTxt: {
    color: colors.txtGrey1,
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    left: 8,
  },
  date: {
    color: colors.darkBlack,
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
  },
  cardBody: {
    marginTop: hp(2),
    // justifyContent: 'space-between',
  },
  package: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    color: '#878787',
  },
  details: {
    color: colors.black,
    fontFamily: Fonts.medium,
    fontSize: fp(1.8),
    marginTop: 8,
  },
  box: {
    width: '48%',
    padding: 10,

    borderColor: colors.txtGrey,
  },
  blackTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  sectionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  paymentSectionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderRadius: 15,
    marginBottom: 15,
  },

  instructionsInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 15,
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    height: fp(6),
    fontFamily: Fonts.regular,
    marginTop: fp(1.5),
  },
  messageBtn: {
    backgroundColor: colors.themeColor,
    borderRadius: 30,
    // padding: 8,
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  containerDetails: {
    flex: 1,
    backgroundColor: colors.white,
  },
  cardGradient: {
    marginHorizontal: 16,
    borderRadius: 12,
    padding: 16,
    marginVertical: 10,
  },
  innerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: wp(85),
  },
  timeTxt: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    left: 10,
  },
  innerTxt: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
    color: colors.darkBlack,
    left: 8,
  },
  row: {
    flexDirection: 'row',
    marginTop: hp(1.5),
    alignItems: 'center',
  },
  mediumTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
  },
  bigTxt: {
    color: colors.darkBlack,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
  },
  meetingView: {
    backgroundColor: colors.lightGreen,
    padding: 8,
    height: fp(4),
    borderRadius: 30,
    marginLeft: 10,
  },
  meetingTxt: {
    fontFamily: Fonts.regular,
    fontSize: fp(1.6),
    color: colors.txtGrey2,
  },
  totalAmountContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  totalAmountText: {
    fontSize: fp(2),
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  totalAmountValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.black,
  },
  checkboxesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  checkboxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    marginRight: 10,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  checkboxSelected: {
    backgroundColor: colors.themeColor,
    borderColor: colors.themeColor,
  },
  checkMark: {
    color: colors.white,
    fontSize: fp(1.8),
  },
  checkboxLabel: {
    fontSize: fp(1.8),
    color: colors.txtGrey1,
    fontFamily: Fonts.medium,
  },

  sectionTitle: {
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
    marginBottom: 10,
  },
});
