export default icon = {
  homeIcon: require('../Assets/Images/homeIcon.png'),
  classIcon: require('../Assets/Images/classIcon.png'),
  walletIcon: require('../Assets/Images/walletIcon.png'),
  walletActive: require('../Assets/Images/walletActive.png'),
  homeIconColor: require('../Assets/Images/homeIconColor.png'),
  classIconColor: require('../Assets/Images/classIconColor.png'),
  taleemLogo: require('../Assets/Images/taleemLogo.png'),
  taleem: require('../Assets/Images/taleem.png'),
  welcomeBackground: require('../Assets/Images/welcomeBackground.png'),
  taleemLogoArbic: require('../Assets/Images/taleemLogoArbic.png'),
  taleemLogoSmall: require('../Assets/Images/taleemLogoSmall.png'),
  taleemLogoSmallEng: require('../Assets/Images/smallTaleemLogo.png'),
  taleemLogoWhite: require('../Assets/Images/taleemLogoWhite.png'),
  backIcon: require('../Assets/Images/back.png'),
  next: require('../Assets/Images/next.png'),
  student: require('../Assets/Images/student.png'),
  parent: require('../Assets/Images/parent.png'),
  tutor: require('../Assets/Images/tutor.png'),
  arrowDown: require('../Assets/Images/arrowDown.png'),
  googleLogo: require('../Assets/Images/googleLogo.png'),
  appleLogo: require('../Assets/Images/appleLogo.png'),
  successIcon: require('../Assets/Images/success.png'),
  hamBurger: require('../Assets/Images/hamBurger.png'),
  location: require('../Assets/Images/location.png'),
  notification: require('../Assets/Images/notification.png'),
  arrowDownWhite: require('../Assets/Images/arrowDownWhite.png'),
  studentImage: require('../Assets/Images/studentImage.png'),
  AcademicOne: require('../Assets/Images/academicOne.png'),
  // recreational: require('../Assets/Images/recreation.png'),
  frame: require('../Assets/Images/frame.png'),
  edit: require('../Assets/Images/edit.png'),
  backbtn: require('../Assets/Images/backwhite.png'),
  check: require('../Assets/Images/check.png'),
  chevronright: require('../Assets/Images/chevronright.png'),
  downchevron: require('../Assets/Images/downchecron.png'),
  checked: require('../Assets/Images/checked.png'),
  unchecked: require('../Assets/Images/unchecked.png'),
  flagAmerica: require('../Assets/Images/flagAmerica.png'),
  flagArabic: require('../Assets/Images/flagArabic.png'),
  flagBritish: require('../Assets/Images/flagBritish.png'),
  flagCanada: require('../Assets/Images/flagCanada.png'),
  locationBlack: require('../Assets/Images/locationBlack.png'),
  downArrowBlack: require('../Assets/Images/downArrow.png'),
  searchIcon: require('../Assets/Images/searchIcon.png'),
  profileImage1: require('../Assets/Images/profileImage.png'),
  rightArrowLarge: require('../Assets/Images/rightArrowLarge.png'),
  star: require('../Assets/Images/star.png'),
  teacherImage1: require('../Assets/Images/teacherImage.png'),
  rightArrowGray: require('../Assets/Images/rightArrowGray.png'),
  editIcon: require('../Assets/Images/editIcon.png'),
  onlineClassImage: require('../Assets/Images/onlineClassImage.png'),
  offlineClassImage: require('../Assets/Images/offlineClassImage.png'),
  calanderImage: require('../Assets/Images/calanderImage.png'),
  starBlack: require('../Assets/Images/starBlack.png'),
  navigationIcon: require('../Assets/Images/navigationIcon.png'),
  upArrow: require('../Assets/Images/upArrow.png'),
  leftArrowWhite: require('../Assets/Images/leftArrowWhite.png'),
  rightArrowWhite: require('../Assets/Images/rightArrowWhite.png'),
  rightArrowGrade: require('../Assets/Images/rightArrowGrade.png'),
  timeSlotClock: require('../Assets/Images/timeSlotClock.png'),
  calanderYellow: require('../Assets/Images/calanderYellow.png'),
  clockYellow: require('../Assets/Images/clockYellow.png'),
  hatYellow: require('../Assets/Images/hatYellow.png'),
  shareIcon: require('../Assets/Images/shareIcon.png'),

  caMusic: require('../Assets/Images/caMusic.png'),
  csArt: require('../Assets/Images/csArt.png'),
  csPhotography: require('../Assets/Images/csPhotography.png'),
  csCooking: require('../Assets/Images/csCooking.png'),
  caDance: require('../Assets/Images/caDance.png'),
  csPainting: require('../Assets/Images/csPainting.png'),
  cdYoga: require('../Assets/Images/cdYoga.png'),
  caLibrary: require('../Assets/Images/caLibrary.png'),

  scDataScience: require('../Assets/Images/caDataScience.png'),
  scUxDesign: require('../Assets/Images/scUxDesign.png'),
  scProductmanagement: require('../Assets/Images/scProductmanagement.png'),
  scBioTechnology: require('../Assets/Images/scBioTechnology.png'),
  scMarketing: require('../Assets/Images/scMarketing.png'),
  scDevelopment: require('../Assets/Images/scDevelopment.png'),
  scBusiness: require('../Assets/Images/scBusiness.png'),
  scFinance: require('../Assets/Images/scFinance.png'),
  calendarIcon: require('../Assets/Images/calendar.png'),
  cross: require('../Assets/Images/cross.png'),
  upload: require('../Assets/Images/upload.png'),
  doneIcon: require('../Assets/Images/doneIcon.png'),

  email: require('../Assets/Images/email.png'),
  phone: require('../Assets/Images/phone.png'),
  hatBlack: require('../Assets/Images/hatBlack.png'),
  homeBlack: require('../Assets/Images/homeBlack.png'),
  tutorImage: require('../Assets/Images/TutorImage.png'),
  //tutor

  booking: require('../Assets/Images/booking1.png'),
  schedule: require('../Assets/Images/fill1.png'),
  earn: require('../Assets/Images/fill2.png'),
  rateCard: require('../Assets/Images/rateCard.png'),

  school: require('../Assets/Images/school.png'),
  timing: require('../Assets/Images/timing.png'),
  person: require('../Assets/Images/person.png'),
  filterIcon: require('../Assets/Images/filterIcon.png'),
  monthCalendar: require('../Assets/Images/monthCalendar.png'),
  studentImg: require('../Assets/Images/studentImg.png'),
  academicIcon: require('../Assets/Images/academicIcon.png'),
  calendarWhite: require('../Assets/Images/calendarWhite.png'),
  languageYellow: require('../Assets/Images/languageYellow.png'),
  messageIcon: require('../Assets/Images/messageIcon.png'),
  pencil: require('../Assets/Images/pencil.png'),
  chatIcon: require('../Assets/Images/chatIcon.png'),
  taleemText: require('../Assets/Images/taleemText.png'),
  // taleemLogoEnglish: require('../Assets/Images/Taleem_logo.png'),
  logondtext: require('../Assets/Images/smallTaleemLogo.png'),
  locationGray: require('../Assets/Images/locationGray.png'),
  mapImageAr: require('../Assets/Images/mapImageAr.png'),
  meetingPointTutor: require('../Assets/Images/meetingPointTutor.png'),
  meetingPointLocation: require('../Assets/Images/meetingPointLocation.png'),
  MyLocation: require('../Assets/Images/MyLocation.png'),
  Recreational: require('../Assets/Images/recreation.png'),
  AllCoursesOne: require('../Assets/Images/AllCoursesOne.png'),

  plusIcon: require('../Assets/Images/addIcon.png'),

  appIconWhite: require('../Assets/Images/appIconWhite.png'),
  selected: require('../Assets/Images/selectedMark.png'),
  maleIcon: require('../Assets/Images/maleIcon.png'),
  femaleIcon: require('../Assets/Images/femaleIcon.png'),

  addImgEditProfile: require('../Assets/Images/addImg.png'),
  dummyTeacher: require('../Assets/Images/DummyTeacher.png'),
  language: require('../Assets/Images/language.png'),
  send: require('../Assets/Images/Send.png'),
  locationArrow: require('../Assets/Images/locationArrow.png'),
  englishFlag: require('../Assets/Images/englishFlag.png'),
  arabicFlag: require('../Assets/Images/arabicFlag.png'),
  logo: {
    welcomeLogoEng: require('../Assets/Images/welcomeLogoEng.png'),
    whiteLogoCap: require('../Assets/Images/whiteLogoCap.png'),
    whiteLogoTextEng: require('../Assets/Images/whiteLogoTextEng.png'),
    whiteLogoTextArabic: require('../Assets/Images/whiteLogoTextArabic.png'),
    welcomeLogoArabic: require('../Assets/Images/welcomeLogoArabic.png'),
    splashLogo: require('../Assets/Images/splashLogo.png'),
    generalLogoEng: require('../Assets/Images/generalLogoEng.png'),
    generalLogoArabic: require('../Assets/Images/generalLogoArabic.png'),
    logoAtSelectLang: require('../Assets/Images/logoAtSelectLang.png'),
  },

  video: {
    splashVideo: require('../Assets/Images/splashVideo.mp4'),
  },
  walletIcon1: require('../Assets/Images/walletIcon1.png'),
  refresh: require('../Assets/Images/refresh.png'),
  bookmark: require('../Assets/Images/bookmark.png'),
  publish: require('../Assets/Images/publish.png'),

  successTick: require('../Assets/Images/successTick.png'),
  contactUsGirlImage: require('../Assets/Images/contactUsGirlImage.png'),
  chatIcons: require('../Assets/Images/chatIcons.png'),
  threeDots: require('../Assets/Images/threeDots.png'),
  fingerPointedHand: require('../Assets/Images/fingerPointedHand.png'),
  redirectArrow: require('../Assets/Images/RedirectArrow.png'),
  addCircle: require('../Assets/Images/AddCircle.png'),
  dustbin: require('../Assets/Images/dustbin.png'),
  invoiceDownload: require('../Assets/Images/invoiceDownload.png'),
  infoIcon: require('../Assets/Images/infoIcon.png'),
  BlockUser: require('../Assets/Images/BlockUser.png'),
};
