import axios from 'axios';

const agoraAPI = 'https://api.netless.link/v5';
// const agoraSDKToken = 'NETLESSSDK_XXXX';
const agoraSDKToken = `NETLESSSDK_YWs9NHY2TEpRN0JtZUtNYjNEUyZub25jZT0xZTYxMzkyMC1iZGY1LTExZWYtOGYzYi0yOTkyYzE3NjlkOTgmcm9sZT0wJnNpZz1mYmRjZDM5YmE5ZjdlYjY2OWY1YjFiYTk0MDYzNzNjOWVhYzk1Njg5ZTE4YzIyNTM4ZGIwZDIzYmZmMzA2NmZl`;

export const appIdentifier = '9N6tAKb_Ee-KHmOYlrNxrA/9Txus_CBzlJcYg';

const api = axios.create({
  baseURL: agoraAPI,
  headers: {
    token: agoraSDKToken,
    region: 'in-mum',
  },
});

export interface RoomDetailsI {
  roomUUID: string;
  teamUUID: string;
  appUUID: string;
  roomToken: string;
}

export async function getAgoraRoomDetails(): Promise<RoomDetailsI> {
  const response = await api.post(`/rooms`, {});

  const data = response.data;
  console.log(data, 'roomCreateion');
  const roomInfo = await api.post(`/tokens/rooms/${data.uuid}/`, {
    lifespan: 3600000,
    role: 'admin',
  });

  return {
    roomUUID: data.uuid,
    teamUUID: data.teamUUID,
    appUUID: data.appUUID,
    roomToken: roomInfo.data,
  };
}
