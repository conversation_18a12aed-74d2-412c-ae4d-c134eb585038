import {Dimensions, Platform, PixelRatio} from 'react-native';
import {IMAGE_BASE_URL} from './getBaseUrl';
import DeviceInfo from 'react-native-device-info';
import {t} from 'i18next';

export const SCREEN_HEIGHT = Dimensions.get('screen').height;
export const SCREEN_WIDTH = Dimensions.get('screen').width;
export const responsiveFontSize = fontSize =>
  fontSize * PixelRatio.getFontScale();

export const IS_IOS = Platform.OS == 'ios';
export const IS_ANDROID = Platform.OS == 'android';

export const SESSION_TYPES_ARRAY = [
  'All',
  'Online',
  'Face to Face',
  // 'Open Session',
];
export const CLASS_TYPES_ARRAY = [
  'Academic',
  'Recreational',
  'Courses',
  'Open Session',
];
export const RECURRENCE_TYPE_DATA = [
  {name: 'Weekly', value: 0},
  {name: 'Monthly', value: 1},
  {name: 'Daily', value: ['0', '1', '2', '3', '4', '5', '6']},
  {name: 'Weekend', value: ['6', '0']},
  {name: 'Week Days', value: ['1', '2', '3', '4', '5']},
  {name: 'Custom', value: []},
];
export const DUMMY_USER_IMG =
  'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png';
// export const PAYMENT_GATEWAY_RETURN_URL =
//   'https://techugoapps.com/taleem/taleem_dev/paymentRes';
export const PAYMENT_GATEWAY_RETURN_URL = 'https://taleem.qa/web/#/paymentRes';
// export const PAYMENT_GATEWAY_RETURN_URL =
//   'https://taleem.qa/taleem/taleem_dev/paymentRes';

export const DUMMY_COURSE_IMG = `${IMAGE_BASE_URL}document/1734024672405-download.jpeg`;
// export const DUMMY_COURSE_IMG =
//   'https://taleem-info.s3.ap-south-1.amazonaws.com/document/1734024672405-download.jpeg';
export const DUMMY_COURSE_IMG2 =
  'https://cdn.pixabay.com/photo/2024/06/08/20/39/robot-8817528_1280.jpg';

export const GOOGLE_PLACE_API_KEY = 'AIzaSyD57FjdNKODFtX95VuIjFYGl5aq92IVFyo'; //personal and working api key
// export const GOOGLE_PLACE_API_KEY = 'AIzaSyAOm0s0UNMwYB58RvfRJqJMYC0xYtGKjPg';
export const CLASS_TYPES = [
  {name: 'Online', value: 'online', label: t('online')},
  {name: 'Face to Face', value: 'faceToFace', label: t('face_to_face')},
];
export const SESSION_TYPES = [
  {name: 'Individual', value: 'individual', label: t('individual')},
  {name: 'Group', value: 'group', label: t('group')},
];
export const staticPaymentDetails = {
  id: 140,
  user_id: 324,
  transaction_id: 'ID1737019479908',
  skipcash_payment_id: 'f6b0dfc9-5be3-476e-9bb1-18db9ad1043b',
  amount: '600.00',
  transaction_type: 'debit',
  status: 'success',
  user_type: 'student',
  payment_type: 'tutorPayout',
  original_transaction_id: null,
  booking_id: null,
  open_session_id: 4,
  currency: 'QAR',
  createdAt: '2025-01-16T09:24:39.000Z',
  updatedAt: '2025-01-16T09:40:03.000Z',
};
export const dayNames = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
];

export const studentFiltersRatingsMap = {
  1: {from: 4, to: 5},
  2: {from: 3, to: 4},
  3: {from: 1, to: 3},
};

export const getReverseGeoCodeGoogleApi = (latitude, longitude) => {
  return `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${GOOGLE_PLACE_API_KEY}`;
};
export const CLASS_TYPES_DATA = [
  {name: t('online'), value: 1},
  {name: t('face_to_face'), value: 2},
];
export const getClassTypesData = t => [
  {name: t('online'), value: 1},
  {name: t('face_to_face'), value: 2},
];
export const APP_VERSION = DeviceInfo.getVersion();

export const generateStyledHtmlContent = (
  staticContentData,
  translationFunc,
) => {
  return `
      <html>
        <head>
          <style>
            body {
              color: #333;
              font-family: 'metropolis-medium' ;
              justify-content: ${
                !staticContentData?.data?.content ? 'center' : 'left'
              }
            }
            p { color: #555; }
            h1, h2, h3, h4, h5, h6 { color: #222; }
            strong { color: #000; }
            ul { color: #666; }
          </style>
        </head>
        <body style="${
          !staticContentData?.data?.content
            ? 'display: flex; align-items: center; justify-content: center; height: 100vh; text-align: center;'
            : 'text-align: left;'
        }">
          ${staticContentData?.data?.content || translationFunc('noData')}
        </body>
      </html>
    `;
};
export const supportEmail = '<EMAIL>';
export const supportPhone = '+974 5997 9444';
