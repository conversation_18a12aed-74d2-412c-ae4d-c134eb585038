export const tutorBooking<PERSON>son = {
  class_type_id: 0,
  class_type: '',
  sessions_id: 0,
  session: '',
  tutor_user_id: 0, //tlm_user?.id
  package_id: null,
  package_start_date: '',
  instructions: '', //take direct from instructions state
  class_title: '', //grade and class
  token: '', //rate card data token
  rate_card_id: 0,
  rate_card_type: '', //1 for academic,2 for r6+ecreational,3 for courses
  participants: [], //array of selected user ids
  meeting_point_preferred: '', //tutor or student
  address: '', //full selected address
  zone: '',
  building: '',
  latitude: null, //number
  longitude: null, //number
  country: '',
  amount: 0, //calculated amount
  payment_type: 'split',
  booking_schedules: [
    {
      tutor_schedule_id: 0,
      date: '',
    },
  ],
  save_address: false,
  meeting_venue_id: '',
  tutor_earned_amount: 0,
  payment_breakdown: {
    quantity: '1',
    slot_amount: '100',
    total: '100',
    discount_percentage: '10',
    discounted_amount: '20',
    grand_total: '100',
    tax_percentage: '0',
    tax_amount: '0',
  },
};

export const resetTutorBooking<PERSON>son = () => {
  return {
    class_type_id: 0,
    class_type: '',
    sessions_id: 0,
    session: '',
    tutor_user_id: 0, //tlm_user?.id
    package_id: null,
    package_start_date: '',
    instructions: '',
    class_title: '',
    token: '',
    rate_card_id: 0,
    rate_card_type: '',
    participants: [],
    meeting_point_preferred: '',
    address: '',
    zone: '',
    building: '',
    latitude: null,
    longitude: null,
    country: '',
    amount: 0,
    payment_type: 'split',
    booking_schedules: [
      {
        tutor_schedule_id: 0,
        date: '',
      },
    ],
    save_address: false,
    meeting_venue_id: '',
    tutor_earned_amount: 0,
  };
};
