import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react';
import {useSelector} from 'react-redux';
import {clearAuthData, setIsLoggedIn} from '../Features/authSlice';
import {updateSplashVisible} from '../Helper/SplashHelper/SplashHelper';
import AsyncStorage from '@react-native-async-storage/async-storage';

// export const baseUrl = 'https://techugoapps.com:3454';
export const baseUrl = 'https://taleem.qa:3454'; //Prod. Url
const baseQuery = fetchBaseQuery({
  baseUrl: `${baseUrl}/user/api/v1/`,
  prepareHeaders: (headers, {getState}) => {
    const token = getState().auth.token;
    const lang = getState()?.auth?.appLocale;
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }
    if (lang) {
      headers.set('lang', lang);
    }
    return headers;
  },
});

const baseQueryWithInterceptor = async (args, api, extraOptions) => {
  const result = await baseQuery(args, api, extraOptions);

  // If we get a 401 unauthorized response, log the user out
  if (result && result?.error?.status == 401) {
    console.log('getting 401 in apiSlice');
    // await updateSplashVisible(null, false, true); // Mark as logout to hide splash screen
    // await AsyncStorage.removeItem('user'); // Clear user data
    // api.dispatch(clearAuthData()); // Clear auth data in Redux store
    // api.dispatch(setIsLoggedIn(false)); // Set login state to false
    // You might also want to navigate to login screen here if using React Navigation
  }

  return result;
};

const apiSlice = createApi({
  reducerPath: 'api',
  // baseQuery: baseQueryWithInterceptor,
  baseQuery: fetchBaseQuery({
    baseUrl: `${baseUrl}/user/api/v1/`,
    prepareHeaders: (headers, {getState}) => {
      const token = getState().auth.token;
      const lang = getState()?.auth?.appLocale;
      console.log('🚀 ~ token:', token);

      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      if (lang) {
        headers.set('lang', lang);
      }
      return headers;
    },
  }),
  endpoints: builder => ({
    signUp: builder.mutation({
      query: newUser => ({
        url: 'sign-up',
        method: 'POST',
        body: newUser,
      }),
    }),

    verifyOtp: builder.mutation({
      query: ({otp, field_value, token, action, country_code}) => ({
        url: 'verify-otp',
        method: 'POST',
        body: {
          otp,
          type: 'mobile_no',
          field_value,
          action,
          token,
          country_code,
        },
      }),
    }),
    switchProfile: builder.mutation({
      query: userRole => ({
        url: '/switch-profile',
        method: 'PUT',
        body: {user_role: userRole},
      }),
    }),
    switchConnectedProfile: builder.mutation({
      query: id => ({
        url: '/switch-connected-profiles',
        method: 'PUT',
        body: {id: id},
      }),
    }),

    sendOtp: builder.mutation({
      query: ({mobileNumber, country_code, user_type, action = 'login'}) => ({
        url: 'send-otp',
        method: 'POST',
        body: {
          type: 'mobile_no',
          field_value: mobileNumber,
          action: action,
          country_code,
          user_type: user_type,
        },
      }),
    }),

    profileDetails: builder.query({
      query: () => ({
        url: 'profile-details',
        method: 'GET',
      }),
    }),

    getQualifications: builder.query({
      query: () => ({
        url: 'qualifications',
        method: 'GET',
      }),
    }),

    getStudentDetail: builder.query({
      query: () => ({
        url: 'student/student-detail',
        method: 'GET',
      }),
    }),

    getGrades: builder.query({
      query: () => ({
        url: 'grades',
        method: 'GET',
      }),
    }),

    chooseYourGrade: builder.query({
      query: () => ({
        url: 'grade-class',
        method: 'GET',
      }),
    }),

    getExpertise: builder.query({
      query: () => ({
        url: 'expertise',
        method: 'GET',
      }),
    }),

    chooseYourCurriculums: builder.query({
      query: params => ({
        url: 'curriculums',
        method: 'GET',
        params: params,
      }),
    }),

    updateTutorProfile: builder.mutation({
      query: profileData => ({
        url: 'tutor/update-tutor-profile',
        method: 'PUT',
        body: profileData,
      }),
      invalidatesTags: ['TutorProfile'],
    }),

    studentAcademic: builder.mutation({
      query: ({grades_id, curriculum_id, class_id}) => ({
        url: 'student/student-acadmic',
        method: 'PUT',
        body: {
          grades_id,
          curriculum_id,
          class_id,
        },
      }),
    }),

    getTutorsDetailAcademic: builder.query({
      query: ({id, bookingFlowType}) => ({
        url: `student/tutor-detail-${bookingFlowType}?id=${id}`,
        method: 'GET',
      }),
    }),

    getTutorsDetailCourses: builder.query({
      query: id => ({
        url: `student/tutor-detail-courses?id=${id}`,
        method: 'GET',
      }),
    }),
    getTutorsDetailRecreational: builder.query({
      query: id => ({
        url: `student/tutor-detail-recreational?id=${id}`,
        method: 'GET',
      }),
    }),

    updateStudentProfile: builder.mutation({
      query: ({
        name,
        mobile_no,
        email,
        address,
        user_type,
        student_type,
        grades_id,
        curriculum_id,
        image,
        class_id,
      }) => {
        // Log the input parameters
        console.log('updateStudentProfile - Input Parameters:', {
          name,
          mobile_no,
          email,
          address,
          user_type,
          student_type,
          grades_id,
          curriculum_id,
          image,
          class_id,
        });

        // Prepare the request object
        const request = {
          url: 'student/student-profile',
          method: 'PUT',
          body: {
            name,
            mobile_no,
            email,
            student_type,
            address,
            user_type,
            grades_id,
            curriculum_id,
            image,
            class_id,
          },
        };

        // Log the full request object
        console.log('updateStudentProfile - Request:', request);

        return request;
      },
    }),

    uploadDocument: builder.mutation({
      query: documentData => ({
        url: 'upload-file/document',
        method: 'POST',
        body: documentData,
      }),
    }),

    uploadTutorDocuments: builder.mutation({
      query: ({
        id_photo,
        academic_document,
        address_proof,
        other_document,
      }) => ({
        url: 'tutor/documents',
        method: 'PUT',
        body: {
          id_photo,
          academic_document,
          address_proof,
          other_document,
        },
      }),
    }),
    getTutorDocuments: builder.query({
      query: () => ({
        url: 'tutor/documents',
        method: 'GET',
      }),
    }),

    subCategory: builder.query({
      query: () => ({
        url: 'expertise',
        method: 'GET',
      }),
    }),

    courseSubCategories: builder.query({
      query: type => ({
        url: `course-categories?type=${type}`,
        method: 'GET',
      }),
    }),

    // getTutorsAcademic: builder.query({
    //   query: () => ({
    //     url: 'student/tutors-academic',
    //     method: 'GET',
    //   }),
    // }),
    getTutorsAcademic: builder.query({
      query: (params = {}) => {
        const {
          expertise_id,
          gender,
          grades_id,
          curriculum_id,
          ratingsFrom,
          ratingsTo,
          priceFrom,
          priceTo,
          language,
          subject_id,
          distance,
          name,
          ratingsFilter,
          class_type_id,
        } = params;
        const filteredParams = Object.fromEntries(
          Object.entries({
            expertise_id,
            gender,
            grades_id,
            curriculum_id,
            ratingsFrom,
            ratingsTo,
            priceFrom,
            priceTo,
            language,
            subject_id,
            distance,
            name,
            ratingsFilter,
            class_type_id,
          }).filter(([_, value]) => value !== undefined && value !== null),
        );
        console.log('🚀 ~ filteredParams:', filteredParams);

        return {
          url: 'student/tutors-academic',
          method: 'GET',
          params: filteredParams,
        };
      },
    }),

    getTutorsRecreational: builder.query({
      query: (params = {}) => {
        const {
          expertise_id,
          gender,
          grades_id,
          curriculum_id,
          ratingsFrom,
          ratingsTo,
          priceFrom,
          priceTo,
          language,
          name,
          class_type_id,
          ratingsFilter,
        } = params;
        const filteredParams = Object.fromEntries(
          Object.entries({
            expertise_id,
            gender,
            grades_id,
            curriculum_id,
            ratingsFrom,
            ratingsTo,
            priceFrom,
            priceTo,
            language,
            name,
            ratingsFilter,
            ratingsFilter,
          }).filter(([_, value]) => value !== undefined && value !== null),
        );
        console.log('904yu53i8920,filteredParams', filteredParams);
        return {
          url: 'student/tutors-recreational',
          method: 'GET',
          params: filteredParams,
        };
      },
    }),

    getTutorsAllCourses: builder.query({
      query: (params = {}) => {
        const {
          course_category_id,
          gender,
          grades_id,
          curriculum_id,
          ratingsFrom,
          ratingsTo,
          priceFrom,
          priceTo,
          course_title,
        } = params;
        const filteredParams = Object.fromEntries(
          Object.entries({
            course_category_id,
            gender,
            grades_id,
            curriculum_id,
            ratingsFrom,
            ratingsTo,
            priceFrom,
            priceTo,
            course_title,
          }).filter(([_, value]) => value !== undefined && value !== null),
        );

        return {
          url: 'student/tutors-all-courses',
          method: 'GET',
          params: filteredParams,
        };
      },
    }),

    getTutorProfileStatus: builder.query({
      query: () => ({
        url: 'tutor/profile-status',
        method: 'GET',
      }),
    }),

    getStudentProfileForTutor: builder.query({
      query: id => ({
        url: `tutor/student-profile-details?id=${id}`,
        method: 'GET',
      }),
    }),

    logout: builder.mutation({
      query: () => ({
        url: 'logout',
        method: 'DELETE',
      }),
    }),

    uploadFileProfile: builder.mutation({
      query: file => {
        const formData = new FormData();
        formData.append('file', {
          uri: file.uri,
          type: file.type || 'image/jpeg',
          name: file.fileName || 'profile.jpg',
        });

        return {
          url: 'upload-file/profile',
          method: 'POST',
          body: formData,
          headers: {
            'Content-Type': 'multipart/form-data', // Allow the boundary to be auto-generated
          },
        };
      },
    }),

    getAcademicRateCard: builder.query({
      query: () => ({
        url: `tutor/academic-rate-card`,
        method: 'GET',
      }),
    }),
    // GET Academic Rate Card Edit
    getAcademicRateCardEditData: builder.query({
      query: id => `tutor/academic-rate-card/${id}`,
    }),

    // PUT Academic Rate Card Edit
    editAcademicRateCard: builder.mutation({
      query: ({id, data}) => ({
        url: `tutor/academic-rate-card/${id}`,
        method: 'PUT',
        body: data,
      }),
    }),
    //add the delete here
    deleteAcademicRateCard: builder.mutation({
      query: id => ({
        url: `tutor/academic-rate-card/${id}`,
        method: 'DELETE',
      }),
    }),

    addAcademicRateCard: builder.mutation({
      query: rateCardData => ({
        url: 'tutor/academic-rate-card',
        method: 'POST',
        body: rateCardData,
      }),
    }),
    getRecreationalRateCard: builder.query({
      query: () => ({
        url: 'tutor/recreational-rate-card',
        method: 'GET',
      }),
    }),
    addRecreationalRateCard: builder.mutation({
      query: rateCardData => ({
        url: 'tutor/recreational-rate-card',

        method: 'POST',

        body: rateCardData,
      }),
    }),
    getRecreationalRateCardEditData: builder.query({
      query: id => `tutor/recreational-rate-card/${id}`,
    }),
    // PUT recreational Rate Card Edit
    editRecreationalRateCard: builder.mutation({
      query: ({id, data}) => ({
        url: `tutor/recreational-rate-card/${id}`,
        method: 'PUT',
        body: data,
      }),
    }),
    // delete here
    deleteRecreationalRateCard: builder.mutation({
      query: id => ({
        url: `tutor/recreational-rate-card/${id}`,
        method: 'DELETE',
      }),
    }),
    getCourseRateCard: builder.query({
      query: () => ({
        url: 'tutor/course-rate-card',
        method: 'GET',
      }),
    }),
    addCourseRateCard: builder.mutation({
      query: rateCardData => ({
        url: 'tutor/course-rate-card',

        method: 'POST',

        body: rateCardData,
      }),
    }),
    getCourseRateCardEditData: builder.query({
      query: id => `tutor/course-rate-card/${id}`,
    }),
    // PUT recreational Rate Card Edit
    // editCourseRateCard: builder.mutation({
    //   query: ({id, data}) => ({
    //     url: `tutor/course-rate-card/${id}`,
    //     method: 'PUT',
    //     body: data,
    //   }),
    // }),
    editCourseRateCard: builder.mutation({
      query: ({id, data}) => {
        // Log the body (data) being sent in the request
        console.log('Request Body:', data);

        return {
          url: `tutor/course-rate-card/${id}`,
          method: 'PUT',
          body: data,
        };
      },
    }),
    //add the delete here
    deleteCourseRateCard: builder.mutation({
      query: id => ({
        url: `tutor/course-rate-card/${id}`,
        method: 'DELETE',
      }),
    }),
    getCourseLevels: builder.query({
      query: () => ({
        url: 'course-levels',
        method: 'GET',
      }),
    }),
    getSubjectsTutor: builder.query({
      query: () => ({
        url: 'subjects',
        method: 'GET',
      }),
    }),
    getSessionsTutor: builder.query({
      query: () => ({
        url: 'sessions',
        method: 'GET',
      }),
    }),
    getClassAndSchedules: builder.query({
      query: () => ({
        url: 'class-and-schedules',
        method: 'GET',
      }),
    }),

    getPackgeTypes: builder.query({
      query: () => ({
        url: 'package-types',
        method: 'GET',
      }),
    }),

    address: builder.mutation({
      query: ({
        address,
        latitude,
        longitude,
        country,
        zone,
        street,
        building,
      }) => ({
        url: 'save-address',
        method: 'PUT',
        body: {
          address,
          latitude,
          longitude,
          country,
          zone,
          street,
          building,
        },
      }),
    }),

    sendOtpAuth: builder.mutation({
      query: ({type, field_value, action}) => ({
        url: 'send-otp-auth',
        method: 'POST',
        body: {
          type,
          field_value,
          action,
        },
      }),
    }),

    verifyOtpAuth: builder.mutation({
      query: ({otp, type, field_value, action, token, country_code}) => ({
        url: 'verify-otp-auth',
        method: 'POST',
        body: {
          otp,
          type,
          field_value,
          action,
          token,
          country_code,
        },
      }),
    }),

    getTutorProfile: builder.query({
      query: () => ({url: 'tutor/tutor-profile', method: 'GET'}),
      providesTags: ['TutorProfile'],
    }),

    socialSignin: builder.mutation({
      query: body => ({
        url: 'social-signin',
        method: 'POST',
        body: body,
      }),
    }),

    //GET TUTOR SCHEDULE -- STUDENT SIDE
    getTutorSchedulesForStudent: builder.query({
      query: ({id, date, class_type_id, currentTime}) => ({
        url: `student/tutor-schedules`,
        method: 'GET',
        params: {
          id,
          date,
          class_type_id,
          currentTime,
        },
      }),
    }),

    getTutorSchedulesForStudent: builder.query({
      query: ({id, date, class_type_id, currentTime}) => {
        // Log the request parameters
        console.log('Request parameters: getTutorSchedulesForStudent', {
          id,
          date,
          class_type_id,
          currentTime,
        });

        return {
          url: `student/tutor-schedules`,
          method: 'GET',
          params: {
            id,
            date,
            class_type_id,
            currentTime,
          },
        };
      },
    }),

    //TUTOR SCHEDULE

    getTutorSchedules: builder.query({
      query: ({date, class_type_id, isOpenSession}) => ({
        url: `tutor/schedules`,
        method: 'GET',
        params: {
          date,
          class_type_id,
          isOpenSession,
        },
      }),
    }),
    getTutorSchedulesForOpenSession: builder.query({
      query: ({date, class_type_id}) => ({
        url: `tutor/schedules-for-open-session`,
        method: 'GET',
        params: {
          date,
          class_type_id,
        },
      }),
    }),

    addTutorScheduleApi: builder.mutation({
      query: body => ({
        url: 'tutor/schedules',
        method: 'POST',
        body: body,
      }),
    }),

    updateTutorSchedule: builder.mutation({
      query: (payload, scheduleId) => ({
        url: 'tutor/update-tutor-profile',
        method: 'PUT',
        body: payload,
        params: scheduleId,
      }),
    }),

    deleteTutorSchedule: builder.mutation({
      query: body => ({
        url: `tutor/delete-schedules`,
        method: 'PUT',
        body: body,
      }),
    }),

    gettMyExpertise: builder.query({
      query: () => ({
        url: 'expertise',
        method: 'GET',
      }),
    }),

    getStudentList: builder.query({
      query: search => ({
        url: `student/student-list`,
        method: 'GET',
        params: search,
      }),
    }),

    getMyBookingsTutor: builder.query({
      query: ({date, rate_card_type}) => ({
        url: `tutor/my-bookings`,
        method: 'GET',
        params: {
          date: date,
          rate_card_type: rate_card_type,
        },
      }),
    }),

    getTutorRateCardDetailsStudentAcademic: builder.query({
      query: ({rate_card_id, bookingFlowType}) => ({
        url: `student/tutor-card-details/${bookingFlowType}`,
        method: 'GET',
        params: {id: rate_card_id},
      }),
    }),

    getTutorRateCardDetailsStudentRecreational: builder.query({
      query: rate_card_id => ({
        url: `student/tutor-card-details/recreational`,
        method: 'GET',
        params: {id: rate_card_id},
      }),
    }),

    // getTutorRateCardDetailsStudentCourses: builder.query({
    //   query: rate_card_id => ({
    //     url: `student/tutor-card-details/recreational`,
    //     method: 'GET',
    //     params: rate_card_id,
    //   }),
    // }),

    getBookingPriceAcademic: builder.query({
      query: ({
        rate_card_id,
        class_type_id,
        tutor_package_id,
        groupLength,
        slotHours,
      }) => ({
        url: `student/booking-price?rate_card_id=${rate_card_id}&rate_card_type=academic&tutor_class_type_id=${class_type_id}&tutor_package_id=${tutor_package_id}&no_of_members=${groupLength}&no_of_hours=${slotHours}`,
        method: 'GET',
      }),
    }),

    getTutorMeetingPointAddress: builder.query({
      query: ({type, tutor_user_id, lat, long}) => ({
        url: `student/get-meeting-points`,
        method: 'GET',
        params: {
          type,
          tutor_user_id,
          lat,
          long,
        },
      }),
    }),
    getTutorBookingDetails: builder.query({
      query: id => ({
        url: `tutor/booking-details?id=${id}`,
        method: 'GET',
      }),
    }),
    getTutorBookingListHome: builder.query({
      query: params => ({
        url: `tutor/booking-list-home`,
        method: 'GET',
        params: params,
      }),
    }),
    gettMyExpertise: builder.query({
      query: () => ({
        url: 'expertise',
        method: 'GET',
      }),
    }),

    //Tutor meeting preferences

    getMeetingPreference: builder.query({
      query: () => ({
        url: `tutor/meeting-preference`,
        method: 'GET',
      }),
    }),
    //update
    editMeetingPreference: builder.mutation({
      query: data => ({
        url: `tutor/meeting-preference`,
        method: 'PUT',
        body: data,
      }),
      // invalidatesTags: ['MeetingPreference']
    }),
    //meeting points
    getMeetingPoints: builder.query({
      query: () => ({
        url: `tutor/meeting-points`,
        method: 'GET',
      }),
      // providesTags: ['MeetingPreference']
    }),
    //meeting points details

    getMeetingPointsById: builder.query({
      query: id => ({
        url: `tutor/meeting-points/${id}`,
        method: 'GET',
      }),
    }),
    //post meeting points
    addMeetingPoint: builder.mutation({
      query: meetingPointData => ({
        url: 'tutor/meeting-points',
        method: 'POST',
        body: meetingPointData,
      }),
    }),
    //update meeting points
    editMeetingPoint: builder.mutation({
      query: ({id, payload}) => ({
        url: `tutor/meeting-points/${id}`,
        method: 'PUT',
        body: payload,
      }),
    }),
    // delete meeting points
    deleteMeetingPoint: builder.mutation({
      query: id => ({
        url: `tutor/meeting-points/${id}`,
        method: 'DELETE',
      }),
    }),

    verifyOtpAuth: builder.mutation({
      query: ({otp, type, field_value, action, token, country_code}) => ({
        url: 'verify-otp-auth',
        method: 'POST',
        body: {
          otp,
          type,
          field_value,
          action,
          token,
          country_code,
        },
      }),
    }),

    getClassTypes: builder.query({
      query: () => ({
        url: `class-types`,
        method: 'GET',
      }),
      // providesTags: ['MeetingPreference']
    }),

    bookTutor: builder.mutation({
      query: bookingJson => ({
        url: `student/confirm-booking`,
        method: 'POST',
        body: bookingJson,
      }),
    }),
    postContacts: builder.mutation({
      query: contacts => {
        return {
          url: `student/contacts/members`,
          method: 'POST',
          body: contacts,
        };
      },
    }),
    postBookingInvite: builder.mutation({
      query: contacts => ({
        url: `student/booking-invite`,
        method: 'POST',
        body: contacts,
      }),
    }),
    postStudentPayment: builder.mutation({
      query: body => ({
        url: `/student/create-booking-payment`,
        method: 'POST',
        body: body,
      }),
    }),
    getStudentBookingList: builder.query({
      query: ({id, date}) => ({
        url: `student/booking-list?class_type_id=${id}&date=${date}`,
        method: 'GET',
      }),
    }),
    getBookingDetails: builder.query({
      query: id => ({
        url: `student/booking-details?id=${id}`,
        method: 'GET',
      }),
    }),
    updateDeviceDetails: builder.mutation({
      query: ({data}) => {
        console.log('updateDeviceDetails - Request Data:', data); // Log the data being sent
        return {
          url: `update-device-details`,
          method: 'PUT',
          body: data,
        };
      },
    }),

    getEarnings: builder.query({
      query: () => ({
        url: 'tutor/total-earnings',
        method: 'GET',
      }),
    }),
    getTotalErningsHistory: builder.query({
      query: ({date, page, limit}) => ({
        url: 'tutor/earning-history',
        method: 'GET',
        params: {
          date: date,
          page: page,
          limit: limit,
        },
      }),
    }),
    requesPayoutTutor: builder.mutation({
      query: withdrawlAmount => ({
        url: 'tutor/request-payout',
        method: 'POST',
        body: withdrawlAmount,
      }),
    }),
    bookOpenSession: builder.mutation({
      query: data => ({
        url: 'tutor/book-open-session',
        method: 'POST',
        body: data,
      }),
    }),
    getRateAllCards: builder.query({
      query: params => ({
        url: 'tutor/rate-cards-list',
        method: 'GET',
        params: params,
      }),
    }),
    getSudendtBookingDetails: builder.query({
      query: id => ({
        url: `student/booking-details?id=${id}`,
        method: 'GET',
      }),
    }),
    // Agora Apis
    generateAgoraToken: builder.mutation({
      query: agoraConfig => ({
        url: 'generate-agora-token',
        method: 'POST',
        body: agoraConfig,
      }),
    }),
    getTutorBookingList: builder.query({
      query: ({id, date}) => ({
        url: `tutor/my-bookings?date=${date}&rate_card_type=${id}`,
        method: 'GET',
      }),
    }),
    getTutorClassDetails: builder.query({
      query: id => ({
        url: `tutor/booking-details?id=${id}`,
        method: 'GET',
      }),
    }),
    // Open Session
    getOpenSessionList: builder.query({
      query: () => ({
        url: `student/all-open-sessions-list?page=0&limit=1000`,
        method: 'GET',
      }),
    }),
    getOpenSessionDetailsById: builder.query({
      query: id => ({
        url: `student/open-session-details?id=${id}`,
        method: 'GET',
      }),
    }),
    getSudendtBookingDetailsById: builder.query({
      query: ({id, user_type}) => ({
        url: `${user_type}/booking-schedule-details?id=${id}`,
        method: 'GET',
      }),
    }),
    getOpenSessionScheduleDetailsById: builder.query({
      query: ({id, user_type}) => ({
        url: `${user_type}/open-session-schedule-details?id=${id}`,
        method: 'GET',
      }),
      onQueryStarted({id, user_type}) {
        console.log(
          `Fetching open session schedule details for ID: ${id}, User Type: ${user_type}`,
        );
      },
    }),
    getTutorBookingDetailsById: builder.query({
      query: id => ({
        url: `tutor/upcoming-schedule-details?id=${id}`,
        method: 'GET',
      }),
    }),
    openSessionPayment: builder.mutation({
      query: body => ({
        url: `student/create-open-session-payment`,
        method: 'POST',
        body: body,
      }),
    }),
    getBookedOpenSessions: builder.query({
      query: params => ({
        url: `student/enrolled-open-session-list?page=0&limit=100`,
        method: 'GET',
        params: params,
      }),
    }),
    getBookedOpenSessionsDetails: builder.query({
      query: id => {
        // console.log('getBookedOpenSessionsDetails params:', {user_type, id});
        return {
          url: `student/enrolled-open-session-details?id=${id}`,
          method: 'GET',
        };
      },
    }),
    getBookedOpenSessionsDetailsTutor: builder.query({
      query: id => {
        // console.log('getBookedOpenSessionsDetails params:', {user_type, id});
        return {
          url: `tutor/open-session-schedule-details?id=${id}`,
          method: 'GET',
        };
      },
    }),

    getOpneSessions: builder.query({
      query: () => ({
        url: `sessions?class_type_id=3`,
        method: 'GET',
      }),
    }),
    getOpneSessionsCalsses: builder.query({
      query: date => ({
        url: `tutor/open-sessions?class_type_id=3&date=${date}`,
        method: 'GET',
      }),
    }),
    getStudentWalletAmount: builder.query({
      query: () => ({
        url: `student/wallet-amount`,
        method: 'GET',
      }),
    }),
    addAmountStudentWallet: builder.mutation({
      query: body => ({
        url: `student/add-wallet-amount`,
        method: 'POST',
        body: body,
      }),
    }),
    // Notification List
    getNotificationList: builder.query({
      query: () => ({
        url: `notification-list?page=0&limit=1000`,
        method: 'GET',
      }),
    }),
    getUpcommingScheduleDetails: builder.query({
      query: id => ({
        url: `student/upcoming-schedule-details?id=${id}`,
        method: 'GET',
      }),
    }),
    getUpcommingTutorScheduleDetails: builder.query({
      query: id => ({
        url: `tutor/upcoming-schedule-details?id=${id}`,
        method: 'GET',
      }),
    }),
    editTutorSchedule: builder.mutation({
      query: body => ({
        url: `tutor/schedules`,
        method: 'PUT',
        body: body,
      }),
    }),
    tutorBookingCancel: builder.mutation({
      query: body => ({
        url: `tutor/cancel-booking-schedule`,
        method: 'POST',
        body: body,
      }),
    }),
    studentRescheduleSession: builder.mutation({
      query: body => ({
        url: `student/reschedule-session`,
        method: 'POST',
        body: body,
      }),
    }),
    addOpenSession: builder.mutation({
      query: withdrawlAmount => ({
        url: 'tutor/add-open-session',
        method: 'POST',
        body: withdrawlAmount,
      }),
    }),
    getTutorOpenSessionDetailsById: builder.query({
      query: id => ({
        url: `tutor/open-sessions-details?id=${id}`,
        method: 'GET',
      }),
    }),
    getTutorSchedulesForCalendar: builder.query({
      query: params => ({
        url: `student/tutor-schedules-dates`,
        method: 'GET',
        params: params,
      }),
    }),
    getStudentFaqs: builder.query({
      query: lang => ({
        url: `faqs`,
        method: 'GET',
        headers: {
          lang: lang, // Allow the boundary to be auto-generated
        },
      }),
    }),
    getStudentStaticContent: builder.query({
      query: ({pageType, user}) => ({
        url: `static-contents?page_type=${pageType}&user_type=${user}`,
        method: 'GET',
      }),
    }),
    getStudentPaymentMethods: builder.query({
      query: () => ({
        url: `student/saved-payment-methods`,
        method: 'GET',
      }),
    }),
    getStudentWalletHistory: builder.query({
      query: ({date, page}) => ({
        url: `student/transactions?page=${page}&limit=120&date=${date}`,
        method: 'GET',
      }),
    }),
    studentTutorReview: builder.mutation({
      query: body => ({
        url: `student/tutor-reviews`,
        method: 'POST',
        body: body,
      }),
    }),
    riseYourConcern: builder.mutation({
      query: body => ({
        url: `raise-concern`,
        method: 'POST',
        body: body,
      }),
    }),
    getTutorReviews: builder.query({
      query: ({id, page = 0, limit = 10}) => ({
        url: `student/tutor-reviews?page=${page}&limit=${limit}&tutor_id=${id}`,
        method: 'GET',
      }),
    }),
    getReviewsByStudent: builder.query({
      query: ({page = 0, limit = 10}) => ({
        url: `student/tutor-reviews-by-me?page=${page}&limit=${limit}`,
        method: 'GET',
      }),
    }),

    getBookingDatesForCalendar: builder.query({
      query: ({params, userType}) => ({
        url: `${userType}/booking-dates`,
        method: 'GET',
        params: params,
      }),
    }),
    getScheduleDatesForCalendarTutor: builder.query({
      query: params => ({
        url: `tutor/schedule-dates`,
        method: 'GET',
        params: params,
      }),
    }),
    getScheduleDatesForCalendarTutorOpenSession: builder.query({
      query: params => ({
        url: `tutor/schedule-dates-for-open-session`,
        method: 'GET',
        params: params,
      }),
    }),
    getTutorRateCardStatus: builder.query({
      query: () => ({
        url: `tutor/profile-status`,
        method: 'GET',
      }),
    }),
    getAgoraRoomNetless: builder.query({
      query: role => ({
        url: `generate-agora-token-netless`,
        method: 'GET',
        params: role,
      }),
    }),
    getAllConnectedProfiles: builder.query({
      query: params => ({
        url: `connected-profiles`,
        method: 'GET',
      }),
    }),
    getAllConnectedProfilesAfterLogin: builder.query({
      query: () => ({
        url: `profile-list-after-login`,
        method: 'GET',
      }),
    }),
    connectNewProfile: builder.mutation({
      query: body => ({
        url: `connect-new-profile`,
        method: 'POST',
        body: body,
      }),
    }),

    getAllConcerns: builder.query({
      query: () => ({
        url: 'concerns',
        method: 'GET',
      }),
    }),
    pushNotificationPreferance: builder.mutation({
      query: status => ({
        url: 'push-notification-preference',
        method: 'PUT',
        body: status,
      }),
    }),
    deleteAccount: builder.mutation({
      query: reason => ({
        url: 'delete-account',
        method: 'PUT',
        body: {
          reason: reason,
        },
      }),
    }),

    startClassTutor: builder.query({
      query: params => ({
        url: 'tutor/start-booking-session',
        method: 'GET',
        params: params,
      }),
    }),

    getConnectProfilesUser: builder.query({
      query: () => ({
        url: 'connected-profiles-users',
        method: 'GET',
      }),
    }),

    putTutorConfirmationStatus: builder.mutation({
      query: body => ({
        url: 'tutor/booking-approval-status',
        method: 'PUT',
        body: body,
      }),
    }),
    getSuggestedVenueDetails: builder.query({
      query: id => ({
        url: `student/meeting-venue-details?id=${id}`,
        method: 'GET',
      }),
    }),
    getTutorSchedulesInRange: builder.query({
      query: body => ({
        url: `student/tutor-slots`,
        method: 'GET',
        params: body,
      }),
    }),
    putVisitedTutorList: builder.mutation({
      query: () => ({
        url: `student/visited-tutor-list`,
        method: 'PUT',
      }),
    }),
    getVisitedTutorList: builder.query({
      query: () => ({
        url: `student/visited-tutor-list`,
        method: 'GET',
      }),
    }),
    // Add Bank Account APIS
    addBankAccountApi: builder.mutation({
      query: body => ({
        url: `tutor/account-details`,
        method: 'POST',
        body: body,
      }),
    }),
    getBankAccountApi: builder.query({
      query: () => ({
        url: `tutor/accounts`,
        method: 'GET',
      }),
    }),

    markAccountAsPrimary: builder.mutation({
      query: data => {
        // Log the body (data) being sent in the request
        console.log('Request Body:', data);

        return {
          url: `/tutor/mark-account-details-primary`,
          method: 'PUT',
          body: data,
        };
      },
    }),

    getConnectedStudents: builder.query({
      query: () => ({
        url: `/connected-student-profiles`,
        method: 'GET',
      }),
    }),
    markActiveKidForParent: builder.mutation({
      query: body => ({
        url: `/active-kid`,
        method: 'PUT',
        body: body,
      }),
    }),
    markAllNotiAsRead: builder.mutation({
      query: body => ({
        url: `/read-notification`,
        method: 'PUT',
        body: body,
      }),
    }),
    getUnreadNotiCount: builder.query({
      query: () => ({
        url: `/all-unread-count`,
        method: 'GET',
      }),
    }),
    getUnreadMessageCount: builder.query({
      query: () => ({
        url: `/message-count`,
        method: 'GET',
      }),
    }),
    deleteConnectedProfile: builder.mutation({
      query: body => ({
        url: `/delete-connected-profiles`,
        method: 'PUT',
        body: body,
      }),
    }),
    getSavedPaymentMethodStudent: builder.query({
      query: () => ({
        url: `/student/saved-payment-methods`,
        method: 'GET',
      }),
    }),
    getCampaignDetails: builder.query({
      query: () => ({
        url: `/campaign-list?platform=1`,
        method: 'GET',
      }),
    }),
    sendSmsInvite: builder.mutation({
      query: body => {
        console.log('Sending SMS Invite Request:', body);
        return {
          url: '/send-app-invite',
          method: 'POST',
          body,
        };
      },
    }),
    validateSocialLoginApi: builder.mutation({
      query: body => {
        console.log('validateSocialLoginApi:', body);
        return {
          url: '/validate-user-social',
          method: 'POST',
          body,
        };
      },
    }),
    getAllBooking: builder.query({
      query: params => {
        const queryParams = {};
        if (params.date) queryParams.date = params.date;

        if (params.class_type_id)
          queryParams.class_type_id = params.class_type_id;
        console.log('🚀 ~ queryParams:', queryParams);
        console.log('🚀 ~ params:', params);
        return {
          url: `${params.userType}/all-bookings`,
          method: 'GET',
          params: queryParams,
        };
      },
    }),
    getTutorBookingConfirmationNotification: builder.query({
      query: () => {
        return {
          url: `tutor/incoming-booking-notifications`,
          method: 'GET',
        };
      },
    }),
    getTutorCommissionRatesApi: builder.query({
      query: () => {
        return {
          url: `tutor/commission-rates`,
          method: 'GET',
        };
      },
    }),

    ////////////////////////////////
  }),
});

export const {
  useSignUpMutation,
  useVerifyOtpMutation,
  useSendOtpMutation,
  useProfileDetailsQuery,
  useGetQualificationsQuery,
  useGetStudentDetailQuery,
  useGetGradesQuery,
  useChooseYourGradeQuery,
  useGetExpertiseQuery,
  useLazyChooseYourCurriculumsQuery,
  useUpdateTutorProfileMutation,
  useStudentAcademicMutation,
  useGetTutorsAcademicQuery,
  useGetTutorsDetailAcademicQuery,
  useGetTutorsDetailCoursesQuery,
  useGetTutorsDetailRecreationalQuery,
  useUpdateStudentProfileMutation,
  useUploadDocumentMutation,
  useUploadTutorDocumentsMutation,
  useGetTutorDocumentsQuery,
  useSubCategoryQuery,
  useCourseSubCategoriesQuery,
  useGetTutorsRecreationalQuery,
  useGetTutorsAllCoursesQuery,
  useGetTutorProfileStatusQuery,
  useLogoutMutation,
  useUploadFileProfileMutation,
  useGetAcademicRateCardQuery,
  useAddAcademicRateCardMutation,
  useLazyGetAcademicRateCardEditDataQuery,
  useGetRecreationalRateCardEditDataQuery,
  useEditAcademicRateCardMutation,
  useDeleteAcademicRateCardMutation,
  useGetRecreationalRateCardQuery,
  useGetCourseRateCardQuery,
  useGetSubjectsTutorQuery,
  useGetSessionsTutorQuery,
  useGetPackgeTypesQuery,
  useAddressMutation,
  useSendOtpAuthMutation,
  useGetTutorProfileQuery,
  useGetStudentProfileForTutorQuery,
  useSocialSigninMutation,
  useGetClassAndSchedulesQuery,
  useLazyGetTutorSchedulesForStudentQuery,

  useLazyGetTutorRateCardDetailsStudentAcademicQuery,
  useLazyGetBookingPriceAcademicQuery,
  useLazyGetTutorMeetingPointAddressQuery,
  // TUTOR SCHEDULE
  useLazyGetTutorSchedulesQuery,
  useLazyGetTutorSchedulesForOpenSessionQuery,
  useAddTutorScheduleApiMutation,
  useUpdateTutorScheduleMutation,
  useDeleteTutorScheduleMutation,
  useAddRecreationalRateCardMutation,
  useAddCourseRateCardMutation,
  useGettMyExpertiseQuery,
  useEditRecreationalRateCardMutation,
  useDeleteRecreationalRateCardMutation,
  useDeleteCourseRateCardMutation,
  useGetCourseRateCardEditDataQuery,
  useEditCourseRateCardMutation,
  useGetCourseLevelsQuery,
  useGetMeetingPreferenceQuery,
  useGetMyBookingsTutorQuery,
  useGetTutorBookingDetailsQuery,
  useGetTutorBookingListHomeQuery,
  //meetinng preferences
  useGetMeetingPointsQuery,
  useGetMeetingPointsByIdQuery,
  useEditMeetingPreferenceMutation,
  useAddMeetingPointMutation,
  useEditMeetingPointMutation,
  useDeleteMeetingPointMutation,
  useGetStudentListQuery,
  useVerifyOtpAuthMutation,
  useGetClassTypesQuery,
  //Tutor Booking Student
  useBookTutorMutation,
  usePostContactsMutation,
  usePostBookingInviteMutation,
  usePostStudentPaymentMutation,
  useGetStudentBookingListQuery,
  useGetBookingDetailsQuery,
  useUpdateDeviceDetailsMutation,
  useGetEarningsQuery,
  useGetTotalErningsHistoryQuery,
  useRequesPayoutTutorMutation,
  useBookOpenSessionMutation,
  useGetRateAllCardsQuery,
  useGetSudendtBookingDetailsQuery,
  useGetTutorBookingListQuery,
  useGetTutorClassDetailsQuery,
  //agora apis
  useGenerateAgoraTokenMutation,
  //Open Session
  useGetOpenSessionListQuery,
  useGetOpenSessionDetailsByIdQuery,
  useGetSudendtBookingDetailsByIdQuery,
  useGetOpenSessionScheduleDetailsByIdQuery,
  useGetTutorBookingDetailsByIdQuery,
  useOpenSessionPaymentMutation,
  useGetBookedOpenSessionsQuery,
  useGetBookedOpenSessionsDetailsQuery,
  useGetOpneSessionsQuery,
  useGetOpneSessionsCalssesQuery,
  useLazyGetStudentWalletAmountQuery,
  useAddAmountStudentWalletMutation,
  //Notification List
  useGetNotificationListQuery,
  useGetUpcommingScheduleDetailsQuery,
  useGetUpcommingTutorScheduleDetailsQuery,
  useEditTutorScheduleMutation,
  useTutorBookingCancelMutation,
  useStudentRescheduleSessionMutation,
  useAddOpenSessionMutation,
  useGetTutorOpenSessionDetailsByIdQuery,
  useGetStudentFaqsQuery,
  useGetStudentStaticContentQuery,
  useGetStudentPaymentMethodsQuery,
  useLazyGetStudentWalletHistoryQuery,
  useStudentTutorReviewMutation,
  useSwitchProfileMutation,
  //Get Tutor Schedule for students booking calendar
  useLazyGetTutorSchedulesForCalendarQuery,
  useRiseYourConcernMutation,
  useGetTutorReviewsQuery,
  useGetReviewsByStudentQuery,
  useLazyGetBookingDatesForCalendarQuery,
  useLazyGetScheduleDatesForCalendarTutorQuery,
  useLazyGetScheduleDatesForCalendarTutorOpenSessionQuery,
  useGetTutorRateCardStatusQuery,
  useGetAgoraRoomNetlessQuery,
  useGetAllConcernsQuery,
  usePushNotificationPreferanceMutation,
  useDeleteAccountMutation,
  //Connected Profile
  useGetAllConnectedProfilesQuery,
  useSwitchConnectedProfileMutation,
  useGetAllConnectedProfilesAfterLoginQuery,
  useConnectNewProfileMutation,
  useLazyStartClassTutorQuery,
  useGetConnectProfilesUserQuery,

  usePutTutorConfirmationStatusMutation,
  useGetSuggestedVenueDetailsQuery,
  useLazyGetTutorSchedulesInRangeQuery,
  //putVisitedTutorList
  usePutVisitedTutorListMutation,
  useGetVisitedTutorListQuery,
  //Add bank account
  useAddBankAccountApiMutation,
  useGetBankAccountApiQuery,
  useMarkAccountAsPrimaryMutation,
  useGetConnectedStudentsQuery,
  useMarkActiveKidForParentMutation,
  useMarkAllNotiAsReadMutation,
  useGetUnreadNotiCountQuery,
  useGetUnreadMessageCountQuery,
  useDeleteConnectedProfileMutation,

  useGetSavedPaymentMethodStudentQuery,
  useGetCampaignDetailsQuery,
  useSendSmsInviteMutation,
  useValidateSocialLoginApiMutation,
  useLazyGetAllBookingQuery,
  useGetTutorBookingConfirmationNotificationQuery,
  useGetTutorCommissionRatesApiQuery,
  useGetBookedOpenSessionsDetailsTutorQuery,
} = apiSlice;

export default apiSlice;
