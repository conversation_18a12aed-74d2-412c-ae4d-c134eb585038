import React from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';

import {useNavigation} from '@react-navigation/native';
import {normalize} from '../Helper/NormalizeFont';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {fp} from '../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';

export const AppHeader = ({
  onPressIcon,
  backIcon,
  title,
  isBackBtn,
  isRightImage,
  style,
}) => {
  const navigation = useNavigation();
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <View
      style={[
        {
          flexDirection: isRTL ? 'row-reverse' : 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: colors.offBlue,
          height: 50,
          width: '100%',
        },
        style,
      ]}>
      <View
        style={{
          width: '13%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {isBackBtn && (
          <IconBtn
            icon={backIcon}
            onPress={() => navigation.navigate('WelcomeScreen')}
            iconStyle={{transform: [{rotate: isRTL ? '-180deg' : '0deg'}]}}
          />
        )}
      </View>

      <View
        style={{
          width: '74%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Text
          style={{
            fontSize: normalize(14),
            color: colors.white,
            fontFamily: 'Poppins-Medium',
          }}>
          {title}
        </Text>
      </View>

      <View
        style={{
          width: '13%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {isRightImage && (
          <IconBtn icon={icons.Home} onPress={() => console.log('FORWORD')} />
        )}
      </View>
    </View>
  );
};

const IconBtn = props => {
  const {icon, style, iconStyle} = props;

  return (
    <TouchableOpacity
      {...props}
      activeOpacity={0.8}
      style={[
        {
          height: 30,
          width: 30,
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      ]}>
      <Image
        resizeMode="contain"
        style={[
          {
            height: fp(3.2),
            width: fp(3.2),
          },
          iconStyle,
        ]}
        source={icon}
      />
    </TouchableOpacity>
  );
};
