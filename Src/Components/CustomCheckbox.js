import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import colors from '../Utils/colors';
import {responsiveFontSize} from '../Utils/constant';
import {Fonts} from '../Utils/Fonts';
import {fp} from '../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';
import HelperTextComponent from './HelperTipComp';

const CustomCheckbox = ({
  label,
  isSelected,
  onSelect,
  style,
  isDisabled = false,
  showTooltip = false,
  helperTxt,
}) => {
  console.log('🚀 ~ label:', label);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [openHelperText, setOpenHelperText] = useState(false);
  return (
    <TouchableOpacity
      disabled={isDisabled}
      style={[
        styles.checkboxContainer,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
        style,
      ]}
      onPress={onSelect}>
      <View
        style={[
          styles.checkbox,
          {marginRight: isRTL ? 0 : 10, marginLeft: isRTL ? 10 : 0},
          isSelected && styles.checkboxSelected,
        ]}>
        {isSelected && <Text style={styles.checkMark}>✓</Text>}
      </View>
      <Text
        style={[
          styles.checkboxLabel,
          {marginRight: isRTL ? 0 : 10, marginLeft: isRTL ? 10 : 0},
        ]}>
        {label}
      </Text>
      {showTooltip && (
        <HelperTextComponent
          helperText={helperTxt}
          setOpen={setOpenHelperText}
          open={openHelperText}
          borderColor={colors.black}
          iconColor={colors.black}
        />
      )}
    </TouchableOpacity>
  );
};

export default CustomCheckbox;

const styles = StyleSheet.create({
  checkboxContainer: {
    alignItems: 'center',
    marginVertical: 10,
    marginLeft: 10,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF',
  },
  checkboxSelected: {
    backgroundColor: '#40A39B',
  },
  checkMark: {
    color: '#fff',
    fontFamily: Fonts.bold,
  },
  checkboxLabel: {
    fontSize: fp(1.6),
    color: colors.searchGray,
    fontFamily: Fonts.medium,
    marginRight: 8,
  },
});
