import React from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import GradBadge from '../GradBadge/GradBadge';
import icons from '../../Utils/icons';
import {Fonts} from '../../Utils/Fonts';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import TutorDetails from './TutorDetails';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {convertToLocal12HourFormat} from '../../Helper/DateHelpers/DateHelpers';
import {useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';

const ClassInfoCard = ({item = {}, onCardPress}) => {
  console.log('🚀 ~ ClassInfoCard ~ item:', item);
  const {user_type} = useSelector(state => state?.auth);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  // Safely access nested properties using optional chaining
  const booking = item?.tlm_booking || {};
  const tutor = booking?.tutor || {};
  const enrollments = booking?.tlm_booking_enrollments?.[0] || {};
  const user = enrollments?.tlm_user || {};
  const academicDetails = user?.tlm_student_academic_details?.[0] || {};
  const imageUri = (() => {
    // Check tutor image first
    if (tutor?.image) {
      console.log('🚀 ~ imageUri ~ tutor?.image:', tutor?.image);
      return {uri: `${IMAGE_BASE_URL}${tutor.image}`};
    }
    // Fallback to user image
    if (user?.image) {
      console.log('🚀 ~ imageUri ~ user?.image:', user?.image);
      return {uri: `${IMAGE_BASE_URL}${user.image}`};
    }
    // Default dummy image
    return {uri: DUMMY_USER_IMG};
  })();
  const renderTime = () => {
    const {start_time, end_time} = item?.tlm_tutor_schedule || {};
    const startTime = `${convertToLocal12HourFormat(start_time)}`;
    const endTime = convertToLocal12HourFormat(end_time);
    return isRTL ? `${endTime} - ${startTime}` : `${startTime} - ${endTime}`;
  };
  const timeTextStyle = {
    ...styles.timeTxt,
    marginLeft: isRTL ? 0 : 10,
    marginRight: isRTL ? 10 : 0,
    textAlign: isRTL ? 'right' : 'left',
  };
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={onCardPress}
      disabled={booking?.tutor_approval_status === '0'}
      style={[
        styles.container,
        {alignItems: isRTL ? 'flex-end' : 'flex-start'},
      ]}>
      <View
        style={[
          styles.innerHeader,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <Text style={[styles.titleTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
          {booking?.class_title || 'No Title'}
        </Text>
        {booking?.tutor_approval_status === '0' && (
          <GradBadge text={t('approval_pending')} />
        )}
      </View>

      <View
        style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Image
          source={icons.clockYellow}
          style={{height: fp(2.6), width: fp(2.6), tintColor: '#EBBE49'}}
          resizeMode="contain"
        />
        <Text style={timeTextStyle}>{`${t('time')} : `}</Text>
        <Text style={[styles.innerTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
          {renderTime()}
        </Text>
      </View>

      <View
        style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Image
          source={icons.calendarWhite}
          style={{height: fp(2.6), width: fp(2.6), tintColor: '#EBBE49'}}
          resizeMode="contain"
        />
        <Text style={timeTextStyle}>
          {t('session')} :{' '}
          <Text style={styles.innerTxt}>
            {booking?.tlm_sessions_type?.name}
          </Text>
        </Text>
      </View>
      {/* {item?.type == 'openSession' && ( */}
      <View
        style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Image
          source={icons.calendarWhite}
          style={{height: fp(2.6), width: fp(2.6), tintColor: '#EBBE49'}}
          resizeMode="contain"
        />
        <Text style={timeTextStyle}>{t('classType')} : </Text>
        <Text style={styles.innerTxt}>{booking?.tlm_class_type?.name}</Text>
      </View>
      {/* )} */}

      <View style={styles.saperator} />

      {(item?.type !== 'openSession' || user_type !== 3) &&
        (user?.name || tutor?.name) && (
          <TutorDetails
            imageUri={imageUri}
            name={tutor?.name || user?.name || 'No Name'}
            occupation={''}
          />
        )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.lightgreay,
    padding: fp(1),
    width: wp(95),
    borderRadius: fp(1.4),
    alignSelf: 'center',
    marginVertical: 8,
  },
  innerHeader: {
    justifyContent: 'space-between',
    width: wp(60),
  },
  timeTxt: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.txtGrey1,
  },
  innerTxt: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.darkBlack,
  },
  row: {
    marginTop: hp(1.2),
    alignItems: 'center',
  },
  saperator: {
    borderWidth: 0.4,
    borderColor: colors.txtGrey,
    marginVertical: hp(2),
  },
  titleTxt: {
    fontFamily: Fonts.bold,
    fontSize: fp(1.7),
    color: colors.black,
    width: fp(30),
    lineHeight: hp(2.2),
  },
});

export default ClassInfoCard;
