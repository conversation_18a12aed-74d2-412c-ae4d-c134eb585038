import {StyleSheet, Text, View, Image, Pressable} from 'react-native';
import React from 'react';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import {useTranslation} from 'react-i18next';

const TutorDetails = ({
  imageUri,
  name,
  occupation,
  rightContent,
  onCardPress,
  nameStyles,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  console.log('🚀 ~ TutorDetails ~ occupation:', occupation);
  return (
    <Pressable
      onPress={onCardPress}
      style={[
        styles.container,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
      ]}>
      <View style={{flexDirection: isRTL ? 'row-reverse' : 'row'}}>
        {imageUri != '' && <Image source={imageUri} style={styles.img} />}

        <View
          style={{
            marginRight: isRTL ? 10 : 0,
            marginLeft: isRTL ? 0 : 10,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text
            style={[
              styles.name,
              {
                textAlign: isRTL ? 'right' : 'left',
              },
              nameStyles,
            ]}>
            {capitalizeFirstLetter(name)}
          </Text>
          {/* 
          {occupation != '' && (
            <Text
              numberOfLines={2}
              style={[
                styles.occupation,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {occupation}
            </Text>
          )} */}
        </View>
      </View>
      {rightContent}
    </Pressable>
  );
};

export default TutorDetails;

const styles = StyleSheet.create({
  container: {
    // marginBottom: 8,
    justifyContent: 'space-between',
    flex: 1,
    alignItems: 'center',
  },
  detailView: {
    left: 8,
  },
  name: {
    fontFamily: Fonts.bold,
    fontSize: fp(1.9),
    color: colors.black,
    paddingTop: hp(0.4),
  },
  occupation: {
    fontFamily: Fonts.poppinsRegular,
    fontSize: fp(1.6),
    color: colors.searchGray,
    paddingTop: hp(0.4),
    width: wp(60),
  },
  img: {
    height: fp(6),
    width: fp(6),
    borderRadius: fp(1),
  },
});
