import React from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';
import colors from '../Utils/colors';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';
import icons from '../Utils/icons';
import {Fonts} from '../Utils/Fonts';
import {responsiveFontSize} from '../Utils/constant';
import {
  convertToLocal12HourFormat,
  convertToLocal24HourFormat,
  isOpenSessionSlotDisabled,
} from '../Helper/DateHelpers/DateHelpers';
import moment from 'moment';
import {applyShadowStyleIos} from '../Helper/ShadowStyleIos';
import {useTranslation} from 'react-i18next';

const AccordianOpenSession = ({item, disable, isRTL}) => {
  console.log('🚀 ~ AccordianOpenSession ~ disable:', disable);
  console.log(
    '🚀 ~ AccordianOpenSession ~ item?.tlm_tutor_schedule?.start_time:',
    item?.tlm_tutor_schedule?.start_time,
  );

  const {t} = useTranslation();
  const isPastSlot = isOpenSessionSlotDisabled(
    item?.date,
    item?.tlm_tutor_schedule?.start_time,
  );
  console.log('🚀 ~ AccordianOpenSession ~ isPastSlot:', isPastSlot);
  return (
    <View
      style={[
        applyShadowStyleIos(styles.container),
        {
          backgroundColor:
            disable || isPastSlot ? colors.lightisGrey : colors?.white,
          // colors?.white,
        },
      ]}>
      <View
        style={[
          styles.headerContent,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <Text style={[styles.date, isPastSlot && styles.pastDate]}>
          {moment(item?.date).format('DD MMM YYYY')}
        </Text>
      </View>

      <View style={styles.content}>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
          }}>
          <Text style={[styles.time, isPastSlot && styles.pastDate]}>
            {`${t('time')} : `}
            <Text style={[styles.timeTxt, isPastSlot && styles.pastDate]}>
              {convertToLocal12HourFormat(item?.tlm_tutor_schedule?.start_time)}
            </Text>{' '}
            -
            <Text style={[styles.timeTxt, isPastSlot && styles.pastDate]}>
              {convertToLocal12HourFormat(item?.tlm_tutor_schedule?.end_time)}
            </Text>
          </Text>
        </View>
      </View>
    </View>
  );
};

export default AccordianOpenSession;

const styles = StyleSheet.create({
  container: {
    marginBottom: hp(2),
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 12,
    padding: 10,
    backgroundColor: '#fff',
    elevation: 2,
  },
  headerContent: {
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  date: {
    fontFamily: Fonts.medium,
    fontSize: responsiveFontSize(16),
    color: colors.darkBlack,
  },
  pastDate: {
    color: colors.txtGrey1,
  },
  content: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: colors.txtGrey,
  },
  time: {
    fontFamily: Fonts.medium,
    color: colors.txtGrey1,
    fontSize: fp(1.8),
  },
  timeTxt: {
    fontFamily: Fonts.medium,
    color: colors.darkBlack,
    fontSize: fp(1.8),
  },
});
