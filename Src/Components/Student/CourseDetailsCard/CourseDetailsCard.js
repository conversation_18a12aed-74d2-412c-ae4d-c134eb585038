import {Image, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {IMAGE_BASE_URL} from '../../../Utils/getBaseUrl';
import icons from '../../../Utils/icons';
import colors from '../../../Utils/colors';
import {DUMMY_COURSE_IMG} from '../../../Utils/constant';
import {Fonts} from '../../../Utils/Fonts';
import {fp, hp} from '../../../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';

const CourseDetailsCard = ({courseData}) => {
  console.log('🚀 ~ CourseDetailsCard ~ courseData:', courseData);
  const {t} = useTranslation();
  const courseImageUrl = courseData?.image
    ? {uri: `${IMAGE_BASE_URL}${courseData.image}`} // Updated image URL concatenation
    : {uri: DUMMY_COURSE_IMG};
  const sessions = courseData?.tlm_tutor_class_sessions || [];

  const reviewCount = courseData?.reviews?.length || 0;
  return (
    <>
      <Image source={courseImageUrl} style={styles.courseImage} />
      <Text style={styles.courseTitle}>
        {courseData.course_title || 'Course Title'}
      </Text>
      <View style={styles.courseInfo}>
        <View style={{display: 'flex', flexDirection: 'column'}}>
          <View style={{display: 'flex', flexDirection: 'column'}}>
            <Text style={styles.courseText}>
              {t('duration')}:{' '}
              <Text style={{color: colors.black}}>
                {courseData.course_duration > 1
                  ? `${courseData.course_duration} Hours`
                  : `${courseData.course_duration} Hour` || 'N/A'}
              </Text>{' '}
            </Text>
          </View>

          {/* <Text style={styles.courseText}>
            {t('level')}:{' '}
            <Text style={{color: colors.black}}>
              {courseData.tlm_course_level?.name || 'N/A'}
            </Text>
          </Text> */}
          {/* <Text style={styles.courseText}>
            {t('type')}:{' '}
            <Text style={{color: colors.black}}>
              {sessions[0]?.tlm_sessions_type?.name || 'N/A'}
            </Text>{' '}
          </Text> */}
        </View>
      </View>
    </>
  );
};

export default CourseDetailsCard;

const styles = StyleSheet.create({
  courseTitle: {
    fontSize: fp(2),
    fontFamily: Fonts.bold,
    color: colors.black,
    marginBottom: 8,
  },
  courseInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  courseText: {
    fontSize: hp(1.6),
    color: colors.txtGrey1,
    marginVertical: 5,
    fontFamily: Fonts.medium,
  },
  courseImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
});
