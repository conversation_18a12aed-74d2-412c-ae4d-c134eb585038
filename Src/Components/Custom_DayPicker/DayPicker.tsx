import React, {useEffect, useState} from 'react';
import {View, TouchableOpacity, Text, StyleSheet} from 'react-native';
import {fp, hp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';

const DayPicker = ({onChange, alreadySelectedDays = [], isEdit = false}) => {
  console.log('🚀 ~ DayPicker ~ alreadySelectedDays:', alreadySelectedDays);
  const [selectedDays, setSelectedDays] = useState(
    alreadySelectedDays.map(day => parseInt(day, 10)),
  );
  const days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
  // useEffect(() => {
  //   if (isEdit == true) {
  //     console.log('is it running');
  //     setSelectedDays(alreadySelectedDays);
  //   }
  // }, [isEdit, alreadySelectedDays]);

  const toggleDay = index => {
    const updatedDays = selectedDays.includes(index)
      ? selectedDays.filter(day => day !== index)
      : [...selectedDays, index];

    setSelectedDays(updatedDays);
    onChange?.(updatedDays); // Callback with selected days if needed
  };

  return (
    <View style={styles.container}>
      {days.map((day, index) => (
        <TouchableOpacity
          key={index}
          onPress={() => toggleDay(index)}
          style={[
            styles.dayButton,
            selectedDays.includes(index) && styles.selectedDayButton,
          ]}>
          <Text
            style={[
              styles.dayText,
              selectedDays.includes(index) && styles.selectedDayText,
            ]}>
            {day}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dayButton: {
    width: 38,
    height: 38,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 1,
  },
  selectedDayButton: {
    backgroundColor: colors.themeBackground,
  },
  dayText: {
    fontSize: fp(1.6),
    color: colors.themeBackground,
    fontFamily: Fonts.semiBold,
  },
  selectedDayText: {
    color: '#FFFFFF',
  },
});

export default DayPicker;
