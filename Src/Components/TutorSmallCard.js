import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {SCREEN_HEIGHT} from '../Utils/constant';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';
import HelperTextComponent from './HelperTipComp';

const TutorSmallCard = ({imageUri, halperTxt, title, ...touchProps}) => {
  const [openHelperText, setOpenHelperText] = useState(false);
  return (
    <TouchableOpacity style={styles.container} {...touchProps}>
      <View
        style={{
          alignSelf: 'center',
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          source={imageUri}
          style={{
            height: fp(4),
            width: fp(4),
            alignSelf: 'center',
            // backgroundColor: 'red',
          }}
          resizeMode="contain"
        />
        <View style={{flexDirection: 'row', alignItems: 'baseline'}}>
          <Text style={styles.titleTxt}>{title}</Text>
          <HelperTextComponent
            helperText={halperTxt}
            setOpen={setOpenHelperText}
            open={openHelperText}
            borderColor={colors.black}
            iconColor={colors.black}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default TutorSmallCard;

const styles = StyleSheet.create({
  container: {
    borderRadius: 10,
    width: wp(46),
    height: hp(12),
    borderColor: colors.lightGrey,
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    marginTop: 2,
  },
  titleTxt: {
    fontSize: fp(1.8),
    textAlign: 'center',
    fontFamily: Fonts.medium,
    color: colors.darkBlack,
    marginTop: hp(1),
    marginRight: 8,
  },
});
