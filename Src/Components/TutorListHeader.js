import {useTranslation} from 'react-i18next';
import {
  I18nManager,
  Image,
  Pressable,
  Text,
  TextInput,
  View,
} from 'react-native';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {wp} from '../Helper/ResponsiveDimensions';
import {Fonts} from '../Utils/Fonts';

const TutorListHeader = ({
  onBackPress,
  searchQuery,
  setSearchQuery,
  onSubmitEditing,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  return (
    <View
      style={{
        flexDirection: isRTL ? 'row-reverse' : 'row',
        alignItems: 'center',
        backgroundColor: colors.themeColor,
        height: 50,
        width: '100%',
      }}>
      <View
        style={{
          width: '13%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Pressable
          onPress={onBackPress}
          style={{
            height: 30,
            width: 30,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Image
            source={isRTL ? icons.rightArrowLarge : icons.backbtn}
            style={{
              height: 18,
              width: 18,
              tintColor: colors.white,
            }}
          />
        </Pressable>
      </View>

      <View
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          width: wp(80),
          height: '100%',
          paddingRight: 10,
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            backgroundColor: colors.white,
            borderRadius: 10,
            height: 35,
            marginLeft: isRTL ? 0 : 10,
            marginRight: isRTL ? 10 : 0,
            paddingHorizontal: 10,
          }}>
          <Image
            style={{
              width: 18,
              height: 18,
              marginRight: isRTL ? 0 : 8,
              marginLeft: isRTL ? 8 : 0,
            }}
            source={icons.searchIcon}
            resizeMode="contain"
          />
          <TextInput
            style={{
              flex: 1,
              fontSize: 14,
              color: colors.searchGray,
              padding: 0,
              textAlign: isRTL ? 'right' : 'left',
              fontFamily: Fonts.medium,
              // backgroundColor: 'red'
            }}
            placeholder={t('searchPlaceholder') || 'Search here...'}
            placeholderTextColor={colors.searchGray}
            value={searchQuery}
            onSubmitEditing={onSubmitEditing}
            onChangeText={text => setSearchQuery(text)}
          />
          {/* <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: colors.offWhite1,
              borderRadius: 20,
              height: 25,
              marginLeft: 10,
              paddingHorizontal: 8,
            }}> */}
          {/* <Image
              source={icons.locationBlack}
              style={{
                width: 14,
                height: 14,
              }}
              resizeMode="contain"
            /> */}
          {/* <Text
              style={{
                marginHorizontal: 8,
                color: colors.black,
                fontSize: 12,
              }}>
              {t('locationLabel')}
            </Text>
            <Image
              source={icons.downArrowBlack}
              style={{
                width: 14,
                height: 14,
              }}
              resizeMode="contain"
            /> */}
          {/* </View> */}
        </View>
      </View>
    </View>
  );
};

export default TutorListHeader;
