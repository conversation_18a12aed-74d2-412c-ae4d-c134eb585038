import React from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';
import {capitalizeFirstLetter} from '../Helper/NormalizeFont';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';

const {width, height} = Dimensions.get('screen');

const StudentDetailComponent = ({
  title,
  subtitle,
  grade,
  classLevel,
  curriculum,
  onPressEdit,
}) => {
  console.log('ckass:', classLevel, curriculum);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <LinearGradient
      colors={['#C6FFC9', '#D4EBFF']}
      start={{
        x: Math.sin((0 * Math.PI) / 180),
        y: -Math.cos((100 * Math.PI) / 180),
      }}
      end={{
        x: Math.sin((50 * Math.PI) / 180),
        y: -Math.cos((200 * Math.PI) / 180),
      }}
      style={{
        marginVertical: 15,
        backgroundColor: colors.white,
        alignItems: 'center',
        paddingVertical: 10,
        width: width * 0.9,
        alignSelf: 'center',

        padding: 16,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <View style={styles.textContainer}>
        <View
          style={[
            styles.titleContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Text style={styles.title}>
            {t('hello')} {capitalizeFirstLetter(title)}
          </Text>
          <TouchableOpacity
            onPress={onPressEdit}
            style={{
              marginRight: 2,
              justifyContent: 'center',
              alignItems: 'center',
              width: 35,
              height: 35,
            }}
            activeOpacity={0.8}>
            <Image
              source={icons.edit}
              resizeMode="contain"
              style={styles.editIcon}
            />
          </TouchableOpacity>
        </View>

        {/* <Text style={styles.subtitle}>{subtitle}</Text> */}
      </View>
      <View
        style={[
          styles.detailsContainer,
          {
            flexDirection: isRTL ? 'row-reverse' : 'row',
          },
        ]}>
        <View style={styles.detailBox}>
          <Text
            style={[
              styles.detailTitle,
              {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            {t('grade')}
          </Text>
          <Text
            style={[
              styles.detailValue,
              {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            {capitalizeFirstLetter(grade)}
          </Text>
        </View>
        <View style={styles.detailBox}>
          <Text
            style={[
              styles.detailTitle,
              {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            {t('class')}
          </Text>
          <Text
            style={[
              styles.detailValue,
              {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            {capitalizeFirstLetter(classLevel)}
          </Text>
        </View>
        <View style={styles.detailBox}>
          <Text
            style={[
              styles.detailTitle,
              {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            {t('curriculum')}
          </Text>
          <Text
            style={[
              styles.detailValue,
              {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            {curriculum}
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    alignItems: 'center',
    paddingVertical: 10,
    width: width * 0.9,
    alignSelf: 'center',
  },
  textContainer: {
    width: '100%',
    alignSelf: 'center',
  },
  title: {
    fontSize: fp(2),
    fontFamily: Fonts.bold,
    marginBottom: height * 0.01,
    color: colors.black,
    letterSpacing: wp(0.1),
  },
  subtitle: {
    fontSize: 12,
    width: width * 0.7,
    color: colors.black,
  },
  detailsContainer: {
    backgroundColor: 'white',
    borderRadius: 14,

    justifyContent: 'space-evenly',
    paddingVertical: 10,
    paddingHorizontal: 12,
    // paddingLeft: wp(4),
    marginTop: hp(1.6),
    elevation: 2,
    width: wp(85),
  },
  detailBox: {
    // alignItems: 'center',
    flex: 1,

    elevation: 2,
  },
  detailTitle: {
    fontSize: fp(1.6),
    color: 'gray',
    marginBottom: 5,
    fontFamily: Fonts.medium,
  },
  detailValue: {
    fontSize: fp(1.8),
    color: colors.black,
    fontFamily: Fonts.medium,
    lineHeight: hp(2.2),
    marginTop: hp(0.2),
  },

  titleContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  editIcon: {
    // backgroundColor: 'green',

    width: 25,
    height: 25,
  },
});

export default StudentDetailComponent;
