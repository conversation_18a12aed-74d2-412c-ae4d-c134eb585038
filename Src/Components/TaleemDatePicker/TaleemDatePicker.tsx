import {
  View,
  StyleSheet,
  Image,
  ImageSourcePropType,
  Pressable,
  Text,
  Dimensions,
} from 'react-native';
import React, {useState} from 'react';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import colors from '../../Utils/colors';
import {fp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {useTranslation} from 'react-i18next';
const SCREEN_WIDTH = Dimensions.get('window').width;

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: 10,
    // width: DEVICE_WIDTH * 0.9,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 50,
    alignSelf: 'center',
  },
  input: {
    width: '87%',
    color: colors.black,
    height: '100%',
    marginHorizontal: 10,
  },
  icon: {
    width: 20,
    height: 20,
    marginRight: 5,
  },
  phoneicon: {
    color: colors.white,
    fontSize: 14,
    marginLeft: 20,
  },
  err: {
    color: colors.red,
    fontSize: 12,
    margin: 5,
  },
});
enum datetypes {
  date = 'date',
  time = 'time',
  datetime = 'datetime',
  undefined = 'undefined',
}

type props = {
  onPress?: () => void;
  touched?: any;
  errors?: any;
  iconleft?: ImageSourcePropType;
  label?: string;
  iconRight?: ImageSourcePropType;
  noborder?: boolean;
  issearch?: boolean;
  setDate: React.Dispatch<React.SetStateAction<string>>;
  date: Date;
  maximumDate?: Date;
  minimumDate?: Date;
  isSmall?: boolean;
  isDisabled: boolean;
  mode: String;
  // control: Control<FieldValues, any>;
  // rules?: Omit<RegisterOptions<FieldValues>, 'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'>,
  // name: string;
  // errortxt: string
};

export default function TaleemDatePicker({
  errors,
  touched,
  iconleft,
  label,
  iconRight,
  onPress,
  noborder,
  issearch,
  setDate,
  date,
  maximumDate,
  minimumDate,
  isSmall,
  isDisabled,
}: props) {
  console.log('🚀 ~ date:', date);
  const [mode, setMode] = useState<datetypes>(datetypes.date);
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const showDatePicker = (mode: datetypes) => {
    setDatePickerVisibility(true);
    setMode(mode);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = date => {
    console.log('🚀 ~ handleConfirm ~ date:', date);
    const newdate = new Date(date);
    setDate(newdate);
    hideDatePicker();
  };

  return (
    <>
      <Pressable
        style={{marginVertical: 8}}
        onPress={() => {
          if (!isDisabled) {
            showDatePicker('date');
          }
        }}>
        {label && (
          <Text
            style={{
              fontSize: fp(2),
              marginVertical: 12,
              fontFamily: Fonts.semiBold,
              color: colors.black,
            }}>
            {label}
          </Text>
        )}
        <View
          style={[
            {
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
              borderWidth: 1,
              borderColor: '#ddd',
              paddingVertical: 10,
              paddingHorizontal: 15,
              borderRadius: 10,
            },
          ]}>
          <View
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
            }}>
            {iconleft ? (
              <Pressable onPress={onPress}>
                <Image
                  source={iconleft}
                  resizeMode="contain"
                  style={{height: 24, width: 24}}
                />
              </Pressable>
            ) : null}
            <Text
              style={{
                fontSize: fp(2),
                marginLeft: isRTL ? 0 : 10,
                marginRight: isRTL ? 10 : 0,
                fontFamily: Fonts.medium,
              }}>
              {date}
            </Text>

            {iconRight ? (
              <Pressable onPress={onPress}>
                <Image source={iconRight} resizeMode={'cover'} />
              </Pressable>
            ) : null}
          </View>
        </View>
        {touched && errors && <Text style={styles.err}>{errors}</Text>}
      </Pressable>

      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        // mode={mode}
        mode={'date'}
        onConfirm={handleConfirm}
        onCancel={hideDatePicker}
        minimumDate={minimumDate}
        maximumDate={maximumDate}
        is24Hour={true}
      />
    </>
  );
}
