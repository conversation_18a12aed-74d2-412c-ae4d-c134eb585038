import React, {useState} from 'react';
import {
  ActivityIndicator,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import colors from '../Utils/colors';
import {responsiveFontSize} from '../Utils/constant';
import {Fonts} from '../Utils/Fonts';
import {fp, hp} from '../Helper/ResponsiveDimensions';
import HelperTextComponent from './HelperTipComp';
import {useTranslation} from 'react-i18next';

export const PrimaryButton = ({
  style,
  textStyle,
  title,
  rightIcon,
  loading,
  disabled,
  showTooltip = false,
  helperTxt = '',
  ...props
}) => {
  const [meetingPointHelper, setMeetingPointHelper] = useState(false);
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <TouchableOpacity
      {...props}
      disabled={disabled || loading}
      style={[
        styles.button,
        style,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
      ]}
      activeOpacity={0.6}
      accessible={true}
      accessibilityLabel={title}
      accessibilityRole="button">
      {loading ? (
        <ActivityIndicator size={'small'} color={colors.white} />
      ) : (
        <Text
          style={[
            styles.text,
            textStyle,
            {marginRight: isRTL ? 0 : 10, marginLeft: isRTL ? 10 : 0},
          ]}>
          {title}
        </Text>
      )}
      {rightIcon && (
        <Image
          resizeMode="contain"
          source={rightIcon}
          style={{height: 25, width: 25}}
        />
      )}
      {showTooltip && (
        <HelperTextComponent
          helperText={helperTxt}
          setOpen={setMeetingPointHelper}
          open={meetingPointHelper}
        />
      )}
    </TouchableOpacity>
  );
};

export const IconTextButton = ({icon, title, ...props}) => {
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <TouchableOpacity
      {...props}
      activeOpacity={0.9}
      style={[styles.iconBtn, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
      <Image
        source={icon}
        style={{
          width: fp(2),
          height: fp(2),
          marginRight: isRTL ? 0 : 10,
          marginLeft: isRTL ? 10 : 0,
        }}
      />
      <Text
        style={{
          fontSize: fp(2),
          fontFamily: Fonts.medium,
          color: colors.black,
        }}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    alignSelf: 'center',
    width: '90%',
    height: hp(5),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    // marginVertical: hp(0.4),
    backgroundColor: colors.themeColor,
  },
  text: {
    fontSize: fp(2),
    color: colors.white,
    fontFamily: Fonts.medium,
  },
  iconBtn: {
    height: hp(4),
    width: '90%',
    alignSelf: 'center',

    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.lightgreay,
    borderRadius: 8,
    marginVertical: 10,
  },
});
