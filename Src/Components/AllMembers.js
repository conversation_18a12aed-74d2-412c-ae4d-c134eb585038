import {
  Image,
  StyleSheet,
  Text,
  FlatList,
  View,
  Pressable,
  Alert,
} from 'react-native';
import React, {useState} from 'react';
import icons from '../Utils/icons';
import {useGetStudentListQuery} from '../Api/ApiSlice';
import {Fonts} from '../Utils/Fonts';
import {responsiveFontSize} from '../Utils/constant';
import colors from '../Utils/colors';
import {IMAGE_BASE_URL} from '../Utils/getBaseUrl';
import TaleemLoader from './TaleemLoader/TaleemLoader';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';
import {PrimaryButton} from './CustomButton';
import {useNavigation} from '@react-navigation/native';
import {showToast} from './ToastHelper';
import {useTranslation} from 'react-i18next';

const AllMembers = ({onSelectStudents}) => {
  const navigation = useNavigation();
  const {data: studentList, isLoading} = useGetStudentListQuery();
  const [selectedStudents, setSelectedStudents] = useState([]);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const handleCardPress = student => {
    const isAlreadySelected = selectedStudents.some(
      selected => selected.id === student.id,
    );

    if (isAlreadySelected) {
      // Remove student from selection
      const updatedSelection = selectedStudents.filter(
        selected => selected.id !== student.id,
      );
      setSelectedStudents(updatedSelection);
      onSelectStudents && onSelectStudents(updatedSelection); // Pass to parent
    } else {
      // Add student to selection
      const updatedSelection = [...selectedStudents, student];
      setSelectedStudents(updatedSelection);
      onSelectStudents && onSelectStudents(updatedSelection); // Pass to parent
    }
  };

  const validateSelection = () => {
    if (selectedStudents.length < 3) {
      showToast('error', t('youMustSelect'), 'bottom', isRTL);
      return false;
    } else if (selectedStudents.length > 10) {
      showToast('error', t('selectStudentError'), 'bottom', isRTL);
    }
    // navigation.navigate();
  };

  const renderItem = (item, index) => {
    const isSelected = selectedStudents.some(
      selected => selected.id === item.id,
    );
    return (
      <Pressable
        key={index}
        onPress={() => {
          handleCardPress(item);
        }}
        style={[styles.card, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <View
          style={[
            styles.innerRow,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Image
            source={
              item?.image
                ? {uri: IMAGE_BASE_URL + item?.image}
                : icons.taleemLogo
            }
            style={styles.img}
          />
          <View style={styles.nameContainer}>
            <Text style={styles.name}>{item?.name}</Text>
            <Text style={styles.grade}>
              {`${t('grade')}: ${
                item?.tlm_student_academic_details[0]?.tlm_grade?.name
              }`}
            </Text>
          </View>
        </View>
        {isSelected && (
          <Image source={icons.selected} style={styles.selected} />
        )}
      </Pressable>
    );
  };

  return (
    <View>
      <Text
        style={{
          fontSize: fp(1.6),
          lineHeight: hp(2.2),
          fontStyle: 'italic',
          paddingHorizontal: wp(2),
          textAlign: isRTL ? 'right' : 'left',
        }}>
        {t('studentListAlert')}
      </Text>

      <FlatList
        data={studentList?.data?.rows}
        contentContainerStyle={{height: hp(70)}}
        renderItem={({item, index}) => renderItem(item, index)}
        keyExtractor={item => item?.id?.toString()}
        ItemSeparatorComponent={<View style={styles.separator} />}
        ListEmptyComponent={
          <View>
            {isLoading ? (
              <TaleemLoader isLoading={isLoading} />
            ) : (
              <Text>{t('noData')}</Text>
            )}
          </View>
        }
      />
      <View style={{marginVertical: hp(4)}}>
        <PrimaryButton
          onPress={() => {
            if (validateSelection()) {
              Alert.alert(t('validSelection'), t('validSelectionMsg'));
            }
          }}
          title={t('submit')}
          style={styles.button}
          textStyle={{fontSize: 16, fontWeight: '500', color: colors.white}}
        />
      </View>
    </View>
  );
};

export default AllMembers;

const styles = StyleSheet.create({
  card: {
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 8,
    padding: 8,
    borderRadius: 10,
  },
  img: {
    height: 38,
    width: 38,
    borderRadius: 8,
    resizeMode: 'center',
  },
  name: {
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    color: colors.black,
  },
  grade: {
    fontFamily: Fonts.regular,
    color: colors.txtGrey1,
    fontSize: responsiveFontSize(12),
  },
  nameContainer: {
    marginLeft: 8,
  },
  separator: {
    borderWidth: 0.4,
    borderColor: colors.greyLight,
  },
  selected: {
    height: 20,
    width: 20,
    alignSelf: 'center',
  },
  innerRow: {
    alignItems: 'center',
  },
  submitButton: {
    marginTop: 16,
    padding: 12,
    backgroundColor: colors.primary,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: colors.white,
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
  },
});
