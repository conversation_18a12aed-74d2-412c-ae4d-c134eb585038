import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';
import {responsiveFontSize} from '../Utils/constant';

const FileUploadBox = ({onBrowsePress}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.uploadContainer}>
      <Image
        source={icons.upload}
        style={styles.uploadIcon}
        resizeMode="contain"
      />
      <Text style={styles.uploadText}>{t('dropFilesToUpload')}</Text>
      <Text style={styles.fileTypes}>{t('fileTypes')}</Text>
      <TouchableOpacity style={styles.browseButton} onPress={onBrowsePress}>
        <Text style={styles.browseButtonText}>{t('browse')}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  uploadContainer: {
    borderStyle: 'dashed',
    borderWidth: 2,
    borderColor: colors.themeColor,
    borderRadius: 10,
    paddingVertical: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
    marginBottom: 16,
  },
  uploadIcon: {
    width: 32,
    height: 32,
    marginBottom: 5,
    tintColor: colors.themeColor,
  },
  uploadText: {
    fontSize: responsiveFontSize(16),
    color: colors.black,
    marginBottom: 8,
    fontFamily: Fonts.medium,
  },
  fileTypes: {
    fontSize: responsiveFontSize(12),
    color: colors.lightGrey,
    marginBottom: 20,
    fontFamily: Fonts.regular,
  },
  browseButton: {
    borderWidth: 1,
    borderColor: colors.themeColor,
    borderRadius: 8,
    paddingVertical: 2,
    paddingHorizontal: 20,
  },
  browseButtonText: {
    fontSize: responsiveFontSize(16),
    color: colors.themeColor,
    fontFamily: Fonts.medium,
  },
});

export default FileUploadBox;
