import {useState} from 'react';

import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';

export function ToggleButton({setPaymentType, paymentType}) {
  const {t} = useTranslation();
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          paymentType === 'split' ? styles.activeButton : styles.inactiveButton,
        ]}
        onPress={() => setPaymentType('split')}>
        <Text
          style={[
            styles.text,
            paymentType === 'split' ? styles.activeText : styles.inactiveText,
          ]}>
          {t('Split')}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.button,
          paymentType === 'organizer'
            ? styles.activeButton
            : styles.inactiveButton,
        ]}
        onPress={() => setPaymentType('organizer')}>
        <Text
          style={[
            styles.text,
            paymentType === 'organizer'
              ? styles.activeText
              : styles.inactiveText,
          ]}>
          {t('Full')}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: colors.themeBackground,
    borderRadius: 20,
    overflow: 'hidden',
    width: wp(30),
    height: hp(3.4),
  },
  button: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    height: hp(3.4),
  },
  activeButton: {
    backgroundColor: colors.themeBackground,
  },
  inactiveButton: {
    backgroundColor: '#fff',
  },
  text: {
    fontSize: fp(1.49),
    fontFamily: Fonts.medium,
  },
  activeText: {
    color: '#fff',
  },
  inactiveText: {
    color: colors.themeBackground,
  },
});
