import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {View, Text, TouchableOpacity} from 'react-native';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import {wp} from '../Helper/ResponsiveDimensions';

const StudentOrProfessional = ({onStudentOrProfessional}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [selectedOption, setSelectedOption] = useState('student'); // Default selection

  const handleOptionSelect = option => {
    setSelectedOption(option);
    if (onStudentOrProfessional) {
      onStudentOrProfessional(option); // Invoke callback with the selected option
    }
  };
  useEffect(() => {
    if (onStudentOrProfessional) {
      onStudentOrProfessional('student'); // Default to student
    }
  }, []);

  return (
    <View>
      <Text
        style={{
          fontSize: 16,
          marginBottom: 10,
          color: colors.black,
          fontFamily: Fonts.medium,
          textAlign: isRTL ? 'right' : 'left',
        }}>
        {t('selectOne')}
      </Text>
      <View
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          marginBottom: 20,
        }}>
        <TouchableOpacity
          onPress={() => handleOptionSelect('student')}
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 18,
            paddingHorizontal: 25,
            borderRadius: 8,
            backgroundColor:
              selectedOption === 'student'
                ? colors.lightthemeColor
                : colors.white,
            borderWidth: 1,
            borderColor:
              selectedOption === 'student'
                ? colors.themeColor
                : colors.lightgreay,
            marginRight: isRTL ? 0 : 10,
            marginLeft: isRTL ? 10 : 0,
          }}
          accessibilityLabel="Student Button">
          <Text
            style={{
              fontFamily: Fonts.medium,
              // letterSpacing: wp(1),
              color:
                selectedOption === 'student'
                  ? colors.themeColor
                  : colors.txtGrey1,
            }}>
            {t('student')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => handleOptionSelect('professional')}
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 18,
            paddingHorizontal: 25,
            borderRadius: 10,
            backgroundColor:
              selectedOption === 'professional'
                ? colors.lightthemeColor
                : colors.white,
            borderWidth: 1,
            borderColor:
              selectedOption === 'professional'
                ? colors.themeColor
                : colors.lightgreay,
          }}
          accessibilityLabel="Professional Button">
          <Text
            style={{
              fontFamily: Fonts.medium,
              color:
                selectedOption === 'professional'
                  ? colors.themeColor
                  : colors.txtGrey1,
            }}>
            {t('professional')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default StudentOrProfessional;
