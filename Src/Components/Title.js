import {View, Text, StyleSheet} from 'react-native';
import React from 'react';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import {responsiveFontSize} from '../Utils/constant';
import {wp} from '../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';

export const Title = ({text, style}) => {
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <Text
      style={[styles.titletext, style, {textAlign: isRTL ? 'right' : 'left'}]}>
      {text}
    </Text>
  );
};

export const SubTitle = ({text, style}) => {
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <Text
      style={[
        styles.subTitle,
        style,
        {
          textAlign: isRTL ? 'right' : 'left',
          marginLeft: isRTL ? 0 : 20,
          marginRight: isRTL ? 20 : 0,
        },
      ]}>
      {text}
    </Text>
  );
};

const styles = StyleSheet.create({
  subTitle: {
    fontSize: responsiveFontSize(14),
    color: colors.txtGrey1,
    marginBottom: 30,
    fontFamily: Fonts.medium,
  },
  titletext: {
    marginTop: 20,
    marginHorizontal: wp(5),
    fontSize: responsiveFontSize(22),
    color: colors.darkBlack,
    fontFamily: Fonts.bold,
  },
  timerView: {
    marginVertical: 30,
    width: '90%',
    alignSelf: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
