import AsyncStorage from '@react-native-async-storage/async-storage';
import {SELECTED_LANG} from '../Utils/storageKeys';
import {useSelector} from 'react-redux';

export const changeLang = async () => {
  // const result = await AsyncStorage.getItem(SELECTED_LANG);
  const {appLocale} = useSelector(state => state?.auth);

  console.log('result is:---', appLocale);
  // const resultParse = await JSON.parse(result);
  return appLocale;
};
