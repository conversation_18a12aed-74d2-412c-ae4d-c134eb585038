import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import {fp, hp} from '../../Helper/ResponsiveDimensions';

const LinearGradView = ({children}) => {
  return (
    <LinearGradient
      colors={['#C6FFC9', '#D4EBFF']}
      start={{
        x: Math.sin((0 * Math.PI) / 180),
        y: -Math.cos((100 * Math.PI) / 180),
      }}
      end={{
        x: Math.sin((50 * Math.PI) / 180),
        y: -Math.cos((200 * Math.PI) / 180),
      }}
      style={{
        // height: 150,
        // width: 200,
        borderRadius: 18,
        // marginVertical: hp(1),
        marginBottom: hp(1.6),
      }}>
      <View
        style={{
          borderRadius: 18,
          flex: 1,
          margin: 1.4,
          backgroundColor: '#fff',
          justifyContent: 'center',
        }}>
        {children}
      </View>
    </LinearGradient>
  );
};

export default LinearGradView;

const styles = StyleSheet.create({});
