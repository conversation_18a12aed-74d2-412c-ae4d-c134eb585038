import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import colors from '../Utils/colors';
import {useNavigation} from '@react-navigation/native';
import icons from '../Utils/icons';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';
import {fp, hp} from '../Helper/ResponsiveDimensions';

const {width, height} = Dimensions.get('window');
const aspectRatio = height / width;

// Dynamically setting baseWidth according to aspect ratio for responsive scaling
const baseWidth = width < 400 ? width : width / aspectRatio;
const scale = width / baseWidth;

const FilterHeader = ({
  onClearFilters,
  title,
  showClearButton,
  showBackButton,
  style,
  titleStyle,
  buttonStyle,
}) => {
  const navigation = useNavigation();
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  return (
    <View
      style={[
        styles.header,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
        style,
      ]}>
      {/* Back Button and Title */}
      <View
        style={[
          styles.leftContainer,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        {showBackButton && (
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}>
            <Image
              source={isRTL ? icons.rightArrowLarge : icons.backIcon}
              style={styles.backIcon}
            />
          </TouchableOpacity>
        )}
        <Text style={[styles.headerTitle, {left: isRTL ? 10 : 0}, titleStyle]}>
          {title}
        </Text>
      </View>

      {/* Clear All Button */}
      {showClearButton && (
        <TouchableOpacity
          onPress={onClearFilters}
          style={styles.clearButtonContainer}>
          <Text style={[styles.headerButton, buttonStyle]}>
            {t('clear_all')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default FilterHeader;

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    backgroundColor: colors.themeColor,
    height: hp(4.6),

    // paddingVertical: hp(2),
    width: '100%',
    paddingHorizontal: 7 * scale,
    justifyContent: 'space-between',
  },
  leftContainer: {
    alignItems: 'center',
  },
  backButton: {
    marginRight: 6 * scale,
  },
  backIcon: {
    width: 20,
    height: 20,
    tintColor: colors.white,
  },
  headerTitle: {
    fontSize: fp(2),
    color: colors.white,
    fontFamily: Fonts.medium,
  },
  clearButtonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButton: {
    fontSize: 16,
    color: colors.white,
    fontFamily: Fonts.medium,
  },
});
