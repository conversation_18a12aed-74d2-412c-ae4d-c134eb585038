import {View, Text, TouchableOpacity, Image} from 'react-native';
import React from 'react';
import icons from '../Utils/icons';
import {SCREEN_HEIGHT} from '../Utils/constant';
import colors from '../Utils/colors';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';

export const AppLogo = ({langType}) => {
  console.log('🚀 ~ AppLogo ~ langType:', langType);
  const logoSource =
    langType === 'ar'
      ? icons.logo?.generalLogoArabic
      : icons.logo?.generalLogoEng;
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <View
      style={{
        paddingLeft: langType === 'ar' ? wp(4) : wp(4),
        flexDirection: isRTL ? 'row-reverse' : 'row',
        alignItems: 'center',
        paddingRight: langType === 'ar' ? wp(4) : 0,
        // marginTop: -hp(2),
        // marginBottom: -hp(4),
      }}>
      <Image
        source={logoSource}
        resizeMode="contain"
        style={{
          width: fp(14), // Keep the width fixed
          height: fp(7), // Let the height adjust automatically
          // aspectRatio: 1.4, // Maintain the aspect ratio of the image
        }}
      />
    </View>
  );
};

export const Or = ({}) => {
  const {t} = useTranslation();
  return (
    <View
      style={{
        width: '90%',
        alignSelf: 'center',
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 20,
      }}>
      <View style={{flex: 1, height: 1, backgroundColor: colors.lightgreay}} />
      <Text style={{marginHorizontal: 10, color: colors.darkgray}}>
        {t('or_Txt')}
      </Text>
      <View style={{flex: 1, height: 1, backgroundColor: colors.lightgreay}} />
    </View>
  );
};

export const TextLink = ({
  title,
  style,
  linkTextStyle,
  isDisabled,
  linkText,
  ...props
}) => {
  return (
    <Text
      style={[
        {
          fontSize: fp(1.8),
          marginTop: SCREEN_HEIGHT / 6,
          textAlign: 'center',
          color: colors.black,
          fontFamily: Fonts.medium,
        },
        style,
      ]}>
      {title}{' '}
      <Text
        {...props}
        style={[
          {
            fontSize: fp(1.8),
            color: '#4DB6AC',
            fontFamily: Fonts.medium,
          },
          linkTextStyle,
        ]}
        disabled={isDisabled}>
        {linkText}
      </Text>
    </Text>
  );
};
