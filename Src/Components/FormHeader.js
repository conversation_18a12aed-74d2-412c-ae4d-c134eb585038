import React from 'react';
import {StyleSheet, Text, View, TouchableOpacity, Image} from 'react-native';
import colors from '../Utils/colors';
import {useNavigation} from '@react-navigation/native';
import icons from '../Utils/icons';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';
import {responsiveFontSize} from '../Utils/constant';

const FormHeader = ({
  onSkip,
  title,
  showSkipButton,
  showBackButton,
  style,
  titleStyle,
  buttonStyle,
}) => {
  const navigation = useNavigation();
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <View
      style={[
        styles.header,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
        style,
      ]}>
      {/* Back Button and Title */}
      <View
        style={[
          styles.leftContainer,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        {showBackButton && (
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}>
            <Image
              source={isRTL ? icons.rightArrowLarge : icons.backIcon}
              style={styles.backIcon}
            />
          </TouchableOpacity>
        )}
        <Text
          style={[
            styles.headerTitle,
            {marginRight: isRTL ? 10 : 0},
            titleStyle,
          ]}>
          {title}
        </Text>
      </View>

      {/* Skip Button */}
      {showSkipButton && (
        <TouchableOpacity onPress={onSkip} style={styles.skipButtonContainer}>
          <Text style={[styles.skipButton, buttonStyle]}>{t('skip')}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default FormHeader;

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    height: 70,
    paddingHorizontal: 16,
  },
  leftContainer: {
    alignItems: 'center',
  },
  backButton: {
    marginRight: 8,
  },
  backIcon: {
    width: 20,
    height: 20,
    tintColor: colors.black,
  },
  headerTitle: {
    fontSize: responsiveFontSize(18),
    color: colors.black,
    fontFamily: Fonts.bold,
  },
  skipButtonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  skipButton: {
    fontSize: responsiveFontSize(16),
    color: colors.black,
    fontFamily: Fonts.medium,
  },
});
