import React, {useEffect, useRef} from 'react';
import {View, Animated, StyleSheet, Image} from 'react-native';
import icons from '../Utils/icons';
import colors from '../Utils/colors';
import Video from 'react-native-video';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';

const MyLottieComponent = () => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const slideAnim = useRef(new Animated.Value(-100)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 2000,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim, slideAnim]);

  return (
    <View style={styles.container}>
      {/* Background Video */}
      <Video
        source={icons?.video?.splashVideo} // Can be a URL or a local file.
        resizeMode="cover"
        style={styles.backgroundVideo}
      />

      {/* Overlay Logo */}
      <Image
        source={icons.logo?.splashLogo}
        resizeMode="contain"
        style={styles.logoOverlay}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundVideo: {
    height: hp(100),
    width: wp(100),
    position: 'absolute', // Ensures the video fills the screen
  },
  logoOverlay: {
    // position: 'absolute', // Overlay the logo on top of the video
    // top: '50%', // Center vertically
    // left: '50%', // Center horizontally
    // transform: [{translateX: -fp(12)}, {translateY: -fp(16)}], // Adjust for the logo's size (assuming 150x150)
    width: fp(28),
    height: fp(28),
    alignSelf: 'center',
    // top: 10,
    // bottom: 0,
  },
});

export default MyLottieComponent;
