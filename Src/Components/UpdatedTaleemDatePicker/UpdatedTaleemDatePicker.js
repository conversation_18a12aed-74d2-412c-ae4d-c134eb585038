import {
  Modal,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import icons from '../../Utils/icons';
import {Image} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker'; // Replace DateTimePicker with this
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import moment from 'moment';

export const UpdatedTaleemDatePicker = ({
  text = 'Select time',
  onToTimeChange, // Callback for 'toTime'
  onFromTimeChange, // Callback for 'fromTime'
  minuteInterval,
  width = wp(24),
  paddingHorizontal = 20,
  paddingVertical = 14,
  time,
  onRealTimeChange,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [toTime, setToTime] = useState('');
  const [fromTime, setFromTime] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleTimeChange = (time, type) => {
    if (type === 'from') {
      setFromTime(time);
      if (onFromTimeChange) {
        onFromTimeChange(time); // Passing fromTime to parent
      }
    } else {
      setToTime(time);
      if (onToTimeChange) {
        onToTimeChange(time); // Passing toTime to parent
      }
    }
  };

  const handleConfirm = selectedDate => {
    setShowDatePicker(false);
    if (selectedDate) {
      handleTimeChange(selectedDate, 'to'); // Update toTime
    }
  };

  const hideDatePicker = () => {
    setShowDatePicker(false);
  };

  return (
    <View
      style={[
        {
          paddingHorizontal: paddingHorizontal,
          paddingVertical: paddingVertical,
        },
      ]}>
      <Text
        style={[styles.sectionTitle, {textAlign: isRTL ? 'right' : 'left'}]}>
        {text}
      </Text>
      <TouchableOpacity
        onPress={() => setShowDatePicker(true)}
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          alignItems: 'center',
          borderWidth: 1,
          borderColor: '#ddd',
          paddingVertical: hp(0.6),
          paddingHorizontal: 15,
          borderRadius: 10,
        }}>
        <Image
          source={icons.calanderImage}
          style={{
            height: 20,
            width: 20,
            marginRight: isRTL ? 0 : 10,
            marginLeft: isRTL ? 10 : 0,
          }}
        />
        <View style={{width: width, paddingVertical: hp(1)}}>
          <Text
            style={{
              fontSize: fp(1.6),
              color: toTime ? colors.black : colors.lightGrey,
              fontFamily: Fonts.medium,
              textAlignVertical: 'center',
              textAlign: isRTL ? 'right' : 'left',
            }}>
            {toTime
              ? moment(toTime).format('hh:mm A')
              : time
              ? moment(time, 'HH:mm:ss').format('hh:mm A')
              : text}
          </Text>
        </View>
      </TouchableOpacity>

      {/* DateTimePickerModal */}
      <DateTimePickerModal
        isVisible={showDatePicker}
        mode="time" // Set mode to 'time'
        onConfirm={handleConfirm}
        onCancel={hideDatePicker}
        is24Hour={true} // Use 24-hour format
        minuteInterval={minuteInterval} // Set minute interval
        onChange={item => {
          console.log(item, 'select date54489489849849');
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  sectionContainer: {},
  sectionTitle: {
    fontFamily: Fonts.medium,
    color: colors.black,
    fontSize: fp(1.6),
    marginBottom: 8,
  },
});

export default UpdatedTaleemDatePicker;
