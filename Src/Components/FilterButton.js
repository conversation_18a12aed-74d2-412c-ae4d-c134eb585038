import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity} from 'react-native';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {Fonts} from '../Utils/Fonts';

export const FilterButton = ({style, textStyle, title, ...props}) => {
  return (
    <TouchableOpacity
      {...props}
      style={[styles.button, style]}
      activeOpacity={0.9}
      accessible={true}
      accessibilityLabel={title}
      accessibilityRole="button">
      <Text style={[styles.text, textStyle]}>{title}</Text>
      <Image source={icons.editIcon} style={styles.iconImage} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    position: 'absolute',
    bottom: 20, // Position the button 20 pixels from the bottom
    alignSelf: 'center',
    width: '33%',
    height: 50,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
    borderRadius: 40,
    backgroundColor: colors.themeColor,
    borderColor: colors.white,
    borderWidth: 5,
  },
  text: {
    fontSize: 20,
    color: colors.white,
    fontFamily: Fonts.regular,
  },
  iconImage: {
    width: 15,
    height: 15,
    marginLeft: 10,
  },
});

export default FilterButton;
