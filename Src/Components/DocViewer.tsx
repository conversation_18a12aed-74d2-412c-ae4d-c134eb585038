import {
  I18nManager,
  Image,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Alert,
} from 'react-native';
import React from 'react';
import {StatusContainer} from './StatusBar';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {AppHeader} from '../Components/HeaderForLogin';

import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';
import {Fonts} from '../Utils/Fonts';
import {useSelector} from 'react-redux';
import {AppLogo} from './Rest';
import {Title} from './Title';
import {IMAGE_BASE_URL} from '../Utils/getBaseUrl';
import Pdf from 'react-native-pdf';
import RNFS from 'react-native-fs';
import {PrimaryButton} from './CustomButton';

const DocViewer = ({navigation, route}) => {
  const {doc} = route?.params;
  console.log('🚀 ~ DocViewer ~ doc:', doc);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const downloadPDF = async () => {
    try {
      const fileUrl = `${IMAGE_BASE_URL}${doc.uri}`;
      const fileName = `downloaded_document.pdf`;
      const downloadDest = `${RNFS.DownloadDirectoryPath}/${fileName}`;

      const options = {
        fromUrl: fileUrl,
        toFile: downloadDest,
        background: true,
      };

      const downloadResult = await RNFS.downloadFile(options).promise;

      if (downloadResult.statusCode === 200) {
        Alert.alert('Success', `PDF saved to: ${downloadDest}`);
      } else {
        Alert.alert('Error', 'Failed to download the PDF');
      }
    } catch (error) {
      console.log('Download Error:', error);
      Alert.alert('Error', 'Download failed');
    }
  };

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => navigation.goBack()}
        style={[
          {
            // height: 30,
            // width: 30,
            flexDirection: isRTL ? 'row-reverse' : 'row',
            // justifyContent: 'center',
            alignItems: 'center',
            marginLeft: wp(4),
            marginTop: hp(2),
            paddingBottom: hp(1),
          },
        ]}>
        <Image
          resizeMode="contain"
          style={[
            {
              height: fp(3),
              width: fp(3),
              transform: isRTL ? [{rotateY: '180deg'}] : [],
            },
          ]}
          source={icons.backIcon}
        />
        <Text
          style={{
            textAlign: I18nManager.isRTL ? 'right' : 'left',
            fontFamily: Fonts.bold,
            fontSize: fp(2.2),
            marginLeft: wp(5),
            color: colors.black,
          }}>
          {t('document')}
        </Text>
      </TouchableOpacity>
      {doc?.type == 'application/pdf' ? (
        <>
          <Pdf
            trustAllCerts={false}
            source={{
              uri: `${IMAGE_BASE_URL + doc.uri}`,
            }}
            onLoadComplete={(numberOfPages, filePath) => {
              console.log(`Number of pages: ${numberOfPages}`);
            }}
            onPageChanged={(page, numberOfPages) => {
              console.log(`Current page: ${page}`);
            }}
            onError={error => {
              console.log(error);
            }}
            onPressLink={uri => {
              console.log(`Link pressed: ${uri}`);
            }}
            style={{
              // marginLeft: wp(10),
              // backgroundColor: 'white',
              flex: 1,
              // borderColor: 'black',
              // borderWidth: 1,
              // height: hp(60),
              // width: wp(90),
              // paddingHorizontal: wp(2),
            }}
          />
          <PrimaryButton
            title={t('download_pdf')}
            onPress={downloadPDF}
            style={{marginVertical: 10}}
          />
        </>
      ) : (
        <Image
          source={{uri: `${IMAGE_BASE_URL}${doc?.uri}`}}
          style={{height: hp(90), width: wp(100), marginHorizontal: wp(2)}}
        />
      )}
    </SafeAreaView>
  );
};

export default DocViewer;

const styles = StyleSheet.create({
  headerContainer: {
    // backgroundColor: colors.themeColor,
    // paddingBottom: 20,
    paddingHorizontal: 5,
    justifyContent: 'center',
    // alignItems: 'center',
  },
  downloadButton: {
    backgroundColor: colors.green,
    padding: hp(1.5),
    margin: wp(5),
    borderRadius: 10,
    alignItems: 'center',
  },
  downloadText: {
    color: '#fff',
    fontSize: fp(2),
    fontFamily: Fonts.bold,
  },
});
