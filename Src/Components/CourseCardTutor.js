import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect} from 'react';
import colors from '../Utils/colors';
import LinearGradient from 'react-native-linear-gradient';
import {useTranslation} from 'react-i18next';
import {PrimaryButton} from './CustomButton';
import {Fonts} from '../Utils/Fonts';
import {responsiveFontSize} from '../Utils/constant';
import TaleemBadge from './Custom_Components/TaleemBadge';
import {fp, hp} from '../Helper/ResponsiveDimensions';

const CourseCardTutor = ({
  item,
  title,
  language,
  level,
  duration,
  type,
  onPressEdit,
  priceOnline,
  priceFaceToFace,
  priceOpenSession,
  commision_price_opensession,
  rate_opensession,
  commission_price_online,
  rate_online,
  commission_price_facetoface,
  rate_facetoface,
  status,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  console.log('commission_price_facetoface', commision_price_opensession);
  // Validation function for duration
  const isValidDuration = () => {
    if (duration === null || duration === undefined) return false;
    if (typeof duration === 'string' && duration.trim().toUpperCase() === 'NA')
      return false;
    if (duration === 0) return false;
    if (typeof duration === 'string' && duration.trim() === '') return false;
    return true;
  };

  return (
    <>
      <View style={styles.container}>
        {/* Title and Language Row */}
        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          <View style={[styles.titleView]}>
            <Text
              style={[styles.titleTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
              {title}
            </Text>
          </View>
          {/* {language && (
            <LinearGradient
              colors={['#C6FFC9', '#D4EBFF']}
              start={{
                x: Math.sin((0 * Math.PI) / 180),
                y: -Math.cos((100 * Math.PI) / 180),
              }}
              end={{
                x: Math.sin((50 * Math.PI) / 180),
                y: -Math.cos((200 * Math.PI) / 180),
              }}
              style={{
                padding: 5,
                borderTopLeftRadius: 10,
                borderBottomLeftRadius: 10,
              }}>
              <Text style={styles.languageTxt}>
                {language === 'NA' ? 'English' : language}
              </Text>
            </LinearGradient>
          )} */}
        </View>

        {/* Commission Badges */}
        {/* <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          {rate_online !== '0' && rate_online > 0 && (
            <TaleemBadge text={`Commission ${rate_online}%`} />
          )}
          {rate_facetoface !== '0' && rate_facetoface > 0 && (
            <TaleemBadge text={`Commission F2F ${rate_facetoface}%`} />
          )}
          {rate_opensession !== '0' && rate_facetoface > 0 && (
            <TaleemBadge text={`Commission ${rate_opensession}%`} />
          )}
        </View> */}

        <View
          style={{
            width: isRTL ? '100%' : '80%',
            marginVertical: 8,
            gap: hp(1.4),
            alignItems: isRTL ? 'flex-end' : 'flex-start',
          }}>
          <View style={{}}>
            {isValidDuration() && (
              <Text style={styles.txtSub}>
                {t('duration')}:{' '}
                <Text style={styles.detailsTxt}>{duration}</Text>
              </Text>
            )}
          </View>

          <Text style={styles.txtSub}>
            {t('price_online')} :{' '}
            <Text style={styles.detailsTxt}>
              {commission_price_online?.length
                ? `${commission_price_online} QAR`
                : 'N/A'}
            </Text>
          </Text>

          {/* Face-to-Face Price Section */}

          <Text style={styles.txtSub}>
            {t('price_for_face_to_face')} : :
            <Text style={styles.detailsTxt}>
              {commission_price_facetoface?.length
                ? `${commission_price_facetoface} QAR`
                : 'NA'}
            </Text>
          </Text>
          <Text style={styles.txtSub}>
            {t('price_for_open_session')} : :
            <Text style={styles.detailsTxt}>
              {commision_price_opensession?.length
                ? `${commision_price_opensession} QAR`
                : 'N/A'}
            </Text>
          </Text>
          <Text style={styles.txtSub}>
            {t('type')} : <Text style={styles.detailsTxt}>{type}</Text>
          </Text>
          {/* <Text style={styles.txtSub}>
            {t('level')} : <Text style={styles.detailsTxt}>{level}</Text>
          </Text> */}
          <Text style={styles.txtSub}>
            {t('status')} :{' '}
            <Text style={styles.detailsTxt}>
              {item?.status
                ? `${
                    item?.status == '0'
                      ? t('Pending')
                      : '1'
                      ? t('Approved')
                      : t('Rejected')
                  }`
                : 'N/A'}
            </Text>
          </Text>
        </View>

        {/* Action Buttons Row */}
        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          <PrimaryButton
            title={t('edit')}
            style={[styles.editBtn]}
            textStyle={styles.btnTxt}
            onPress={onPressEdit}
          />
        </View>
      </View>
      {item?.status == '0' && (
        <Text
          style={{
            fontSize: fp(1.4),
            color: colors.txtGrey1,
            fontFamily: Fonts.medium,
            lineHeight: hp(1.6),
          }}>
          {t('rateCardDiscription')}
        </Text>
      )}
    </>
  );
};

export default CourseCardTutor;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 16,
    borderWidth: 0.7,
    borderColor: colors.txtGrey,
    shadowColor: colors.black,
    shadowOpacity: 0.1,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
    elevation: 2,
    width: '100%',
    paddingVertical: 10,
    paddingLeft: 10,
    paddingRight: 10,
  },
  titleView: {
    width: '50%',
  },
  titleTxt: {
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
  },
  languageTxt: {
    fontSize: 12,
    color: colors.black,
    fontFamily: Fonts.semiBold,
  },
  row: {
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  txtSub: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.txtGrey1,
  },
  detailsTxt: {
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  editBtn: {
    backgroundColor: colors.white,
    width: '45%',
    borderWidth: 1,
    borderColor: colors.themeColor,
  },
  btnTxt: {
    fontSize: fp(2),
    fontFamily: Fonts.medium,
    color: colors.themeColor,
  },
});
