import React, {useState, useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {View, Text, TouchableOpacity, Image, StyleSheet} from 'react-native';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import icons from '../Utils/icons';
import {responsiveFontSize} from '../Utils/constant';

const GenderSelector = ({onGenderSelect}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [gender, setGender] = useState('male');

  useEffect(() => {
    if (onGenderSelect) {
      onGenderSelect('male');
    }
  }, []);

  const handleGenderSelect = selectedGender => {
    setGender(selectedGender);
    if (onGenderSelect) {
      onGenderSelect(selectedGender);
    }
  };

  return (
    <View>
      <Text style={[styles.lableTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
        {t('selectgender')}
      </Text>
      <View
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          marginBottom: 20,
        }}>
        <TouchableOpacity
          onPress={() => handleGenderSelect('male')}
          style={[
            styles.btnContainer,
            {
              flexDirection: isRTL ? 'row-reverse' : 'row',
              marginRight: isRTL ? 0 : 10,
              marginLeft: isRTL ? 10 : 0,
              backgroundColor:
                gender === 'male' ? colors.lightthemeColor : colors.white,

              borderColor:
                gender === 'male' ? colors.themeColor : colors.lightgreay,
            },
          ]}
          accessibilityLabel="Male Button">
          <Image source={icons.maleIcon} />
          <Text
            style={[
              styles.txt,
              {
                color: gender === 'male' ? colors.themeColor : colors.txtGrey1,
              },
            ]}>
            {t('male')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => handleGenderSelect('female')}
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 18,
            paddingHorizontal: 25,
            borderRadius: 10,
            backgroundColor:
              gender === 'female' ? colors.lightthemeColor : colors.white,
            borderWidth: 1,
            borderColor:
              gender === 'female' ? colors.themeColor : colors.lightgreay,
          }}
          accessibilityLabel="Female Button">
          <Image source={icons.femaleIcon} />
          <Text
            style={[
              styles.txt,
              {
                color:
                  gender === 'female' ? colors.themeColor : colors.txtGrey1,
              },
            ]}>
            {t('female')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default GenderSelector;

const styles = StyleSheet.create({
  txt: {
    fontFamily: Fonts.regular,
    left: 5,
    fontSize: responsiveFontSize(14),
  },
  lableTxt: {
    fontSize: 16,
    marginBottom: 10,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  btnContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 25,
    borderRadius: 8,
    borderWidth: 1,
  },
});
