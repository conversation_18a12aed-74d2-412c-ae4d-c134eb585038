import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import icons from '../Utils/icons';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import {fp} from '../Helper/ResponsiveDimensions';

const MeetingPointCard = ({title, address, onEdit, isRtl}) => {
  return (
    <View style={styles.cardContainer}>
      <View style={styles.cardContentOne}>
        <View style={styles.cardContent}>
          {/* I want to wrap titleContainer into gradient */}
          <LinearGradient
            colors={['#C6FFC9', '#D4EBFF']}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}
            style={styles.titleContainer}>
            <Text
              style={[styles.titleText, {textAlign: isRtl ? 'right' : 'left'}]}>
              {title}
            </Text>
          </LinearGradient>
          <TouchableOpacity onPress={onEdit} style={styles.editButton}>
            <Image
              resizeMode="contain"
              source={icons.pencil}
              style={styles.editIcon}
            />
          </TouchableOpacity>
        </View>
        <Text style={styles.addressText}>{address}</Text>
      </View>
    </View>
  );
};

export default MeetingPointCard;

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: '#D4EBFF',
    borderRadius: 12,
    padding: 15,
    marginBottom: 6,
  },
  cardContentOne: {
    flexDirection: 'column',
  },
  cardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  titleContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 8,
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.themeColor,
  },
  titleText: {
    color: colors.black,
    fontSize: fp(1.6),
    marginRight: 8,
    fontFamily: Fonts.semiBold, // Applied the semiBold font
  },
  addressText: {
    fontSize: fp(1.6),
    color: '#333',
    lineHeight: 20,
    fontFamily: Fonts.medium, // Applied the regular font
  },
  editButton: {
    padding: 5,
  },
  editIcon: {
    width: 20,
    height: 20,
  },
});
