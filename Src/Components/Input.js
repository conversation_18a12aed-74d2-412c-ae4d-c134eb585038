import {View, Text, TextInput} from 'react-native';
import React, {useState} from 'react';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import {fp} from '../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';
import HelperTextComponent from './HelperTipComp';

export const PrimaryInput = ({
  title,
  containerStyle,
  textInputStyle,
  lableStyle,
  inputMarginBottom = 20,
  showTooltip = false,
  helperTxt,
  ...props
}) => {
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [openHelperText, setOpenHelperText] = useState(false);
  console.log('903824779', isRTL);
  return (
    <View
      keyboardShouldPersistTaps
      style={[{width: '100%', alignSelf: 'center'}, containerStyle]}>
      {title && (
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'baseline',
            flexWrap: 'wrap',
            marginBottom: 10,
          }}>
          <Text
            style={[
              {
                fontSize: fp(1.8),
                fontFamily: Fonts.medium,

                color: colors.black,
                textAlign: isRTL ? 'right' : 'left',
              },
              lableStyle,
            ]}>
            {title}
          </Text>
          {showTooltip && (
            <HelperTextComponent
              helperText={helperTxt}
              setOpen={setOpenHelperText}
              open={openHelperText}
              borderColor={colors.black}
              iconColor={colors.black}
            />
          )}
        </View>
      )}
      <TextInput
        {...props}
        keyboardShouldPersistTaps
        style={[
          {
            alignItems: 'center',
            // height: 50,
            borderWidth: 1,
            borderColor: '#ddd',
            borderRadius: 10,
            padding: 12,
            marginBottom: inputMarginBottom,
            color: colors.txtGrey1,
            fontFamily: Fonts.medium,
            textAlign: isRTL ? 'right' : 'left',
          },
          textInputStyle,
        ]}
      />
    </View>
  );
};
