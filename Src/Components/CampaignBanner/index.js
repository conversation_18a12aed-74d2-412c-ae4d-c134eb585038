import React, {useState, useEffect, useRef} from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  FlatList,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';

const {width, height} = Dimensions.get('screen');

const CampaignBannerItem = ({item}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  return (
    <LinearGradient
      colors={['#C6FFC9', '#D4EBFF']}
      start={{
        x: Math.sin((0 * Math.PI) / 180),
        y: -Math.cos((100 * Math.PI) / 180),
      }}
      end={{
        x: Math.sin((50 * Math.PI) / 180),
        y: -Math.cos((200 * Math.PI) / 180),
      }}
      style={{
        marginHorizontal: 5,
        backgroundColor: colors.white,
        paddingVertical: 10,
        width: width * 0.9,
        padding: 16,
        borderRadius: 10,
      }}>
      <View style={styles.textContainer}>
        <View
          style={[
            styles.titleContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Text style={styles.title}>{item.title}</Text>
        </View>
        <Text style={styles.description}>{item.description}</Text>
      </View>
    </LinearGradient>
  );
};

const CampaignBanner = ({data}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef(null);

  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (currentIndex + 1) % data.length;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({
        index: nextIndex,
        animated: true,
      });
    }, 3000); // 3 seconds

    return () => clearInterval(interval);
  }, [currentIndex, data.length]);

  const onViewableItemsChanged = useRef(({viewableItems}) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
  }).current;

  return (
    <View style={styles.carouselContainer}>
      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={({item}) => <CampaignBannerItem item={item} />}
        keyExtractor={item => item.id.toString()}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onViewableItemsChanged={onViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
        getItemLayout={(data, index) => ({
          length: width * 0.9 + 10, // item width + margin
          offset: (width * 0.9 + 10) * index,
          index,
        })}
      />
      <View style={styles.pagination}>
        {data.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              index === currentIndex && styles.paginationDotActive,
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  carouselContainer: {
    marginTop: 15,
    marginHorizontal: wp(3.2),
    // height: hp(25), // Adjust based on your content
  },
  textContainer: {
    width: '100%',
  },
  title: {
    fontSize: fp(2),
    marginBottom: hp(1),
    color: colors.black,
    fontFamily: Fonts.semiBold,
    lineHeight: hp(2.4),
  },
  description: {
    fontSize: fp(1.8),
    color: colors.darkGray,
    marginBottom: hp(1),
    fontFamily: Fonts.medium,
    lineHeight: hp(2.4),
    width: wp(80),
  },
  userTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: hp(1),
  },
  userTypePill: {
    backgroundColor: colors.primary,
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginRight: 8,
    marginBottom: 5,
  },
  userTypeText: {
    color: colors.white,
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
  },
  dates: {
    fontSize: fp(1.6),
    color: colors.gray,
    fontFamily: Fonts.medium,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: hp(1),
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.themeColorDimmed,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: colors.themeColor,
    width: 12,
  },
  titleContainer: {
    justifyContent: 'space-between',
  },
});

export default CampaignBanner;
