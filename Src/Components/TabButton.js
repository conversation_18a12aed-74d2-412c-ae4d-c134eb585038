import React from 'react';
import {TouchableOpacity, Text, StyleSheet} from 'react-native';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';

const TabButton = ({title, isActive, onPress, style, textStyle}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.button,
        style,
        {
          backgroundColor: isActive ? colors.themeColor : colors.white,
        },
      ]}>
      <Text
        style={[
          styles.text,
          textStyle,
          {
            color: isActive ? colors.white : colors.txtGrey1,
          },
        ]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 7,
    paddingHorizontal: 15,
    borderRadius: 20,
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginHorizontal: 4,
    marginVertical: 8,
  },
  text: {
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
});

export default TabButton;
