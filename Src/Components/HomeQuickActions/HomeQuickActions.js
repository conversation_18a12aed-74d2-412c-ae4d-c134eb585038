import React, {useState} from 'react';
import {View, Text, StyleSheet, FlatList} from 'react-native';
import CardComponent from '../contentCard';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {useNavigation} from '@react-navigation/native';

import TutorSmallCard from '../TutorSmallCard';
import {showToast} from '../ToastHelper';
import {useDispatch, useSelector} from 'react-redux';
import {
  updateBookingFlowType,
  updateClassType,
  updateGroupStudents,
  updateMeetingTutorPoint,
  updatePackage,
  updateSlot,
} from '../../Redux/Slices/Student/TutorBookingSlice';
import {updateAddCurriculumFlowType} from '../../Redux/Slices/Student/GeneralStudentSlice';
import {hp} from '../../Helper/ResponsiveDimensions';
import {
  resetTutorBooking<PERSON>son,
  tutorBookingJson,
} from '../../Api/Model/TutorBookingModel';
import {resetSelectedSlots} from '../../Redux/Slices/Student/SlotSlice';
import HelperTextComponent from '../HelperTipComp';

const HomeQuickActions = ({
  taleemStudentAcademicDetails,
  studentType, //2 is professional
  userType,
}) => {
  console.log('🚀 ~ studentType:', studentType);
  const userData = useSelector(state => state.auth);
  console.log('🚀 ~ userData:', userData);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const handlePress = name => {
    console.log('🚀 ~ handlePress ~ name:', name);
    if (name === 'recreational') {
      Object.assign(tutorBookingJson, resetTutorBookingJson());
      dispatch(updateSlot([]));
      dispatch(updateGroupStudents([]));
      dispatch(updatePackage({}));
      dispatch(updateClassType({}));
      dispatch(resetSelectedSlots());
      dispatch(updateMeetingTutorPoint({}));
      dispatch(updateBookingFlowType('recreational'));
      navigation.navigate('ChooseCategory');
    } else if (name === 'courses') {
      Object.assign(tutorBookingJson, resetTutorBookingJson());
      dispatch(updateSlot([]));
      dispatch(updateGroupStudents([]));
      dispatch(updatePackage({}));
      dispatch(updateClassType({}));
      dispatch(resetSelectedSlots());
      dispatch(updateMeetingTutorPoint({}));
      dispatch(updateBookingFlowType('courses'));
      navigation.navigate('ChooseCategoryCourses');
    } else {
      if (studentType == '2') {
        Object.assign(tutorBookingJson, resetTutorBookingJson());
        dispatch(updateSlot([]));
        dispatch(updateGroupStudents([]));
        dispatch(updatePackage({}));
        dispatch(updateClassType({}));
        dispatch(resetSelectedSlots());
        dispatch(updateMeetingTutorPoint({}));
        dispatch(updateBookingFlowType('academic'));
        navigation.navigate('TutorList');
      } else {
        if (
          !taleemStudentAcademicDetails ||
          taleemStudentAcademicDetails.length === 0
        ) {
          if (userData?.user_type == '2') {
            dispatch(updateBookingFlowType('academic'));
            navigation.navigate('TutorList');
          } else {
            navigation.navigate('Chooseyourgrade');
            dispatch(updateAddCurriculumFlowType('academic'));
          }
        } else {
          Object.assign(tutorBookingJson, resetTutorBookingJson());
          dispatch(updateSlot([]));
          dispatch(updateGroupStudents([]));
          dispatch(updatePackage({}));
          dispatch(updateClassType({}));
          dispatch(resetSelectedSlots());
          dispatch(updateMeetingTutorPoint({}));
          dispatch(updateBookingFlowType('academic'));
          navigation.navigate('TutorList');
        }
      }

      // else if (studentType == '1') { //professional
      //   navigation.navigate('Chooseyourgrade');
      //   dispatch(updateAddCurriculumFlowType('academic'));
      // } else {
      //   Object.assign(tutorBookingJson, resetTutorBookingJson());
      //   dispatch(updateSlot([]));
      //   dispatch(updateGroupStudents([]));
      //   dispatch(updatePackage({}));
      //   dispatch(updateClassType({}));
      //   dispatch(resetSelectedSlots());
      //   dispatch(updateMeetingTutorPoint({}));
      //   dispatch(updateBookingFlowType('academic'));
      //   navigation.navigate('TutorList');
      // }
    }
  };
  const TUTORCARDDATA = [
    {
      id: 1,
      image: icons.booking,
      title: t('my_booking'),
      screen: 'My Class',
      helperTxt: t('myBookingTooltip'),
    },
    {
      id: 2,
      image: icons.rateCard,
      title: t('rate_card'),
      screen: 'RateCardScreen',
      helperTxt: t('rateCardTooltip'),
    },
    {
      id: 3,
      image: icons.schedule,
      title: t('schedule'),
      screen: 'ScheduleTutor',
      helperTxt: t('scheduleTooltip'),
    },
    {
      id: 4,
      image: icons.earn,
      title: t('earning'),
      screen: 'Wallet',
      helperTxt: t('earningBtnTooltip'),
    },
  ];
  return (
    <>
      {userType == '3' ? (
        <View style={styles.viewContent}>
          <FlatList
            data={TUTORCARDDATA}
            numColumns={2}
            columnWrapperStyle={{
              justifyContent: 'space-between',
              flexDirection: isRTL ? 'row-reverse' : 'row',
            }}
            contentContainerStyle={{marginHorizontal: 10}}
            renderItem={({item, index}) => {
              return (
                <TutorSmallCard
                  title={t(item?.title)}
                  imageUri={item.image}
                  activeOpacity={0.8}
                  halperTxt={item?.helperTxt}
                  onPress={() => {
                    if (item.screen) {
                      navigation.navigate(item.screen);
                    } else {
                      showToast('success', t('coming_soon'), 'bottom', isRTL);
                    }
                  }}
                />
              );
            }}
          />
        </View>
      ) : (
        <View style={styles.viewContent}>
          <CardComponent
            imageUrl={icons.AcademicOne}
            title={t('academic')}
            name="academic"
            handlePress={() => handlePress('academic')}
            halperTxt={t('academicTooltip')}
          />

          <CardComponent
            imageUrl={require('../../Assets/Images/recreation.png')}
            title={t('recreational')}
            handlePress={() => handlePress('recreational')}
            name="recreational"
            halperTxt={t('recreationalTooltip')}
          />

          <CardComponent
            imageUrl={icons.AllCoursesOne}
            title={t('courses')}
            name="courses"
            handlePress={() => handlePress('courses')}
            halperTxt={t('coursesTooltip')}
          />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewContent: {
    marginBottom: 50,
    marginTop: hp(2),
  },
});

export default HomeQuickActions;
