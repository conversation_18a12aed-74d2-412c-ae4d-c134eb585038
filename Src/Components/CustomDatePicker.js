import React, {useState} from 'react';
import {
  View,
  Text,
  Platform,
  Modal,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import moment from 'moment';
import icons from '../Utils/icons';
import {fp} from '../Helper/ResponsiveDimensions';
import {Fonts} from '../Utils/Fonts';
import colors from '../Utils/colors';
import {useTranslation} from 'react-i18next';

const CustomDatePicker = ({
  initialDate = new Date(),
  onDateChange,
  minimumDate,
  maximumDate,
  label,
  isRTL,
  marginBottom = 8,
  marginVertical = fp(2),
}) => {
  const [date, setDate] = useState(initialDate);
  const [showPicker, setShowPicker] = useState(false);
  const {t} = useTranslation();

  const handleConfirm = selectedDate => {
    if (selectedDate) {
      setDate(selectedDate);
      onDateChange && onDateChange(selectedDate);
    }
    setShowPicker(false);
  };

  const formatDate = date => {
    return moment(date).format('DD MMM YYYY');
  };

  return (
    <View style={[styles.container, {marginBottom: marginBottom}]}>
      {label && <Text style={styles.label}>{label}</Text>}
      <TouchableOpacity
        onPress={() => setShowPicker(true)}
        style={[
          styles.datePicker,
          {
            flexDirection: isRTL ? 'row-reverse' : 'row',
            marginVertical: marginVertical,
          },
        ]}>
        <Text style={styles.dateTxt}>{formatDate(initialDate)}</Text>
        <Image source={icons.downchevron} />
      </TouchableOpacity>

      <DateTimePickerModal
        isVisible={showPicker}
        mode="date"
        onConfirm={handleConfirm}
        onCancel={() => setShowPicker(false)}
        minimumDate={minimumDate}
        maximumDate={maximumDate}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  label: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  datePicker: {
    paddingHorizontal: fp(1.6),

    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'space-between',
    height: fp(6.4),
  },
  dateTxt: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.black,
  },
});

export default CustomDatePicker;
