import {Dimensions, Platform, StatusBar, View} from 'react-native';
import colors from '../Utils/colors';
import {SCREEN_HEIGHT} from '../Utils/constant';

export const StatusContainer = ({color}) => {
  const SCREEN_HEIGHT = Dimensions.get('window').height;
  return (
    <View
      style={{
        height:
          Platform.OS == 'ios'
            ? SCREEN_HEIGHT >= 812
              ? 60
              : 20
            : StatusBar.currentHeight,
        backgroundColor: color || colors.themeColor,
        top: 0,
        right: 0,
        left: 0,
        position: 'absolute',
      }}>
      <StatusBar
        backgroundColor={color || colors.themeColor}
        barStyle={Platform.OS === 'ios' ? 'dark-content' : 'dark-content'}
        translucent={false}
      />
    </View>
  );
};
