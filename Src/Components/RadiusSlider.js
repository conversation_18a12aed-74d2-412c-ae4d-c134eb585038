import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  PanResponder,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import {useTranslation} from 'react-i18next'; // Import translation hook
import {Fonts} from '../Utils/Fonts'; // Import custom fonts
import colors from '../Utils/colors';
import {fp, wp} from '../Helper/ResponsiveDimensions';

const {width: SCREEN_WIDTH} = Dimensions.get('window');
const SLIDER_WIDTH = wp(80);
const THUMB_SIZE = fp(1.8);

const RadiusSlider = ({
  title = 'Booking Preference Radius',
  initialValue = 60,
  min = 0,
  max = 120,
  onValueChange,
  activeTrackColor = colors.themeColor,
  gradientColors = ['#C6FFC9', '#D4EBFF'],
  trackWidth = '100%',
}) => {
  const {t, i18n} = useTranslation(); // Initialize translation
  const [value, setValue] = useState(initialValue);
  const [valueKm, setValueKm] = useState(initialValue); // Match initialValue directly
  const pan = useRef(new Animated.ValueXY()).current;
  const [resolvedTrackWidth, setResolvedTrackWidth] = useState(SLIDER_WIDTH); // Default to full width
  const isRTL = i18n.language === 'ar';
  // Calculate resolved track width from percentage or fixed value
  useEffect(() => {
    if (typeof trackWidth === 'string' && trackWidth.endsWith('%')) {
      const percentage = parseFloat(trackWidth) / 100;
      setResolvedTrackWidth(SLIDER_WIDTH * percentage);
    } else if (typeof trackWidth === 'number') {
      setResolvedTrackWidth(trackWidth);
    } else {
      setResolvedTrackWidth(SLIDER_WIDTH);
    }
  }, [trackWidth]);

  // Calculate initial position
  const initialX = ((initialValue - min) / (max - min)) * resolvedTrackWidth;

  useEffect(() => {
    pan.setValue({x: initialX, y: 0});
    setValueKm(initialValue);
  }, [initialX, initialValue, pan]);

  useEffect(() => {
    setValueKm(value);
  }, [value]);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gesture) => {
        let newX = gesture.moveX - 30;
        if (newX < 0) newX = 0;
        if (newX > resolvedTrackWidth) newX = resolvedTrackWidth; // Clamp to track width
        pan.x.setValue(newX);
        const newValue = Math.round(
          (newX / resolvedTrackWidth) * (max - min) + min,
        );
        setValue(newValue);
        if (onValueChange) onValueChange(newValue);
      },
    }),
  ).current;

  return (
    <LinearGradient
      colors={gradientColors}
      start={{
        x: Math.sin((0 * Math.PI) / 180),
        y: -Math.cos((100 * Math.PI) / 180),
      }}
      end={{
        x: Math.sin((50 * Math.PI) / 180),
        y: -Math.cos((200 * Math.PI) / 180),
      }}
      style={styles.gradientContainer}>
      <View style={[styles.container]}>
        <Text
          style={[
            styles.title,
            {
              // textAlign: isRTL ? 'right' : 'left',
              alignSelf: isRTL ? 'flex-end' : 'flex-start',
            },
          ]}>
          {t(title)}
        </Text>
        <Text style={styles.value}>{valueKm} km</Text>
        <View style={[styles.sliderContainer, {width: resolvedTrackWidth}]}>
          <View style={[styles.track, {width: resolvedTrackWidth}]} />
          <Animated.View
            style={[
              styles.activeTrack,
              {
                width: pan.x.interpolate({
                  inputRange: [0, resolvedTrackWidth],
                  outputRange: [0, resolvedTrackWidth],
                  extrapolate: 'clamp',
                }),
                backgroundColor: activeTrackColor,
              },
            ]}
          />
          <Animated.View
            style={[
              styles.thumb,
              {
                transform: [{translateX: pan.x}],
                backgroundColor: activeTrackColor,
              },
            ]}
            {...panResponder.panHandlers}
          />
        </View>
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{min} Km</Text>
          <Text style={styles.label}>{max} Km</Text>
        </View>
      </View>
    </LinearGradient>
  );
};

RadiusSlider.propTypes = {
  title: PropTypes.string,
  initialValue: PropTypes.number,
  min: PropTypes.number,
  max: PropTypes.number,
  onValueChange: PropTypes.func,
  activeTrackColor: PropTypes.string,
  gradientColors: PropTypes.arrayOf(PropTypes.string),
};

RadiusSlider.defaultProps = {
  title: 'Booking Preference Radius',
  initialValue: 60,
  min: 0,
  max: 120,
  onValueChange: () => {},
  activeTrackColor: colors.themeColor,
  gradientColors: ['#C6FFC9', '#D4EBFF'],
};

const styles = StyleSheet.create({
  gradientContainer: {
    paddingHorizontal: 10,
    paddingVertical: 15,
    borderRadius: 12,
    width: '100%',
  },
  container: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    width: '100%',
  },
  title: {
    fontSize: fp(1.8),
    color: colors.offBlack,
    marginBottom: 8,
    fontFamily: Fonts.semiBold, // Apply custom font
  },
  value: {
    fontSize: fp(1.6),
    color: '#333',
    alignSelf: 'center',
    marginBottom: 1,
    fontFamily: Fonts.semiBold, // Apply custom font
  },
  sliderContainer: {
    width: SLIDER_WIDTH,
    height: 40,
    justifyContent: 'center',
    alignContent: 'center',
    alignSelf: 'center',
  },
  track: {
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    position: 'absolute',
  },
  activeTrack: {
    height: 4,
    borderRadius: 2,
    position: 'absolute',
  },
  thumb: {
    width: THUMB_SIZE,
    height: THUMB_SIZE,
    borderRadius: THUMB_SIZE / 2,
    position: 'absolute',
    top: (40 - THUMB_SIZE) / 2,
    left: -THUMB_SIZE / 2,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  labelContainer: {
    width: '100%',
    marginTop: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  label: {
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium, // Apply custom font
  },
});

export default RadiusSlider;
