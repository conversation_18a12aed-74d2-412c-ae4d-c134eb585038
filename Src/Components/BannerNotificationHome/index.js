import {StyleSheet, Text, View, FlatList} from 'react-native';
import React, {useState, useRef} from 'react';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import colors from '../../Utils/colors';
import moment from 'moment';
import {useNavigation} from '@react-navigation/native';
import {PrimaryButton} from '../CustomButton';
import {useTranslation} from 'react-i18next';

const BannerNotificationHomeTutor = ({data}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigation = useNavigation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef(null);

  const onViewableItemsChanged = useRef(({viewableItems}) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
  }).current;

  const renderNotificationItem = ({item}) => (
    <View style={styles.notificationItem}>
      <Text
        style={{
          fontSize: fp(2),
          fontFamily: Fonts.medium,
          color: colors.black,
          paddingVertical: hp(0.6),
          lineHeight: hp(2.4),
          textAlign: isRTL ? 'right' : 'left',
        }}>
        {item?.title}
      </Text>
      <Text
        style={{
          fontSize: fp(1.6),
          fontFamily: Fonts.medium,
          color: colors.black,
          lineHeight: hp(2),
          textAlign: isRTL ? 'right' : 'left',
        }}>
        {item?.notification}
      </Text>
      <Text
        style={{
          fontSize: fp(1.4),
          fontFamily: Fonts.medium,
          color: colors.greyLight,
          lineHeight: hp(2),
          marginTop: hp(1),
          textAlign: isRTL ? 'right' : 'left',
        }}>
        {moment(item?.createdAt).format('DD MMM, YYYY, hh:mm A')}
      </Text>
      <View
        style={{
          alignSelf: isRTL ? 'flex-end' : 'flex-start',
          marginTop: hp(2),
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <PrimaryButton
          title={t('view')}
          style={{width: wp(30), height: fp(4)}}
          textStyle={{
            fontSize: fp(1.8),
            fontFamily: Fonts.semiBold,
          }}
          onPress={() => {
            navigation.navigate('BookingDetailsTutor', {
              bookingId: item?.reference_id,
            });
          }}
        />
      </View>
    </View>
  );

  return (
    <View style={styles.carouselContainer}>
      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={renderNotificationItem}
        keyExtractor={item => item.id.toString()}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onViewableItemsChanged={onViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
        getItemLayout={(data, index) => ({
          length: wp(90) + 10, // item width + margin
          offset: (wp(90) + 10) * index,
          index,
        })}
      />
      {data.length > 1 && (
        <View style={styles.pagination}>
          {data.map((_, index) => (
            <View
              key={index}
              style={[
                styles.paginationDot,
                index === currentIndex && styles.paginationDotActive,
              ]}
            />
          ))}
        </View>
      )}
    </View>
  );
};

export default BannerNotificationHomeTutor;

const styles = StyleSheet.create({
  carouselContainer: {
    width: wp(100),
    alignItems: 'center',
  },
  notificationItem: {
    ...applyShadowStyleIos({
      borderWidth: fp(0.1),
      width: wp(90),
      borderColor: colors.lightisGrey,
      paddingHorizontal: wp(3),
      paddingVertical: hp(1),
      borderRadius: fp(1),
      backgroundColor: colors.white,
      marginHorizontal: wp(5),
      marginVertical: hp(2),
      alignSelf: 'center',
    }),
  },
  pagination: {
    flexDirection: 'row',
    // position: 'absolute',
    // bottom: hp(4),
    alignSelf: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.themeColorDimmed,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: colors.themeColor,
  },
});
