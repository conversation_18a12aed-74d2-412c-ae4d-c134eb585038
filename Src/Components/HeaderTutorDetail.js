import React from 'react';
import {Image, Text, TouchableOpacity, View, Share} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {normalize} from '../Helper/NormalizeFont';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {Fonts} from '../Utils/Fonts';

export const HeaderTutorDetail = ({
  backIcon,
  title,
  isBackBtn,
  share,
  isWhite,
  style,
  isRTL,
}) => {
  const navigation = useNavigation();

  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: 'Check out this amazing tutor on our platform!',
        url: 'https://www.yourwebsite.com/tutor-detail',
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('Shared with activity type: ', result.activityType);
        } else {
          console.log('Shared successfully!');
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('Share dismissed');
      }
    } catch (error) {
      console.error('Error sharing:', error.message);
    }
  };

  return (
    <View
      style={[
        {
          flexDirection: isRTL ? 'row-reverse' : 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: isWhite ? colors.white : colors.themeColor,
          height: 50,
          width: '100%',
        },
        style,
      ]}>
      <View
        style={{
          width: '13%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {isBackBtn && (
          <IconBtn
            icon={backIcon}
            onPress={() => navigation.goBack()}
            iconStyle={{tintColor: isWhite ? colors.black : colors.white}}
          />
        )}
      </View>

      <View
        style={{
          width: '74%',
          height: '100%',
          justifyContent: 'center',
          alignItems: isRTL ? 'flex-end' : 'flex-start',
        }}>
        <Text
          style={{
            fontSize: normalize(7),
            color: isWhite ? colors.black : colors.white,
            fontFamily: Fonts.medium,
          }}>
          {title}
        </Text>
      </View>

      <View
        style={{
          width: '13%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {share && (
          <ShareIcon
            onPress={handleShare}
            iconStyle={{tintColor: isWhite ? colors.black : colors.white}}
          />
        )}
      </View>
    </View>
  );
};

const ShareIcon = props => {
  const {style, iconStyle} = props;
  return (
    <TouchableOpacity
      {...props}
      activeOpacity={0.8}
      style={[
        {
          height: 30,
          width: 30,
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      ]}>
      <Image
        resizeMode="contain"
        style={[
          {
            height: 18,
            width: 18,
          },
          iconStyle,
        ]}
        source={icons.shareIcon}
      />
    </TouchableOpacity>
  );
};

const IconBtn = props => {
  const {icon, style, iconStyle} = props;
  return (
    <TouchableOpacity
      {...props}
      activeOpacity={0.8}
      style={[
        {
          height: 30,
          width: 30,
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      ]}>
      <Image
        resizeMode="contain"
        style={[
          {
            height: 18,
            width: 18,
          },
          iconStyle,
        ]}
        source={icon}
      />
    </TouchableOpacity>
  );
};
