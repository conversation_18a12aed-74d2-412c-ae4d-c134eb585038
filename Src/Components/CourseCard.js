import React from 'react';
import {View, Text, Image, StyleSheet, Pressable} from 'react-native';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import {applyShadowStyleIos} from '../Helper/ShadowStyleIos';
import {fp, hp} from '../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';

const CourseCard = ({
  title,
  level,
  type,
  duration,
  price,
  rating,
  icon,
  onPress,
  imgUrl,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  console.log('onPressonPressonPress:::::', onPress);
  return (
    <Pressable onPress={onPress} style={styles.container}>
      <View style={styles.imageContainer}>
        <Image source={imgUrl} style={styles.courseImage} />
        {/* <View style={styles.ratingContainer}>
          <Image source={icons.star} style={styles.starIcon} />
          <Text style={styles.ratingText}>{rating}</Text>
        </View> */}
      </View>

      <View style={styles.detailsContainer}>
        <Text style={[styles.title, {textAlign: isRTL ? 'right' : 'left'}]}>
          {title}
        </Text>

        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          {/* <View
            style={[
              styles.leftColumn,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
              {t('level')} :
            </Text>
            <Text style={[styles.value, {textAlign: isRTL ? 'right' : 'left'}]}>
              {level}
            </Text>
          </View> */}
          <View style={styles.rightColumn}>
            <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
              {t('duration')} :{' '}
              <Text
                style={[styles.value, {textAlign: isRTL ? 'right' : 'left'}]}>
                {duration} Hour
              </Text>
            </Text>
          </View>
        </View>

        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          {/* <View
            style={[
              styles.leftColumn,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
              {t('type')} :
            </Text>
            <Text style={[styles.value, {textAlign: isRTL ? 'right' : 'left'}]}>
              {type}
            </Text>
          </View> */}
          <View style={styles.rightColumn}>
            <Text style={[styles.label, {textAlign: isRTL ? 'right' : 'left'}]}>
              {t('price')} :{' '}
              <Text
                style={[styles.value, {textAlign: isRTL ? 'right' : 'left'}]}>
                {price}
              </Text>
            </Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: applyShadowStyleIos({
    backgroundColor: colors.white,
    borderRadius: 10,
    marginBottom: 16,
    elevation: 3,
    width: '100%',
  }),
  imageContainer: {
    position: 'relative',
  },
  courseImage: {
    width: '100%',
    height: hp(20),
    borderRadius: 10,
  },
  ratingContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingVertical: 4,
    paddingHorizontal: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  starIcon: {
    width: 15,
    height: 15,
    marginRight: 4,
  },
  ratingText: {
    fontSize: 14,
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  detailsContainer: {
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: fp(2),
    color: colors.black,
    marginBottom: 8,
    fontFamily: Fonts.bold,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  leftColumn: {
    alignItems: 'center',
  },
  rightColumn: {
    alignItems: 'center',
    marginTop: hp(0.4),
  },
  label: {
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    marginRight: 4,
    fontFamily: Fonts.medium,
  },
  value: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
});

export default CourseCard;
