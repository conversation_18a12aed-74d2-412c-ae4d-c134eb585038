import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import CountryPicker from 'react-native-country-picker-modal';
import colors from '../Utils/colors';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';
import {responsiveFontSize} from '../Utils/constant';
import {fp, hp} from '../Helper/ResponsiveDimensions';

const MobileNumberInput = ({
  title,
  value,
  onChange,
  containerStyle,
  onCodeChange,
  onCountryCodeChange,
  countryCodeProp = 'QA',
  callingCodeProp = '974',
  width = '90%',
  titleStyle,
  ...props
}) => {
  const [countryCode, setCountryCode] = useState(countryCodeProp);
  const [callingCode, setCallingCode] = useState(callingCodeProp);
  const [isPickerVisible, setIsPickerVisible] = useState(false);

  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const openCountryPicker = () => {
    setIsPickerVisible(true);
  };

  return (
    <View
      keyboardShouldPersistTaps
      style={[
        {
          // width: '90%',
          width: width,
          alignSelf: 'center',
          justifyContent: 'center',
          marginBottom: 20,
          textAlign: isRTL ? 'right' : 'left',
        },
        containerStyle,
      ]}>
      <Text
        style={[
          styles.title,
          titleStyle,
          {textAlign: isRTL ? 'right' : 'left'},
        ]}>
        {title}
      </Text>
      <View
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          alignSelf: 'center',
        }}>
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.countryPicker}
          onPress={openCountryPicker}>
          <CountryPicker
            countryCode={countryCode}
            withCallingCode
            withFlag
            withFilter
            withEmoji={false}
            // withAlphaFilter
            onSelect={country => {
              setCountryCode(country.cca2);
              const selectedCallingCode = country.callingCode[0];
              setCallingCode(selectedCallingCode);
              if ((onCodeChange, onCountryCodeChange)) {
                onCodeChange(selectedCallingCode);
                onCountryCodeChange(country.cca2);
              }
            }}
            visible={isPickerVisible}
            onClose={() => setIsPickerVisible(false)}
          />

          <Text style={styles.callingCode}>+{callingCode}</Text>
        </TouchableOpacity>
        <TextInput
          {...props}
          keyboardShouldPersistTaps
          value={value}
          onChangeText={onChange}
          keyboardType="numeric"
          placeholderTextColor={colors.txtGrey1}
          placeholder={t('enter_number_Txt')}
          style={[
            styles.textInput,
            {
              textAlign: 'left',
              marginLeft: isRTL ? 0 : 10,
              marginRight: isRTL ? 10 : 0,
            },
          ]}
        />
      </View>
    </View>
  );
};

export default MobileNumberInput;

const styles = StyleSheet.create({
  title: {
    fontSize: fp(1.6),
    marginBottom: hp(1.2),
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  textInput: {
    flex: 1,
    height: '100%',
    padding: 12,
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
    borderWidth: 1,
    borderColor: colors.lightgreay,
    borderRadius: 10,
  },
  callingCode: {
    color: colors.black,
    fontSize: fp(1.4),
    fontFamily: Fonts.medium,
    // alignSelf: 'center',
  },
  countryPicker: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
    borderWidth: 1,
    borderColor: colors.lightgreay,
    borderRadius: 10,
  },
});
