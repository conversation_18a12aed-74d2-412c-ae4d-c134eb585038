import React, {useState} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import {useTranslation} from 'react-i18next';

const SupportComponent = ({phone, email}) => {
  const [activeTab, setActiveTab] = useState('Call');
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  const handleCallPress = async phone => {
    const url = `tel:${phone}`;
    await Linking.openURL(url);
    // const supported = await Linking.canOpenURL(url);
    // console.log('supported', supported);
    // if (supported) {
    //   await Linking.openURL(url);
    // } else {
    //   Alert.alert('Error', t('dialingError'));
    // }
  };
  const handleEmailPress = email => {
    const url = `mailto:${email}`;
    Linking.openURL(url);
    // Linking.openURL(url);
    // Linking.canOpenURL(url)
    //   .then(supported => {
    //     if (supported) {
    //       Linking.openURL(url);
    //     } else {
    //       Alert.alert('Error', t('linkEmailError'));
    //     }
    //   })
    //   .catch(err => console.error('Error opening email:', err));
  };
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.card}>
        <View
          style={[
            styles.tabContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'Call' && styles.activeTab]}
            onPress={() => setActiveTab('Call')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'Call' && styles.activeTabText,
              ]}>
              {t('call')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'Email' && styles.activeTab]}
            onPress={() => setActiveTab('Email')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'Email' && styles.activeTabText,
              ]}>
              {t('Email')}
            </Text>
          </TouchableOpacity>
        </View>

        {activeTab === 'Call' ? (
          <View
            style={[
              styles.contentContainer,
              {alignItems: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            <Text style={[styles.heading, ,]}>{t('callUs')}</Text>
            <Text style={styles.description}>{t('connectWithSupport')}</Text>
            <TouchableOpacity onPress={() => handleCallPress(phone)}>
              <Text style={[styles.contact, {textDecorationLine: 'underline'}]}>
                {phone}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View
            style={[
              styles.contentContainer,
              {alignItems: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            <Text style={styles.heading}>{t('emailUs')}</Text>
            <Text style={styles.description}>{t('emailForSupport')}</Text>
            <TouchableOpacity onPress={() => handleEmailPress(email)}>
              <Text style={[styles.contact, {textDecorationLine: 'underline'}]}>
                {email}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

export default SupportComponent;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    width: '90%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingVertical: 20,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tabContainer: {
    justifyContent: 'space-between',
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    paddingHorizontal: 1,
    paddingVertical: 5,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 5,
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  activeTab: {
    backgroundColor: colors.themeColor,
  },
  tabText: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: colors.textInactive,
  },
  activeTabText: {
    color: colors.white,
  },
  contentContainer: {
    marginTop: 5,
  },
  heading: {
    fontSize: 18,
    fontFamily: Fonts.semiBold,
    color: '#333333',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    fontFamily: Fonts.regular,
    color: '#666666',
    marginBottom: 16,
  },
  contact: {
    fontSize: 18,
    fontFamily: Fonts.semiBold,
    color: colors.black,
  },
});
