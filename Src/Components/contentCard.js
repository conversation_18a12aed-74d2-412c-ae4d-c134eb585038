import React, {useEffect, useState} from 'react';
import {
  Image,
  StyleSheet,
  Dimensions,
  Text,
  TouchableOpacity,
  Alert,
  View,
} from 'react-native';
import colors from '../Utils/colors';
import {showToast} from './ToastHelper';
import {useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';
import {updateBookingFlowType} from '../Redux/Slices/Student/TutorBookingSlice';
import {Fonts} from '../Utils/Fonts';
import {SvgUri} from 'react-native-svg';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';
import HelperTextComponent from './HelperTipComp';

const SCREEN_WIDTH = Dimensions.get('window').width;

const CardComponent = ({
  imageUrl,
  title,
  name,
  navigation,
  handlePress,
  halperTxt,
}) => {
  console.log('🚀 ~ CardComponent ~ imageUrl:', imageUrl);
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const [openHelperText, setOpenHelperText] = useState(false);
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.card}
      onPress={handlePress}>
      <Image
        source={imageUrl}
        style={styles.image}
        resizeMode="contain"
        tintColor={'white'}
      />
      <View style={{flexDirection: 'row', alignItems: 'baseline'}}>
        <Text style={styles.text}>{title}</Text>
        <HelperTextComponent
          helperText={halperTxt}
          setOpen={setOpenHelperText}
          open={openHelperText}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.themeColor,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    width: SCREEN_WIDTH * 0.9,
    height: fp(14),
    alignSelf: 'center',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.lightgreay,
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    marginTop: 20,
  },
  image: {
    width: wp(20),
    height: hp(6),
  },
  text: {
    fontSize: 17,
    fontFamily: Fonts.medium,
    color: colors.white,
    marginTop: 10,
    marginRight: 8,
  },
});

export default CardComponent;
