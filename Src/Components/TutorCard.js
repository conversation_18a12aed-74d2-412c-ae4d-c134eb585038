import React from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  View,
  I18nManager,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {PrimaryButton} from './CustomButton';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';

const {width, height} = Dimensions.get('screen');

const TutorCardComponent = ({
  title,
  subtitle,
  onButtonPress,
  buttonTitle,
  showImage = true,
  showButton = false,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <View
      style={{
        alignItems: isRTL ? 'flex-end' : 'center',
        paddingVertical: 10,
        width: width * 0.9,
        alignSelf: 'center',
        borderRadius: 10,
        justifyContent: 'center',
        maxHeight: hp(30),
      }}>
      <View
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          width: '100%',
        }}>
        <LinearGradient
          colors={['#C6FFC9', '#D4EBFF']}
          start={{
            x: Math.sin((0 * Math.PI) / 180),
            y: -Math.cos((100 * Math.PI) / 180),
          }}
          end={{
            x: Math.sin((50 * Math.PI) / 180),
            y: -Math.cos((200 * Math.PI) / 180),
          }}
          style={{
            backgroundColor: colors.white,
            padding: 16,
            borderRadius: 10,
            width: wp(90),
            flexDirection: isRTL ? 'row-reverse' : 'row',
          }}>
          <View
            style={[
              styles.textContainer,
              {alignItems: isRTL ? 'flex-end' : 'flex-start'},
            ]}>
            <Text style={[styles.title, {textAlign: isRTL ? 'right' : 'left'}]}>
              {title}
            </Text>
            <Text
              style={[styles.subtitle, {textAlign: isRTL ? 'right' : 'left'}]}>
              {subtitle}
            </Text>
            {showButton && (
              <PrimaryButton
                onPress={onButtonPress}
                title={buttonTitle}
                style={[
                  styles.button,
                  {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
                ]}
                textStyle={{
                  fontSize: 16,
                  fontWeight: '500',
                  color: colors.white,
                }}
              />
            )}
          </View>
        </LinearGradient>

        {showImage && (
          <Image
            source={icons.tutorImage}
            style={[
              styles.image,
              // {alignSelf: I18nManager.isRTL ? 'left' : 'right'},
              isRTL ? styles.imageRTL : styles.imageLTR,
            ]}
            resizeMode="contain"
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  textContainer: {
    width: '100%',
  },
  title: {
    fontSize: fp(2),
    marginTop: height * 0.01,
    color: colors.black,
    fontFamily: Fonts.semiBold,
  },
  subtitle: {
    fontSize: fp(1.8),
    width: wp(70),
    color: colors.black,
    marginVertical: hp(1),
    fontFamily: Fonts.medium,
    lineHeight: hp(2.2),
  },
  button: {
    backgroundColor: colors.themeColor,
    marginVertical: 6,
    borderRadius: 15,
    fontSize: 20,
    width: '35%',
    height: 45,
  },
  image: {
    width: fp(14),
    height: fp(14),
    position: 'absolute',
    top: hp(14),
  },
  imageLTR: {
    right: wp(4), // Image on the right for LTR
  },
  imageRTL: {
    left: wp(4), // Image on the left for RTL
  },
});

export default TutorCardComponent;
