import React, {useState} from 'react';
import {
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {Fonts} from '../Utils/Fonts';
import {useTranslation} from 'react-i18next';

export const PrimaryDateTimePicker = ({
  title,
  containerStyle,
  onDateSelect,
  ...props
}) => {
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [date, setDate] = useState(new Date());
  const [showPicker, setShowPicker] = useState(false);

  const handleConfirm = selectedDate => {
    setShowPicker(false);
    if (selectedDate) {
      setDate(selectedDate);
      onDateSelect && onDateSelect(selectedDate);
    }
  };

  const getFormattedDateWithAge = date => {
    if (!date || isNaN(date.getTime())) return 'DD/MM/YYYY (Age - xx)';

    const today = new Date();
    let age = today.getFullYear() - date.getFullYear();
    if (
      today.getMonth() < date.getMonth() ||
      (today.getMonth() === date.getMonth() && today.getDate() < date.getDate())
    ) {
      age--;
    }

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year} (Age - ${age})`;
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <Text style={[styles.title, {textAlign: isRTL ? 'right' : 'left'}]}>
        {title}
      </Text>

      <TouchableOpacity
        onPress={() => setShowPicker(true)}
        activeOpacity={0.8}
        style={styles.touchable}>
        <TextInput
          {...props}
          value={getFormattedDateWithAge(date)}
          editable={false}
          style={styles.textInput}
        />
        <Image source={icons.calendarIcon} style={styles.calendarIcon} />
      </TouchableOpacity>

      <DateTimePickerModal
        isVisible={showPicker}
        mode="date"
        onConfirm={handleConfirm}
        onCancel={() => setShowPicker(false)}
        maximumDate={
          new Date(new Date().setFullYear(new Date().getFullYear() - 18))
        }
        minimumDate={new Date(1900, 0, 1)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '90%',
    alignSelf: 'center',
  },
  title: {
    fontSize: 16,
    marginBottom: 10,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  touchable: {
    position: 'relative',
  },
  textInput: {
    height: 50,
    borderWidth: 1,
    borderColor: colors.lightgreay,
    borderRadius: 10,
    padding: 12,
    paddingRight: 40,
    color: colors.txtGrey1,
    fontFamily: Fonts.regular,
  },
  calendarIcon: {
    position: 'absolute',
    right: 10,
    top: 10,
    width: 30,
    height: 30,
  },
});

export default PrimaryDateTimePicker;
