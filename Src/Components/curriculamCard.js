import React from 'react';
import {View, Image, StyleSheet, Dimensions} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import icons from '../Utils/icons'; // Assuming the icon paths are correct
import {IMAGE_BASE_URL} from '../Utils/getBaseUrl';
import {fp} from '../Helper/ResponsiveDimensions';

const {width} = Dimensions.get('screen');

const CurriculumCard = ({flag}) => {
  const imageUrl = `${IMAGE_BASE_URL}${flag}`;

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#C6FFC9', '#D4EBFF']}
        start={{x: 0.0, y: 0.0}}
        end={{x: 1.0, y: 0.0}}
        style={styles.gradient}>
        <Image source={{uri: imageUrl}} style={styles.chevronImage} />
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopLeftRadius: 15,
    borderBottomLeftRadius: 15,
    overflow: 'hidden',
  },
  gradient: {
    width: width * 0.3,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chevronImage: {
    width: fp(8),
    height: fp(5),
  },
});

export default CurriculumCard;
