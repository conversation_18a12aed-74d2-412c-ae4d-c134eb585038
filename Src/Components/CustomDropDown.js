import React, {useState, useEffect} from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import SelectDropdown from 'react-native-select-dropdown';
import colors from '../Utils/colors';
import {normalize} from '../Helper/NormalizeFont';
import icons from '../Utils/icons';
import {responsiveFontSize} from '../Utils/constant';
import {Fonts} from '../Utils/Fonts';
import {fp, hp} from '../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';
import HelperTextComponent from './HelperTipComp';

const CustomDropDown = ({
  width = '100%',
  data = [],

  onSelect,
  defaultValue = 'Select',
  imageIcon,
  lable,
  lableStyle,
  lableIcon,
  height = 0,
  backgroundColor = colors.white,
  radius = 12,
  borderWidth = 0,
  borderColor,
  disabled,
  showIcon = true,
  isSearch = null,
  alreadySelectedItem,
  marginVertical = 12,
  helperTxt,
  showTooltip = false,
}) => {
  console.log('🚀 ~ defaultValue:', defaultValue);
  console.log('🚀 ~ data:', data?.length);
  console.log('🚀 ~ data:', data);
  console.log('🚀 ~ alreadySelectedItem:', alreadySelectedItem);
  // State to hold the selected item
  const [selectedItem, setSelectedItem] = useState(alreadySelectedItem);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [openHelperText, setOpenHelperText] = useState(false);

  // Effect to handle initial default value
  useEffect(() => {
    // Ensure data is an array before processing
    if (!Array.isArray(data) || data.length === 0) {
      return;
    }

    // If defaultValue is a string and matches an item's name, set that item
    // if (alreadySelectedItem) {
    //   setSelectedItem(alreadySelectedItem);
    // } else

    if (typeof defaultValue === 'string') {
      console.log('set value in dropdown');
      const matchedItem = data.find(
        item =>
          item && (item.name === defaultValue || item.label === defaultValue),
      );
      console.log('🚀 ~ useEffect ~ matchedItem:', matchedItem);
      if (matchedItem) {
        setSelectedItem(matchedItem);
      }
    }
  }, [defaultValue, data, alreadySelectedItem]);

  // Determine display text
  const getDisplayText = () => {
    // If an item is selected, show its name
    if (selectedItem && (selectedItem.name || selectedItem.label)) {
      return selectedItem.name || selectedItem.label;
    }

    // If defaultValue is an object with a name or label, use that
    if (typeof defaultValue === 'object' && defaultValue) {
      return defaultValue.name || defaultValue.label || t('select');
    }

    // Fallback to default
    return t('select');
  };
  const dropdownStyle = {
    ...styles.dropdownMenuStyle,
    height: data?.length > 8 ? 300 : null,
  };
  return (
    <View style={{marginVertical: marginVertical}}>
      {lable && (
        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          {lableIcon && <Image source={lableIcon} />}
          <Text style={[styles.lable, lableStyle]}>{lable}</Text>
          {showTooltip && (
            <HelperTextComponent
              helperText={helperTxt}
              setOpen={setOpenHelperText}
              open={openHelperText}
              borderColor={colors.black}
              iconColor={colors.black}
            />
          )}
        </View>
      )}
      <SelectDropdown
        data={data}
        onSelect={item => {
          // Ensure item is not null before setting
          if (item) {
            console.log('🚀 ~ item: custom dropdown', item);
            // Update selected item state

            setSelectedItem(item);

            // Call the provided onSelect callback with the entire item
            // This allows the parent component to access full item details
            if (onSelect) onSelect(item);
          }
        }}
        search={isSearch || null}
        disabled={disabled}
        defaultButtonText={getDisplayText()}
        renderButton={() => {
          return (
            <View
              style={[
                styles.dropdownButtonStyle,
                {
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  width: width,
                  height: height,
                  backgroundColor: backgroundColor,
                  borderRadius: radius,
                  borderWidth: borderWidth,
                  borderColor: borderColor,
                },
              ]}>
              <Text
                style={[
                  styles.dropdownButtonTxtStyle,
                  {
                    textAlign: showIcon ? (isRTL ? 'right' : 'left') : 'center',
                    color: selectedItem ? colors.black : colors.txtGrey1,
                    // Grey if no selection
                  },
                ]}>
                {getDisplayText()}
              </Text>
              {showIcon && <Image source={icons.arrowDown} />}
            </View>
          );
        }}
        renderItem={(item, index, isSelected) => {
          // Ensure item exists before rendering
          if (!item) return 'No Data Available';

          return (
            <View
              style={{
                ...styles.dropdownItemStyle,
                ...(isSelected && {backgroundColor: colors.themeColor}),
              }}>
              {imageIcon && (
                <Image
                  source={imageIcon}
                  style={styles.dropdownItemIconStyle}
                />
              )}
              <Text
                style={[
                  styles.dropdownItemTxtStyle,
                  {
                    color: isSelected ? colors.white : colors.black,
                    textAlign: isRTL ? 'right' : 'left',
                  },
                ]}>
                {item.name || 'Unknown'}
              </Text>
            </View>
          );
        }}
        showsVerticalScrollIndicator={false}
        dropdownStyle={dropdownStyle}
      />
    </View>
  );
};

export default CustomDropDown;

const styles = StyleSheet.create({
  dropdownButtonStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 10,
    marginTop: hp(0.6),
  },
  dropdownButtonTxtStyle: {
    flex: 1,
    fontSize: fp(1.6),
    fontFamily: Fonts.poppinsRegular,
    color: colors.black,
  },
  dropdownMenuStyle: {
    backgroundColor: '#E9ECEF',
    borderRadius: 8,
    marginTop: hp(0.6),
    // paddingBottom: hp(5),

    // height: hp(10),
  },
  dropdownItemStyle: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 5,
    alignItems: 'center',
    paddingVertical: 10,
  },
  dropdownItemTxtStyle: {
    flex: 1,
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  dropdownItemIconStyle: {
    marginRight: 8,
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
  },
  row: {
    alignItems: 'center',
    marginBottom: 8,
  },
  lable: {
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
  },
});
