import {Image, Text, TouchableOpacity, View, StyleSheet} from 'react-native';
import React from 'react';
import {useNavigation} from '@react-navigation/native';
import {normalize} from '../Helper/NormalizeFont';
import colors from '../Utils/colors';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';

const HeaderWithDopdown = ({
  backIcon,
  title,
  isBackBtn,
  rightContent,
  isWhite,
  style,
}) => {
  const navigation = useNavigation();
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <View
      style={[
        {
          flexDirection: isRTL ? 'row-reverse' : 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: isWhite ? colors.white : colors.themeColor,
          height: 50,
          width: '100%',
        },
        style,
      ]}>
      <View
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          alignItems: 'center',
          marginLeft: isRTL ? 0 : 10,
          marginRight: isRTL ? 10 : 0,
        }}>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          {isBackBtn && (
            <IconBtn
              icon={backIcon}
              onPress={() => navigation.goBack()}
              iconStyle={{tintColor: isWhite ? colors.black : colors.white}}
            />
          )}
        </View>

        <View
          style={{
            justifyContent: 'center',
            alignItems: 'flex-start',
          }}>
          <Text
            style={{
              fontSize: normalize(7),
              color: isWhite ? colors.black : colors.white,
              fontFamily: Fonts.medium,
            }}>
            {title}
          </Text>
        </View>
      </View>

      {rightContent && (
        <View style={{alignItems: 'center'}}>{rightContent}</View>
      )}
    </View>
  );
};

const IconBtn = props => {
  const {icon, style, iconStyle} = props;
  return (
    <TouchableOpacity
      {...props}
      activeOpacity={0.8}
      style={[
        {
          height: 30,
          width: 30,
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      ]}>
      <Image
        resizeMode="contain"
        style={[
          {
            height: 18,
            width: 18,
          },
          iconStyle,
        ]}
        source={icon}
      />
    </TouchableOpacity>
  );
};

export default HeaderWithDopdown;

const styles = StyleSheet.create({});
