import Toast from 'react-native-toast-message';
import colors from '../Utils/colors';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';
import {Fonts} from '../Utils/Fonts';

export const showToast = (
  type = 'info',
  message,
  position = 'bottom',
  isRTL = false,
) => {
  // Toast.show({  const {i18n} = useTranslation();

  //   type: type,
  //   text1: message,
  //   position: position,
  //   visibilityTime: 2000,
  //   autoHide: true,
  //   topOffset: 30,
  //   bottomOffset: 40,
  //   props: {
  //     text1NumberOfLines: '2', //number of how many lines you want
  //   },
  // style: {
  //   width: '100%',
  //   paddingVertical: 12,
  //   paddingHorizontal: 15,
  //   borderRadius: 10,
  //   borderColor: type === 'error' ? colors.orangeLight : colors.themeColor,
  //   borderWidth: 1,
  //   backgroundColor: colors.white,
  //   shadowColor: colors.black,
  //   shadowOpacity: 0.15,
  //   shadowRadius: 6,
  //   shadowOffset: {width: 0, height: 2},
  // },
  // text1Style: {
  //   fontSize: 14,
  //   fontWeight: 'bold',
  //   color: colors.black,
  //   textAlign: 'left',
  //   lineHeight: 18,
  // },
  // });

  // Toast.show({
  //   topOffset: 100,
  //   type: 'error',
  //   text1: 'ERROR',
  //   text2: `${message}`,
  //   visibilityTime: 7000,
  //   props: {
  //     text2NumberOfLines: 0, //number of how many lines you want
  //   },
  // });

  Toast.show({
    position: position,
    visibilityTime: 2500,
    autoHide: true,
    topOffset: 30,
    bottomOffset: 50,
    type: 'tomatoToast',
    name: type,
    // And I can pass any custom props I want
    props: {
      text: message,
      style: {
        width: wp(90),
        paddingVertical: 12,
        paddingHorizontal: 15,
        borderRadius: 10,
        borderColor: type === 'error' ? colors.orangeLight : colors.themeColor,
        borderWidth: 1,
        backgroundColor: colors.white,
        shadowColor: colors.black,
        shadowOpacity: 0.15,
        shadowRadius: 6,
        shadowOffset: {width: 0, height: 2},
        height: 'auto',
      },
      textStyle: {
        fontSize: fp(1.6),
        color: colors.black,
        textAlign: isRTL ? 'right' : 'left',
        lineHeight: hp(2),
        fontFamily: Fonts.medium,
      },
    },
  });
};
