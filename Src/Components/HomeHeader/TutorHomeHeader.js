import React, {useEffect} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import TodayClassComponent from '../../Screens/HomeTutor/TodayClassComponent';
import SearchBar from '../SearchBar';
import TutorCardComponent from '../TutorCard';
import {showToast} from '../ToastHelper';
import {useTranslation} from 'react-i18next';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {hp} from '../../Helper/ResponsiveDimensions';
import {useGetTutorRateCardStatusQuery} from '../../Api/ApiSlice';

const TutorHomeHeader = ({
  showClassComponents,
  showSetupCard,
  // handleSetupNowPress,
  profileData,
  handleGetStartedPress,
  data,
  count,
  search,
  setSearch,
  activeBtn,
  setActiveBtn,
}) => {
  console.log('🚀 ~ profileData:', profileData);
  const {t} = useTranslation();
  const navigation = useNavigation();
  const handleHomeBannerOnPress = () => {
    if (profileData?.data?.tutorProfileCompletionStep == '0') {
      navigation.navigate('CompleteProfileTutorAfterSignUp');
    } else if (profileData?.data?.tutorProfileCompletionStep == '1') {
      navigation.navigate('CompleteProfileTutorAfterSignUpTwo');
    }
  };

  const {
    data: tutorProfileStatus,
    isLoading,
    refetch,
  } = useGetTutorRateCardStatusQuery();

  useEffect(() => {
    refetch();
  }, []);
  useFocusEffect(
    React.useCallback(() => {
      refetch();
    }, [refetch]), // Include refetch in the dependency array if it can change
  );
  console.log('🚀 ~ tutorProfileStatus:', tutorProfileStatus);

  const handleSetUpRateCard = () => {
    navigation.navigate('RateCardScreen');
  };
  // Tutor-specific UI

  return (
    <View>
      {
        <>
          {profileData?.data?.tutorStatus === '0' && (
            // need to add ths also tutorProfileCompletionStep == '1'
            <View style={{marginBottom: hp(1)}}>
              <TutorCardComponent
                title={t('completeProfile')}
                subtitle={t('profile_not_complete')}
                buttonTitle={t('get_Started')}
                showImage={true}
                onButtonPress={handleHomeBannerOnPress}
                showButton={true}
              />
            </View>
          )}
          {profileData?.data?.tutorStatus === '3' && (
            <TutorCardComponent
              title={t('greeting_message', {name: profileData?.data?.name})}
              subtitle={t('profileUnderReview')}
              // buttonTitle={t('setup_now')}
              showImage={false}
              // onButtonPress={handleSetupNowPress}
            />
          )}
          {profileData?.data?.tutorStatus === '2' && (
            <TutorCardComponent
              title={t('greeting_message', {name: profileData?.data?.name})}
              subtitle={t('profileRejectedMsg')}
              // buttonTitle={t('setup_now')}
              showImage={false}
              // onButtonPress={handleSetupNowPress}
            />
          )}
          {/* {profileData?.data?.tutorStatus === '1' && ( //profile is approved by admin
            <>
              <TutorCardComponent
                title={t('greeting_message', {name: profileData?.data?.name})}
                subtitle={t('set_ratecard')}
                buttonTitle={t('setup_now')}
                showImage={false}
                onButtonPress={handleSetUpRateCard}
                showButton={true}
              />
              <TodayClassComponent
                title={t('today_class')}
                onPress={() => navigation.navigate('StudentClassDetails')}
                data={data}
                count={count}
                search={search}
                setSearch={setSearch}
                activeBtn={activeBtn}
                setActiveBtn={setActiveBtn}
              />
            </>
          )} */}

          {profileData?.data?.tutorStatus !== '0' ? (
            profileData?.data?.tutorStatus !== '3' ? (
              profileData?.data?.tutorStatus !== '2' ? (
                tutorProfileStatus?.data?.is_rate_card_created == false ? (
                  <>
                    <TutorCardComponent
                      title={t('greeting_message', {
                        name: profileData?.data?.name,
                      })}
                      subtitle={t('set_ratecard')}
                      buttonTitle={t('setup_now')}
                      showImage={false}
                      onButtonPress={handleSetUpRateCard}
                      showButton={true}
                    />
                  </>
                ) : null
              ) : null
            ) : null
          ) : null}
        </>

        // <>
        //   <TutorCardComponent
        //     title={t('greeting_message', {name: profileData?.data?.name})}
        //     subtitle={t('set_ratecard')}
        //     buttonTitle={t('setup_now')}
        //     showImage={false}
        //     onButtonPress={handleSetupNowPress}
        //   />
        //   {/* <View style={{marginBottom: hp(1)}}>
        //     <TutorCardComponent
        //       title={t('Complete your profile')}
        //       subtitle={
        //         'Your profile is not yet complete. Kindly provide your details to complete the process'
        //       }
        //       buttonTitle={t('Get Started')}
        //       showImage={true}
        //       onButtonPress={handleSetupNowPress}
        //     />
        //   </View> */}

        //   <TodayClassComponent
        //     title={t('today_class')}
        //     onPress={() => navigation.navigate('StudentClassDetails')}
        //     data={data}
        //     count={count}
        //     search={search}
        //     setSearch={setSearch}
        //     activeBtn={activeBtn}
        //     setActiveBtn={setActiveBtn}
        //   />
        // </>
      }
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TutorHomeHeader;
