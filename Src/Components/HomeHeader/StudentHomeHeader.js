import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import StudentDetailComponent from '../StudentDetailsCard';
import ProfessionalCard from '../ProfessionalCard';
import StudentComponent from '../StudentCard';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {updateAddCurriculumFlowType} from '../../Redux/Slices/Student/GeneralStudentSlice';
import {useDispatch} from 'react-redux';

const StudentHomeHeader = ({
  taleemStudentAcademicDetails,
  studentType,
  userProfile,
}) => {
  console.log(
    '🚀 ~ taleemStudentAcademicDetails:',
    taleemStudentAcademicDetails,
  );
  console.log('🚀 ~ studentType: header', studentType);
  console.log('🚀 ~ userProfile:', userProfile);
  const navigation = useNavigation();
  const {t, i18n} = useTranslation();
  const dispatch = useDispatch();
  const isRTL = i18n.language === 'ar';

  const handleNavigation = () => {
    try {
      navigation.navigate('Chooseyourgrade');
      dispatch(updateAddCurriculumFlowType('edit'));
    } catch (error) {
      console.error('Error in navigation:', error);
    }
  };

  return (
    <>
      {
        // !taleemStudentAcademicDetails ||
        // taleemStudentAcademicDetails.length === 0 ? (
        //   userProfile?.user_type === '1' && userProfile?.student_type === '1' ? (
        //     <StudentComponent
        //       title={userProfile?.name || ''}
        //       subtitle={t('add_grade_details')}
        //       onButtonPress={handleNavigation}
        //     />
        //   ) : (
        //     <ProfessionalCard
        //       title={userProfile?.name || t('hello_professional')}
        //       subtitle={t('proffetionalSubtitle')}
        //     />
        //   )
        userProfile?.user_type === '1' && userProfile?.student_type === '2' ? (
          <ProfessionalCard
            title={userProfile?.name || t('hello_professional')}
            subtitle={t('proffetionalSubtitle')}
          />
        ) : // Then check if it's a regular student (type 1) with no academic details
        userProfile?.user_type === '1' &&
          userProfile?.student_type === '1' &&
          taleemStudentAcademicDetails?.length === 0 ? (
          <StudentComponent
            title={userProfile?.name || ''}
            subtitle={t('add_grade_details')}
            onButtonPress={handleNavigation}
          />
        ) : (
          <StudentDetailComponent
            title={userProfile?.name || t('hello_student')}
            subtitle={t('your_details')}
            grade={
              Array.isArray(taleemStudentAcademicDetails) &&
              taleemStudentAcademicDetails?.length > 0 &&
              taleemStudentAcademicDetails[0]?.tlm_grade?.name
                ? taleemStudentAcademicDetails[0].tlm_grade.name
                : 'N/A'
            }
            classLevel={
              Array.isArray(taleemStudentAcademicDetails) &&
              taleemStudentAcademicDetails?.length > 0 &&
              taleemStudentAcademicDetails[0]?.tlm_class?.name
                ? taleemStudentAcademicDetails[0].tlm_class.name
                : 'N/A'
            }
            curriculum={
              Array.isArray(taleemStudentAcademicDetails) &&
              taleemStudentAcademicDetails?.length > 0 &&
              taleemStudentAcademicDetails[0]?.tlm_curriculum?.name
                ? taleemStudentAcademicDetails[0].tlm_curriculum.name
                : 'N/A'
            }
            onPressEdit={handleNavigation}
          />
        )
      }
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default StudentHomeHeader;
