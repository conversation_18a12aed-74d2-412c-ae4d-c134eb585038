import React from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  View,
  I18nManager,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../Utils/colors';
import {PrimaryButton} from './CustomButton';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';
import {fp, hp} from '../Helper/ResponsiveDimensions';

const {width} = Dimensions.get('screen');

const ContactUsCard = ({
  title,
  subtitle,
  onButtonPress,
  image,
  isButton,
  height = 160,
  imgHeight = 95,
  imgWidth = 95,
  imgStyle,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <LinearGradient
      colors={['#C6FFC9', '#D4EBFF']}
      start={{
        x: Math.sin((0 * Math.PI) / 180),
        y: -Math.cos((100 * Math.PI) / 180),
      }}
      end={{
        x: Math.sin((50 * Math.PI) / 180),
        y: -Math.cos((200 * Math.PI) / 180),
      }}
      style={[
        styles.card,
        {
          height: height,
        },
      ]}>
      <View style={styles.textContainer}>
        <Text style={[styles.title, {textAlign: isRTL ? 'right' : 'left'}]}>
          {title}
        </Text>
        <Text
          style={[
            styles.subtitle,
            {
              textAlign: isRTL ? 'right' : 'left',
              alignSelf: isRTL ? 'flex-end' : 'flex-start',
            },
          ]}>
          {subtitle}
        </Text>

        {isButton && (
          <PrimaryButton
            onPress={onButtonPress}
            title={t('clickHereToStart')}
            style={[
              styles.button,
              {alignSelf: isRTL ? 'flex-end' : 'flex-start'},
            ]}
            textStyle={{fontSize: 16, fontWeight: '500', color: colors.white}}
          />
        )}
      </View>
      <Image
        source={image}
        style={[
          styles.image,
          isRTL ? styles.imageRTL : styles.imageLTR,
          imgStyle,
          {
            height: Number(imgHeight), // Dynamically set height from props
            width: Number(imgWidth), // Dynamically set width from props
          },
        ]}
        resizeMode="contain"
      />
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  card: {
    marginVertical: 15,
    backgroundColor: colors.white,
    alignItems: 'center',
    paddingVertical: 10,
    width: width * 0.9,
    alignSelf: 'center',
    padding: 16,
    borderRadius: 10,
    justifyContent: 'center',
  },
  textContainer: {
    width: '100%',
    alignSelf: 'center',
  },
  title: {
    fontSize: fp(2),
    fontFamily: Fonts.bold,
    marginBottom: hp(1),
    color: colors.black,
  },
  subtitle: {
    fontSize: fp(1.8),
    width: '70%',
    color: '#5F5F5F',
    fontWeight: '400',
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  button: {
    backgroundColor: colors.themeColor,
    borderRadius: 15,

    fontSize: fp(2),
    width: '50%',
    marginTop: hp(2),
  },
  image: {
    position: 'absolute',
  },
  imageLTR: {
    marginLeft: width * 0.02,
    right: width * 0.01,
  },
  imageRTL: {
    marginRight: width * 0.02,
    left: width * 0.01,
  },
});

export default ContactUsCard;
