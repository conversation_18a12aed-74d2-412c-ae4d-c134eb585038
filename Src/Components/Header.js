import React from 'react';
import {Image, Pressable, Text, TouchableOpacity, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {normalize} from '../Helper/NormalizeFont';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {Fonts} from '../Utils/Fonts';
import {fp} from '../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';

export const AppHeader = ({
  backIcon,
  title,
  titleStyle,
  isBackBtn,
  isRightImage,
  isWhite,
  style,
  isHome,
  righContent,
  navigateBack, // Default function
}) => {
  const navigation = useNavigation();
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const handleNavigationBack = () => {
    if (navigateBack) {
      navigateBack();
    } else {
      navigation.goBack();
    }
  };
  return (
    <View
      style={[
        {
          flexDirection: isRTL ? 'row-reverse' : 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: isWhite ? colors.white : colors.themeColor,
          height: 50,
          width: '100%',
        },
        style,
      ]}>
      <View
        style={{
          width: '13%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {isBackBtn && (
          <IconBtn
            icon={backIcon}
            onPress={() => handleNavigationBack()}
            iconStyle={{
              tintColor: isWhite ? colors.black : colors.white,
              transform: isRTL ? [{rotate: '180deg'}] : [{rotate: '0deg'}],
            }}
          />
        )}
      </View>

      <View
        style={{
          width: righContent ? '80%' : '74%',
          height: '100%',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexDirection: isRTL ? 'row-reverse' : 'row',
        }}>
        <Text
          style={[
            {
              fontSize: fp(1.8),
              color: isWhite ? colors.black : colors.white,
              fontFamily: Fonts.bold,
            },
            titleStyle,
          ]}>
          {title}
        </Text>
        {righContent && <View>{righContent}</View>}
      </View>

      {isHome && (
        <Pressable onPress={() => navigation.navigate('Home')}>
          <Image
            source={icons.homeIcon}
            style={{height: 20}}
            tintColor={colors.white}
            resizeMode="contain"
          />
        </Pressable>
      )}

      <View
        style={{
          width: '13%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {isRightImage && (
          <IconBtn
            icon={icons.Home}
            onPress={() => console.log('FORWARD')}
            iconStyle={{tintColor: isWhite ? colors.black : colors.white}}
          />
        )}
      </View>
    </View>
  );
};

export const IconBtn = props => {
  const {icon, style, iconStyle} = props;
  return (
    <TouchableOpacity
      {...props}
      activeOpacity={0.8}
      style={[
        {
          height: 30,
          width: 30,
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      ]}>
      <Image
        resizeMode="contain"
        style={[
          {
            height: fp(3.2),
            width: fp(3.2),
          },
          iconStyle,
        ]}
        source={icon}
      />
    </TouchableOpacity>
  );
};
