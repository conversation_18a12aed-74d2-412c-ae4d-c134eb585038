import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Image} from 'react-native';
import colors from '../Utils/colors';
import {SCREEN_WIDTH} from '../Utils/constant';
import {Fonts} from '../Utils/Fonts';

const CardContainer = ({leftIcon, rightIcon, text, onPress}) => {
  return (
    <View style={styles.container} onPress={onPress}>
      <View style={styles.leftSection}>
        <Image resizeMode="contain" source={leftIcon} style={styles.icon} />
        <Text style={styles.text}>{text}</Text>
      </View>
      <Image resizeMode="contain" source={rightIcon} style={styles.icon} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'center',
    padding: 20,
    marginVertical: 20,
    backgroundColor: colors.white,
    borderRadius: 8,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.12,
    shadowRadius: 4,
    elevation: 2,
    width: SCREEN_WIDTH * 0.9,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
    height: 20,
    width: 20,
  },
  text: {
    fontSize: 16,
    color: colors.black,
    marginLeft: 5,
    fontFamily: Fonts.regular,
  },
});

export default CardContainer;
