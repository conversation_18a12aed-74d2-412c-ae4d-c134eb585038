import React, {useRef, useEffect} from 'react';
import {
  Animated,
  View,
  Text,
  StyleSheet,
  Easing,
  Image,
  TouchableWithoutFeedback,
  Dimensions,
} from 'react-native';
import icons from '../../Utils/icons';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {t} from 'i18next';

const {width, height} = Dimensions.get('window'); // Get screen dimensions

const BlinkingArrow = () => {
  const moveAnim = useRef(new Animated.Value(0)).current; // Initial value for translateY: 0

  useEffect(() => {
    // Create a looped animation for up-and-down movement
    Animated.loop(
      Animated.sequence([
        Animated.timing(moveAnim, {
          toValue: 10, // Move down by 10px
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(moveAnim, {
          toValue: 0, // Move back to the original position
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
      ]),
    ).start();
  }, [moveAnim]);

  return (
    <TouchableWithoutFeedback onPress={() => {}}>
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.text}>{t('find_ideal_tutor_effortlessly')}</Text>
          <Animated.View
            style={{
              transform: [{translateY: moveAnim}], // Apply translateY animation
            }}>
            <Image
              source={icons.fingerPointedHand}
              style={{
                height: fp(7),
                width: fp(7),
                marginTop: hp(2),
                transform: [{rotate: '-180deg'}],
              }}
              tintColor={colors.white}
            />
          </Animated.View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: width,
    height: height,
    backgroundColor: 'rgba(0, 0, 0, 0.8)', // Semi-transparent black background
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(34),
    // padding: 20,
    // backgroundColor: colors.white, // Background color for the container
    borderRadius: 10, // Rounded corners
  },
  text: {
    marginRight: 10,
    fontSize: fp(1.8),
    fontFamily: Fonts.bold,
    color: colors.white, // Text color
    width: wp(60),
    textAlign: 'center',
    lineHeight: hp(2.2),
  },
});

export default BlinkingArrow;
