import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import colors from '../../Utils/colors';
import {useNavigation} from '@react-navigation/native';
import icons from '../../Utils/icons';
import {Fonts} from '../../Utils/Fonts';

const ParentHomeHeader = ({
  connectedStudentsRes,
  handleSelectStudent,
  selectedKid,
}) => {
  const navigation = useNavigation();
  return (
    <ScrollView
      horizontal
      contentContainerStyle={{
        marginHorizontal: wp(6),
        marginVertical: wp(4),
        flexDirection: 'row',
        gap: hp(1),
      }}>
      {connectedStudentsRes?.map(item => {
        return (
          <TouchableOpacity
            onPress={() => handleSelectStudent(item)} // Pass the item here
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              alignSelf: 'center',
            }}>
            <Image
              source={{uri: DUMMY_USER_IMG}}
              style={{
                height: fp(10),
                width: fp(10),
                borderRadius: fp(10),
                borderWidth: selectedKid?.id == item?.id ? 1 : 0,
                borderColor: colors.themeBackground,
              }}
            />
            <Text
              style={{
                marginTop: hp(1),
                // fontFamily: Fonts.bold,
                fontFamily:
                  selectedKid?.id == item?.id ? Fonts.bold : Fonts.medium,
                fontSize: fp(2),
                width: fp(10),
                textAlign: 'center',
              }}>
              {item?.name?.length > 20
                ? `${item?.name.substring(0, 18)}..`
                : item?.name}
            </Text>
          </TouchableOpacity>
        );
      })}
      <TouchableOpacity
        onPress={() => navigation.navigate('AddAccount', {type: 'student'})}>
        <Image
          source={icons.addCircle}
          style={{height: fp(10), width: fp(10), borderRadius: fp(10)}}
        />
      </TouchableOpacity>

      {/* </View> */}
    </ScrollView>
  );
};

export default ParentHomeHeader;

const styles = StyleSheet.create({});
