import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {globalStyles} from '../../Utils/globalStyles';
import {showToast} from '../ToastHelper';
import icons from '../../Utils/icons';
import {useSelector} from 'react-redux';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

import {Fonts} from '../../Utils/Fonts';
import {useTranslation} from 'react-i18next';
import colors from '../../Utils/colors';
import {useGetUnreadNotiCountQuery} from '../../Api/ApiSlice';
import useGlobalUnreadCount from '../../Helper/CustomHooks/UseGloabalUnreadNotiCount';
const TaleemHeader = ({isLocationIcon}) => {
  const navigation = useNavigation();
  const {appLocale} = useSelector(state => state?.auth);
  const currentLoc = useSelector(state => state.currentLocSlice.currentLoc);
  const {unreadCount, refetchUnreadCount} = useGlobalUnreadCount();
  console.log('🚀 ~ TaleemHeader ~ unreadCount:', unreadCount);
  // const {data: unreadCountData, refetch: refetchUnreadCount} =
  //   useGetUnreadNotiCountQuery();
  // console.log('🚀 ~ TaleemHeader ~ unreadCountData:', unreadCountData);
  useFocusEffect(
    React.useCallback(() => {
      refetchUnreadCount(); // Ensure fresh data when navigating to a screen with this header
    }, [refetchUnreadCount]),
  );

  // const unreadNotiCount = unreadCount?.count || 0;

  const logoSource =
    appLocale === 'ar'
      ? icons.logo.welcomeLogoArabic
      : icons.logo.welcomeLogoEng;

  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <View
      style={[
        styles.headerContainer,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
      ]}>
      <View
        style={[
          styles.leftHeader,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <TouchableOpacity
          // style={{alignItems: isRTL ? 'right' : 'left'}}
          onPress={() => navigation.openDrawer()}>
          <Image source={icons.hamBurger} style={globalStyles.hamburgerIcon} />
        </TouchableOpacity>

        <View style={styles.logoContainer}>
          <Image
            source={logoSource}
            style={globalStyles.logoImage}
            resizeMode="contain"
          />
        </View>
      </View>

      <View
        style={[
          styles.rightHeader,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        {isLocationIcon && currentLoc && (
          <View
            activeOpacity={0.8}
            style={[
              styles.locationContainer,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}
            onPress={() =>
              showToast('success', t('coming_soon'), 'bottom', isRTL)
            }>
            {/* onPress={() => navigation?.navigate('VideoTest')}> */}
            <Image
              source={icons.location}
              style={styles.locationIcon}
              resizeMode="cover"
            />

            {/* <Text style={styles.locationText}>{t('Qatar')}</Text> */}
            <Text style={styles.locationText}>{currentLoc?.country}</Text>
            {/* <Image
              source={icons.arrowDownWhite}
              style={styles.arrowDownIcon}
              resizeMode="contain"
            /> */}
          </View>
        )}

        <TouchableOpacity
          style={styles.notificationContainer}
          // onPress={() => showToast('success', t('notifications_unavailable'))}>
          onPress={() => navigation.navigate('Notification')}>
          {unreadCount > 0 && <View style={styles.redDot} />}
          <Image
            source={icons.notification}
            style={styles.notificationIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default TaleemHeader;

const styles = StyleSheet.create({
  redDot: {
    height: fp(1),
    width: fp(1),
    backgroundColor: 'red',
    borderRadius: fp(1),
    alignSelf: 'flex-end',
    top: hp(1),
    right: wp(1.2),
    zIndex: 1,
  },
  headerContainer: {
    paddingHorizontal: 20,
    height: hp(7),
    marginBottom: hp(1),
    justifyContent: 'center',
  },
  leftHeader: {
    flex: 0.5,
    alignItems: 'center',
  },
  hamburgerIcon: {
    width: fp(3),
    height: fp(3),
  },
  logoContainer: {
    alignItems: 'center',
    paddingHorizontal: 5,
  },
  logoImage: {
    width: fp(12), // Keep the width fixed
    height: fp(6),
  },
  rightHeader: {
    flex: 0.5,
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingRight: 10,
    minWidth: fp(7),
  },
  locationContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    // marginRight: 15,
    minWidth: 80,
    padding: 12,
  },
  locationIcon: {
    height: fp(2.4),
    width: fp(2),
  },
  locationText: {
    marginHorizontal: 6,
    color: colors.white,
    fontFamily: Fonts.medium,
    fontSize: fp(1.4),
  },
  arrowDownIcon: {
    width: 12,
    height: 12,
    alignSelf: 'center',
  },
  notificationContainer: {
    padding: 5,
  },
  notificationIcon: {
    width: fp(3.5),
    height: fp(3.5),
  },
});
