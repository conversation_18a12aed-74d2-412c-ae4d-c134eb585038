import {Image, StyleSheet, TextInput, View, Platform} from 'react-native';
import React from 'react';
import icons from '../Utils/icons';
import colors from '../Utils/colors';
import {SCREEN_HEIGHT} from '../Utils/constant';
import {Fonts} from '../Utils/Fonts';
import {fp} from '../Helper/ResponsiveDimensions';

const SearchBar = ({
  placeholder,
  onChangeText,
  value,
  onSubmitEditing,
  containerStyle,
  isRTL,
}) => {
  return (
    <View
      style={[
        styles.container,
        containerStyle,
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
      ]}>
      <Image source={icons.searchIcon} style={styles.icon} />
      <TextInput
        placeholder={placeholder}
        placeholderTextColor={colors.searchGray}
        onChangeText={onChangeText}
        value={value}
        onSubmitEditing={onSubmitEditing}
        style={[
          styles.input,
          {marginLeft: isRTL ? 0 : 10, marginRight: isRTL ? 10 : 0},
        ]}
      />
    </View>
  );
};

export default SearchBar;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: colors.offWhite1,
    marginTop: 20,
    marginHorizontal: 20,
    padding: 10,
    borderRadius: 10,
    height:
      Platform.OS === 'android' ? SCREEN_HEIGHT * 0.065 : SCREEN_HEIGHT * 0.06,
  },
  icon: {
    height: fp(2.4),
    width: fp(2.4),
  },
  input: {
    flex: 1,
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
  },
});
