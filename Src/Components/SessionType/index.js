import {StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import {styles} from './styles';
import CustomCheckbox from '../CustomCheckbox';
import {useTranslation} from 'react-i18next';

const SessionType = ({navigation}) => {
  const {t} = useTranslation();
  const [checkboxes, setCheckboxes] = React.useState([
    {label: t('online'), isSelected: false},
    {label: t('face_to_face'), isSelected: false},
    {label: t('group_sessions'), isSelected: false},
    {label: t('open_session'), isSelected: false},
  ]);

  const toggleCheckbox = index => {
    const updatedCheckboxes = checkboxes.map((checkbox, i) =>
      i === index ? {...checkbox, isSelected: !checkbox.isSelected} : checkbox,
    );
    setCheckboxes(updatedCheckboxes);
  };

  return (
    <View>
      <Text style={[styles.addDetails]}>{t('sessionType')}</Text>
      <View style={[styles.checkboxView]}>
        {checkboxes.map((checkbox, index) => (
          <View
            key={index}
            style={{marginLeft: index == 1 || index == 3 ? 24 : 0}}>
            <CustomCheckbox
              label={checkbox.label}
              isSelected={checkbox.isSelected}
              onSelect={() => toggleCheckbox(index)}
            />
          </View>
        ))}
      </View>
    </View>
  );
};

export default SessionType;
