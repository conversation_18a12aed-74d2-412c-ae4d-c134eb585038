import React from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  View,
  I18nManager,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {PrimaryButton} from './CustomButton';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../Utils/Fonts';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';

const {width, height} = Dimensions.get('screen');

const StudentComponent = ({title, subtitle, onButtonPress}) => {
  const {t} = useTranslation();

  return (
    <LinearGradient
      colors={['#C6FFC9', '#D4EBFF']}
      start={{
        x: Math.sin((0 * Math.PI) / 180),
        y: -Math.cos((100 * Math.PI) / 180),
      }}
      end={{
        x: Math.sin((50 * Math.PI) / 180),
        y: -Math.cos((200 * Math.PI) / 180),
      }}
      style={{
        marginVertical: 15,
        backgroundColor: colors.white,
        alignItems: 'center',
        paddingVertical: 10,
        width: width * 0.9,
        alignSelf: 'center',
        padding: 16,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <View style={styles.textContainer}>
        <Text
          style={[
            styles.title,
            {textAlign: I18nManager.isRTL ? 'right' : 'left'},
          ]}>
          {t('hello')} {title}
        </Text>
        <Text
          style={[
            styles.subtitle,
            {width: wp(60)},
            {textAlign: I18nManager.isRTL ? 'right' : 'left'},
          ]}>
          {subtitle}
        </Text>

        <PrimaryButton
          onPress={onButtonPress}
          title={t('add_details')}
          style={styles.button}
          textStyle={{fontSize: 16, fontWeight: '500', color: colors.white}}
        />
      </View>
      <Image
        source={icons.studentImage}
        style={[
          styles.image,
          I18nManager.isRTL ? styles.imageRTL : styles.imageLTR,
        ]}
        resizeMode="contain"
      />
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  textContainer: {
    width: '100%',
    alignSelf: 'center',
  },
  title: {
    fontSize: 16,
    marginBottom: height * 0.01,
    color: colors.black,
    fontFamily: Fonts.bold,
  },
  subtitle: {
    fontSize: fp(1.4),
    width: width * 0.8,
    color: colors.black,
    fontFamily: Fonts.medium,
    lineHeight: hp(1.7),
  },
  button: {
    backgroundColor: colors.themeColor,
    marginVertical: 6,
    borderRadius: 15,
    alignSelf: 'flex-start',
    fontSize: 20,
    width: '42%',
  },
  image: {
    width: width * 0.29,
    height: width * 0.29,
    position: 'absolute',
    bottom: 0,
  },
  imageLTR: {
    marginLeft: width * 0.02,
    right: width * 0.01,
  },
  imageRTL: {
    marginRight: width * 0.02,
    left: width * 0.01,
  },
});

export default StudentComponent;
