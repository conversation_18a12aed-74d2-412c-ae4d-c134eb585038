import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {Calendar} from 'react-native-calendars';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {t} from 'i18next';
import {useTranslation} from 'react-i18next';

const TaleemEventCalendar = ({
  selectedDate,
  handleDateSelect,
  markedDates,
  handleOnMonthChange,
  isLoading,
  isShowAllInstructions = true,
  isYellowDotInstruction = true,
  minDate = '',
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <View
      style={{
        backgroundColor: colors.white,
        borderRadius: fp(2),
        width: wp(90),
        // height: hp(50),
        elevation: 1,
        paddingTop: hp(1),
        paddingBottom: hp(2),
        marginHorizontal: wp(1),
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <Calendar
        // Customize the appearance of the calendar
        style={{
          marginHorizontal: wp(1),
          borderRadius: fp(2),
          width: wp(90),
          // height: hp(44),

          paddingTop: hp(1),
          paddingBottom: hp(2),
        }}
        // Specify the current date
        initialDate={selectedDate}
        displayLoadingIndicator={isLoading}
        // Callback that gets called when the user selects a day
        onDayPress={day => {
          console.log('selected day', day);
          handleDateSelect(day);
        }}
        markingType={'custom'}
        markedDates={markedDates}
        theme={{
          backgroundColor: '#ffffff',
          calendarBackground: 'white',
          textSectionTitleColor: '#b6c1cd',
          selectedDayBackgroundColor: colors.themeBackground, // Ensure this matches your selected color
          selectedDayTextColor: colors.white,
          todayTextColor: '#00adf5',
          dayTextColor: '#2d4150',
          textDisabledColor: colors.lightGrey,
        }}
        hideExtraDays={true}
        enableSwipeMonths={true}
        headerStyle={{}}
        minDate={minDate}
        // onLeftArrowPress={() => {
        //   console.log('arrow pressed');
        // }}
        // onPressArrowRight={}
        onMonthChange={handleOnMonthChange}

        // customHeader={TaleemCalendarHeader}
        // Mark specific dates as marked
      />
      <View style={{marginTop: hp(0), paddingHorizontal: wp(4)}}>
        <Text
          style={{
            fontFamily: Fonts.medium,
            lineHeight: hp(2),
            color: colors.darkgray,
            textAlign: isRTL ? 'right' : 'left',
          }}>
          {isShowAllInstructions &&
            `• ${t('dateInstruction1')}\n\n• ${t('dateInstruction2')}\n\n`}
          {isYellowDotInstruction && `• ${t('dateInstrunction3')}`}
        </Text>
      </View>
    </View>
  );
};

export default TaleemEventCalendar;

const styles = StyleSheet.create({});
