import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import colors from '../Utils/colors';
import LinearGradient from 'react-native-linear-gradient';
import {useTranslation} from 'react-i18next';
import {PrimaryButton} from './CustomButton';
import {Fonts} from '../Utils/Fonts';
import TaleemBadge from './Custom_Components/TaleemBadge';
import {fp, hp} from '../Helper/ResponsiveDimensions';

const RateDetailsCard = ({
  title,
  language = 'English',
  curriculum,
  duration,
  onlinePrice,
  packageAvailable,
  priceForFaceToFace,
  onPressEdit,
  onPressView,
  isAcademic,
  onlineCommissionRate,
  onlineCommissionPrice,
  faceToFaceCommissionRate,
  faceToFaceCommissionPrice,
  openSessionCommisionRate,
  openSessionCommisionPrice,
  status,
}) => {
  console.log('🚀 ~ curriculum:', curriculum);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  console.log(
    'onlineCommissionRate & all:',
    `${onlineCommissionRate} ${onlineCommissionPrice} ${faceToFaceCommissionRate} ${typeof faceToFaceCommissionPrice}`,
  );

  const isValidDuration = () => {
    if (duration === null || duration === undefined) return false;
    if (typeof duration === 'string' && duration.trim().toUpperCase() === 'NA')
      return false;
    if (duration === 0) return false;
    if (typeof duration === 'string' && duration.trim() === '') return false;
    return true;
  };

  return (
    <>
      <View
        style={[
          styles.container,
          {
            alignItems: isRTL ? 'flex-end' : 'flex-start',
            marginBottom: status == '0' ? 0 : 6,
          },
        ]}>
        {/* Title and Language Row */}
        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          <View style={[styles.titleView]}>
            <Text
              style={[styles.titleTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
              {title}
            </Text>
          </View>
          {/* {language && (
          <LinearGradient
            colors={['#C6FFC9', '#D4EBFF']}
            start={{
              x: Math.sin((0 * Math.PI) / 180),
              y: -Math.cos((100 * Math.PI) / 180),
            }}
            end={{
              x: Math.sin((50 * Math.PI) / 180),
              y: -Math.cos((200 * Math.PI) / 180),
            }}
            style={{
              padding: 5,
              borderTopLeftRadius: 10,
              borderBottomLeftRadius: 10,
            }}>
            <Text>{language === 'NA' ? 'English' : language}</Text>
          </LinearGradient>
        )} */}
        </View>
        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          {/* {onlineCommissionRate !== '0' && onlineCommissionRate > 0 && (
            <TaleemBadge text={`Commission ${onlineCommissionRate}%`} />
          )} */}
          {/* {faceToFaceCommissionRate !== '0' && faceToFaceCommissionRate > 0 && (
            <TaleemBadge text={`Commission F2F ${faceToFaceCommissionRate}%`} />
          )} */}
        </View>

        <View
          style={{
            width: isRTL ? '100%' : '80%',
            marginVertical: 8,
            alignItems: isRTL ? 'flex-end' : 'flex-start',
          }}>
          <View style={{marginTop: 5}}>
            {curriculum != 'N/A' && (
              <Text style={styles.txtSub}>
                {t('curriculum')}:{' '}
                <Text style={styles.detailsTxt}>{curriculum}</Text>
              </Text>
            )}
            {/* {isValidDuration() && (
            <View style={{marginTop: 5}}>
              <Text style={styles.txtSub}>
                {t('duration')}:{' '}
                <Text style={styles.detailsTxt}>{duration}</Text>
              </Text>
            </View>
          )} */}
          </View>

          <View style={{marginTop: hp(1.4)}}>
            <Text style={styles.txtSub}>
              {t('price_for_online')} :{' '}
              <Text style={styles.detailsTxt}>
                {onlineCommissionPrice ? `${onlineCommissionPrice} QAR` : 'N/A'}
              </Text>
            </Text>
          </View>
          <View style={{marginTop: hp(1.4)}}>
            <Text style={styles.txtSub}>
              {t('packageAvailable')} :
              {packageAvailable && (
                <Text style={styles.detailsTxt}>
                  {packageAvailable ? ' Yes' : ' No'}
                </Text>
              )}
            </Text>
          </View>
          <View style={{marginTop: hp(1.4)}}>
            <Text style={styles.txtSub}>
              {t('price_for_face_to_face')} :{' '}
              <Text style={styles.detailsTxt}>
                {faceToFaceCommissionPrice?.length
                  ? `${faceToFaceCommissionPrice} QAR`
                  : 'N/A'}
              </Text>
            </Text>
            {/* <View style={{marginTop: hp(1.4)}}>
              <Text style={styles.txtSub}>
                {t('price_for_open_session')} :{' '}
                <Text style={styles.detailsTxt}>
                  {openSessionCommisionPrice?.length
                    ? `${openSessionCommisionPrice} QAR`
                    : 'N/A'}
                </Text>
              </Text>
            </View> */}
            <View
              style={{
                marginTop: hp(1.4),
              }}>
              <Text style={styles.txtSub}>
                {t('status')} :{' '}
                <Text style={styles.detailsTxt}>
                  {status
                    ? `${
                        status == '0'
                          ? t('Pending')
                          : '1'
                          ? t('Approved')
                          : t('Rejected')
                      }`
                    : 'N/A'}
                </Text>
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons Row */}
        <View
          style={[styles.row, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          <PrimaryButton
            title={t('edit')}
            style={[styles.editBtn]}
            textStyle={styles.btnTxt}
            onPress={onPressEdit}
          />
        </View>
      </View>
      {status == '0' && (
        <Text
          style={{
            fontSize: fp(1.4),
            color: colors.txtGrey1,
            fontFamily: Fonts.medium,
            lineHeight: hp(1.6),
            marginVertical: hp(1.4),
          }}>
          {t('rateCardDiscription')}
        </Text>
      )}
    </>
  );
};

export default RateDetailsCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 10,
    overflow: 'hidden',

    borderWidth: 0.7,
    borderColor: colors.txtGrey,
    shadowColor: colors.black,
    shadowOpacity: 0.3,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
    elevation: 2,
    width: '100%',
    paddingVertical: 10,
    paddingLeft: 10,
  },
  titleView: {
    width: '50%',
  },
  titleTxt: {
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
  },
  languageTxt: {
    fontSize: 12,
    color: colors.black,
    fontFamily: Fonts.semiBold,
  },
  row: {
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 1,
    paddingRight: 10,
  },
  txtSub: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.txtGrey1,
  },
  detailsTxt: {
    fontSize: fp(1.6),
    color: colors.black,
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  editBtn: {
    backgroundColor: colors.white,
    width: '45%',
    borderWidth: 1,
    borderColor: colors.themeColor,
  },
  btnTxt: {
    fontSize: fp(2),
    fontFamily: Fonts.medium,
    color: colors.themeColor,
  },
});
