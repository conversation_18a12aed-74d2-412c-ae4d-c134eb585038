import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';

const CustomCheckbox = ({label, isSelected, onSelect, isRtl}) => (
  <TouchableOpacity
    style={[
      styles.checkboxWrapper,
      {
        flexDirection: isRtl ? 'row-reverse' : 'row',
        marginRight: isRtl ? 10 : 0,
        marginLeft: isRtl ? 0 : 10,
      },
    ]}
    onPress={onSelect}>
    <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
      {isSelected && <Text style={styles.checkMark}>✓</Text>}
    </View>
    <Text
      style={[
        styles.label,
        {marginRight: isRtl ? 10 : 0, marginLeft: isRtl ? 0 : 10},
      ]}>
      {label}
    </Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  checkboxWrapper: {
    alignItems: 'center',
    marginVertical: 5,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderRadius: 4,
    borderColor: colors.greyLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: colors.themeColor, // Replace with your theme color
    // borderColor: '#666', // Replace with your theme color
    borderColor: colors.greyLight,
  },
  checkMark: {
    color: '#FFF', // Replace with your desired checkmark color
    fontSize: 16,
    fontFamily: Fonts.bold,
    alignSelf: 'center',
    position: 'absolute',
    bottom: 1,
  },
  label: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: colors.grey,
  },
});

export default CustomCheckbox;
