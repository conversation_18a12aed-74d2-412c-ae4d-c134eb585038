import {StyleSheet, Dimensions, Platform} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';

const {width} = Dimensions.get('screen');
const dynamicPadding = width * 0.04;
const dynamicRadius = width * 0.02;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: dynamicPadding,
    backgroundColor: colors.white,
    zIndex: 2,
  },
  content: {
    flex: 1,
    paddingHorizontal: dynamicPadding,
  },
  profileContainer: {
    marginBottom: dynamicPadding,
  },
  loggedInButton: {
    borderRadius: dynamicRadius,
    marginBottom: dynamicPadding,
    paddingVertical: dynamicPadding * 0.6,
    paddingHorizontal: dynamicPadding * 0.8,
  },
  loggedInButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  loggedInText: {
    color: '#FFF',
    fontSize: responsiveFontSize(14),
    fontFamily: Fonts.medium,
  },
  arrowDownIcon: {
    width: dynamicPadding * 0.7,
    height: dynamicPadding * 0.7,
    tintColor: '#FFF',
  },
  drawerItemsContainer: {
    flex: 0.8,
    justifyContent: 'space-between',
  },
  drawerItemWrapper: {
    marginVertical: dynamicPadding * 0.2,
    borderRadius: dynamicRadius,
    backgroundColor: colors.white,
    shadowColor: colors.txtGrey,
    shadowOffset: {width: 0, height: Platform.OS === 'ios' ? 2 : 0.1},
    shadowOpacity: Platform.OS === 'ios' ? 0.5 : 0.05,
    shadowRadius: Platform.OS === 'ios' ? 6 : 0.4,
    elevation: Platform.OS === 'android' ? 5 : 0,
  },
  drawerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: dynamicPadding * 0.6,
    paddingHorizontal: dynamicPadding,
    borderRadius: dynamicRadius,
  },
  drawerItemText: {
    fontSize: dynamicPadding * 1,
    color: colors.txtGrey1,
    flex: 1,
    fontFamily: Fonts.regular,
  },
  rightArrowGray: {
    width: dynamicPadding * 2,
    height: dynamicPadding * 2,
    opacity: 0.5,
  },
});

export default styles;
