import AsyncStorage from '@react-native-async-storage/async-storage';
import {CommonActions} from '@react-navigation/native';
import React, {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {
  I18nManager,
  Image,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useDispatch} from 'react-redux';
import {useProfileDetailsQuery} from '../../Api/ApiSlice';
import {showToast} from '../../Components/ToastHelper';
import {clearAuthData, setIsLoggedIn} from '../../Features/authSlice';
import icons from '../../Utils/icons';
import ProfileCard from '../Custom_Components/ProfileCard';
import styles from './styles';

const DrawerItemTutor = ({item, onPress}) => {
  return (
    <View style={styles.drawerItemWrapper}>
      <TouchableOpacity style={styles.drawerItem} onPress={onPress}>
        <Text
          style={[
            styles.drawerItemText,
            {textAlign: I18nManager.isRTL ? 'right' : 'left'},
          ]}>
          {item.label}
        </Text>
        <Image
          source={icons.rightArrowGray}
          style={styles.rightArrowGray}
          resizeMode="contain"
        />
      </TouchableOpacity>
    </View>
  );
};

const CustomDrawerContentTutor = props => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dispatch = useDispatch();

  const {data: profileData, error, isLoading} = useProfileDetailsQuery();

  useEffect(() => {
    if (error) {
      showToast('error', t('failedToLoadProfile'), 'bottom', isRTL);
    }
  }, [error]);

  const menuItems = [
    {label: t('home'), route: 'home'},
    {label: t('earning'), route: 'CompleteYourProfilePageOne'},
    {label: t('my_rate_card')},
    {label: t('doc')},
    {label: t('contact_us')},
    {label: t('change_language')},
    {label: t('logout'), route: 'Logout'},
  ];

  const handleLogout = async () => {
    try {
      await AsyncStorage.removeItem('user');

      dispatch(clearAuthData());
      dispatch(setIsLoggedIn(false));
      showToast('success', t('logged_out'), 'bottom', isRTL);

      props.navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{name: 'Login'}],
        }),
      );
    } catch (error) {
      showToast('error', t('logged_out_fail'), 'bottom', isRTL);
    }
  };

  const handleNavigation = route => {
    if (!route) {
      showToast('success', t('coming_soon'), 'bottom', isRTL);
    } else if (route === 'Logout') {
      handleLogout();
    } else {
      props.navigation.navigate(route);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.profileContainer}>
          <ProfileCard
            name={profileData?.data?.name || 'N/A'}
            email={profileData?.data?.email || 'N/A'}
            imageUrl={icons.profileImage1}
            onViewProfile={() =>
              showToast('success', 'Comming Soon', 'bottom', isRTL)
            }
          />
        </View>
        <LinearGradient
          colors={['#40A39B', '#C6FFC9']}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
          style={styles.loggedInButton}>
          <View style={styles.loggedInButtonContent}>
            <Text style={styles.loggedInText}>{t('logged_in_as_tutor')}</Text>
            <Image
              source={icons.arrowDownWhite}
              style={styles.arrowDownIcon}
              resizeMode="contain"
            />
          </View>
        </LinearGradient>

        <View style={styles.drawerItemsContainer}>
          {menuItems.map((item, index) => (
            <DrawerItemTutor
              key={index}
              item={item}
              onPress={() => handleNavigation(item.route)}
            />
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CustomDrawerContentTutor;
