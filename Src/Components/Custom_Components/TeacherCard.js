import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  Pressable,
  Platform,
  I18nManager,
} from 'react-native';
import TopTutorsButton from './TopTutorsButton';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {Fonts} from '../../Utils/Fonts';
import {hp} from '../../Helper/ResponsiveDimensions';

const {width} = Dimensions.get('window');

const TeacherCard = ({
  name,
  subject,
  price,
  rating,
  imageUrl,
  isTopTutor,
  onPress,
}) => {
  console.log('🚀 ~ subject:', subject);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  console.log('🚀 ~ imageUrl:', imageUrl);
  const imageSource =
    typeof imageUrl === 'string' || typeof imageUrl === 'number'
      ? {uri: `${imageUrl}`}
      : {uri: imageUrl?.uri};

  const dynamicImageHeight = width * 0.4;

  return (
    <Pressable
      onPress={onPress}
      style={[
        styles.cardContainer,
        Platform.select({
          android: styles.androidCardContainer,
        }),
      ]}>
      <Image
        source={imageSource}
        style={[styles.teacherImage, {height: dynamicImageHeight}]}
      />
      {rating > 0 && (
        <View
          style={[
            styles.ratingContainer,
            Platform.select({
              android: styles.androidRatingContainer,
            }),
          ]}>
          <Image style={styles.star} source={icons.star} resizeMode="contain" />
          <Text style={styles.ratingText}>{rating}</Text>
        </View>
      )}

      {isTopTutor && (
        <TopTutorsButton
          style={[styles.topTutor, {top: dynamicImageHeight - 37}]}
        />
      )}
      <View
        style={[
          styles.contentContainer,
          {textAlign: isRTL ? 'right' : 'left'},
        ]}>
        <Text style={[styles.name, {textAlign: isRTL ? 'right' : 'left'}]}>
          {name}
        </Text>
        {subject.length > 0 && (
          <Text style={[styles.subject, {textAlign: isRTL ? 'right' : 'left'}]}>
            {subject.join(' • ')}
          </Text>
        )}

        <Text style={[styles.price, {textAlign: isRTL ? 'right' : 'left'}]}>
          <Text
            style={[
              styles.priceHighlight,
              {textAlign: isRTL ? 'right' : 'left'},
            ]}>
            {price}
          </Text>{' '}
          {t('currencyPerHour')}
        </Text>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    width: width * 0.4,
    borderRadius: 10,
    backgroundColor: 'white',
    padding: 0,
    marginBottom: 20,
    overflow: 'hidden',
  },
  androidCardContainer: {
    elevation: 0,
    borderWidth: 0,
  },
  teacherImage: {
    width: '100%',
    borderRadius: 10,
  },
  contentContainer: {
    padding: 10,
  },
  ratingContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'white',
    borderRadius: 6,
    paddingVertical: hp(0.2),
    paddingHorizontal: hp(0.4),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  androidRatingContainer: {
    elevation: 0,
  },
  ratingText: {
    fontSize: 10,
    marginLeft: 4,
    color: colors.grey,
    fontFamily: Fonts.semiBold,
  },
  name: {
    fontSize: 16,
    fontFamily: Fonts.semiBold,
    color: colors.black,
  },
  subject: {
    fontSize: 13,
    color: colors.darkGrey,
    marginTop: 2,
    fontFamily: Fonts.medium,
  },
  price: {
    fontSize: 14,
    color: colors.darkGrey,
    marginTop: 5,
    fontFamily: Fonts.semiBold,
  },
  priceHighlight: {
    fontSize: 14,
    color: '#40A39B',
    fontFamily: Fonts.semiBold,
  },
  topTutor: {
    position: 'absolute',
    left: 10,
  },
  star: {
    height: 15,
    width: 15,
  },
});

export default TeacherCard;
