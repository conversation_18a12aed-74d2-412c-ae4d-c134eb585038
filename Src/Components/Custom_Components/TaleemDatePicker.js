import {
  Modal,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import icons from '../../Utils/icons';
import {Image} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import moment from 'moment';

export const TaleemDatePicker = ({
  text = 'Select time',
  onToTimeChange, // Callback for 'toTime'
  onFromTimeChange, // Callback for 'fromTime'
  minuteInterval,
  width = wp(64),
  paddingHorizontal = 20,
  paddingVertical = 14,
  time,
}) => {
  console.log('🚀 ~ time:', time);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [toTime, setToTime] = useState('');
  const [fromTime, setFromTime] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  console.log('🚀 ~ toTime:', toTime);
  const handleTimeChange = (time, type) => {
    console.log('🚀 ~ handleTimeChange ~ time:', time);
    if (type === 'from') {
      setFromTime(time);
      if (onFromTimeChange) {
        onFromTimeChange(time); // Passing fromTime to parent
      }
    } else {
      setToTime(time);
      if (onToTimeChange) {
        onToTimeChange(time); //  Passing toTime to parent
      }
    }
  };

  return (
    <View
      style={[
        {
          paddingHorizontal: paddingHorizontal,
          paddingVertical: paddingVertical,
        },
      ]}>
      <Text
        style={[styles.sectionTitle, {textAlign: isRTL ? 'right' : 'left'}]}>
        {text}
      </Text>
      <TouchableOpacity
        onPress={() => setShowDatePicker(true)}
        style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          alignItems: 'center',
          borderWidth: 1,
          borderColor: '#ddd',
          paddingVertical: hp(0.6),
          paddingHorizontal: 15,
          borderRadius: 10,
        }}>
        <Image
          source={icons.calanderImage}
          style={{
            height: 20,
            width: 20,
            marginRight: isRTL ? 0 : 10,
            marginLeft: isRTL ? 10 : 0,
          }}
        />
        <View style={{width: width, paddingVertical: hp(1)}}>
          {/* <View style={{}}> */}
          <Text
            style={{
              fontSize: fp(1.6),
              color: toTime ? colors.black : colors.lightGrey,
              fontFamily: Fonts.medium,
              textAlignVertical: 'center',
              textAlign: isRTL ? 'right' : 'left',
            }}>
            {/* {moment(toTime).format('hh:mm A')} */}
            {toTime
              ? moment(toTime).format('hh:mm A')
              : time
              ? moment(time, 'HH:mm:ss').format('hh:mm A')
              : text}
          </Text>
        </View>
      </TouchableOpacity>

      {/* iOS-specific Modal for DateTimePicker */}
      {Platform.OS === 'ios' ? (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showDatePicker}
          onRequestClose={() => setShowDatePicker(false)}>
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
            }}>
            <View
              style={{
                backgroundColor: '#fff',
                marginHorizontal: 20,
                borderRadius: 10,
                padding: 20,
                alignItems: 'center',
              }}>
              <Text
                style={{
                  fontFamily: Fonts.medium,
                  color: colors.black,
                  fontSize: fp(1.6),
                }}>
                {text}
              </Text>
              <DateTimePicker
                value={new Date()}
                mode="time"
                display="spinner"
                minuteInterval={minuteInterval}
                onChange={(event, time) => {
                  setShowDatePicker(false);
                  if (time) {
                    handleTimeChange(time, 'to');
                  }
                }}
              />
              <TouchableOpacity
                onPress={() => setShowDatePicker(false)}
                style={{
                  marginTop: 20,
                  backgroundColor: colors.themeColor,
                  paddingVertical: 10,
                  paddingHorizontal: 20,
                  borderRadius: 5,
                }}>
                <Text style={{color: '#fff', fontSize: 16}}>{t('close')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      ) : (
        // Direct DateTimePicker for Android
        showDatePicker && (
          // <View style={{width: wp(80)}}>
          <DateTimePicker
            value={toTime ? new Date(toTime) : new Date()} // Ensure a valid Date object
            // value={time ? new Date(time) : new Date()} // Ensure a valid Date object
            mode="time"
            display="spinner"
            onChange={(event, selectedDate) => {
              setShowDatePicker(false);
              if (selectedDate) {
                console.log(
                  '🚀 ~ AddTutorSchedule ~ selectedDate:',
                  selectedDate,
                );
                handleTimeChange(selectedDate, 'to');
              }
            }}
            minuteInterval={minuteInterval}
            style={{
              width: '100%',
              borderColor: colors.themeBackground,
            }}
          />
          // </View>
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  sectionContainer: {},
  sectionTitle: {
    fontFamily: Fonts.medium,
    color: colors.black,
    fontSize: fp(1.6),
    marginBottom: 8,
  },
});

export default TaleemDatePicker;
