import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';

const SmallFeturesButtonNonPrassable = props => {
  const {style, title, textStyle, highlighted, isRTL} = props;
  return (
    <View
      style={[
        {
          // backgroundColor: colors.themeBackground,
          padding: '1%',
          // justifyContent: 'center',
          alignItems: 'center',
          // alignSelf: 'center',
          flexDirection: isRTL ? 'row-reverse' : 'row',
        },
        style,
      ]}>
      <Text
        style={[
          {
            color: 'white',
            fontSize: 14,
            fontFamily: Fonts.medium,
          },
          textStyle, // Overwrite or extend the default style with textStyle
        ]}>
        &#x25cf; {title}
      </Text>
    </View>
  );
};

export default SmallFeturesButtonNonPrassable;

const styles = StyleSheet.create({});
