import React from 'react';
import {View, Text, StyleSheet, Dimensions, Platform} from 'react-native';
import colors from '../../Utils/colors';
import {useTranslation} from 'react-i18next';

const {width} = Dimensions.get('window');

const TopTutorsButton = ({style}) => {
  const {t} = useTranslation();

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.text}>✨{t('top_tutors')}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.themeColor2,
    borderRadius: 6,
    paddingHorizontal: width * 0.03,
    paddingVertical: width * 0.012,
    alignSelf: 'flex-start',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 1.5},
        shadowOpacity: 0.15,
        shadowRadius: 1.5,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  text: {
    color: 'white',
    fontSize: width > 400 ? 10 : 9,
    fontWeight: 'bold',
  },
});

export default TopTutorsButton;
