import {
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
  Dimensions,
  Platform,
  I18nManager,
} from 'react-native';
import React from 'react';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {Fonts} from '../../Utils/Fonts';
import {DUMMY_USER_IMG, responsiveFontSize} from '../../Utils/constant';

const {width, height} = Dimensions.get('window');

const ProfileCard = ({name, email, imageUrl, onViewProfile}) => {
  console.log('🚀 ~ ProfileCard ~ imageUrl:', imageUrl);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const imageSource =
    typeof imageUrl === 'string' || imageUrl != null
      ? {uri: `${IMAGE_BASE_URL}${imageUrl}`}
      : {uri: DUMMY_USER_IMG};

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.content,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <Image
          source={imageSource}
          style={[
            styles.profileImage,
            {
              marginRight: isRTL ? 0 : width * 0.03,
              marginLeft: isRTL ? width * 0.03 : 0,
            },
          ]}
        />
        <View style={styles.textContainer}>
          <Text style={[styles.name, {textAlign: isRTL ? 'right' : 'left'}]}>
            {name}
          </Text>
          <Text style={[styles.email, {textAlign: isRTL ? 'right' : 'left'}]}>
            {email}
          </Text>
          <TouchableOpacity
            style={[
              styles.viewProfileButton,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}
            onPress={onViewProfile}>
            <Text
              style={[
                styles.viewProfileText,
                {textAlign: isRTL ? 'right' : 'left'},
              ]}>
              {t('view_profile')}
            </Text>
            <Image
              source={isRTL ? icons.backIcon : icons.rightArrowLarge}
              style={[
                styles.arrow,
                {
                  marginLeft: isRTL ? 0 : width * 0.04,
                  marginRight: isRTL ? width * 0.04 : 0,
                },
              ]}
            />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default ProfileCard;

const styles = StyleSheet.create({
  container: {
    width: width * 0.72,
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: width * 0.02,
    alignSelf: 'center',
    marginTop: Platform.OS === 'android' ? height * 0.015 : height * 0.008,
  },
  content: {
    alignItems: 'center',
  },
  profileImage: {
    width: width * 0.14,
    height: width * 0.14,
    borderRadius: (width * 0.14) / 2,
  },
  textContainer: {
    flex: 1,
  },
  name: {
    fontSize: responsiveFontSize(14),
    color: '#000',
    marginBottom: height * 0.004,
    fontFamily: Fonts.medium,
  },
  email: {
    fontSize: responsiveFontSize(12),
    color: '#666',
    marginBottom: height * 0.008,
    fontFamily: Fonts.medium,
  },
  viewProfileButton: {
    // flexDirection: 'row',
    alignItems: 'center',
  },
  viewProfileText: {
    fontSize: responsiveFontSize(12),
    color: '#000',
    fontFamily: Fonts.medium,
    textDecorationLine: 'underline',
  },
  arrow: {
    height: width * 0.038,
    width: width * 0.038,
    tintColor: '#000',
  },
});
