import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../../Utils/colors';
import {fp, wp} from '../../Helper/ResponsiveDimensions';
import {Fonts} from '../../Utils/Fonts';

const TaleemBadge = ({text = 'Test', isRTL}) => {
  return (
    <View
      style={[
        styles.statusBadge,
        {alignItems: isRTL ? 'flex-end' : 'flex-start'},
      ]}>
      <LinearGradient
        colors={['#C6FFC9', '#D4EBFF']}
        style={{
          borderRadius: fp(1.8),
          paddingVertical: fp(0.6),
          paddingHorizontal: fp(1.6),
        }}>
        <Text style={[styles.statusText]}>{text}</Text>
      </LinearGradient>
    </View>
  );
};

export default TaleemBadge;

const styles = StyleSheet.create({
  statusText: {
    color: colors.black,
    fontSize: fp(1.4),
    fontFamily: Fonts.semiBold,
  },
  statusBadge: {
    paddingVertical: 8,
    borderRadius: 12,
    borderRadius: fp(4),
    flex: 1,
    // width: wp(2),
    justifyContent: 'center',
  },
});
