import React from 'react';
import {View, Text, Image, StyleSheet, Dimensions} from 'react-native';
import TopTutorsButton from './TopTutorsButton';
import icons from '../../Utils/icons';
import colors from '../../Utils/colors';
import LinearGradient from 'react-native-linear-gradient';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {responsiveFontSize} from '../../Utils/constant';
import {useTranslation} from 'react-i18next';
import {useGetAllConnectedProfilesAfterLoginQuery} from '../../Api/ApiSlice';

const {width} = Dimensions.get('window');

const TutorDetailsCard = ({
  name,
  subject,
  rating,
  location,
  imageUrl,
  isTopTutor,
  experience,
  nationality,
  occupation,
}) => {
  console.log(
    'name isTopTutor subject  rating location imageUrl experience subject',
    name,
    subject,
    rating,
    location,
    imageUrl,
    isTopTutor,
    experience,
    nationality,
  );
  console.log('🚀 ~ rating:', rating);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <View
      style={[
        styles.cardContainer,
        {
          flexDirection: isRTL ? 'row-reverse' : 'row',
          alignSelf: isRTL ? 'flex-end' : 'flex-start',
        },
      ]}>
      {/* <Image source={image} style={styles.tutorImage} /> */}
      <Image source={imageUrl} style={styles.tutorImage} />
      <View
        style={[
          styles.infoContainer,
          {paddingLeft: isRTL ? 0 : 20, paddingRight: isRTL ? 20 : 0},
        ]}>
        <View
          style={[
            styles.topSection,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          {isTopTutor && <TopTutorsButton style={styles.topTutorsButton} />}

          <LinearGradient
            colors={['#C6FFC9', '#D4EBFF']}
            start={{x: 0.0, y: 0.0}}
            end={{x: 1.0, y: 0.0}}
            style={styles.gradient}>
            <View
              style={[
                styles.ratingContainer,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Image
                style={styles.star}
                source={icons.star}
                resizeMode="contain"
              />
              <Text style={styles.ratingText}>{rating}</Text>
            </View>
          </LinearGradient>
        </View>
        <Text style={[styles.name, {textAlign: isRTL ? 'right' : 'left'}]}>
          {name}
        </Text>
        {/* {nationality && <Text style={styles.name}>{}</Text>} */}

        <Text style={[styles.subject, {textAlign: isRTL ? 'right' : 'left'}]}>
          {occupation}
        </Text>
        {/* <Text style={styles.subject}>{subject?.join(' • ')}</Text> */}
        <View
          style={[
            styles.locationContainer,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}>
          <Image
            style={[
              styles.locationIcon,
              {
                marginRight: isRTL ? 0 : width * 0.016,
                marginLeft: isRTL ? width * 0.016 : 0,
              },
            ]}
            source={icons.location}
            resizeMode="contain"
          />
          <Text style={styles.locationText}>
            {location && `${location}, `}
            {`${nationality}`}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default TutorDetailsCard;

const styles = StyleSheet.create({
  cardContainer: {
    borderRadius: 8,
    backgroundColor: colors.themeColor,
    padding: 8,
    alignItems: 'center',
    width: width * 0.7,
  },
  tutorImage: {
    width: width * 0.24,
    height: width * 0.24,
    borderRadius: 8,
    borderWidth: 1.6,
    borderColor: 'white',
  },
  infoContainer: {
    flex: 1,
  },
  topSection: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  topTutorsButton: {
    marginRight: 8,
  },
  ratingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingText: {
    fontSize: 10,
    fontFamily: Fonts.medium,
    marginLeft: 4,
    color: colors.black,
  },
  name: {
    fontSize: fp(2),
    color: 'white',
    marginBottom: 4,
    fontFamily: Fonts.semiBold,
  },
  subject: {
    fontSize: fp(1.8),
    color: 'white',
    marginBottom: 8,
    fontFamily: Fonts.medium,
    lineHeight: hp(2),
  },
  locationContainer: {
    alignItems: 'center',
  },
  locationText: {
    fontSize: fp(1.4),
    color: 'white',
    fontFamily: Fonts.medium,
    lineHeight: hp(1.6),
  },
  star: {
    height: 12,
    width: 12,
  },
  locationIcon: {
    width: width * 0.024,
    height: width * 0.024,
  },
  gradient: {
    borderRadius: 6,
    paddingVertical: 6,
    paddingHorizontal: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
});
