import React from 'react';
import {View, Text, Image, StyleSheet, Dimensions} from 'react-native';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

const {width} = Dimensions.get('window');

const BookYourTutorCard = ({
  name,
  title,
  location,
  rating,
  reviews,
  expertise,
  experience,
  imgUrl,
  nationality,
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';

  return (
    <View style={styles.cardContainer}>
      <View
        style={[
          styles.topSection,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <Image
          source={imgUrl}
          style={[
            styles.tutorImage,
            {marginRight: isRTL ? 0 : 15, marginLeft: isRTL ? 15 : 0},
          ]}
        />
        <View style={styles.infoContainer}>
          <Text style={[styles.name, {textAlign: isRTL ? 'right' : 'left'}]}>
            {name}
          </Text>
          <Text style={[styles.title, {textAlign: isRTL ? 'right' : 'left'}]}>
            {title}
          </Text>
          <View
            style={[
              styles.ratingContainer,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            {location && (
              <>
                <Image
                  source={icons.navigationIcon}
                  style={{
                    height: 20,
                    width: 20,
                    marginRight: isRTL ? 0 : 1,
                    marginLeft: isRTL ? 1 : 0,
                  }}
                />
                <Text style={styles.location}>{location}</Text>
                <View style={styles.column}></View>
              </>
            )}
            <Image
              source={icons.starBlack}
              style={{
                height: 16,
                width: 16,
                marginHorizontal: 7,
              }}
            />
            <Text style={styles.rating}>
              {rating} {t('rating_en')}
            </Text>
            <View style={styles.column}></View>
            <Text style={styles.reviews}>
              {reviews} {t('reviews')}
            </Text>
          </View>
        </View>
      </View>

      <View
        style={[
          styles.detailsContainer,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}>
        <View style={styles.expertiseSection}>
          <Text style={[styles.sectionTitle, {textAlign: 'center'}]}>
            {nationality}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    width: width * 0.9,
    marginVertical: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
    alignItems: 'center', // Center all content horizontally
    padding: 15, // Add padding for better spacing
    height: hp(13),
  },
  topSection: {
    alignItems: 'center', // Center items vertically
    justifyContent: 'center', // Center items horizontally
    width: '100%',
  },
  tutorImage: {
    width: fp(9),
    height: fp(10),
    borderRadius: 10,
  },
  infoContainer: {
    flex: 1,
    // alignItems: 'center', // Center text and rating container
  },
  name: {
    fontSize: 18,
    color: '#333',
    fontFamily: Fonts.bold,
  },
  title: {
    fontSize: 16,
    color: colors.darkGrey,
    marginVertical: 3,
    fontFamily: Fonts.medium,
  },
  ratingContainer: {
    alignItems: 'center', // Center items vertically
    // justifyContent: 'center', // Center items horizontally
    flexWrap: 'wrap', // Allow wrapping if needed
    marginTop: 5,
  },
  location: {
    fontSize: fp(1.4),
    color: colors.darkGrey,
    fontFamily: Fonts.regular,
  },
  rating: {
    fontSize: fp(1.4),
    color: colors.darkGrey,
    fontFamily: Fonts.medium,
  },
  reviews: {
    fontSize: fp(1.4),
    color: colors.darkGrey,
    fontFamily: Fonts.medium,
  },
  detailsContainer: {
    justifyContent: 'center', // Center horizontally
    alignItems: 'center', // Center vertically
    width: '100%',
    marginTop: 10,
  },
  expertiseSection: {
    alignItems: 'center', // Center expertise content
  },
  sectionTitle: {
    fontSize: 14,
    color: colors.darkGrey,
    fontFamily: Fonts.medium,
    marginBottom: 5,
  },
  column: {
    width: 1,
    height: 14,
    backgroundColor: '#E6E6E6',
    marginHorizontal: 5, // Add spacing between columns
  },
});

export default BookYourTutorCard;
