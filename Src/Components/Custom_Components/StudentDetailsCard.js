import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

const StudentDetailsCard = ({ name, grade, classLevel, curriculum }) => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.greeting}>Hello {name}</Text>
        <Text style={styles.greeting}>🖋️</Text>
       
      </View>
      <Text style={styles.subheader}>Your Details</Text>
      <View style={styles.detailsContainer}>
        <View style={styles.detailBox}>
          <Text style={styles.detailTitle}>Grade</Text>
          <Text style={styles.detailValue}>{grade}</Text>
        </View>
        <View style={styles.detailBox}>
          <Text style={styles.detailTitle}>Class</Text>
          <Text style={styles.detailValue}>{classLevel}</Text>
        </View>
        <View style={styles.detailBox}>
          <Text style={styles.detailTitle}>Curriculum</Text>
          <Text style={styles.detailValue}>{curriculum}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#d4edda',
    borderRadius: 15,
    padding: width * 0.05,
    width: width * 0.9,
    alignSelf: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  greeting: {
    fontSize: width * 0.06,
    fontWeight: 'bold',
    color: 'black',
  },
  editIcon: {
    width: 20,
    height: 20,
  },
  subheader: {
    fontSize: width * 0.045,
    marginBottom: 15,
    color: 'black',

  },
  detailsContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  detailBox: {
    alignItems: 'center',
    flex: 1,
  },
  detailTitle: {
    fontSize: width * 0.035,
    color: 'gray',
    marginBottom: 5,
    alignSelf: 'flex-start'
  },
  detailValue: {
    fontSize: width * 0.04,
    alignSelf: 'flex-start',
    // fontWeight: 'bold',
    color: 'black',
  },
});

export default StudentDetailsCard;
