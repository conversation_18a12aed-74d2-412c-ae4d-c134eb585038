import React from 'react';
import { View, Text, Image, StyleSheet, Dimensions } from 'react-native';
import CustomButton from './customButton';
import StudentImage from '../assets/asliStudent.png';

const { width, height } = Dimensions.get('window');

const StudentComponent = ({ title, subtitle, buttonText, onButtonPress }) => {
  return (
    <View style={styles.container}>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.subtitle}>{subtitle}</Text>
        <CustomButton style={styles.button} title={buttonText} onPress={onButtonPress} textStyle={{ fontSize: 20 }}  />
      </View>
      <Image
        source={StudentImage}
        style={styles.image}
        resizeMode="contain"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#d4edda',
    padding: width * 0.05,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: width * 0.95,
    alignSelf: 'center',
  },
  textContainer: {
    alignItems: 'flex-start',
    justifyContent: 'center',
    flex: 1,
    paddingRight: width * 0.02,
  },
  title: {
    fontSize: width * 0.06,
    fontWeight: 'bold',
    marginBottom: height * 0.01,
    color: 'black'
  },
  subtitle: {
    fontSize: width * 0.04,
    width: width * 0.7,
    color: 'black'

  },
  button: {
    backgroundColor: '#40A39B',
    paddingVertical: height * 0.015,
    paddingHorizontal: width * 0.07,
    borderRadius: 15,
    alignSelf: 'flex-start',
    fontSize: 20
  },
  image: {
    width: width * 0.29,
    height: width * 0.29,
    marginLeft: width * 0.02,
    position: 'absolute',
    right: width * 0.01,
    bottom: 0,
  },
});

export default StudentComponent;
