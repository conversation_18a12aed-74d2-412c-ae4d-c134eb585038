import {Text, TouchableOpacity} from 'react-native';
import React from 'react';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';

const CustomButton = props => {
  const {style, title, onPress, textStyle} = props;
  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.8}
      style={[
        {
          backgroundColor: colors.white,
          marginTop: '5%',
          padding: '3%',
          justifyContent: 'center',
          alignItems: 'center',
          alignSelf: 'center',
          flexDirection: 'row',
        },
        style,
      ]}>
      <Text
        style={[
          {
            color: 'white',
            fontSize: 14, // Default font size if none is provided
            fontFamily: Fonts.regular,
          },
          textStyle, // Overwrite or extend the default style with textStyle
        ]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default CustomButton;
