import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Platform,
  Dimensions,
} from 'react-native';
import colors from '../Utils/colors';
import icons from '../Utils/icons';
import {DUMMY_USER_IMG} from '../Utils/constant';
import {IMAGE_BASE_URL} from '../Utils/getBaseUrl';
import {Fonts} from '../Utils/Fonts';

const {width} = Dimensions.get('window');

const ReviewCard = ({name, date, reviewText, rating, image}) => {
  const renderStaticStars = () => {
    return (
      <View style={styles.starsContainer}>
        {/* Render filled stars based on the rating */}
        {[...Array(5)].map((_, index) => (
          <Image
            key={index}
            source={icons.star} // Replace with your star icon
            style={[
              styles.star,
              {tintColor: index < rating ? colors.starGold : colors.searchGray}, // Color based on rating
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={styles.cardContainer}>
      <View style={styles.upperView}>
        <Image
          source={image ? {uri: IMAGE_BASE_URL + image} : {uri: DUMMY_USER_IMG}}
          style={styles.profileImage}
        />
        <View style={styles.nameDateContainer}>
          <Text style={styles.name}>{name}</Text>
          <Text style={styles.date}>{date}</Text>
        </View>
        <View style={styles.ratingContainer}>
          {renderStaticStars()}
          <Text style={styles.ratingText}>{rating}</Text>
        </View>
      </View>

      <Text style={styles.reviewText}>{reviewText}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    padding: width * 0.02,
    backgroundColor: colors.white,
    borderRadius: Platform.OS === 'ios' ? width * 0.04 : 0,
    marginVertical: width * 0.01,
    width: width * 0.9,
    alignSelf: 'center',
  },
  upperView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: width * 0.02,
  },
  profileImage: {
    width: width * 0.11,
    height: width * 0.11,
    borderRadius: width * 0.06,
    marginRight: width * 0.03,
  },
  nameDateContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  name: {
    fontSize: width * 0.037,
    color: colors.black,
    fontFamily: Fonts.semiBold,
  },
  date: {
    fontSize: width * 0.035,
    color: colors.searchGray,
    fontFamily: Fonts.medium,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
    position: 'absolute',
    top: 3,
    right: 10,
  },
  starsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  star: {
    width: width * 0.05,
    height: width * 0.05,
    marginRight: width * 0.005,
  },
  ratingText: {
    fontSize: width * 0.035,
    color: colors.searchGray,
    marginLeft: width * 0.01,
    fontFamily: Fonts.medium,
  },
  reviewText: {
    fontSize: width * 0.033,
    color: colors.searchGray,
    marginTop: width * 0.02,
    lineHeight: width * 0.045,
    width: '100%',
    fontFamily: Fonts.medium,
  },
});

export default ReviewCard;
