import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';

const GradBadge = ({text, type = 'normal', isRTL}) => {
  return (
    <LinearGradient
      colors={['#C6FFC9', '#D4EBFF']}
      start={{
        x: Math.sin((0 * Math.PI) / 180),
        y: -Math.cos((100 * Math.PI) / 180),
      }}
      end={{
        x: Math.sin((50 * Math.PI) / 180),
        y: -Math.cos((200 * Math.PI) / 180),
      }}
      style={{
        backgroundColor: '#DFF6E3', // Light green background
        borderRadius: type == 'big' ? fp(4) : 14, // Rounded edges
        paddingVertical: type == 'big' ? 10 : 6,
        paddingHorizontal: type == 'big' ? 24 : 14,
        alignSelf: isRTL ? 'flex-end' : 'flex-start',
        justifyContent: 'center',
        // maxWidth: '35%',
      }}>
      <Text
        numberOfLines={2}
        style={[
          styles.badgeText,
          {fontSize: type == 'big' ? fp(1.8) : fp(1.4)},
        ]}>
        {text}
      </Text>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  badgeContainer: {
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 10,
    alignSelf: 'flex-start',
  },
  badgeText: {
    color: colors.black, // Dark green text color
    fontFamily: Fonts.medium,
    width: wp(20),
    textAlign: 'center',
    lineHeight: hp(2),
  },
});

export default GradBadge;
