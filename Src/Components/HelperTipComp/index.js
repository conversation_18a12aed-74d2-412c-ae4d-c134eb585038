import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
  Modal,
  TouchableHighlight,
} from 'react-native';
import Tooltip from 'react-native-walkthrough-tooltip';

import colors from '../../Utils/colors';
// import {Tooltip} from '@rneui/themed';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import icons from '../../Utils/icons';

const HelperTextComponent = ({
  helperText = 'Hey its me',
  setOpen,
  open,
  borderColor = '#e6f0fa',
}) => {
  const [iconMeasured, setIconMeasured] = useState(false);
  const layoutRef = useRef(false); // Track if layout has been measured

  const helperTextLength = helperText?.length || 0;
  const calculatedWidth = Math.max(helperTextLength * wp(2.4), wp(20));
  const width = Math.min(calculatedWidth, wp(90));
  const height =
    calculatedWidth > wp(90)
      ? 25 * (Math.round((calculatedWidth / wp(90)) * 100) / 100)
      : 40;

  const handleIconLayout = () => {
    if (!layoutRef.current) {
      layoutRef.current = true;
      setIconMeasured(true);
    }
  };

  const handlePress = () => {
    if (layoutRef.current) {
      setOpen(!open);
    } else {
      // Wait for layout to settle on first press
      setTimeout(() => {
        setOpen(!open);
      }, 100);
    }
  };

  return (
    <View style={styles.view}>
      <Tooltip
        isVisible={open}
        placement="bottom"
        contentStyle={{backgroundColor: colors.tooltipBgColor}}
        content={
          <Text
            style={{
              color: colors.blackSkatch,
              fontFamily: Fonts.medium,
              fontSize: fp(1.6),
              lineHeight: hp(2),
            }}>
            {helperText}
          </Text>
        }
        onClose={() => setOpen(false)}>
        <TouchableOpacity onPress={handlePress} onLayout={handleIconLayout}>
          <Image source={icons.infoIcon} />
        </TouchableOpacity>
      </Tooltip>
      {/* <Tooltip
        key={helperText}
        visible={open && iconMeasured}
        onClose={() => setOpen(false)}
        width={width}
        height={height}
        backgroundColor={colors.tooltipBgColor}
        withOverlay
        overlayColor={'rgba(0,0,0,0.6)'}
        animationType="fade"
        skipAndroidStatusBar={true}
        popover={
          <View
            style={{
              height,
              width,
              paddingHorizontal: 8,
              justifyContent: 'center',
            }}>
            <Text
              style={{
                fontFamily: Fonts.poppinsRegular,
                color: colors.txtGrey2,
                fontSize: fp(1.6),
              }}>
              {helperText}
            </Text>
          </View>
        }>
        <TouchableOpacity onPress={handlePress} onLayout={handleIconLayout}>
          <Image source={icons.infoIcon} />
        </TouchableOpacity>
      </Tooltip> */}
    </View>
  );
};

const styles = StyleSheet.create({
  view: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
});

export default HelperTextComponent;
