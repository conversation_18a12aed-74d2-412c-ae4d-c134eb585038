import {Modal, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {Fonts} from '../../Utils/Fonts';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import colors from '../../Utils/colors';
import TaleemDatePicker from '../Custom_Components/TaleemDatePicker';
import {t} from 'i18next';
import {PrimaryButton} from '../CustomButton';
import DayPicker from '../Custom_DayPicker/DayPicker';
import CustomCheckbox from '../../Components/CustomCheckbox';
import {
  CLASS_TYPES_ARRAY,
  getClassTypesData,
  RECURRENCE_TYPE_DATA,
} from '../../Utils/constant';
import CustomDropDown from '../CustomDropDown';
import {useTranslation} from 'react-i18next';
import {
  convertToLocal12HourFormat,
  convertUTCToLocal,
} from '../../Helper/DateHelpers/DateHelpers';
import {forModalPresentationIOS} from '@react-navigation/stack/lib/typescript/src/TransitionConfigs/CardStyleInterpolators';
import UpdatedTaleemDatePicker from '../UpdatedTaleemDatePicker/UpdatedTaleemDatePicker';
import HelperTextComponent from '../HelperTipComp';

const TutorAddSlotModal = ({
  editData = {},
  showAddSlotModal,
  setShowAddSlotModal,
  handleFromTimeChange,
  handleToTimeChange,
  heading,
  // recurrenceType,
  // setRecurrenceType,
  selectedTab,
  setSelectedTab,
  handleAddButton,
  selectedDaysCallback,
  type = 'general',
  handleRealTimeFromChange,
  handleRealTimeToChange,
  showDatePicker = false,
  setShowDatePicker,

  fromTime, // Pre-filled fromTime from parent
  toTime, // Pre-filled toTime from parent
}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const CLASS_TYPES_DATA = getClassTypesData(t);
  const getClassTypeById = classTypeId => {
    return CLASS_TYPES_DATA.find(type => type.value === classTypeId) || null;
  };
  const [openTextHelper, setOptenTextHelper] = useState(false);

  // const classTypeData = editData
  //   ? Object?.keys(editData).length > 0
  //     ? [getClassTypeById(editData?.class_type_id)]
  //     : CLASS_TYPES_DATA
  //   : CLASS_TYPES_DATA;
  const handleCheckboxToggle = value => {
    setSelectedTab(
      prev =>
        prev.includes(value)
          ? prev.filter(v => v !== value) // Deselect
          : [...prev, value], // Select
    );
  };
  return (
    <Modal
      transparent={true}
      animationType="slide"
      visible={showAddSlotModal}
      onRequestClose={() => setShowAddSlotModal(false)}>
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
        }}>
        <View
          style={{
            backgroundColor: '#fff',
            marginHorizontal: 20,
            borderRadius: 10,
            padding: 20,
            // alignItems: 'center',
          }}>
          <View
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'baseline',
            }}>
            <Text
              style={{
                fontSize: fp(2),
                marginBottom: 20,
                fontFamily: Fonts.semiBold,
                color: colors.black,
                textAlign: isRTL ? 'right' : 'left',
                marginRight: isRTL ? 0 : 8,
                marginLeft: isRTL ? 8 : 0,
              }}>
              {heading}
            </Text>
            <HelperTextComponent
              helperText={t('addTimeSlotHelper')}
              setOpen={setOptenTextHelper}
              open={openTextHelper}
              borderColor="#000"
              iconColor="#000"
            />
          </View>

          <View
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <UpdatedTaleemDatePicker
              text={t('From')}
              onToTimeChange={handleFromTimeChange}
              time={convertToLocal12HourFormat(editData?.start_time)}
              minuteInterval={15}
              width={wp(18)}
              paddingHorizontal={0}
              paddingVertical={10}
              // onRealTimeChange={handleRealTimeFromChange}
              showDatePicker={showDatePicker}
              setShowDatePicker={setShowDatePicker}
              type={'from'}
              initialTime={fromTime} // Pre-fill with toTime
            />
            {/* <TaleemDatePicker
              text={t('From')}
              onToTimeChange={handleFromTimeChange}
              time={convertToLocal12HourFormat(editData?.start_time)}
              minuteInterval={15}
              width={wp(18)}
              paddingHorizontal={0}
              paddingVertical={10}
            /> */}
            <Text
              style={{
                fontSize: fp(2),
                marginTop: 20,
                fontFamily: Fonts.semiBold,
                color: colors.black,
                alignSelf: 'center',
              }}>
              -
            </Text>
            <UpdatedTaleemDatePicker
              text={t('To')}
              onToTimeChange={handleToTimeChange}
              time={convertToLocal12HourFormat(editData?.end_time)}
              minuteInterval={15}
              width={wp(18)}
              paddingHorizontal={0}
              paddingVertical={10}
              type={'to'}
              onRealTimeChange={handleRealTimeToChange}
              showDatePicker={showDatePicker}
              setShowDatePicker={setShowDatePicker}
              initialTime={toTime} // Pre-fill with toTime
            />
            {/* <TaleemDatePicker
              text={t('To')}
              time={convertToLocal12HourFormat(editData?.end_time)}
              onToTimeChange={handleToTimeChange}
              minuteInterval={15}
              width={wp(18)}
              paddingHorizontal={0}
              paddingVertical={10}
            /> */}
          </View>

          {/* 
          {recurrenceType &&
          typeof recurrenceType?.name === 'string' &&
          recurrenceType.name.toLowerCase() === 'custom' ? ( */}
          <View style={{marginTop: hp(1)}}>
            <Text style={[styles.drowpdownLable, {marginBottom: hp(1)}]}>
              {t('repeatOn')}
            </Text>
            <View style={{marginTop: hp(0.4), marginBottom: hp(3)}}>
              <DayPicker
                onChange={selectedDaysCallback}
                alreadySelectedDays={editData?.days_of_week}
                isEdit={
                  editData
                    ? Object?.keys(editData).length > 0
                      ? true
                      : false
                    : false
                }
              />
            </View>
          </View>
          {/* <CustomDropDown
            lable={t('select_class_type')}
            data={CLASS_TYPES_DATA || []}
            lableStyle={styles.drowpdownLable}
            backgroundColor={colors.txtGrey}
            defaultValue={
              editData
                ? Object?.keys(editData).length > 0
                  ? editData?.class_type_id === '1' // Check if class_type_id is '1' for Online
                    ? 'Online'
                    : 'Face to Face'
                  : selectedTab === '1' // Fallback to selectedTab if editData is not available
                  ? 'Online'
                  : 'Face to Face'
                : ''
            }
            height={40}
            onSelect={selected => {
              console.log('🚀 ~ selected:', selected);
              setSelectedTab(selected?.value);
            }}
          /> */}
          <View
            style={[
              styles.checkboxView,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            {CLASS_TYPES_DATA?.map((type, index) => {
              console.log('type', type);
              return (
                <View
                  key={type.id}
                  style={{
                    // marginLeft: index % 2 === 1 ? 24 : 0,
                    marginBottom: 10,
                    // flex: 1,
                    // justifyContent: 'center',
                  }}>
                  <CustomCheckbox
                    label={type.name}
                    isSelected={
                      editData
                        ? Object.keys(editData).length > 0
                          ? getClassTypeById(editData?.class_type_id)
                          : selectedTab?.includes(type?.value)
                        : selectedTab?.includes(type?.value)
                    }
                    // onSelect={() => setSelectedTab([...selectedTab, type?.value])}
                    onSelect={() => handleCheckboxToggle(type.value)}
                    isDisabled={
                      editData
                        ? Object.keys(editData).length > 0
                          ? true
                          : false
                        : false
                    }
                  />
                </View>
              );
            })}
          </View>

          <View
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              justifyContent: 'space-between',
              width: wp(80),
              marginTop: hp(3),
            }}>
            <PrimaryButton
              title={t('close')}
              style={{width: wp(17), height: fp(4)}}
              textStyle={{
                fontSize: fp(1.6),
                fontFamily: Fonts.semiBold,
              }}
              onPress={() => setShowAddSlotModal(false)}
            />
            <PrimaryButton
              title={t('addkey')}
              style={{width: wp(17), height: fp(4)}}
              textStyle={{
                fontSize: fp(1.6),
                fontFamily: Fonts.semiBold,
              }}
              onPress={handleAddButton}
            />
          </View>

          {/* <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  width: wp(80),
                }}>
                <TouchableOpacity
                  onPress={() => setShowAddSlotModal(false)}
                  style={{
                    marginTop: 20,
                    backgroundColor: colors.themeColor,
                    paddingVertical: 10,
                    paddingHorizontal: 20,
                    borderRadius: 5,
                  }}>
                  <Text style={styles.addKey}>{t('close')}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={addScheduleApi}
                  style={{
                    marginTop: 20,
                    backgroundColor: colors.themeColor,
                    paddingVertical: 10,
                    paddingHorizontal: 20,
                    borderRadius: 5,
                  }}>
                  <Text style={styles.addKey}>{t('addkey')}</Text>
                </TouchableOpacity>
              </View> */}
        </View>
      </View>
    </Modal>
  );
};

export default TutorAddSlotModal;

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: fp(1.8),
    color: colors.black,
    fontFamily: Fonts.semiBold,
    marginBottom: hp(1.4),
    alignSelf: 'flex-start',
  },
  drowpdownLable: {
    fontSize: fp(1.6),
    fontFamily: Fonts.medium,
    color: colors.black,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    marginBottom: 10,
  },
});
