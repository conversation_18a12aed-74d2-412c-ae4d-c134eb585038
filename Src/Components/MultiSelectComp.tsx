import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import colors from '../Utils/colors';
import {Fonts} from '../Utils/Fonts';
import {fp, hp} from '../Helper/ResponsiveDimensions';
import {useTranslation} from 'react-i18next';

interface MultiSelectDropdownProps {
  options: {label: string; value: string}[];
  label: string;
  value: {label: string; value: string}[];
  onChange: (selectedValues: {label: string; value: string}[]) => void;
}

const MultiSelectDropdown = ({
  options,
  label,
  value,
  onChange,
}: MultiSelectDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [selectedOptions, setSelectedOptions] = useState(value || []);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  // Sync selectedOptions with value prop
  useEffect(() => {
    setSelectedOptions(value || []);
  }, [value]);

  // Toggle dropdown visibility
  const toggleDropdown = () => {
    setIsOpen(prev => !prev);
  };

  // Handle search input change
  const handleSearchChange = (text: string) => {
    setSearch(text);
  };

  // Handle checkbox selection
  const handleOptionSelect = (option: {label: string; value: string}) => {
    let updatedOptions;
    if (selectedOptions.some(item => item.value === option.value)) {
      // Remove the option if it's already selected
      updatedOptions = selectedOptions.filter(
        item => item.value !== option.value,
      );
    } else {
      // Add the option if it's not selected
      updatedOptions = [...selectedOptions, option];
    }
    setSelectedOptions(updatedOptions);
    onChange(updatedOptions); // Pass updated selections to parent
  };

  // Filter options based on search query
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(search.toLowerCase()),
  );

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity
        style={[
          styles.selectContainer,
          {flexDirection: isRTL ? 'row-reverse' : 'row'},
        ]}
        onPress={toggleDropdown}>
        <Text style={styles.selectedValues}>
          {selectedOptions.length > 0
            ? selectedOptions.map(option => option.label).join(', ')
            : t('Select')}
        </Text>
      </TouchableOpacity>

      <Modal
        visible={isOpen}
        transparent={true}
        animationType="fade"
        onRequestClose={toggleDropdown}>
        <TouchableWithoutFeedback onPress={toggleDropdown}>
          <View style={styles.overlay}>
            <TouchableWithoutFeedback>
              <View style={styles.dropdown}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search..."
                  value={search}
                  onChangeText={handleSearchChange}
                />
                <FlatList
                  data={filteredOptions}
                  showsVerticalScrollIndicator={false}
                  keyExtractor={item => item.value}
                  nestedScrollEnabled
                  renderItem={({item}) => (
                    <TouchableOpacity
                      style={[
                        styles.option,
                        selectedOptions.some(
                          option => option.value === item.value,
                        ) && styles.selectedOption,
                      ]}
                      onPress={() => handleOptionSelect(item)}>
                      <Text
                        style={[
                          styles.optionText,
                          selectedOptions.some(
                            option => option.value === item.value,
                          ) && styles.selectedOptionText,
                        ]}>
                        {item.label}
                      </Text>
                    </TouchableOpacity>
                  )}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

export default MultiSelectDropdown;

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
    width: '100%',
  },
  label: {
    fontSize: fp(2),
    color: colors.black,
    marginBottom: 5,
    fontFamily: Fonts.medium,
  },
  selectContainer: {
    borderRadius: 10,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.1)',

    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedValues: {
    fontSize: 14,
    color: colors.black,
    fontFamily: Fonts.medium,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdown: {
    width: '90%',
    borderRadius: 8,
    backgroundColor: 'rgba(227,231,235,1)',
    maxHeight: 250,
    overflow: 'hidden',
    elevation: 5,
  },
  searchInput: {
    padding: 10,
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: '#333',
    backgroundColor: 'white',
  },
  option: {
    padding: 10,
    borderBottomWidth: 0.2,
    borderBottomColor: colors.lightgreay,
  },
  optionText: {
    fontSize: 14,
    color: '#333',
    fontFamily: Fonts.medium,
  },
  selectedOption: {
    backgroundColor: colors.themeColor,
  },
  selectedOptionText: {
    color: colors.white,
    fontFamily: Fonts.medium,
  },
});
