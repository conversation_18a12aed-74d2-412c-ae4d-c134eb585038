import {StyleSheet, Dimensions, Platform} from 'react-native';
import colors from '../../Utils/colors';
import {Fonts} from '../../Utils/Fonts';
import {responsiveFontSize} from '../../Utils/constant';
import {fp, hp} from '../../Helper/ResponsiveDimensions';

const {width} = Dimensions.get('screen');
const dynamicPadding = width * 0.04;
const dynamicRadius = width * 0.02;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: dynamicPadding,
    backgroundColor: colors.white,
    zIndex: 2,
  },
  content: {
    flex: 1,
    paddingHorizontal: dynamicPadding,
  },
  profileContainer: {
    marginBottom: dynamicPadding,
  },
  loggedInButton: {
    borderRadius: dynamicRadius,
    marginBottom: hp(1),
    paddingVertical: dynamicPadding * 0.6,
    paddingHorizontal: dynamicPadding * 0.8,
  },
  loggedInButtonContent: {
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  loggedInText: {
    color: '#FFF',
    fontSize: dynamicPadding * 0.9,
    fontFamily: Fonts.medium,
  },
  arrowDownIcon: {
    width: dynamicPadding * 0.9,
    height: dynamicPadding * 0.9,
    tintColor: '#FFF',
  },
  drawerItemsContainer: {
    justifyContent: 'space-between',
  },
  drawerItemWrapper: {
    marginVertical: dynamicPadding * 0.4,
    borderRadius: dynamicRadius,
    backgroundColor: colors.white,
    shadowColor: colors.themeBackground,
    shadowOffset: {width: 0, height: Platform.OS === 'ios' ? 1 : 0.1},
    shadowOpacity: Platform.OS === 'ios' ? 0.2 : 0.05,
    shadowRadius: Platform.OS === 'ios' ? 0.4 : 0.4,
    elevation: Platform.OS === 'android' ? 1 : 0,
  },
  drawerItem: {
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: dynamicPadding * 0.4,
    paddingHorizontal: dynamicPadding,
    borderRadius: dynamicRadius,
  },
  drawerItemText: {
    fontSize: fp(1.6),
    color: colors.txtGrey1,
    flex: 1,
    fontFamily: Fonts.medium,
  },
  rightArrowGray: {
    opacity: 0.8,
    tintColor: colors.grey,
  },
});

export default styles;
