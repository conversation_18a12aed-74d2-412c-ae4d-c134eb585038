import React, {useEffect, useCallback, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  I18nManager,
  Modal,
  StyleSheet,
  ScrollView,
  FlatList,
  Dimensions,
  TouchableWithoutFeedback,
} from 'react-native';
import ProfileCard from '../Custom_Components/ProfileCard';
import LinearGradient from 'react-native-linear-gradient';
import icons from '../../Utils/icons';
import styles from './styles';
import {
  CommonActions,
  useFocusEffect,
  useNavigation,
} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {showToast} from '../../Components/ToastHelper';
import {
  useLogoutMutation,
  useGetStudentDetailQuery,
  useGetTutorProfileQuery,
  useProfileDetailsQuery,
  useSwitchProfileMutation,
  useGetUnreadMessageCountQuery,
} from '../../Api/ApiSlice';
import {useSelector, useDispatch} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  clearAuthData,
  setAuthData,
  setIsLoggedIn,
} from '../../Features/authSlice';
import {WAS_KILLED} from '../../Utils/storageKeys';
import useAppStateCheck from '../../Helper/UseGetAppState';
import {updateSplashVisible} from '../../Helper/SplashHelper/SplashHelper';
import {Fonts} from '../../Utils/Fonts';
import colors from '../../Utils/colors';
import {APP_VERSION, responsiveFontSize} from '../../Utils/constant';
import CustomDropDown from '../CustomDropDown';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import RNRestart from 'react-native-restart';
import TaleemLoader from '../TaleemLoader/TaleemLoader';
import i18next from '../../Config/i18next';
import {setAppLocale} from '../../Features/authSlice';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import useGlobalUnreadCount from '../../Helper/CustomHooks/UseGloabalUnreadNotiCount';

const DrawerItem = ({
  item,
  onPress,
  unreadMessageCount,
  unreadSupportMessageCount,
}) => {
  console.log('🚀 ~ unreadSupportMessageCount:', unreadSupportMessageCount);
  const {width} = Dimensions.get('screen');
  const dynamicPadding = width * 0.04;
  const dynamicRadius = width * 0.02;
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  console.log('🚀 ~ DrawerItem ~ unreadMessageCount:', unreadMessageCount);
  return (
    // <View style={applyShadowStyleIos(styles.drawerItemWrapper)}>
    <View>
      <View style={styles.drawerItemWrapper}>
        <TouchableOpacity
          style={[
            styles.drawerItem,
            {flexDirection: isRTL ? 'row-reverse' : 'row'},
          ]}
          onPress={onPress}>
          <Text
            style={[
              styles.drawerItemText,
              {textAlign: isRTL ? 'right' : 'left'},
            ]}>
            {item.label}
          </Text>
          {item.route == 'ChatList' && unreadMessageCount > 0 && (
            <View
              style={applyShadowStyleIos({
                backgroundColor: colors.themeBackground,
                width: fp(3), // Fixed size; adjust as needed
                height: fp(3), // Equal to width for a circle
                borderRadius: fp(2), // Half of width/height for a circle
                justifyContent: 'center', // Vertically center the text
                alignItems: 'center', // Horizontally center the text
                alignSelf: 'center',
                // marginLeft: hp(60),
              })}>
              <Text
                style={{
                  color: 'white',
                  fontSize: fp(1.4),
                  fontFamily: Fonts.medium,
                }}>
                {unreadMessageCount}
              </Text>
            </View>
          )}
          {item.route == 'SupportMenu' && unreadSupportMessageCount > 0 && (
            <View
              style={applyShadowStyleIos({
                backgroundColor: colors.themeBackground,
                width: fp(3), // Fixed size; adjust as needed
                height: fp(3), // Equal to width for a circle
                borderRadius: fp(2), // Half of width/height for a circle
                justifyContent: 'center', // Vertically center the text
                alignItems: 'center', // Horizontally center the text
                alignSelf: 'center',
                // marginLeft: hp(60),
              })}>
              <Text
                style={{
                  color: 'white',
                  fontSize: fp(1.4),
                  fontFamily: Fonts.medium,
                }}>
                {unreadSupportMessageCount}
              </Text>
            </View>
          )}
          <Image
            source={isRTL ? icons.leftArrowWhite : icons.rightArrowGray}
            style={[
              styles.rightArrowGray,
              {
                width: isRTL ? wp(4) : wp(5),
                height: isRTL ? wp(4) : wp(5),
              },
            ]}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const CustomDrawerContent = props => {
  const {t, i18n} = useTranslation();
  const dispatch = useDispatch();
  const token = useSelector(state => state.auth.token);
  const navigation = useNavigation();
  // const [logout, {isLoading: isLogoutLoading}] = useLogoutMutation();
  const [appStateStatus, setAppStateStatus] = useState(undefined);
  const [selectedUserRole, setSelectedUserRole] = useState({});
  const [isRoleSwitchLoading, setIsRoleSwitchLoading] = useState(false);
  const dynamicPadding = width * 0.04;
  const dynamicRadius = width * 0.02;
  const {width} = Dimensions.get('screen');
  useAppStateCheck({setAppStateStatus});
  const userType = useSelector(state => state.auth.user_type);
  console.log('🚀 ~ MainNavigator ~ userType:', typeof userType);
  const isRTL = i18n.language === 'ar';

  const {
    data: studentData,
    refetch: refetchStudentData,
    error,
  } = useProfileDetailsQuery();

  const [switchProfile, {data: switchProfileRes}] = useSwitchProfileMutation();
  const authState = useSelector(state => state.auth); // Get current auth state
  const {unreadMessageCount, refetchMessageCount, unreadSupportMessageCount} =
    useGlobalUnreadCount();
  // const {unreadCount, unreadMessageCount, unreadSupportMessageCount} =
  //   useSelector(state => state.notifications);

  console.log('🚀 ~ unreadMessageCount:', unreadMessageCount);
  //   data: studentData,
  //   error,
  //   refetch: refetchStudentData,
  //   isLoading,
  // } = userType == '3' ? useGetTutorProfileQuery() : useGetStudentDetailQuery();

  console.log('🚀 ~ CustomDrawerContent ~ data: Custommmm', studentData?.data);

  const [isLogoutModalVisible, setIsLogoutModalVisible] = useState(false);
  const [isLanguageModalVisible, setIsLanguageModalVisible] = useState(false);
  const [isChangeUserType, setIsChangeUserType] = useState(false);
  const {appLocale} = useSelector(state => state?.auth);
  const refetchDataOnFocus = useCallback(() => {
    console.log('Refetching student data as drawer is focused');
    refetchStudentData();
  }, [refetchStudentData]);

  useFocusEffect(refetchDataOnFocus);
  useEffect(() => {
    if (error && error?.status === 401) {
      handleLogout();
      showToast('error', t('failedToLoadProfile'), 'bottom', isRTL);
    }
  }, [error]);

  const menuItems = [
    {label: t('home'), route: 'Home'},
    {label: t('bookOpenSession'), route: 'BookOpenSessionList'},
    {label: t('chats'), route: 'ChatList'},
    {label: t('rating_feedbacks'), route: 'RantingsAndFeedback'},
    {label: t('settings'), route: 'SettingsMenu'},
    {label: t('support'), route: 'SupportMenu'},
    {label: t('about'), route: 'AboutMenu'},

    {label: t('logout'), route: 'Logout'},
  ];

  const tutorMenuItems = [
    {label: t('home'), route: 'Home'},
    // {label: t('earning'), route: 'Earnings'},
    {label: t('chats'), route: 'ChatList'},
    {label: t('meetingPreference'), route: 'MeetingPreference'},
    {label: t('settings'), route: 'SettingsMenu'},
    {label: t('support'), route: 'SupportMenu'},
    {label: t('about'), route: 'AboutMenu'},
    // {label: t('my_rate_card'), route: 'RateCardScreen'},
    // {label: t('EditMeetingPreference'), route: 'MeetingPreference'},
    // {label: t('change_language'), route: 'Language'},

    {label: t('logout'), route: 'Logout'},
  ];

  const handleLogoutConfirmation = () => {
    // Show logout confirmation modal
    setIsLogoutModalVisible(true);
  };

  const handleCancelLogout = () => {
    // Hide logout confirmation modal
    setIsLogoutModalVisible(false);
  };
  const handleCancelLanguageChange = () => {
    // Hide logout confirmation modal
    setIsLanguageModalVisible(false);
  };
  const handleLanguageConfirmation = () => {
    // Show logout confirmation modal
    setIsLanguageModalVisible(true);
  };
  const handleLogout = async () => {
    try {
      // Set the splash screen visibility to false on logout
      await updateSplashVisible(null, false, true); // Mark as logout to hide splash screen
      await AsyncStorage.removeItem('user'); // Clear user data
      dispatch(clearAuthData()); // Clear auth data in Redux store
      dispatch(setIsLoggedIn(false)); // Set login state to false

      showToast('success', t('logged_out'), 'bottom', isRTL);

      // Close logout modal
      setIsLogoutModalVisible(false);

      // Reset navigation stack and navigate to WelcomeScreen
      props.navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{name: 'WelcomeScreen'}], // Navigate directly to WelcomeScreen
        }),
      );
    } catch (error) {
      console.log('Logout API error:', error);
      showToast('error', t('logged_out_fail'), 'bottom', isRTL);
      setIsLogoutModalVisible(false); // Close the modal in case of error
    }
  };
  const onContinueClick = async appLang => {
    await i18next.changeLanguage(appLang);
    await AsyncStorage.setItem('selectedLang', JSON.stringify(appLang));
    dispatch(setAppLocale(appLang));
    setTimeout(() => {
      handleCancelLanguageChange();
      props.navigation.closeDrawer();
    }, 1000);
  };

  const handleNavigation = route => {
    if (route === 'Logout') {
      // Change to show confirmation modal instead of direct logout
      handleLogoutConfirmation();
    } else if (route == 'HomeTutorScreen') {
      props.navigation.closeDrawer();
    } else if (route === 'Language') {
      handleLanguageConfirmation();
    } else {
      props.navigation.navigate(route);
    }
  };

  console.log('🚀 ~ handleSwitchUserRole ~ authState:', authState);

  function handleSwitchUserRole(params) {
    props.navigation.navigate('ChooseProfile');
    // setIsChangeUserType(false);
    // if (isRoleSwitchLoading) {
    //   return;
    // }
    // setIsRoleSwitchLoading(true);
    // switchProfile(userType == '3' ? '1' : '3')
    //   .unwrap()
    //   .then(response => {
    //     console.log('🚀 ~ Switch Tutor:', response);
    //     showToast('error', response?.message, 'bottom', isRTL);
    //     // Create updated user data with the new user_type
    //     setIsChangeUserType(false);
    //     const updatedUserData = {
    //       token: authState.token, // Keep existing token
    //       userId: authState.userId, // Keep existing userId
    //       user_type: response.data.user_type, // Update user_type
    //       action_type: authState.action_type, // Keep existing action_type
    //       profile_image: authState.profile_image, // Keep existing profile_image
    //     };

    //     // Dispatch setAuthData with the updated user data
    //     dispatch(setAuthData(updatedUserData));

    //     setTimeout(() => {
    //       setIsRoleSwitchLoading(false);
    //       RNRestart.Restart();
    //     }, 500);
    //   })
    //   .catch(err => {
    //     setIsRoleSwitchLoading(false);
    //     setIsChangeUserType(false);
    //     console.error('Switch Tutor:', err);
    //     showToast('error', err?.data?.message, 'bottom', isRTL);
    //   });
  }

  // useFocusEffect(
  //   useCallback(() => {
  //     setIsChangeUserType(false); // Reset the state when the drawer is focused
  //   }, []),
  // );

  return (
    <SafeAreaView style={styles.container}>
      <TaleemLoader isLoading={isRoleSwitchLoading} />
      <View style={styles.content}>
        <View style={styles.profileContainer}>
          {userType == '3' ? (
            <ProfileCard
              name={studentData?.data?.name || t('daniela_name')}
              email={studentData?.data?.email || t('daniela_email')}
              imageUrl={studentData?.data?.image}
              onViewProfile={() => props.navigation.navigate('ProfilePage')}
            />
          ) : (
            <ProfileCard
              name={studentData?.data?.name || t('daniela_name')}
              email={studentData?.data?.email || t('daniela_email')}
              imageUrl={studentData?.data?.image}
              onViewProfile={() => props.navigation.navigate('ProfilePage')}
            />
          )}
        </View>
        <TouchableOpacity
          style={{}}
          onPress={() => props.navigation.navigate('ChooseProfileAfterLogin')}>
          <LinearGradient
            colors={['#40A39B', '#C6FFC9']}
            start={isRTL ? {x: 1, y: 1} : {x: 0, y: 0}}
            end={isRTL ? {x: 0, y: 0} : {x: 1, y: 1}}
            style={styles.loggedInButton}>
            <View
              style={[
                styles.loggedInButtonContent,
                {flexDirection: isRTL ? 'row-reverse' : 'row'},
              ]}>
              <Text
                style={[
                  styles.loggedInText,
                  {textAlign: isRTL ? 'left' : 'right'},
                ]}>
                {t('manage_profile')}
              </Text>
              {/* {userType == '3' ? (
                <Text
                  style={[
                    styles.loggedInText,
                    {textAlign: isRTL ? 'left' : 'right'},
                  ]}>
                  {t('logged_in_as_tutor')}
                </Text>
              ) : userType == '2' ? (
                <Text
                  style={[
                    styles.loggedInText,
                    {textAlign: isRTL ? 'left' : 'right'},
                  ]}>
                  {t('loggedInAsParent')}
                </Text>
              ) : (
                <Text
                  style={[
                    styles.loggedInText,
                    {textAlign: isRTL ? 'left' : 'right'},
                  ]}>
                  {t('logged_in_as_student')}
                </Text>
              )} */}
              <Image
                source={icons.rightArrowGrade}
                style={[
                  styles.arrowDownIcon,
                  {
                    transform: isRTL
                      ? [{rotate: '180deg'}]
                      : [{rotate: '0deg'}],
                  },
                ]}
                resizeMode="contain"
              />
            </View>
          </LinearGradient>
        </TouchableOpacity>
        {isChangeUserType == true && (
          <View
            style={
              {
                // position: 'absolute',
                // zIndex: 1000,
                // flex: 1,
              }
            }>
            <TouchableOpacity
              onPress={handleSwitchUserRole}
              style={{
                backgroundColor: '#fff',
                borderRadius: 10,
                // top: hp(16),
                // marginTop: hp(16),
                width: wp(72),
                paddingVertical: hp(0.4),
                alignItems: 'flex-start',
                justifyContent: 'center',
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 3,
                // marginLeft: wp(5),
              }}>
              <Text
                style={[
                  styles.loggedInText,
                  {
                    textAlign: 'center',
                    color: colors.grey,
                    marginVertical: hp(0.8),
                    marginLeft: wp(2),
                  },
                ]}>
                {`Log in as ${userType == '3' ? 'Student' : 'Tutor'}`}
              </Text>
            </TouchableOpacity>
          </View>
        )}
        <ScrollView showsVerticalScrollIndicator={false}>
          <View
            style={[
              styles.drawerItemsContainer,
              {flex: userType == '3' ? 0.8 : 1},
            ]}>
            {userType == '3'
              ? tutorMenuItems.map((item, index) => (
                  // <ScrollView>
                  <DrawerItem
                    key={index}
                    item={item}
                    onPress={() => handleNavigation(item.route)}
                    unreadMessageCount={unreadMessageCount}
                    unreadSupportMessageCount={unreadSupportMessageCount}
                  />
                  // </ScrollView>
                ))
              : menuItems.map((item, index) => (
                  // <ScrollView nestedScrollEnabled>
                  <DrawerItem
                    key={index}
                    item={item}
                    onPress={() => handleNavigation(item.route)}
                    unreadMessageCount={unreadMessageCount}
                    unreadSupportMessageCount={unreadSupportMessageCount}
                  />
                  // </ScrollView>
                ))}
          </View>
        </ScrollView>
        <Text
          style={{
            alignSelf: 'center',
            position: 'absolute',
            bottom: 0,
            fontFamily: Fonts.medium,
          }}>
          {t('version')} {APP_VERSION}
        </Text>
      </View>

      {/* Logout Confirmation Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={isLogoutModalVisible}
        onRequestClose={handleCancelLogout}>
        <View style={logoutModalStyles.centeredView}>
          <View style={logoutModalStyles.modalView}>
            <Text style={logoutModalStyles.modalTitle}>
              {t('confirmLogout')}
            </Text>
            <Text style={logoutModalStyles.modalText}>
              {t('logOutMessage')}
            </Text>
            <View style={logoutModalStyles.buttonContainer}>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  logoutModalStyles.cancelButton,
                ]}
                onPress={handleCancelLogout}>
                <Text style={logoutModalStyles.cancelButtonText}>
                  {t('cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  logoutModalStyles.logoutButton,
                ]}
                onPress={handleLogout}>
                <Text style={logoutModalStyles.logoutButtonText}>
                  {t('logout')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      <Modal
        animationType="fade"
        transparent={true}
        visible={isLanguageModalVisible}
        onRequestClose={handleCancelLanguageChange}>
        <View style={logoutModalStyles.centeredView}>
          <View style={logoutModalStyles.modalView}>
            <Text style={logoutModalStyles.modalTitle}>
              {t('selectLanguage')}
            </Text>
            <Text style={logoutModalStyles.modalText}>
              {t('chooseLanguage')}
            </Text>
            <View style={logoutModalStyles.buttonContainer}>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  appLocale == 'ar'
                    ? logoutModalStyles.logoutButton
                    : logoutModalStyles.cancelButton,
                ]}
                onPress={() => onContinueClick('ar')}>
                <Text
                  style={
                    appLocale == 'ar'
                      ? logoutModalStyles.logoutButtonText
                      : logoutModalStyles.cancelButtonText
                  }>
                  عربي
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  appLocale == 'en'
                    ? logoutModalStyles.logoutButton
                    : logoutModalStyles.cancelButton,
                ]}
                onPress={() => onContinueClick('en')}>
                <Text
                  style={
                    appLocale == 'en'
                      ? logoutModalStyles.logoutButtonText
                      : logoutModalStyles.cancelButtonText
                  }>
                  {/* {t('englishL')} */}
                  English
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

// Additional styles for the logout modal
const logoutModalStyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 18,
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  modalText: {
    marginBottom: 15,
    fontFamily: Fonts.medium,
    color: colors.black,
    textAlign: 'center',
    fontSize: fp(1.6),
  },
  buttonContainer: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    marginHorizontal: wp(1),
    minWidth: 100,
  },
  cancelButton: {
    backgroundColor: colors.txtGrey,
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    color: 'black',
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    textAlign: 'center',
  },
  logoutButtonText: {
    color: 'white',
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    textAlign: 'center',
  },
});

export default CustomDrawerContent;
