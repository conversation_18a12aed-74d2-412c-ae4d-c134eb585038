import {ActivityIndicator, Modal, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {hp, wp} from '../../Helper/ResponsiveDimensions';

const TaleemLoader = ({isLoading}) => {
  console.log('🚀 ~ isLoading:', isLoading);
  return (
    <Modal animationType={'fade'} visible={isLoading} transparent>
      <View style={styles.loadingContainer}>
        <View style={styles.loader}>
          <ActivityIndicator size={'large'} color={'#40A39B'} />
        </View>
      </View>
    </Modal>
  );
};

export default TaleemLoader;

const styles = StyleSheet.create({
  loadingContainer: {
    height: hp(120),
    width: wp(100),
    alignSelf: 'center',
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
});
