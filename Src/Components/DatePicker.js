import React, {useState} from 'react';
import {
  View,
  Text,
  Button,
  Platform,
  Modal,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import moment from 'moment';
import icons from '../Utils/icons';
import {fp} from '../Helper/ResponsiveDimensions';
import {Fonts} from '../Utils/Fonts';
import colors from '../Utils/colors';
import {useTranslation} from 'react-i18next';

const DatePicker = ({
  initialDate = new Date(),
  onDateChange,
  minimumDate,
  maximumDate,
  label,
}) => {
  const [date, setDate] = useState(initialDate);
  const [showPicker, setShowPicker] = useState(false);
  const {t} = useTranslation();

  const onChange = (event, selectedDate) => {
    if (selectedDate) {
      setDate(selectedDate);
      onDateChange && onDateChange(selectedDate); // Trigger callback
    }
    setShowPicker(false); // Close picker
  };

  const formatDate = date => {
    return moment(date).format('DD MMM YYYY'); // Example: Jan 07, 2025
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      <TouchableOpacity
        onPress={() => setShowPicker(true)}
        style={styles.datePicker}>
        <Text style={styles.dateTxt}>{formatDate(initialDate)}</Text>
        <Image source={icons.downchevron} />
      </TouchableOpacity>

      {/* Modal for iOS */}
      {Platform.OS === 'ios' && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showPicker}
          onRequestClose={() => setShowPicker(false)}>
          <View style={styles.modalContainer}>
            <View style={styles.pickerContainer}>
              <TouchableOpacity
                style={styles.doneButton}
                onPress={() => setShowPicker(false)}>
                <Text style={styles.doneText}>{t('done')}</Text>
              </TouchableOpacity>
              <DateTimePicker
                value={date}
                mode="date"
                display="spinner"
                onChange={onChange}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
              />
            </View>
          </View>
        </Modal>
      )}

      {/* Picker for Android */}
      {Platform.OS === 'android' && showPicker && (
        <DateTimePicker
          value={date}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowPicker(false);
            onChange(event, selectedDate);
          }}
          minimumDate={new Date(2000, 0, 1)}
          maximumDate={new Date(2100, 11, 31)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  date: {
    fontSize: 16,
    marginBottom: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  pickerContainer: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 10,
    padding: 16,
  },
  doneButton: {
    alignSelf: 'flex-end',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  doneText: {
    color: '#007AFF',
    fontSize: 16,

    fontWeight: '600',
  },
  datePicker: {
    paddingHorizontal: fp(1.6),
    marginVertical: fp(2),
    borderWidth: 1,
    borderColor: colors.txtGrey,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: fp(6.4),
  },
  dateTxt: {
    fontFamily: Fonts.medium,
    fontSize: fp(1.6),
    color: colors.black,
  },
});

export default DatePicker;
