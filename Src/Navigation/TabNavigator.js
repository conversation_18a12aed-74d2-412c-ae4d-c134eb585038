import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createDrawerNavigator} from '@react-navigation/drawer';
import {createStackNavigator} from '@react-navigation/stack';
import React from 'react';
import {Image, View, StyleSheet} from 'react-native';
import CustomDrawerContent from '../Components/CustomDrawerContent';
import {normalize} from '../Helper/NormalizeFont';
import HomeScreen from '../Screens/Home';
import MyClassScreen from '../Screens/MyClass';
import SettingsScreen from '../Screens/Settings';
import LoginScreen from '../Screens/SignIn';
import WalletScreen from '../Screens/Wallet';
import colors from '../Utils/colors';
import icon from '../Utils/icons';
import {useTranslation} from 'react-i18next';
import {useSelector} from 'react-redux';
import CustomDrawerContentTutor from '../Components/CustomDrawerTutor';
import {Fonts} from '../Utils/Fonts';
import {hp} from '../Helper/ResponsiveDimensions';
import Earnings from '../Screens/Earning';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

// Tab Navigator Component
const TabNavigator = () => {
  const {t, i18n} = useTranslation();
  const {user_type} = useSelector(state => state?.auth);
  const isRTL = i18n.language === 'ar';

  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={({route}) => ({
        tabBarIcon: ({focused}) => {
          let iconSource;

          if (route.name === 'Home') {
            iconSource = focused ? icon.homeIconColor : icon.homeIcon;
          } else if (route.name === 'My Class') {
            iconSource = focused ? icon.classIconColor : icon.classIcon;
          } else if (route.name === 'Wallet') {
            iconSource = focused ? icon.walletIcon : icon.walletIcon;
          }

          return (
            <View style={styles.tabIconContainer}>
              <Image
                source={iconSource}
                style={styles.tabIcon}
                tintColor={focused ? colors.themeColor : null}
              />
            </View>
          );
        },
        headerShown: false,
        tabBarActiveTintColor: colors.themeColor,
        tabBarInactiveTintColor: 'gray',
        tabBarLabelStyle: styles.tabBarLabel,
        tabBarStyle: styles.tabBar,
      })}>
      {isRTL ? (
        <>
          <Tab.Screen
            name="Wallet"
            component={user_type == 3 ? Earnings : WalletScreen}
            options={{tabBarLabel: t('wallet')}}
          />
          <Tab.Screen
            name="My Class"
            component={MyClassScreen}
            options={{tabBarLabel: t('myclass')}}
          />
          <Tab.Screen
            name="Home"
            component={HomeScreen}
            options={{tabBarLabel: t('home')}}
          />
        </>
      ) : (
        <>
          <Tab.Screen
            name="Home"
            component={HomeScreen}
            options={{tabBarLabel: t('home')}}
          />
          <Tab.Screen
            name="My Class"
            component={MyClassScreen}
            options={{tabBarLabel: t('myclass')}}
          />

          {user_type == 3 ? (
            <Tab.Screen
              name="Wallet"
              component={Earnings}
              options={{tabBarLabel: t('earning')}}
            />
          ) : (
            <Tab.Screen
              name="Wallet"
              component={WalletScreen}
              options={{tabBarLabel: t('wallet')}}
            />
          )}
        </>
      )}
    </Tab.Navigator>
  );
};

// Main Navigator - Drawer wraps the Tab Navigator
const MainNavigator = () => {
  const userType = useSelector(state => state.auth.user_type);
  console.log('🚀 ~ MainNavigator ~ userType:', userType);
  const {i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  return (
    <Drawer.Navigator
      initialRouteName="MainTabs"
      // drawerContent={props => {userType == '3' ? <CustomDrawerContentTutor {...props}/> : <CustomDrawerContent {...props} />}}
      drawerContent={props => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: styles.drawer,
        overlayColor: 'rgba(0,0,0,0.5)',
        drawerType: 'front',
        swipeEnabled: false,
        drawerPosition: isRTL ? 'right' : 'left',
      }}>
      <Drawer.Screen name="MainTabs" component={TabNavigator} />
      <Drawer.Screen name="Settings" component={SettingsScreen} />
      <Drawer.Screen name="Login" component={LoginScreen} />
    </Drawer.Navigator>
  );
};

const styles = StyleSheet.create({
  drawer: {
    width: '80%',
    zIndex: 1000,
  },
  tabIconContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    height: hp(3),
  },
  tabIcon: {
    width: 24,
    height: 24,
    // marginTop: hp(0.6),
  },
  tabBarLabel: {
    fontSize: normalize(4),
    fontFamily: Fonts.medium,
    marginBottom: hp(0.4),
    // lineHeight: hp(2.4),
  },
  tabBar: {
    position: 'absolute',
    bottom: 0,
    zIndex: 0,
    elevation: 0,
    backgroundColor: colors.white,

    // height: hp(6),
  },
});

export default MainNavigator;
