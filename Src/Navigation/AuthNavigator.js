import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import React, {useEffect, useState} from 'react';
import OtpVerificationScreen from '../Screens/OtpVerify';
import SelectLanguage from '../Screens/SelectLanguage';
import LoginScreen from '../Screens/SignIn';
import CreateYourAccount from '../Screens/SignUp';
import Splash from '../Screens/Splash';
import WelcomeScreen from '../Screens/WelcomeScreen';
import TabNavigator from './TabNavigator';
import Chooseyourgrade from '../Screens/Chooseyourgrade';
import ChooseyourCurriculum from '../Screens/Chooseyourcurriculum';
import TutorList from '../Screens/TutorsList';
import TutorsDetails from '../Screens/TutorsDetails';
import FilterScreen from '../Screens/Filter';
import BookYourTutor from '../Screens/BookYourTutor';
import SelectAvailableSlot from '../Screens/SelectAvailableSlot';
import BookYourTutorConformation from '../Screens/BookYourTutorConformation';
import Search from '../Screens/Search';
import ChooseCategory from '../Screens/ChooseCategory';
import TutorsListRecreational from '../Screens/TutorsListRecreational';
import TutorsListCourses from '../Screens/TutorsListCourses';
import TutorsDetailsCourses from '../Screens/TutorsDetailsCourses';
import CompleteYourProfilePageTwo from '../Screens/CompleteYourProfile/CompleteYourProfilePageTwo';
import CompleteYourProfilePageOne from '../Screens/CompleteYourProfile/CompleteYourProfilePageOne';
import AllSetPage from '../Screens/CompleteYourProfile/AllSetPage';
import HomeScreen from '../Screens/Home';
import ProfilePage from '../Screens/Profile';
import UpdateProfile from '../Screens/Profile/UpdateProfile';
import ChooseCategoryCourses from '../Screens/ChooseCategoryCourses';
import TutorMainNavigator from './TutorTabNavigator';
import RateCardScreen from '../Screens/RateCard';
import TutorTabNavigator from './TutorTabNavigator';
import ClassDetails from '../Screens/ClassDetails';
import AddYourRateCard from '../Screens/AddYourRateCard';
import StartClassScreen from '../Screens/StartClass';
import StudentAddress from '../Screens/StudentAddress';
import ChangeEmail from '../Screens/editNumberAndEmail/changeEmail';
import ChangePhoneNumber from '../Screens/editNumberAndEmail/changePhoneNumber';
import MeetingPoint from '../Screens/MeetingPoint ';
import ManageProfile from '../Screens/SideMenuFlow/ManageProfile';
import AddAccount from '../Screens/SideMenuFlow/ManageProfile/AddAccount';
import RaiseYourConcern from '../Screens/SideMenuFlow/RaiseYourConcern';
import ContactUs from '../Screens/SideMenuFlow/ContactUs';
import UploadDocTutor from '../Screens/UploadDocTutor';
import MyBookings from '../Screens/MyBookings';
import ScheduleTutor from '../Screens/ScheduleTutor/Index';
import {useSelector} from 'react-redux';
import TutorApprovalPending from '../Screens/CompleteYourProfile/TutorApprovalPending';
import AddTutorSchedule from '../Screens/AddTutorSchedule';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {FCM_TOKEN, SHOW_SPLASH} from '../Utils/storageKeys';
import EditAcademic from '../Screens/AddYourRateCard/EditAcademic';
import EditRecreational from '../Screens/AddYourRateCard/EditRecreational';
import EditCourses from '../Screens/AddYourRateCard/EditCourses';
import MeetingPreference from '../Screens/MeetingPreference';
import SetYourPreference from '../Screens/MeetingPreference/SetYourPreference';
import EditMeetingPoint from '../Screens/MeetingPreference/EditMeetingPoint';
import StudentList from '../Screens/StudentList/StudentList';
import BookYourTutorConfirmation from '../Screens/BookYourTutorConformation';
import QualificationsAndOtherPreferences from '../Screens/QualificationsAndOtherPreferences/QualificationsAndOtherPreferences';
import BookYourTutorCourses from '../Screens/BookYourTutor/BookYourTutorCourses';
import StudentClassDetails from '../Screens/StudentClassDetails';
import ClassDetailsTwo from '../Screens/StudentClassDetails/ClassDetailsTwo';
import RatingScreen from '../Screens/RatingScreen';
import ChatScreen from '../Screens/ChatScreen';
import OfflineScreen from '../Screens/OflineMeeting';
import ChatList from '../Screens/ChatList';
import DeviceInfo from 'react-native-device-info';
import {useUpdateDeviceDetailsMutation} from '../Api/ApiSlice';
import {Platform} from 'react-native';
import PaymentScreen from '../Screens/BookYourTutor/PaymentGateway/PaymentScreen';
import PaymentSuccessfull from '../Screens/PaymentSuccessfull/PaymentSuccessfull';
import OpenSession from '../Screens/OpenSession';
import Earnings from '../Screens/Earning';
import Withdrawal from '../Screens/Withdrawl';
// import Test from '../Screens/TestScreen/Test';
import VideoTest from '../Screens/TestScreen/VideoTest';
import BookOpenSessionList from '../Screens/BookOpenSession/BookOpenSessionList';
import ViewOpenSessionDetails from '../Screens/BookOpenSession/ViewOpenSessionDetails';
import ViewEnrolledOpenSessionDetails from '../Screens/MyClass/ViewEnrolledOpenSessionDetails';
import Notification from '../Screens/Notifications/Notification';
import StudentProfileForTutor from '../Screens/Profile/StudentProfileForTutor';
import DocViewer from '../Components/DocViewer';
import RebookTutor from '../Screens/BookYourTutor/RebookTutor';
import TutorDocuments from '../Screens/TutorDocuments/TutorDocuments';
import CompleteProfileTutorAfterSignUpTwo from '../Screens/CompleteProfileTutorAfterSignUp/CompleteProfileTutorAfterSignUpTwo';
import CompleteProfileTutorAfterSignUp from '../Screens/CompleteProfileTutorAfterSignUp/CompleteProfileTutorAfterSignUp';
import FaqScreeen from '../Screens/SideMenuFlow/Faq';
import ChatWithAdmin from '../Screens/SideMenuFlow/ChatWithAdmin';
import TermsOfService from '../Screens/SideMenuFlow/TermsOfService';
import NotficationSetting from '../Screens/SideMenuFlow/NotificationTab';
import Transactions from '../Screens/Transactions';
import RantingsAndFeedback from '../Screens/SideMenuFlow/RatingsAndFeedback';
import SavedPaymentMethods from '../Screens/SavedPaymentMethods';
import WhiteBoard from '../Screens/StartClass/WhiteBoard';
import ConcernScreen from '../Screens/SideMenuFlow/Concern';
import Settings from '../Screens/SideMenuFlow/Settings';
import AboutUs from '../Screens/SideMenuFlow/AboutUs';
import DeleteAccount from '../Screens/SideMenuFlow/DeleteAccount';
import BookingDetailsTutor from '../Screens/BookingDetailsTutor';
import SudgestedLoc from '../Screens/SudgestedLoc';
import SuggestedLocList from '../Screens/SudgestedLoc/ SuggestedLocList';
import ViewAllList from '../Screens/SudgestedLoc/ViewAllList';
import SettingsMenu from '../Screens/SettingsMenu/Index';
import AddBankAccount from '../Screens/AddBankAccount';
import ChooseProfile from '../Screens/ChooseProfile';
import AboutMenu from '../Screens/AboutMenu/Index';
import SupportMenu from '../Screens/SupportMenu/Index';
import SelectAvailableSlotTutor from '../Screens/ScheduleTutor/SelectAvailableSlotTutor';
import SelectMeetingPointTutorForOpenSession from '../Screens/OpenSession/SelectMeetingPointTutorForOpenSession';
import ChooseProfileAfterLogin from '../Screens/ChooseProfileAfterLogin';
import PrivacyPolicyScreen from '../Screens/PrivacyPolicy/PrivacyPolicy';
const Stack = createNativeStackNavigator();

export const BeforeAuthNavigator = ({isSplashVisible}) => {
  const {isLoggedIn} = useSelector(state => state.auth);
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    const checkSplashVisibility = async () => {
      const splashValue = await AsyncStorage.getItem(SHOW_SPLASH);
      console.log('🚀 ~ checkSplashVisibility ~ splashValue:', splashValue);
      if (splashValue === 'true') {
        setShowSplash(true); // Show splash if the value is true
      } else {
        setShowSplash(false); // Otherwise, don't show splash
      }
    };
    checkSplashVisibility();
  }, []);

  return (
    <Stack.Navigator
      initialRouteName={showSplash ? 'Splash' : 'WelcomeScreen'}
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}>
      <Stack.Screen name="Splash" component={Splash} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="SelectLanguage" component={SelectLanguage} />
      <Stack.Screen name="WelcomeScreen" component={WelcomeScreen} />
      <Stack.Screen
        name="OtpVerificationScreen"
        component={OtpVerificationScreen}
      />
      <Stack.Screen name="CreateYourAccount" component={CreateYourAccount} />
      <Stack.Screen
        name="CompleteYourProfilePageOne"
        component={CompleteYourProfilePageOne}
      />
      <Stack.Screen
        name="CompleteYourProfilePageTwo"
        component={CompleteYourProfilePageTwo}
      />

      <Stack.Screen name="AllSetPage" component={AllSetPage} />
      <Stack.Screen
        name="TutorApprovalPending"
        component={TutorApprovalPending}
      />
      <Stack.Screen name="DocViewer" component={DocViewer} />
      <Stack.Screen name="ChooseProfile" component={ChooseProfile} />
    </Stack.Navigator>
  );
};

const AuthNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}>
      <Stack.Screen name="TabScreen" component={TabNavigator} />
      <Stack.Screen name="TabTutorScreen" component={TutorMainNavigator} />
      <Stack.Screen name="Chooseyourgrade" component={Chooseyourgrade} />
      <Stack.Screen
        name="ChooseyourCurriculum"
        component={ChooseyourCurriculum}
      />
      <Stack.Screen name="TutorList" component={TutorList} />
      <Stack.Screen name="HomeScreen" component={HomeScreen} />
      <Stack.Screen name="TutorsDetails" component={TutorsDetails} />
      <Stack.Screen name="FilterScreen" component={FilterScreen} />
      <Stack.Screen name="BookYourTutor" component={BookYourTutor} />
      <Stack.Screen
        name="SelectAvailableSlot"
        component={SelectAvailableSlot}
      />
      <Stack.Screen
        name="BookYourTutorConformation"
        component={BookYourTutorConformation}
      />
      <Stack.Screen name="Search" component={Search} />
      <Stack.Screen name="ChooseCategory" component={ChooseCategory} />
      <Stack.Screen name="TutorsListCourses" component={TutorsListCourses} />
      <Stack.Screen
        name="TutorsDetailsCourses"
        component={TutorsDetailsCourses}
      />
      <Stack.Screen name="ProfilePage" component={ProfilePage} />
      <Stack.Screen name="UpdateProfile" component={UpdateProfile} />
      <Stack.Screen name="RateCardScreen" component={RateCardScreen} />
      <Stack.Screen name="EditAcademic" component={EditAcademic} />
      <Stack.Screen name="EditRecreational" component={EditRecreational} />
      <Stack.Screen name="EditCourses" component={EditCourses} />
      <Stack.Screen name="MeetingPreference" component={MeetingPreference} />
      <Stack.Screen name="SetYourPreference" component={SetYourPreference} />
      <Stack.Screen name="EditMeetingPoint" component={EditMeetingPoint} />
      <Stack.Screen
        name="QualificationsAndOtherPreferences"
        component={QualificationsAndOtherPreferences}
      />
      <Stack.Screen
        name="ChooseCategoryCourses"
        component={ChooseCategoryCourses}
      />
      <Stack.Screen
        name="TutorsListRecreational"
        component={TutorsListRecreational}
      />
      <Stack.Screen name="TutorTabScreen" component={TutorTabNavigator} />
      <Stack.Screen name="AddYourRateCard" component={AddYourRateCard} />
      <Stack.Screen name="ClassDetails" component={ClassDetails} />
      <Stack.Screen name="StartClassScreen" component={StartClassScreen} />
      <Stack.Screen name="StudentAddress" component={StudentAddress} />
      <Stack.Screen name="ChangePhoneNumber" component={ChangePhoneNumber} />
      <Stack.Screen name="ChangeEmail" component={ChangeEmail} />
      <Stack.Screen name="MeetingPoint" component={MeetingPoint} />
      {/* todays changes 25 nov */}
      <Stack.Screen name="ManageProfile" component={ManageProfile} />
      <Stack.Screen name="AddAccount" component={AddAccount} />
      <Stack.Screen name="ContactUs" component={ContactUs} />
      <Stack.Screen name="RaiseYourConcern" component={RaiseYourConcern} />
      {/* todays changes 25 nov */}
      <Stack.Screen name="UploadDocTutor" component={UploadDocTutor} />
      {/* //tutor */}
      <Stack.Screen name="MyBookings" component={MyBookings} />
      <Stack.Screen name="ScheduleTutor" component={ScheduleTutor} />
      <Stack.Screen name="AddTutorSchedule" component={AddTutorSchedule} />
      <Stack.Screen name="StudentList" component={StudentList} />
      <Stack.Screen
        name="BookYourTutorCourses"
        component={BookYourTutorCourses}
      />
      <Stack.Screen
        name="BookYourTutorConfirmation"
        component={BookYourTutorConfirmation}
      />
      <Stack.Screen
        name="StudentClassDetails"
        component={StudentClassDetails}
      />
      <Stack.Screen name="ClassDetailsTwo" component={ClassDetailsTwo} />
      <Stack.Screen name="RatingScreen" component={RatingScreen} />
      <Stack.Screen name="ChatScreen" component={ChatScreen} />
      <Stack.Screen name="OfflineScreen" component={OfflineScreen} />
      <Stack.Screen name="ChatList" component={ChatList} />
      <Stack.Screen name="WhiteBoard" component={WhiteBoard} />
      <Stack.Screen name="PaymentScreen" component={PaymentScreen} />
      <Stack.Screen name="PaymentSuccessfull" component={PaymentSuccessfull} />
      <Stack.Screen name="OpenSession" component={OpenSession} />
      <Stack.Screen name="Earnings" component={Earnings} />
      <Stack.Screen name="Withdrawal" component={Withdrawal} />
      {/* <Stack.Screen name="Test" component={Test} /> */}
      <Stack.Screen name="VideoTest" component={VideoTest} />
      <Stack.Screen
        name="StudentProfileForTutor"
        component={StudentProfileForTutor}
      />
      <Stack.Screen name="TutorDocuments" component={TutorDocuments} />
      <Stack.Screen
        name="BookOpenSessionList"
        component={BookOpenSessionList}
      />
      <Stack.Screen
        name="ViewOpenSessionDetails"
        component={ViewOpenSessionDetails}
      />
      <Stack.Screen
        name="ViewEnrolledOpenSessionDetails"
        component={ViewEnrolledOpenSessionDetails}
      />
      <Stack.Screen name="Notification" component={Notification} />
      <Stack.Screen name="RebookTutor" component={RebookTutor} />
      <Stack.Screen
        name="CompleteProfileTutorAfterSignUp"
        component={CompleteProfileTutorAfterSignUp}
      />
      <Stack.Screen
        name="CompleteProfileTutorAfterSignUpTwo"
        component={CompleteProfileTutorAfterSignUpTwo}
      />
      <Stack.Screen name="DocViewer" component={DocViewer} />
      <Stack.Screen name="FaqScreeen" component={FaqScreeen} />
      <Stack.Screen name="ChatWithAdmin" component={ChatWithAdmin} />
      <Stack.Screen name="TermsOfService" component={TermsOfService} />
      <Stack.Screen name="NotficationSetting" component={NotficationSetting} />
      <Stack.Screen name="Transactions" component={Transactions} />
      <Stack.Screen
        name="SavedPaymentMethods"
        component={SavedPaymentMethods}
      />
      <Stack.Screen
        name="RantingsAndFeedback"
        component={RantingsAndFeedback}
      />
      <Stack.Screen name="SettingScreen" component={Settings} />
      <Stack.Screen name="ConcernScreen" component={ConcernScreen} />
      <Stack.Screen name="AboutUs" component={AboutUs} />
      <Stack.Screen name="DeleteAccount" component={DeleteAccount} />
      <Stack.Screen name="SudgestedLoc" component={SudgestedLoc} />
      <Stack.Screen
        name="BookingDetailsTutor"
        component={BookingDetailsTutor}
      />
      <Stack.Screen name="SuggestedLocList" component={SuggestedLocList} />
      <Stack.Screen name="ViewAllList" component={ViewAllList} />
      <Stack.Screen name="SettingsMenu" component={SettingsMenu} />
      <Stack.Screen name="AddBankAccount" component={AddBankAccount} />
      <Stack.Screen name="AboutMenu" component={AboutMenu} />
      <Stack.Screen name="SupportMenu" component={SupportMenu} />
      <Stack.Screen
        name="SelectAvailableSlotTutor"
        component={SelectAvailableSlotTutor}
      />
      <Stack.Screen
        name="SelectMeetingPointTutorForOpenSession"
        component={SelectMeetingPointTutorForOpenSession}
      />

      <Stack.Screen
        name="ChooseProfileAfterLogin"
        component={ChooseProfileAfterLogin}
      />
      <Stack.Screen
        name="PrivacyPolicyScreen"
        component={PrivacyPolicyScreen}
      />
      {/* <Stack.Screen
        name="SavedPaymentMethods"
        component={SavedPaymentMethods}
      /> */}
    </Stack.Navigator>
  );
};
export default AuthNavigator;
