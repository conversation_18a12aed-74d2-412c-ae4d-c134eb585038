import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createDrawerNavigator} from '@react-navigation/drawer';
import {createStackNavigator} from '@react-navigation/stack';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Image, StyleSheet, View} from 'react-native';
import CustomDrawerContentTutor from '../Components/CustomDrawerTutor';
import {normalize} from '../Helper/NormalizeFont';
import HomeTutorScreen from '../Screens/HomeTutor';
import MyClassScreen from '../Screens/MyClass';
import SettingsScreen from '../Screens/Settings';
import LoginScreen from '../Screens/SignIn';
import WalletScreen from '../Screens/Wallet';
import colors from '../Utils/colors';
import icon from '../Utils/icons';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

const TutorTabNavigator = () => {
  console.log('inside --> tutorMainNavigator');
  const {t} = useTranslation();

  return (
    <Tab.Navigator
      initialRouteName="HomeTutor"
      screenOptions={({route}) => ({
        tabBarIcon: ({focused}) => {
          let iconSource;

          if (route.name === 'HomeTutor') {
            iconSource = focused ? icon.homeIconColor : icon.homeIcon;
          } else if (route.name === 'My Class') {
            iconSource = focused ? icon.classIconColor : icon.classIcon;
          } else if (route.name === 'Wallet') {
            iconSource = focused ? icon.walletIcon : icon.walletIcon;
          }

          return (
            <View style={styles.tabIconContainer}>
              <Image source={iconSource} style={styles.tabIcon} />
            </View>
          );
        },
        headerShown: false,
        tabBarActiveTintColor: colors.themeColor,
        tabBarInactiveTintColor: 'gray',
        tabBarLabelStyle: styles.tabBarLabel,
        tabBarStyle: styles.tabBar,
      })}>
      <Tab.Screen
        name="HomeTutor"
        component={HomeTutorScreen}
        options={{tabBarLabel: t('home')}}
      />
      <Tab.Screen
        name="My Class"
        component={MyClassScreen}
        options={{tabBarLabel: t('myclass')}}
      />
      <Tab.Screen
        name="Wallet"
        component={WalletScreen}
        options={{tabBarLabel: t('wallet')}}
      />
    </Tab.Navigator>
  );
};

const TutorMainNavigator = () => (
  <Drawer.Navigator
    initialRouteName="TutorMainTabs"
    drawerContent={props => <CustomDrawerContentTutor {...props} />}
    screenOptions={{
      headerShown: false,
      drawerStyle: styles.drawer,
      overlayColor: 'rgba(0,0,0,0.5)',
      drawerType: 'front',
      swipeEnabled: false,
    }}>
    <Drawer.Screen name="TutorMainTabs" component={TutorTabNavigator} />
    <Drawer.Screen name="Settings" component={SettingsScreen} />
    <Drawer.Screen name="Login" component={LoginScreen} />
    {/* <Drawer.Screen name="MyBookings" component={MyBookings} />

    <Drawer.Screen name="ScheduleTutor" component={ScheduleTutor} /> */}
  </Drawer.Navigator>
);

const styles = StyleSheet.create({
  drawer: {
    width: '80%',
    zIndex: 1000,
  },
  tabIconContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
  },
  tabIcon: {
    width: 24,
    height: 24,
  },
  tabBarLabel: {
    fontSize: normalize(4),
    fontWeight: 'bold',
    lineHeight: 16,
  },
  tabBar: {
    position: 'absolute',
    bottom: 0,
    zIndex: 0,
    elevation: 0,
    backgroundColor: colors.white,
  },
});

export default TutorMainNavigator;
