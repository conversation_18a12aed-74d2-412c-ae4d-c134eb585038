import React, {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import AuthNavigator, {BeforeAuthNavigator} from './AuthNavigator';
import useAppStateCheck from '../Helper/UseGetAppState';
import {updateSplashVisible} from '../Helper/SplashHelper/SplashHelper';
import ChooseProfile from '../Screens/ChooseProfile';

const EntryStack = () => {
  const {isLoggedIn} = useSelector(state => state.auth);
  console.log('🚀 ~ EntryStack ~ isLoggedIn:', isLoggedIn);
  const [appStateStatus, setAppStateStatus] = useState(undefined);

  useAppStateCheck({setAppStateStatus});
  useEffect(() => {
    console.log('isLoggedIn changed:', isLoggedIn);
  }, [isLoggedIn]);
  useEffect(() => {
    updateSplashVisible(appStateStatus, isLoggedIn);
  }, [appStateStatus, isLoggedIn]);

  return isLoggedIn ? <AuthNavigator /> : <BeforeAuthNavigator />;
  // return <ChooseProfile />;
};

export default EntryStack;
