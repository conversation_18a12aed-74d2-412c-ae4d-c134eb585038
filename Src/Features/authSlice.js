import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  token: null,
  userId: null,
  user_type: null,
  action_type: null,
  isLoggedIn: null,
  appLocale: null,
  profile_image: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setAuthData: (state, action) => {
      const {token, userId, user_type, action_type, profile_image} =
        action.payload;
      state.token = token;
      state.userId = userId;
      state.user_type = user_type;
      state.action_type = action_type;

      // console.log('Auth data set in reducer:', state);
    },
    clearAuthData: state => {
      state.token = null;
      state.userId = null;
      state.user_type = null;
      state.action_type = null;
      state.profile_image = null;
      // console.log('Auth data cleared:', state);
    },
    setIsLoggedIn: (state, action) => {
      state.isLoggedIn = action.payload;
    },
    setAppLocale: (state, action) => {
      state.appLocale = action.payload;
    },
    setUserProfileImage: (state, action) => {
      state.profile_image = action.payload;
    },
  },
});

export const {
  setAuthData,
  clearAuthData,
  setIsLoggedIn,
  setAppLocale,
  setUserProfileImage,
} = authSlice.actions;
export default authSlice.reducer;
