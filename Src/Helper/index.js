import {
  FlatList,
  Image,
  Modal,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {StatusContainer} from '../../Components/StatusBar';
import colors from '../../Utils/colors';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {AppHeader, IconBtn} from '../../Components/Header';
import {applyShadowStyleIos} from '../../Helper/ShadowStyleIos';
import {Fonts} from '../../Utils/Fonts';
import {DUMMY_USER_IMG} from '../../Utils/constant';
import {
  useDeleteConnectedProfileMutation,
  useGetAllConnectedProfilesAfterLoginQuery,
  useLazyGetAllConnectedProfilesAfterLoginQuery,
  useSwitchConnectedProfileMutation,
} from '../../Api/ApiSlice';
import {useDispatch, useSelector} from 'react-redux';
import {setAuthData, setIsLoggedIn} from '../../Features/authSlice';
import {capitalizeFirstLetter} from '../../Helper/NormalizeFont';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {FlipOutYRight} from 'react-native-reanimated';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {responsiveFontSize} from 'react-native-responsive-dimensions';

const ChooseProfileAfterLogin = () => {
  const {t, i18n} = useTranslation();
  const navigation = useNavigation();
  const {isLoggedIn} = useSelector(state => state.auth);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedProfileId, setSelectedProfileId] = useState(null); // Track which profile to delete
  const isRTL = i18n.language === 'ar';
  const {
    data: connectedProfileRes,
    isLoading: profileLoading,
    refetch,
  } = useGetAllConnectedProfilesAfterLoginQuery(undefined, {
    refetchOnFocus: true,
    refetchOnMountOrArgChange: true,
  });

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch]),
  );

  const handleCancelLogout = () => {
    setModalVisible(false);
    setSelectedProfileId(null);
  };

  const onPressDelete = async () => {
    if (!selectedProfileId) return;

    try {
      const body = {id: selectedProfileId};
      await deleteConnectedProfileApi(body).unwrap();
      refetch();
      setModalVisible(false);
      setSelectedProfileId(null);
    } catch (error) {
      console.error('Error deleting profile:', error);
    }
  };

  const [
    switchConnectedProfileApi,
    {data: switchProfileRes, isLoading: switchProfileLoading},
  ] = useSwitchConnectedProfileMutation();
  const [deleteConnectedProfileApi] = useDeleteConnectedProfileMutation();

  const dispatch = useDispatch();

  async function handleProfilePress(id) {
    try {
      const response = await switchConnectedProfileApi(id);
      const userData = {
        token: response?.data?.data?.token,
        userId: response?.data?.data?.id,
        user_type: response?.data?.data?.user_type,
        action_type: response?.data?.data?.action_type,
        profile_image: null,
        profile_accounts: response?.data?.data?.profile_accounts,
      };
      await AsyncStorage.setItem('user', JSON.stringify(userData));
      dispatch(setAuthData(userData));
      navigation.navigate('Home');
    } catch (error) {
      console.error('Error in handleProfilePress:', error);
    }
  }

  const ProfileView = ({item}) => {
    return (
      <View
        style={applyShadowStyleIos({
          height: fp(10),
          width: fp(10),
          borderRadius: fp(10),
          backgroundColor: colors.white,
          elevation: 2,
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1,
        })}>
        <Image
          source={{
            uri: item?.image
              ? `${IMAGE_BASE_URL + item?.image}`
              : `${DUMMY_USER_IMG}`,
          }}
          style={{height: fp(9), width: fp(9), borderRadius: fp(10)}}
        />
      </View>
    );
  };

  const AddBtnView = () => {
    return (
      <TouchableOpacity
        style={{
          alignItems: 'center',
          width: wp(30),
          justifyContent: 'center',
        }}
        onPress={() => navigation.navigate('AddAccount', {type: 'all'})}>
        <View
          style={applyShadowStyleIos({
            height: fp(10),
            width: fp(10),
            borderRadius: fp(10),
            backgroundColor: colors.white,
            elevation: 2,
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1,
          })}>
          <Image
            source={icons.addCircle}
            style={{
              height: fp(9),
              width: fp(9),
            }}
          />
        </View>
        <Text
          style={{
            marginTop: hp(1),
            fontSize: fp(1.8),
            fontFamily: Fonts.medium,
            color: colors.white,
            textAlign: 'center',
            lineHeight: hp(2),
          }}>
          {t('addProfile')}
        </Text>
      </TouchableOpacity>
    );
  };

  const userTypeMap = {
    1: t('student'),
    2: t('parent'),
    3: t('tutor'),
  };

  const FullProfileView = ({item, index}) => {
    console.log('108973642489301', item);
    return (
      <TouchableOpacity
        style={{
          alignItems: 'center',
          width: wp(30),
          justifyContent: 'center',
        }}
        onPress={() => handleProfilePress(item?.id)}>
        {/* Always render the cross icon container for consistent spacing */}
        <View
          style={{
            borderRadius: fp(10),
            alignSelf: 'flex-end',
            top: hp(2),
            right: wp(5),
            zIndex: 2,
            // Make it invisible if there's only one profile
            opacity: connectedProfileRes?.data?.list.length > 1 ? 1 : 0,
            // Disable touch events when invisible
            pointerEvents:
              connectedProfileRes?.data?.list.length > 1 ? 'auto' : 'none',
          }}>
          <TouchableOpacity
            onPress={() => {
              setSelectedProfileId(item?.id);
              setModalVisible(true);
            }}
            disabled={connectedProfileRes?.data?.list.length <= 1}>
            <Image
              source={icons?.dustbin}
              style={{height: fp(2.8), width: fp(2.8)}}
              tintColor={colors.greyish}
            />
          </TouchableOpacity>
        </View>

        <ProfileView item={item} />
        <Text
          style={{
            marginTop: hp(1),
            fontSize: fp(1.8),
            fontFamily: Fonts.medium,
            color: colors.white,
            textAlign: 'center',
            lineHeight: hp(2),
          }}>
          {item?.name}
        </Text>
        <Text
          style={{
            marginTop: hp(0.2),
            fontSize: fp(1.4),
            fontFamily: Fonts.poppinsRegular,
            color: colors.white,
          }}>
          {userTypeMap[item?.user_type]}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {Platform.OS === 'ios' && <StatusContainer color={colors.themeColor} />}
      <View
        style={{
          marginHorizontal: wp(8),
          alignItems: isRTL ? 'flex-end' : 'flex-start',
        }}>
        <IconBtn
          icon={icons.backIcon}
          onPress={() => navigation.goBack()}
          iconStyle={{
            tintColor: colors.white,
            height: fp(4),
            width: fp(4),
            transform: [{rotate: isRTL ? '180deg' : '0deg'}],
          }}
        />
      </View>
      <View style={styles.body}>
        <Text
          style={{
            fontFamily: Fonts.bold,
            alignSelf: 'center',
            fontSize: fp(3),
            color: colors.white,
            marginBottom: hp(2),
          }}>
          {t('choose_profile')}
        </Text>
        <View
          style={{
            marginTop: hp(1),
            flexDirection: isRTL ? 'row-reverse' : 'row',
            width: wp(90),
            alignItems: 'center',
            justifyContent: 'center',
            flexWrap: 'wrap',
            gap: hp(3),
          }}>
          {connectedProfileRes?.data?.list.map((item, index) => (
            <FullProfileView key={index} item={item} />
          ))}
          <AddBtnView />
        </View>

        <Modal
          animationType="fade"
          transparent={true}
          visible={modalVisible}
          onRequestClose={handleCancelLogout}>
          <View style={styles.centeredView}>
            <View style={styles.modalView}>
              <Text style={styles.modalTitle}>
                {t('areYouSureAccountProfile')}
              </Text>
              <Text style={styles.modalText}>{t('deleteMsg')}</Text>
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={handleCancelLogout}>
                  <Text style={styles.cancelButtonText}>{t('cancel')}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={onPressDelete}
                  style={[styles.button, styles.logoutButton]}>
                  <Text style={styles.logoutButtonText}>{t('Delete')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </SafeAreaView>
  );
};
export default ChooseProfileAfterLogin;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.themeColor,
    marginBottom: fp(2),
  },
  body: {
    flex: 1,
    padding: fp(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    margin: 20,
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: wp(2),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    marginVertical: 15,
    textAlign: 'center',
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
    lineHeight: hp(2),
    width: wp(80),
  },
  modalText: {
    marginBottom: fp(1.4),
    fontFamily: Fonts.medium,
    color: colors.black,
    textAlign: 'center',
    fontSize: fp(1.6),
    lineHeight: hp(2),
    width: wp(80),
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
    width: '90%',
    marginHorizontal: wp(2),
    gap: wp(1.2),
  },
  button: {
    borderRadius: 10,
    width: '48%',
    padding: 10,
    height: hp(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.themeColor,
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    color: colors.themeColor,
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    textAlign: 'center',
  },
  modalView: {
    margin: 20,
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: wp(2),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },

    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalTitle: {
    marginVertical: 15,
    textAlign: 'center',
    fontSize: fp(1.8),
    fontFamily: Fonts.semiBold,
    color: colors.black,
    lineHeight: hp(2),
    width: wp(80),
  },
  modalText: {
    marginBottom: fp(1.4),
    fontFamily: Fonts.medium,
    color: colors.black,
    textAlign: 'center',
    fontSize: fp(1.6),
    lineHeight: hp(2),
    width: wp(80),
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
    width: '90%',
    marginHorizontal: wp(2),
    gap: wp(1.2),
  },
  button: {
    borderRadius: 10,
    width: '48%',
    padding: 10,
    height: hp(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.themeColor,
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    color: colors.themeColor,
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
    textAlign: 'center',
  },
  logoutButtonText: {
    color: 'white',
    fontFamily: Fonts.bold,
    fontSize: fp(1.8),
    textAlign: 'center',
  },
});
