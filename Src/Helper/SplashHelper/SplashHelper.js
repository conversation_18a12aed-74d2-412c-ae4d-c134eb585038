import AsyncStorage from '@react-native-async-storage/async-storage';

export const updateSplashVisible = async (
  appStateStatus,
  isLoggedIn,
  isLogout = false,
) => {
  // If it's a logout action, set SHOW_SPLASH to false
  if (isLogout) {
    await AsyncStorage.setItem('SHOW_SPLASH', 'false');
    return false; // Prevent splash screen from showing after logout
  }

  const splashValue = await AsyncStorage.getItem('SHOW_SPLASH');

  if (appStateStatus === 'background' || appStateStatus === 'initial') {
    if (!isLoggedIn) {
      await AsyncStorage.setItem('SHOW_SPLASH', 'true');
      return true; // Show splash screen if logged out and app is in background or initial state
    } else {
      await AsyncStorage.setItem('SHOW_SPLASH', 'false');
      return false; // Don't show splash if already logged in
    }
  } else {
    // If app is in foreground, return splash value
    return splashValue === 'true';
  }
};
