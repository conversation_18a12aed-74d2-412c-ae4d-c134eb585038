import {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {
  useGetUnreadNotiCountQuery,
  useGetUnreadMessageCountQuery,
} from '../../Api/ApiSlice';
import {
  setUnreadCount,
  setUnreadMessageCount,
  setUnreadSupportMessageCount,
} from '../../Redux/Slices/NotiSlice/NotificationSlice';

const useGlobalUnreadCount = () => {
  const dispatch = useDispatch();
  const {
    unreadCount,
    unreadMessageCount: reduxUnreadMessageCount,
    unreadSupportMessageCount: reduxUnreadSupportMessageCount,
  } = useSelector(state => state.notifications);

  // Fetch unread notification count (includes all counts in one API)
  const {data: unreadCountData, refetch: refetchUnreadCount} =
    useGetUnreadNotiCountQuery(undefined, {
      // pollingInterval: 30000, // Poll every 30 seconds for real-time updates (optional)
    });

  // Fetch unread message count (optional, only if separate from getUnreadNotiCount)
  const {
    data: unreadMessageCountData,
    refetch: refetchMessageCount,
    error: unreadMessageCountErr,
  } = useGetUnreadMessageCountQuery(undefined, {
    skip: !!unreadCountData, // Skip if getUnreadNotiCount already provides all data
  });

  // Sync all counts with Redux when unreadCountData updates
  useEffect(() => {
    if (unreadCountData?.data) {
      const {unreadNotificationCount, unreadChatCount, unreadSuportMessages} =
        unreadCountData.data;
      console.log('🚀 ~ useEffect ~ unreadCountData:', unreadCountData.data);

      dispatch(setUnreadCount(unreadNotificationCount || 0));
      dispatch(setUnreadMessageCount(unreadChatCount || 0));
      dispatch(setUnreadSupportMessageCount(unreadSuportMessages || 0));
    }
  }, [unreadCountData, dispatch]);

  // Sync message count if using a separate API
  useEffect(() => {
    if (unreadMessageCountData?.data) {
      console.log(
        '🚀 ~ useEffect ~ unreadMessageCountData:',
        unreadMessageCountData.data,
      );
      dispatch(setUnreadMessageCount(unreadMessageCountData.data || 0));
    }
  }, [unreadMessageCountData, dispatch]);

  // Log errors for debugging
  useEffect(() => {
    if (unreadMessageCountErr) {
      console.error(
        'Error fetching unread message count:',
        unreadMessageCountErr,
      );
    }
  }, [unreadMessageCountErr]);

  return {
    unreadCount,
    unreadMessageCount: reduxUnreadMessageCount,
    unreadSupportMessageCount: reduxUnreadSupportMessageCount,
    refetchUnreadCount,
    refetchMessageCount,
  };
};

export default useGlobalUnreadCount;
