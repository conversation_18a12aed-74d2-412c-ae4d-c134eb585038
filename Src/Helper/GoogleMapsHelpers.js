import {Linking} from 'react-native';

export const openGoogleMaps = (latitude, longitude, label = 'Location') => {
  const url = Platform.select({
    ios: `maps://?q=${latitude},${longitude}&label=${label}`,
    android: `geo:${latitude},${longitude}?q=${latitude},${longitude}(${label})`,
  });

  Linking.openURL(url).catch(() => {
    // Fallback if Google Maps is not installed
    Linking.openURL(
      `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`,
    );
  });
};

export const openDirections = (
  destinationLat,
  destinationLng,
  destinationName = 'Tutor Meeting Point',
) => {
  // URL Schemes for Google Maps & Apple Maps
  const url = Platform.select({
    ios: `http://maps.apple.com/?daddr=${destinationLat},${destinationLng}&dirflg=d`,
    android: `https://www.google.com/maps/dir/?api=1&destination=${destinationLat},${destinationLng}&travelmode=driving`,
  });

  Linking.openURL(url).catch(() => {
    // Fallback: Open in browser if Maps app fails
    Linking.openURL(
      `https://www.google.com/maps/dir/?api=1&destination=${destinationLat},${destinationLng}`,
    );
  });
};
