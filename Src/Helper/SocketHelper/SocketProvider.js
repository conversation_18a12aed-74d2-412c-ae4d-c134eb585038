import {createContext, useContext, useEffect, useRef} from 'react';
import {baseUrl} from '../../Api/ApiSlice';
import {io} from 'socket.io-client';

const SocketContext = createContext(undefined);

export const SocketProvider = ({children}) => {
  const socket = useRef();
  useEffect(() => {
    socket.current = io(baseUrl, {
      transports: ['websocket'],
    });

    return () => {
      socket.current?.disconnect();
    };
  }, []);

  const socketDisconnect = () => {
    if (socket?.current) {
      socket.current.disconnect();
      socket.current = undefined;
    }
  };

  const emit = (event, data) => {
    socket?.current?.emit(event, data);
  };
  const on = (event, cb) => {
    socket.current?.on(event, cb);
  };

  const off = event => {
    socket.current?.off(event);
  };

  const removeListener = listenerName => {
    socket?.current?.removeListener(listenerName);
  };

  const onAny = (event, data) => {
    socket?.current?.onAny(event, data);
  };
  const socketService = {
    initializeSocket: () => {},
    socketDisconnect,
    emit,
    on,
    off,
    removeListener,
    onAny,
  };

  return (
    <SocketContext.Provider value={socketService}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = () => {
  const socketService = useContext(SocketContext);
  if (!socketService) {
    console.log('socket error');
  }
  return socketService;
};
