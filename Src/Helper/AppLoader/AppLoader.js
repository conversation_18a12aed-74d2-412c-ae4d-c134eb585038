import React, {PureComponent} from 'react';
import {View, StyleSheet, Modal, ActivityIndicator} from 'react-native';

export class AppLoader extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
    };
  }

  showLoader = isLoading => {
    this.setState({isLoading});
  };

  render() {
    const {isLoading} = this.state;
    return (
      <Modal animationType={'fade'} visible={isLoading} transparent>
        <View style={styles.container}>
          <View style={styles.loader}>
            <ActivityIndicator size={'large'} color={'#40A39B'} />
          </View>
        </View>
      </Modal>
    );
  }
}

export default AppLoader;

const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  loader: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.29,
    shadowRadius: 4.65,
    elevation: 7,
  },
});
