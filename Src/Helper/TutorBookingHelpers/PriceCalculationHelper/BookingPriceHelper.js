import {extractNumber} from '..';

export function calculateBookingPrice(
  packagePrice, //package Price
  discount, //discount given by admin on packages
  classPrice, //class price hourly rate set by tutor
  totalHours, // total selected hours by student
  totalMember, //total members for group class
) {
  console.log('🚀 ~ totalMember:', totalMember);
  console.log('🚀 ~ totalHours:', totalHours);
  console.log('🚀 ~ classPrice:', classPrice);
  console.log('🚀 ~ discount:', discount);

  packagePrice = Number(packagePrice);
  discount = Number(discount);
  classPrice = Number(classPrice);
  totalHours = Number(totalHours);
  totalMember = Number(totalMember);
  let finalPrice = 0;
  if (classPrice > 0) {
    const tempFinalPrice = classPrice * totalHours * totalMember;
    const discountedFinalPrice = (tempFinalPrice * discount) / 100;
    finalPrice = tempFinalPrice - discountedFinalPrice;
  }

  console.log('🚀 ~ finalPrice:', finalPrice);
  return finalPrice + packagePrice;
}

export function calculateBookingPriceForGroup(
  packagePrice, //package Price
  discount, //discount given by admin on packages
  classPrice, //class price hourly rate set by tutor
  totalHours, // total selected hours by student
  totalMember, //total members for group class
  groupDiscount, // group Discount
) {
  console.log(
    discount, //discount given by admin on packages
    classPrice, //class price hourly rate set by tutor
    totalHours, // total selected hours by student
    totalMember,
  );
  console.log('i m being called');
  packagePrice = Number(packagePrice);
  discount = Number(discount);
  classPrice = Number(classPrice);
  totalHours = Number(totalHours);
  totalMember = Number(totalMember);
  groupDiscount = Number(groupDiscount);
  let finalPrice = 0;
  if (classPrice > 0) {
    const tempFinalPrice = classPrice * totalHours * totalMember;
    const discountedFinalPrice = (tempFinalPrice * discount) / 100;
    finalPrice = tempFinalPrice - discountedFinalPrice;
  }

  console.log('🚀 ~ finalPrice:', finalPrice);
  return finalPrice + packagePrice;
}
export function getDiscountedAmountForIndividual(
  packagePrice, //package Price
  discount, //discount given by admin on packages
  classPrice, //class price hourly rate set by tutor
  totalHours, // total selected hours by student
  totalMember, //total members for group class
  groupDiscount, // group Discount
) {
  console.log(
    discount, //discount given by admin on packages
    classPrice, //class price hourly rate set by tutor
    totalHours, // total selected hours by student
    totalMember,
  );
  console.log('i m being called');
  packagePrice = Number(packagePrice);
  discount = Number(discount);
  classPrice = Number(classPrice);
  totalHours = Number(totalHours);
  totalMember = Number(totalMember);
  groupDiscount = Number(groupDiscount);
  let discount_amount = 0;
  if (classPrice > 0) {
    const tempFinalPrice = classPrice * totalHours * totalMember;
    discount_amount = (tempFinalPrice * discount) / 100;
  }

  return discount_amount;
}

export function calculateBookingPriceCourses(
  classPrice, //class price hourly rate set by tutor
  totalHours, // total selected hours by student
  totalMember = 1, //total members for group class
) {
  console.log('🚀 ~ totalHours:', totalHours);
  console.log('🚀 ~ classPrice:', classPrice);
  console.log('🚀 ~ totalMember:', totalMember);

  classPrice = Number(classPrice);
  totalMember = Number(totalMember);
  return classPrice * extractNumber(totalHours) * totalMember || 0;
}
