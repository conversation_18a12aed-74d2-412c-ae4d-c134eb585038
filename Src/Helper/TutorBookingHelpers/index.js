import {t} from 'i18next';
import {showToast} from '../../Components/ToastHelper';

export const validateBookingDetails = (
  rate_card_id,
  classType,
  packageIdRedux,
  slot,
  sessionType,
  isRTL,
) => {
  if (!rate_card_id) {
    showToast('error', t('tuttorDataMissing'), 'bottom', isRTL);
    return false;
  }
  if (!classType) {
    showToast('error', t('selectClassType'), 'bottom', isRTL);
    return false;
  }
  if (!sessionType) {
    showToast('error', t('pleaseSelectSession'), 'bottom', isRTL);
    return false;
  }
  if (!packageIdRedux) {
    showToast('error', t('selectPackage'), 'bottom', isRTL);
    return false;
  }
  if (!slot || Object.keys(slot).length === 0) {
    showToast('error', t('selectAvailabeSlots'), 'bottom', isRTL);
    return false;
  }
  return true;
};

export const transformToBookingSchedulesArray = scheduleData =>
  Object.entries(scheduleData).flatMap(([date, slots]) =>
    slots.map(slot => ({
      date,
      tutor_schedule_id: slot.id,
    })),
  );

export const extractNumber = str => {
  const match = str.match(/\d+/); // Matches any sequence of digits in the string
  return match ? parseInt(match[0], 10) : null;
};
