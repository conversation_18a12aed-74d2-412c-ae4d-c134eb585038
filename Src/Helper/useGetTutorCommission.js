import {useMemo} from 'react';
import {useGetTutorCommissionRatesApiQuery} from '../Api/ApiSlice';

// Mapping constants
const classTypeMap = {
  1: 'Online',
  2: 'FaceToFace',
};

const rateCardCategoryMap = {
  1: 'Academic',
  2: 'Recreational',
  3: 'Courses',
};

export const useTutorCommissions = () => {
  const {
    data: response,
    isLoading,
    error,
  } = useGetTutorCommissionRatesApiQuery();

  const commissions = useMemo(() => {
    if (!response?.data?.commissionData) return null;

    return response.data.commissionData.reduce((acc, item) => {
      const category = rateCardCategoryMap[item.rate_card_category];
      const classType = classTypeMap[item.class_type_id];

      if (!acc[category]) {
        acc[category] = {};
      }

      acc[category][classType] = {
        rate: item.rate,
        id: item.id,
      };

      return acc;
    }, {});
  }, [response]);

  // Helper function to get specific commission
  const getCommission = (category, classType) => {
    if (!commissions) return null;
    return commissions[category]?.[classType] ?? null;
  };

  return {
    commissions, // The fully organized commission structure
    getCommission, // Helper to get specific rates
    isLoading,
    error,

    // Pre-defined getters for convenience
    academicOnline: getCommission('Academic', 'Online'),
    academicFaceToFace: getCommission('Academic', 'FaceToFace'),
    recreationalOnline: getCommission('Recreational', 'Online'),
    recreationalFaceToFace: getCommission('Recreational', 'FaceToFace'),
    coursesOnline: getCommission('Courses', 'Online'),
    coursesFaceToFace: getCommission('Courses', 'FaceToFace'),
  };
};
