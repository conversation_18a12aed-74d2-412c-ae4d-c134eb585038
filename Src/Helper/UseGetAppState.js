// useAppStateCheck.tsx

import { useCallback, useEffect } from 'react';
import { NativeEventSubscription, AppState, AppStateStatus } from 'react-native';



export default function useAppStateCheck(props) {
  const { setAppStateStatus } = props;

  const handleAppStateChange = useCallback(async (nextAppState) => {
    setAppStateStatus(nextAppState);
  }, [setAppStateStatus]);

  useEffect(() => {
    let eventListener;
    eventListener = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      eventListener && eventListener.remove();
    };
  }, [handleAppStateChange]);
}