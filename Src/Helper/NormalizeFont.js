import {PixelRatio} from 'react-native';
import {SCREEN_HEIGHT} from '../Utils/constant';

const scale = SCREEN_HEIGHT / 320;

export function normalize(size) {
  const newSize = size * scale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
}
export const capitalizeFirstLetter = string => {
  if (!string) return ''; // Handle empty or null strings
  return string.charAt(0).toUpperCase() + string.slice(1);
};
