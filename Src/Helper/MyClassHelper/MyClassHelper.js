import {View, Text} from 'react-native';
import React from 'react';

export default MyClassHelper = slots => {
  const now = new Date();
  console.log('0982347376439', slots);
  // Add 20 minutes to current time for the window
  const windowEnd = new Date(now.getTime() + 20 * 60000);

  // Convert slots to Date objects with full datetime
  const slotsWithDateTime = slots?.map(slot => {
    const date = new Date(slot?.date || slot?.specific_date);
    const [startHour, startMin] =
      slot?.tlm_tutor_schedule?.start_time?.split(':') ||
      slot?.start_time?.split(':');

    // Create full datetime for the slot
    const slotDateTime = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      parseInt(startHour),
      parseInt(startMin),
    );

    return {
      ...slot,
      dateTime: slotDateTime,
    };
  });

  // Find the closest upcoming slot within 20 minutes
  const upcomingSlot = slotsWithDateTime?.find(slot => {
    return slot.dateTime > now && slot.dateTime <= windowEnd;
  });

  console.log('Current time:', now.toLocaleString());
  console.log('Window end:', windowEnd.toLocaleString());

  if (upcomingSlot) {
    console.log('Found upcoming slot:', {
      date: upcomingSlot.date,
      start_time: upcomingSlot.tlm_tutor_schedule.start_time,
      end_time: upcomingSlot.tlm_tutor_schedule.end_time,
    });
    return upcomingSlot;
  }

  console.log('No upcoming slots in the next 20 minutes');
  return null;
};
