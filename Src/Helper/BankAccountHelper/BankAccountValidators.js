export const validateBankName = bankName => {
  const validBanks = ['QNB', 'Doha Bank', 'Commercial Bank of Qatar', 'Other'];
  return validBanks.includes(bankName);
};
export const validateAccountHolderName = name => {
  return /^[A-Za-z\s]{2,}$/.test(name);
};

export const validateAccountNumber = accountNumber => {
  return /^\d{8,16}$/.test(accountNumber);
};
export const validateIBAN = iban => {
  return /^QA\d{2}[A-Z0-9]{1,21}$/.test(iban);
};
export const validateAmount = amount => {
  return amount > 0 && amount <= accountBalance; // Replace `accountBalance` with the actual balance.
};
export const validateCurrency = currency => {
  const validCurrencies = ['QAR', 'USD', 'EUR']; // Add more if needed.
  return validCurrencies.includes(currency);
};
export const validateWithdrawalPurpose = purpose => {
  return purpose.length <= 100; // Optional field.
};
export const validateTerms = isChecked => {
  return isChecked;
};
