import moment from 'moment';
import {useTranslation} from 'react-i18next';

export const convertTo12HourFormat = time24 => {
  console.log('🚀 ~ time24:', time24);
  const [hours, minutes] = time24.split(':');
  const hour = parseInt(hours, 10);
  const isPM = hour >= 12;
  const formattedHour = hour % 12 || 12; // Convert 0 or 12 to 12 for 12-hour format
  const period = isPM ? 'PM' : 'AM';
  return `${String(formattedHour).padStart(2, '0')}:${minutes} ${period}`;
};

export const convertToHoursAndMinutes = decimalHours => {
  // Get the whole number part as hours
  decimalHours = Number(decimalHours);
  const hours = Math.floor(decimalHours);
  // Convert the fractional part to minutes
  const minutes = Math.round((decimalHours - hours) * 60);

  let result = '';

  if (hours > 0) {
    const hourLabel = hours > 1 ? 'hours' : 'hour';
    result += `${hours} ${hourLabel}`;
  }

  if (minutes > 0) {
    if (result) result += ' and '; // Add "and" if hours are included
    result += `${minutes} minutes`;
  }
  return result || '0 minutes'; // Fallback if both hours and minutes are 0
};

export function convertToDMY(dateStr) {
  const dateObj = new Date(dateStr); // Create a Date object
  const day = String(dateObj.getDate()).padStart(2, '0'); // Get day and pad with 0 if needed
  const month = String(dateObj.getMonth() + 1).padStart(2, '0'); // Get month (0-indexed) and pad
  const year = dateObj.getFullYear(); // Get year
  return `${day}-${month}-${year}`; // Combine into dmy format
}

export const formatDateStringToPaymentUI = dateString => {
  const date = new Date(dateString);

  // Extract UTC components
  const day = date.getUTCDate().toString().padStart(2, '0'); // 2-digit day
  const month = date.toLocaleString('en-US', {month: 'short', timeZone: 'UTC'}); // Abbreviated month
  const year = date.getUTCFullYear();
  const hours = date.getUTCHours();
  const minutes = date.getUTCMinutes().toString().padStart(2, '0'); // 2-digit minutes

  // Format hour in 12-hour format with AM/PM
  const hour12 = hours % 12 || 12; // Convert 0 to 12 for midnight
  const amPm = hours >= 12 ? 'PM' : 'AM';

  return `${day} ${month}, ${year}, ${hour12}:${minutes} ${amPm}`;
};

export const convertToUTC = time => {
  try {
    console.log('🚀 ~ time: in convertToUTC function', time);

    // Validate the input
    const parsedDate = new Date(time);
    if (isNaN(parsedDate)) {
      throw new Error(`Invalid date input: ${time}`);
    }

    // Convert to UTC and extract time
    const utcDate = parsedDate.toISOString(); // Converts to UTC in ISO 8601 format
    const formattedTime = utcDate.split('T')[1].split('.')[0]; // Extract "HH:mm:ss"

    console.log('🚀 ~ UTC Time:', formattedTime);
    return formattedTime;
  } catch (error) {
    console.error('Error in convertToUTC:', error.message);
    return null; // Return null or handle the error as needed
  }
};

export const convertUTCToLocal = utcTime => {
  console.log('🚀 ~ utcTime:', utcTime);
  try {
    const localTime = moment.utc(utcTime).local().format('YYYY-MM-DD HH:mm:ss'); // Convert to local time
    console.log('🚀 ~ Local Time:', localTime);
    return localTime;
  } catch (error) {
    console.error('Error in convertUTCToLocal:', error.message);
    return null;
  }
};

export const convertToLocal12HourFormat = (time, isRTL = false) => {
  try {
    console.log('🚀 ~ Input Time (24-hour UTC):', time);

    // Create a Date object using the time (assuming it's in UTC)
    const [hours, minutes, seconds] = time.split(':'); // Split the time string
    const utcDate = new Date(); // Create a new Date object for today
    utcDate.setUTCHours(hours, minutes, seconds, 0); // Set the time in UTC

    // Convert to local time in 12-hour format
    const localTime = utcDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      // second: '2-digit',
      hour12: true, // Use 12-hour format
    });

    console.log('🚀 ~ Local Time (12-hour): output', localTime);
    return localTime;
  } catch (error) {
    console.log('Error in convertToLocal12HourFormat:', error.message);
    return null; // Return null or handle the error as needed
  }
};

export const convertToLocal24HourFormat = time => {
  try {
    console.log('🚀 ~ Input Time (24-hour UTC):', time);

    // Create a Date object using the time (assuming it's in UTC)
    const [hours, minutes, seconds] = time.split(':'); // Split the time string
    const utcDate = new Date(); // Create a new Date object for today
    utcDate.setUTCHours(hours, minutes, seconds, 0); // Set the time in UTC

    // Convert to local time in 12-hour format
    const localTime = utcDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false, // Use 12-hour format
      // second: '2-digit',
    });

    console.log(
      '🚀 ~ Local Time (12-hour): convertToLocal24HourFormat',
      localTime,
    );
    return localTime;
  } catch (error) {
    console.error('Error in convertToLocal12HourFormat:', error.message);
    return null; // Return null or handle the error as needed
  }
};

export const isShowJoinButton = (givenDate, startTime, endTime) => {
  console.log('🚀 ~ isShowJoinButton ~ endTime:', endTime);
  console.log('🚀 ~ isShowJoinButton ~ startTime:', startTime);
  console.log('🚀 ~ isShowJoinButton ~ givenDate:', givenDate);
  const currentDate = new Date(); // Get the current date and time
  const givenDateObj = new Date(givenDate); // Convert the given date to a Date object
  // Compare only the date part (ignore time)
  const currentDateOnly = currentDate.toDateString(); // Get the date part as a string
  console.log('🚀 ~ isShowJoinButton ~ currentDateOnly:', currentDateOnly);
  const givenDateOnly = givenDateObj.toDateString(); // Get the date part as a string
  console.log('🚀 ~ isShowJoinButton ~ givenDateOnly:', givenDateOnly);

  // If the given date is today, check the time range
  if (currentDateOnly === givenDateOnly) {
    console.log(currentDate.getHours(), 'current time');
    const currentTime = currentDate.getHours() * 60 + currentDate.getMinutes(); // Current time in minutes
    const startMinutes =
      parseInt(startTime.split(':')[0]) * 60 +
      parseInt(startTime.split(':')[1]); // Start time in minutes
    const endMinutes =
      parseInt(endTime.split(':')[0]) * 60 + parseInt(endTime.split(':')[1]); // End time in minutes

    // Check if the current time is within the range
    if (currentTime >= startMinutes && currentTime <= endMinutes) {
      return false; // Disable = false
    }
    if (currentTime > endMinutes) {
      return true; // Hide Join Button (Disable = true)
    }
  }

  return true;
  // Disable = true for all other cases (1. if date is for future or past)
};

export const convertTo12HourFormatAvailableSlot = time => {
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours, 10);
  const period = hour >= 12 ? 'PM' : 'AM';
  const adjustedHour = hour % 12 || 12;
  return `${adjustedHour}:${minutes} ${period}`;
};

export function convertDecimalHoursToHoursAndMinutes(decimalHours) {
  // Get the whole hours
  const hours = Math.floor(decimalHours);

  // Get the decimal part and convert to minutes
  const decimalPart = decimalHours - hours; // e.g., 2.02 - 2 = 0.02
  const minutes = Math.round(decimalPart * 60); // e.g., 0.02 * 60 = 1.2, rounded to 1

  // Construct the string with proper pluralization
  const hoursText = hours === 1 ? 'hour' : 'hours';
  const minutesText = minutes === 1 ? 'minute' : 'minutes';

  if (minutes === 0) {
    return `${hours} ${hoursText}`; // e.g., "2 hours"
  } else if (hours === 0) {
    return `${minutes} ${minutesText}`; // e.g., "2 minutes"
  } else {
    return `${hours} ${hoursText} and ${minutes} ${minutesText}`; // e.g., "2 hours and 2 minutes"
  }
}

export const isEndTimeReached = (givenDate, endTime) => {
  const currentDate = new Date();
  const givenDateObj = new Date(givenDate);

  const currentDateOnly = currentDate.toDateString();
  const givenDateOnly = givenDateObj.toDateString();

  // If it's the same day, check the time
  if (currentDateOnly === givenDateOnly) {
    const currentTime = currentDate.getHours() * 60 + currentDate.getMinutes();
    const [endHours, endMinutes] = endTime.split(':');
    const endTimeInMinutes = parseInt(endHours) * 60 + parseInt(endMinutes);

    return currentTime >= endTimeInMinutes;
  }

  // If date is in the past, return true
  if (currentDate > givenDateObj) {
    return true;
  }

  // If date is in the future, return false
  return false;
};

export const convertUTCToLocalTime = utcTime => {
  const [hours, minutes, seconds] = utcTime.split(':').map(Number);

  const utcDate = new Date();
  utcDate.setUTCHours(hours, minutes, seconds);

  const formatter = new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  });

  const localTime = formatter?.format(utcDate);
  console.log('🚀 ~ localTime:', localTime);
  return localTime;
};

// export const isOpenSessionSlotDisabled = (scheduleDate, scheduleEndTime) => {
//   const localEndTime = convertUTCToLocalTime(scheduleEndTime);

//   console.log('🚀 ~ isOpenSessionSlotDisabled ~ scheduleDate:', scheduleDate);
//   // Get current date and time in local timezone
//   const currentMoment = moment();

//   // Parse UTC inputs and convert to local time
//   const slotDate = moment.utc(scheduleDate).local();
//   const slotEndTime = moment.utc(`${scheduleDate} ${localEndTime}`).local();

//   console.log('Current local time:', currentMoment.format());
//   console.log('Slot date (local):', slotDate.format());
//   console.log('Slot end time (local):', slotEndTime.format());

//   // Check if slot is in the past
//   const isPastSlot =
//     slotDate.isBefore(currentMoment, 'day') || // Date has passed
//     (slotDate.isSame(currentMoment, 'day') &&
//       slotEndTime.isBefore(currentMoment)); // Same day but time has passed

//   return isPastSlot;
// };

export function isOpenSessionSlotDisabled(dateStr, timeStr) {
  // Combine date and time into one datetime string
  const localtimeStr = convertUTCToLocalTime(timeStr);
  const dateTimeStr = `${dateStr}T${localtimeStr}`;

  // Create a Date object from the input
  const inputDateTime = new Date(dateTimeStr);

  // Get the current date and time
  const now = new Date();
  console.log(now, inputDateTime);
  // Compare and return
  return inputDateTime < now;
}

// export const isOpenSessionSlotDisabled = (scheduleDate, scheduleEndTime) => {
//   const localEndTime = convertUTCToLocalTime(scheduleEndTime);
//   console.log('🚀 ~ isOpenSessionSlotDisabled ~ scheduleDate:', scheduleDate);

//   // Get current date and time
//   const currentDate = new Date();

//   // Create date objects from inputs
//   const slotDate = new Date(scheduleDate);
//   const slotEndTime = new Date(`${scheduleDate} ${localEndTime}`);

//   console.log('Current local time:', currentDate.toLocaleString());
//   console.log('Slot date (local):', slotDate.toLocaleString());
//   console.log('Slot end time (local):', slotEndTime.toLocaleString());

//   // Check if slot is in the past
//   const isPastSlot =
//     slotDate < currentDate || // Date has passed
//     (slotDate.toDateString() === currentDate.toDateString() &&
//       slotEndTime < currentDate); // Same day but time has passed

//   return isPastSlot;
// };

export const getCurrentUTCTime = () => {
  const utcFormatter = new Intl.DateTimeFormat('en-US', {
    timeZone: 'UTC',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  });
  return utcFormatter.format(new Date());
};
