import {PermissionsAndroid} from 'react-native';
import {GOOGLE_PLACE_API_KEY} from '../../Utils/constant';
import Geolocation from 'react-native-geolocation-service';

export const fetchPlaceDetails = async placeId => {
  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${GOOGLE_PLACE_API_KEY}`,
    );
    const data = await response.json();
    if (data.status === 'OK') {
      const {lat, lng} = data.result.geometry.location;
      const country = data.result.address_components.find(comp =>
        comp.types.includes('country'),
      )?.long_name;
      return {lat, lng, country};
    } else {
      console.error('Failed to fetch place details:', data.status);
    }
  } catch (error) {
    console.error('Error fetching place details:', error);
  }
};

export const requestLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      const status = await Geolocation.requestAuthorization('whenInUse');
      if (status === 'granted') {
        return true;
      }
    } else {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        return true;
      }
    }
    return false;
  } catch (err) {
    console.warn(err);
    return false;
  }
};
