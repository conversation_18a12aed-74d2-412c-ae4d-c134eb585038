import colors from '../../Utils/colors';

// Function to get all dates of a given month
function getRemainingDates(year, month, scheduledDates) {
  // const year = parseInt(scheduledDates[0].substring(0, 4));
  const monthIndex = month - 1;
  const daysInMonth = new Date(year, monthIndex + 1, 0).getDate();

  return [...Array(daysInMonth)]
    .map((_, i) => {
      const day = (i + 1).toString().padStart(2, '0');
      const monthStr = (monthIndex + 1).toString().padStart(2, '0');
      return `${year}-${monthStr}-${day}`;
    })
    .filter(date => !scheduledDates.includes(date));
}

export function getFormattedDatesWithSlots(
  dates,
  bookingDates,
  textFontWeight = 'bold',
  textColor = colors.darkBlack,
) {
  console.log('🚀 ~ dates:', dates);
  console.log('🚀 ~ bookingDates:', bookingDates);
  return dates.reduce((acc, date) => {
    acc[date] = {
      customStyles: {
        text: {fontWeight: textFontWeight, color: textColor},
      },
    };

    return acc;
  }, {});
}

export function getFormattedBookingDatesWithSlots(
  bookingDates,
  existingFormattedDates = {},
  textFontWeight = 'bold',
  textColor = colors.darkBlack,
) {
  const formattedBookingDates = {};

  bookingDates.forEach(date => {
    if (existingFormattedDates[date]) {
      formattedBookingDates[date] = {
        ...existingFormattedDates[date],
        marked: true,
        dotColor: 'yellow',
      };
    } else {
      formattedBookingDates[date] = {
        marked: true,
        dotColor: 'yellow',
      };
    }
  });

  return formattedBookingDates;
}

export function getRemainingDatesFormatted(
  scheduleDate,
  bookingDatesApiRes,
  year,
  month,
  textFontWeight,
  textColor,
) {
  const remainingDates = getRemainingDates(year, month, scheduleDate);

  // Format scheduled dates with 'darkBlack' and remaining dates with 'grey'
  // const scheduledDatesFormatted = getFormattedDatesWithSlots(scheduleDate, 'darkBlack');
  const remainingDatesFormatted = getFormattedDatesWithSlots(
    remainingDates,
    bookingDatesApiRes,
    textFontWeight,
    textColor,
  );

  // Combine both objects
  return remainingDatesFormatted;
}

export const getFirstAndLastDates = dateStr => {
  try {
    // Parse the date string into a Date object
    const date = new Date(dateStr);

    // Check if the date is invalid
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date string');
    }

    // Get the year and zero-based month
    const year = date.getUTCFullYear();
    const month = date.getUTCMonth();

    // Create a Date object for the last day of the month
    const lastDay = new Date(Date.UTC(year, month + 1, 0));
    const firstDay = new Date(Date.UTC(year, month, 0));

    // Return the last day in YYYY-MM-DD format
    return {
      firstDay: firstDay.toISOString().split('T')[0],
      lastDay: lastDay.toISOString().split('T')[0],
      year: year,
      month: month,
    };
  } catch (error) {
    console.error('Error: getFirstAndLastDates', error.message);
    return null;
  }
};
