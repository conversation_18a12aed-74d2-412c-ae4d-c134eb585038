{"v": "5.9.6", "fr": 60, "ip": 0, "op": 122, "w": 500, "h": 500, "nm": "Check_2", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Group 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"t": 26, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [293.19, 327.521, 0], "t": 16, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [294.402, 328.495, 0], "t": 17, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [297.493, 330.979, 0], "t": 18, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [301.913, 334.53, 0], "t": 19, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [307.329, 338.883, 0], "t": 20, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [313.519, 343.857, 0], "t": 21, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [320.287, 349.296, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [327.487, 355.082, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [334.972, 361.096, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [342.447, 367.103, 0], "t": 25, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [349.19, 372.521, 0], "t": 26, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [353.735, 376.173, 0], "t": 27, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [357.334, 379.066, 0], "t": 28, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [360.064, 381.259, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [362.009, 382.822, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [363.26, 383.827, 0], "t": 31, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [363.908, 384.348, 0], "t": 32, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [363.764, 384.233, 0], "t": 34, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [363.144, 383.734, 0], "t": 35, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [362.264, 383.027, 0], "t": 36, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [361.195, 382.168, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [360, 381.208, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [358.733, 380.19, 0], "t": 39, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [357.441, 379.152, 0], "t": 40, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [356.164, 378.125, 0], "t": 41, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [354.932, 377.135, 0], "t": 42, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [353.77, 376.201, 0], "t": 43, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [352.696, 375.339, 0], "t": 44, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [351.724, 374.557, 0], "t": 45, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [350.861, 373.864, 0], "t": 46, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [350.109, 373.26, 0], "t": 47, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [349.469, 372.745, 0], "t": 48, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [348.938, 372.318, 0], "t": 49, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [348.509, 371.974, 0], "t": 50, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [348.177, 371.707, 0], "t": 51, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [347.932, 371.51, 0], "t": 52, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [348.252, 371.768, 0], "t": 62, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [348.521, 371.983, 0], "t": 64, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [348.765, 372.18, 0], "t": 66, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [349.055, 372.413, 0], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [349.19, 372.521, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 9.328], [-9.328, 0], [0, -9.328], [9.328, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.678, 0.922, 0.9017, 0.5, 0.3356, 0.7844, 0.747, 1, 0.1716, 0.4884, 0.462], "ix": 9}}, "s": {"a": 0, "k": [-16.19, -0.521], "ix": 5}, "e": {"a": 0, "k": [17.257, 0.245], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [349.19, 372.521], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 16, "op": 616, "st": 16, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Group 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [0]}, {"t": 18, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [308.411, 163.748, 0], "t": 8, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [310.208, 162.449, 0], "t": 9, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [314.788, 159.138, 0], "t": 10, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [321.34, 154.402, 0], "t": 11, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [329.367, 148.599, 0], "t": 12, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [338.536, 141.971, 0], "t": 13, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [348.577, 134.712, 0], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [359.262, 126.988, 0], "t": 15, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [381.418, 110.972, 0], "t": 17, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [391.411, 103.748, 0], "t": 18, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [398.147, 98.878, 0], "t": 19, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [403.482, 95.022, 0], "t": 20, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.528, 92.097, 0], "t": 21, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [410.411, 90.013, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.265, 88.673, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.226, 87.978, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.432, 87.829, 0], "t": 25, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.013, 88.132, 0], "t": 26, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.093, 88.797, 0], "t": 27, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [410.789, 89.739, 0], "t": 28, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [409.205, 90.885, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.433, 92.165, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [405.555, 93.523, 0], "t": 31, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [403.641, 94.907, 0], "t": 32, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [401.747, 96.276, 0], "t": 33, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [399.921, 97.596, 0], "t": 34, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [398.199, 98.841, 0], "t": 35, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [396.608, 99.991, 0], "t": 36, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [395.167, 101.033, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [393.887, 101.958, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [392.773, 102.763, 0], "t": 39, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [391.825, 103.449, 0], "t": 40, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [391.037, 104.018, 0], "t": 41, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [390.402, 104.477, 0], "t": 42, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [389.91, 104.833, 0], "t": 43, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [389.546, 105.096, 0], "t": 44, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [389.298, 105.275, 0], "t": 45, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [389.152, 105.381, 0], "t": 46, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [389.296, 105.277, 0], "t": 50, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [389.449, 105.166, 0], "t": 51, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [389.627, 105.037, 0], "t": 52, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [389.821, 104.897, 0], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [390.021, 104.752, 0], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [390.223, 104.607, 0], "t": 55, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [390.419, 104.465, 0], "t": 56, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [390.607, 104.329, 0], "t": 57, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [390.782, 104.203, 0], "t": 58, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [390.942, 104.087, 0], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [391.085, 103.983, 0], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [391.211, 103.892, 0], "t": 61, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [391.32, 103.814, 0], "t": 62, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [391.411, 103.748, 0], "t": 63, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [391.545, 103.651, 0], "t": 65, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [391.407, 103.751, 0], "t": 86, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [391.411, 103.748, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 15.982], [-15.982, 0], [0, -15.982], [15.982, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.678, 0.922, 0.9017, 0.5, 0.3356, 0.7844, 0.747, 1, 0.1716, 0.4884, 0.462], "ix": 9}}, "s": {"a": 0, "k": [15.589, 0.252], "ix": 5}, "e": {"a": 0, "k": [-30.028, -0.769], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [391.411, 103.748], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 8, "op": 608, "st": 8, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Group 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 10, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [182.548, 199.775, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [181.162, 198.217, 0], "t": 1, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [177.631, 194.244, 0], "t": 2, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [172.579, 188.56, 0], "t": 3, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [166.389, 181.596, 0], "t": 4, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [159.319, 173.643, 0], "t": 5, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [151.581, 164.937, 0], "t": 6, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [143.347, 155.674, 0], "t": 7, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [126.254, 136.444, 0], "t": 9, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.548, 127.775, 0], "t": 10, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [113.354, 121.932, 0], "t": 11, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [109.24, 117.304, 0], "t": 12, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [106.12, 113.794, 0], "t": 13, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [103.897, 111.293, 0], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.468, 109.685, 0], "t": 15, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.727, 108.851, 0], "t": 16, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.568, 108.673, 0], "t": 17, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.891, 109.037, 0], "t": 18, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.6, 109.834, 0], "t": 19, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [103.606, 110.965, 0], "t": 20, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [104.827, 112.34, 0], "t": 21, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [106.194, 113.876, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [107.641, 115.505, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [109.118, 117.166, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [110.578, 118.809, 0], "t": 25, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [111.986, 120.393, 0], "t": 26, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [113.314, 121.887, 0], "t": 27, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [114.541, 123.267, 0], "t": 28, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [115.652, 124.517, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [116.639, 125.627, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [117.498, 126.594, 0], "t": 31, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.229, 127.416, 0], "t": 32, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.836, 128.099, 0], "t": 33, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.326, 128.65, 0], "t": 34, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.706, 129.078, 0], "t": 35, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.986, 129.393, 0], "t": 36, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [120.177, 129.608, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [120.29, 129.735, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [120.179, 129.61, 0], "t": 42, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [120.061, 129.477, 0], "t": 43, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.923, 129.323, 0], "t": 44, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.774, 129.155, 0], "t": 45, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.62, 128.981, 0], "t": 46, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.464, 128.806, 0], "t": 47, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.313, 128.636, 0], "t": 48, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.168, 128.473, 0], "t": 49, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [119.033, 128.321, 0], "t": 50, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.91, 128.182, 0], "t": 51, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.799, 128.058, 0], "t": 52, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.702, 127.949, 0], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.618, 127.854, 0], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.548, 127.775, 0], "t": 55, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.444, 127.659, 0], "t": 57, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [118.556, 127.785, 0], "t": 79, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [118.548, 127.775, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 12.5], [-12.5, 0], [0, -12.5], [12.5, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.678, 0.922, 0.9017, 0.5, 0.3356, 0.7844, 0.747, 1, 0.1716, 0.4884, 0.462], "ix": 9}}, "s": {"a": 0, "k": [-13.548, -0.775], "ix": 5}, "e": {"a": 0, "k": [11.305, -0.775], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [118.548, 127.775], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Check", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [249.853, 245.209, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"k": [{"s": [0, 0, 100], "t": 14, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [2.165, 2.165, 100], "t": 15, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [7.678, 7.678, 100], "t": 16, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [15.572, 15.572, 100], "t": 17, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [25.248, 25.248, 100], "t": 18, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [36.294, 36.294, 100], "t": 19, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [48.393, 48.393, 100], "t": 20, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [61.267, 61.267, 100], "t": 21, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [87.96, 87.96, 100], "t": 23, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 24, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [108.075, 108.075, 100], "t": 25, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [114.471, 114.471, 100], "t": 26, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [119.322, 119.322, 100], "t": 27, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [122.778, 122.778, 100], "t": 28, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [125, 125, 100], "t": 29, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [126.152, 126.152, 100], "t": 30, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [126.399, 126.399, 100], "t": 31, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [125.896, 125.896, 100], "t": 32, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [124.795, 124.795, 100], "t": 33, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [123.231, 123.231, 100], "t": 34, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [121.332, 121.332, 100], "t": 35, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [119.208, 119.208, 100], "t": 36, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [116.957, 116.957, 100], "t": 37, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [114.661, 114.661, 100], "t": 38, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [112.391, 112.391, 100], "t": 39, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [110.202, 110.202, 100], "t": 40, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [108.137, 108.137, 100], "t": 41, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [106.23, 106.23, 100], "t": 42, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [104.503, 104.503, 100], "t": 43, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [102.968, 102.968, 100], "t": 44, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [101.633, 101.633, 100], "t": 45, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.496, 100.496, 100], "t": 46, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.552, 99.552, 100], "t": 47, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.791, 98.791, 100], "t": 48, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.2, 98.2, 100], "t": 49, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.764, 97.764, 100], "t": 50, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.467, 97.467, 100], "t": 51, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.291, 97.291, 100], "t": 52, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.322, 97.322, 100], "t": 55, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.464, 97.464, 100], "t": 56, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.648, 97.648, 100], "t": 57, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.861, 97.861, 100], "t": 58, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.093, 98.093, 100], "t": 59, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.334, 98.334, 100], "t": 60, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.575, 98.575, 100], "t": 61, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.811, 98.811, 100], "t": 62, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.035, 99.035, 100], "t": 63, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.245, 99.245, 100], "t": 64, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.437, 99.437, 100], "t": 65, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.609, 99.609, 100], "t": 66, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.76, 99.76, 100], "t": 67, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.89, 99.89, 100], "t": 68, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 69, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.09, 100.09, 100], "t": 70, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.161, 100.161, 100], "t": 71, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.253, 100.253, 100], "t": 73, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.163, 100.163, 100], "t": 83, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.069, 100.069, 100], "t": 87, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.97, 99.97, 100], "t": 97, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}], "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-40.326, -0.831], [-12.366, 26.409], [40.326, -26.409]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 26, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 14, "op": 614, "st": 14, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Circle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"k": [{"s": [0, 0, 100], "t": 10, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [2.165, 2.165, 100], "t": 11, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [7.678, 7.678, 100], "t": 12, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [15.572, 15.572, 100], "t": 13, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [25.248, 25.248, 100], "t": 14, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [36.294, 36.294, 100], "t": 15, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [48.393, 48.393, 100], "t": 16, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [61.267, 61.267, 100], "t": 17, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [87.96, 87.96, 100], "t": 19, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 20, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [108.075, 108.075, 100], "t": 21, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [114.471, 114.471, 100], "t": 22, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [119.322, 119.322, 100], "t": 23, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [122.778, 122.778, 100], "t": 24, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [125, 125, 100], "t": 25, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [126.152, 126.152, 100], "t": 26, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [126.399, 126.399, 100], "t": 27, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [125.896, 125.896, 100], "t": 28, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [124.795, 124.795, 100], "t": 29, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [123.231, 123.231, 100], "t": 30, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [121.332, 121.332, 100], "t": 31, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [119.208, 119.208, 100], "t": 32, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [116.957, 116.957, 100], "t": 33, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [114.661, 114.661, 100], "t": 34, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [112.391, 112.391, 100], "t": 35, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [110.202, 110.202, 100], "t": 36, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [108.137, 108.137, 100], "t": 37, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [106.23, 106.23, 100], "t": 38, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [104.503, 104.503, 100], "t": 39, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [102.968, 102.968, 100], "t": 40, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [101.633, 101.633, 100], "t": 41, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.496, 100.496, 100], "t": 42, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.552, 99.552, 100], "t": 43, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.791, 98.791, 100], "t": 44, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.2, 98.2, 100], "t": 45, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.764, 97.764, 100], "t": 46, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.467, 97.467, 100], "t": 47, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.291, 97.291, 100], "t": 48, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.322, 97.322, 100], "t": 51, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.464, 97.464, 100], "t": 52, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.648, 97.648, 100], "t": 53, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.861, 97.861, 100], "t": 54, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.093, 98.093, 100], "t": 55, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.334, 98.334, 100], "t": 56, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.575, 98.575, 100], "t": 57, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.811, 98.811, 100], "t": 58, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.035, 99.035, 100], "t": 59, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.245, 99.245, 100], "t": 60, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.437, 99.437, 100], "t": 61, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.609, 99.609, 100], "t": 62, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.76, 99.76, 100], "t": 63, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.89, 99.89, 100], "t": 64, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 65, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.09, 100.09, 100], "t": 66, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.161, 100.161, 100], "t": 67, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.253, 100.253, 100], "t": 69, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.163, 100.163, 100], "t": 79, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.069, 100.069, 100], "t": 83, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.97, 99.97, 100], "t": 93, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}], "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [200, 200], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.678, 0.922, 0.9017, 0.5, 0.3356, 0.7844, 0.747, 1, 0.1716, 0.4884, 0.462], "ix": 9}}, "s": {"a": 0, "k": [0, -100], "ix": 5}, "e": {"a": 0, "k": [0, 100], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 610, "st": 10, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Circle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"k": [{"s": [0, 0, 100], "t": 6, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [2.165, 2.165, 100], "t": 7, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [7.678, 7.678, 100], "t": 8, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [15.572, 15.572, 100], "t": 9, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [25.248, 25.248, 100], "t": 10, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [36.294, 36.294, 100], "t": 11, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [48.393, 48.393, 100], "t": 12, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [61.267, 61.267, 100], "t": 13, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [87.96, 87.96, 100], "t": 15, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 16, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [108.075, 108.075, 100], "t": 17, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [114.471, 114.471, 100], "t": 18, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [119.322, 119.322, 100], "t": 19, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [122.778, 122.778, 100], "t": 20, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [125, 125, 100], "t": 21, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [126.152, 126.152, 100], "t": 22, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [126.399, 126.399, 100], "t": 23, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [125.896, 125.896, 100], "t": 24, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [124.795, 124.795, 100], "t": 25, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [123.231, 123.231, 100], "t": 26, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [121.332, 121.332, 100], "t": 27, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [119.208, 119.208, 100], "t": 28, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [116.957, 116.957, 100], "t": 29, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [114.661, 114.661, 100], "t": 30, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [112.391, 112.391, 100], "t": 31, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [110.202, 110.202, 100], "t": 32, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [108.137, 108.137, 100], "t": 33, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [106.23, 106.23, 100], "t": 34, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [104.503, 104.503, 100], "t": 35, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [102.968, 102.968, 100], "t": 36, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [101.633, 101.633, 100], "t": 37, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.496, 100.496, 100], "t": 38, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.552, 99.552, 100], "t": 39, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.791, 98.791, 100], "t": 40, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.2, 98.2, 100], "t": 41, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.764, 97.764, 100], "t": 42, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.467, 97.467, 100], "t": 43, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.291, 97.291, 100], "t": 44, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.322, 97.322, 100], "t": 47, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.464, 97.464, 100], "t": 48, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.648, 97.648, 100], "t": 49, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [97.861, 97.861, 100], "t": 50, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.093, 98.093, 100], "t": 51, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.334, 98.334, 100], "t": 52, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.575, 98.575, 100], "t": 53, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [98.811, 98.811, 100], "t": 54, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.035, 99.035, 100], "t": 55, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.245, 99.245, 100], "t": 56, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.437, 99.437, 100], "t": 57, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.609, 99.609, 100], "t": 58, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.76, 99.76, 100], "t": 59, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.89, 99.89, 100], "t": 60, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100, 100, 100], "t": 61, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.09, 100.09, 100], "t": 62, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.161, 100.161, 100], "t": 63, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.253, 100.253, 100], "t": 65, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.163, 100.163, 100], "t": 75, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [100.069, 100.069, 100], "t": 79, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}, {"s": [99.97, 99.97, 100], "t": 89, "i": {"x": [1, 1, 1], "y": [1, 1, 1]}, "o": {"x": [0, 0, 0], "y": [0, 0, 0]}}], "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [269.236, 269.236], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.9, 1, 0.9917, 0.5, 0.637, 0.923, 0.8992, 1, 0.251, 0.6392, 0.6078], "ix": 9}}, "s": {"a": 0, "k": [0, -135], "ix": 5}, "e": {"a": 0, "k": [0, 134.236], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 6, "op": 606, "st": 6, "ct": 1, "bm": 0}], "markers": []}