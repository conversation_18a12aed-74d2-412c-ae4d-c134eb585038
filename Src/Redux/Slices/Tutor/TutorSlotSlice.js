import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  selectedSlots: {}, // { [date]: [slot1, slot2, ...] }
  selectedSlotDate: null,
};

const tutorSlotSlice = createSlice({
  name: 'tutorSlot',
  initialState,
  reducers: {
    updateSelectedSlots(state, action) {
      const {date, slots} = action.payload;

      // Update the selectedSlots object immutably
      state.selectedSlots = {
        ...state.selectedSlots, // Keep existing dates and slots
        [date]: slots, // Add or update the slots for the given date
      };

      // Filter out undefined keys from the selectedSlots object
      state.selectedSlots = Object.fromEntries(
        Object.entries(state.selectedSlots).filter(
          ([key, value]) => value !== undefined,
        ),
      );
    },
    updateSelectedSlotDate(state, action) {
      state.selectedSlotDate = action.payload;
    },
    resetTutorSlots(state) {
      state.selectedSlots = {};
      state.selectedSlotDate = null;
    },
  },
});

export const {updateSelectedSlots, updateSelectedSlotDate, resetTutorSlots} =
  tutorSlotSlice.actions;
export default tutorSlotSlice.reducer;
