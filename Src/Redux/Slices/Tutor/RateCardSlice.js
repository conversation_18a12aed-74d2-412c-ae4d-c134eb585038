import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  rateCardLength: '',
};

const rateCardSlice = createSlice({
  name: 'rateCardSlice',
  initialState,
  reducers: {
    updateRateCardLength: (state, action) => {
      console.log('🚀 ~ reducer working:', action.payload);
      state.rateCardLength = action.payload;
    },
  },
});

export const {updateRateCardLength} = rateCardSlice.actions;

export default rateCardSlice.reducer;
