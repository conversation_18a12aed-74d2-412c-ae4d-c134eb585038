import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  currentLoc: {},
};

const currentLocSlice = createSlice({
  name: 'currentLocSlice',
  initialState,
  reducers: {
    updateCurrentLoc: (state, action) => {
      console.log('🚀 ~ reducer working:', action.payload);
      state.currentLoc = action.payload;
    },
  },
});

export const {updateCurrentLoc} = currentLocSlice.actions;

export default currentLocSlice.reducer;
