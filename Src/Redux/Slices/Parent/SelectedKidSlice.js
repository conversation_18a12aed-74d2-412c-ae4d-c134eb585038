import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  selectedKid: {},
};

const selectedKidSlice = createSlice({
  name: 'selectedKidSlice',
  initialState,
  reducers: {
    updateSelectedKid: (state, action) => {
      console.log('🚀 ~ reducer working:', action.payload);
      state.selectedKid = action.payload;
    },
  },
});

export const {updateSelectedKid} = selectedKidSlice.actions;

export default selectedKidSlice.reducer;
