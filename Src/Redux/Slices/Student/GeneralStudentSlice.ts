import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  addCurriculumFlowType: '', // edit , academic
};

const generalStudentSlice = createSlice({
  name: 'generalStudentSlice',
  initialState,
  reducers: {
    updateAddCurriculumFlowType(state, action) {
      state.addCurriculumFlowType = action.payload;
    },
  },
});

export const {updateAddCurriculumFlowType} = generalStudentSlice.actions;

export default generalStudentSlice.reducer;
