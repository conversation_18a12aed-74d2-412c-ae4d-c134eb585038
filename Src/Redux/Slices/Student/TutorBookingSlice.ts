import {createSlice} from '@reduxjs/toolkit';
import {State} from 'react-native-gesture-handler';

const initialState = {
  tutorData: {},
  classType: {},
  sessionType: {},
  packageStartDate: new Date().toISOString().split('T')[0],
  package: {},
  slot: [],
  meetingPoint: {},
  instructions: '',
  groupStudents: [],
  tutorId: '',
  calculatedBookingAmount: '',
  totalDurationSelectedSlots: '',
  selectedSlotDate: '',
  bookingFlowType: '',
  expertiseIdRecreational: '',
  courseIdCourses: '',
  commissionRate: '',
  courseRateCardId: '',
  tutorMeetingPoint: {},
  selectedRateCardId: '',
};

const tutorBookingSlice = createSlice({
  name: 'tutorBookingSlice',
  initialState,
  reducers: {
    updateClassType: (state, action) => {
      state.classType = action.payload;
    },
    updateTutorData: (state, action) => {
      state.tutorData = action.payload;
    },
    updateSessionType: (state, action) => {
      state.sessionType = action.payload;
    },
    updatePackageStartDate: (state, action) => {
      console.log('action.payload', action.payload);
      state.packageStartDate = action.payload;
    },
    updatePackage: (state, action) => {
      state.package = action.payload;
    },
    updateSlot: (state, action) => {
      state.slot = action.payload;
    },
    updateMeetingPoint: (state, action) => {
      console.log('action.payload', action?.payload);
      state.meetingPoint = action.payload;
    },
    updateInstructions: (state, action) => {
      state.instructions = action.payload;
    },
    updateGroupStudents: (state, action) => {
      state.groupStudents = action.payload;
    },
    updateTutorId: (state, action) => {
      state.tutorId = action.payload;
    },
    updateTotalDurationSelectedSlots: (state, action) => {
      state.totalDurationSelectedSlots = action.payload;
    },
    updateCalculatedBookingAmount: (state, action) => {
      state.calculatedBookingAmount = action.payload;
    },
    updateSelectedSlotDate: (state, action) => {
      state.selectedSlotDate = action.payload;
    },
    updateBookingFlowType: (state, action) => {
      state.bookingFlowType = action.payload;
    },
    resetBookingFlowType: state => {
      state.bookingFlowType = '';
    },
    updateExpertiseIdRecreational: (state, action) => {
      state.expertiseIdRecreational = action.payload;
    },
    updateCourseIdCourses: (state, action) => {
      state.courseIdCourses = action.payload;
    },
    updateCommissionRate: (state, action) => {
      state.commissionRate = action.payload;
    },
    updateCourseRateCardId: (state, action) => {
      state.courseRateCardId = action.payload;
    },
    updateMeetingTutorPoint: (state, action) => {
      console.log('action.payload', action?.payload);
      state.tutorMeetingPoint = action.payload;
    },
    updateSelectedRateCardId: (state, action) => {
      console.log('action.payload', action?.payload);
      state.selectedRateCardId = action.payload;
    },
  },
});

export const {
  updateClassType,
  updateSessionType,
  updatePackageStartDate,
  updatePackage,
  updateSlot,
  updateMeetingPoint,
  updateInstructions,
  updateTutorData,
  updateTutorId,
  updateGroupStudents,
  updateTotalDurationSelectedSlots,
  updateCalculatedBookingAmount,
  updateSelectedSlotDate,
  updateBookingFlowType,
  resetBookingFlowType,
  updateExpertiseIdRecreational,
  updateCourseIdCourses,
  updateCommissionRate,
  updateCourseRateCardId,
  updateMeetingTutorPoint,
  updateSelectedRateCardId,
} = tutorBookingSlice.actions;

export default tutorBookingSlice.reducer;
