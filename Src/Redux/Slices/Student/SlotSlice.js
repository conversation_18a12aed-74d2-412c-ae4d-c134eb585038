import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  currentDate: '',
  isScheduleAtSimilarTime: false,
  selectedSlots: {}, // Store selected slots by date for grey out feature
  total_days: 1,
  bookingSchedules: [], // store schedule in api acceptable form
  selectedPackageData: {},
  updateGreySlots: {},
};

const slotSlice = createSlice({
  name: 'slotSlice',
  initialState,
  reducers: {
    updateCurrentDate(state, action) {
      state.currentDate = action.payload;
    },
    updateIsScheduleAtSimilarTime(state, action) {
      state.isScheduleAtSimilarTime = action.payload;
    },
    // please use resetSelectedSlots to reset the slots
    // updateSelectedSlots(state, action) {
    //   const {date, slots} = action.payload;

    //   // Update the selectedSlots object immutably
    //   state.selectedSlots = {
    //     ...state.selectedSlots, // Keep existing dates and slots
    //     [date]: slots, // Add or update the slots for the given date
    //   };

    //   // Filter out undefined keys from the selectedSlots object
    //   state.selectedSlots = Object.fromEntries(
    //     Object.entries(state.selectedSlots).filter(
    //       ([key, value]) => value !== undefined,
    //     ),
    //   );
    // },
    updateSelectedSlots(state, action) {
      const {date, slots} = action.payload;

      // Create a new object with the updated slots
      const updatedSelectedSlots = {
        ...state.selectedSlots,
        [date]: slots,
      };

      // Filter out dates with undefined or empty slots
      const filteredSlots = Object.fromEntries(
        Object.entries(updatedSelectedSlots).filter(
          ([_, value]) => value !== undefined && value.length > 0,
        ),
      );

      // Update the state only if there are slots left
      if (Object.keys(filteredSlots).length > 0) {
        state.selectedSlots = filteredSlots;
      } else {
        // If no slots left, reset to empty object
        state.selectedSlots = {};
      }
    },
    resetSelectedSlots(state) {
      state.selectedSlots = {};
    },
    updateGreySlots(state, action) {
      const {date, slots} = action.payload;

      // Update the selectedSlots object immutably
      state.greySlots = {
        ...state.greySlots, // Keep existing dates and slots
        [date]: slots, // Add or update the slots for the given date
      };

      // Filter out undefined keys from the selectedSlots object
      state.greySlots = Object.fromEntries(
        Object.entries(state.greySlots).filter(
          ([key, value]) => value !== undefined,
        ),
      );
    },

    updateTotalDays(state, action) {
      state.total_days = action.payload;
    },
    updateSelectedPackageData(state, action) {
      state.selectedPackageData = action.payload;
    },
  },
});

export const {
  updateCurrentDate,
  updateTotalDays,
  updateSelectedSlots,
  updateIsScheduleAtSimilarTime,
  updateBookingSchedules,
  resetBookingSchedules,
  updateSelectedPackageData,
  updateGreySlots,
  resetSelectedSlots,
} = slotSlice.actions;

export default slotSlice.reducer;
