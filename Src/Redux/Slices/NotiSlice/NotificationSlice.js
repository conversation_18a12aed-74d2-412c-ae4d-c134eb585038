import {createSlice} from '@reduxjs/toolkit';

const notificationSlice = createSlice({
  name: 'notifications',
  initialState: {
    unreadCount: 0, // For notifications
    unreadMessageCount: 0, // For messages
    unreadSupportMessageCount: 0, // for support messages,
  },
  reducers: {
    setUnreadCount(state, action) {
      state.unreadCount = action.payload;
    },
    setUnreadMessageCount(state, action) {
      state.unreadMessageCount = action.payload;
    },
    setUnreadSupportMessageCount(state, action) {
      state.unreadSupportMessageCount = action.payload;
    },
  },
});

export const {
  setUnreadCount,
  setUnreadMessageCount,
  setUnreadSupportMessageCount,
} = notificationSlice.actions;
export default notificationSlice.reducer;
