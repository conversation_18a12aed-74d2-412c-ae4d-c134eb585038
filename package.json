{"name": "Tale<PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.4.0", "@netless/react-native-whiteboard": "^0.0.14", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.0.0", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-community/slider": "^4.5.5", "@react-native-firebase/app": "^21.6.1", "@react-native-firebase/auth": "^21.6.1", "@react-native-firebase/messaging": "^21.6.1", "@react-native-google-signin/google-signin": "^10.0.1", "@react-native-picker/picker": "^2.9.0", "@react-native-vector-icons/common": "^11.0.0", "@react-native/gradle-plugin": "^0.77.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.4.1", "@reduxjs/toolkit": "^2.2.8", "@rneui/base": "^4.0.0-rc.7", "@rneui/themed": "^4.0.0-rc.8", "agora-react-native-rtm": "^1.5.1", "agora-rn-uikit": "^5.0.2", "axios": "^1.7.9", "i": "^0.3.7", "i18next": "^23.16.0", "lottie-react-native": "^7.0.0", "moment": "^2.30.1", "npm": "^11.0.0", "react": "18.3.1", "react-i18next": "^15.1.0", "react-native": "0.75.4", "react-native-agora": "^4.5.0", "react-native-blob-util": "^0.21.2", "react-native-calendars": "^1.1308.1", "react-native-contacts": "^8.0.4", "react-native-country-picker-modal": "^2.0.0", "react-native-device-info": "^14.0.2", "react-native-document-picker": "^9.3.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.20.0", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-image-picker": "^7.1.2", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal-datetime-picker": "^18.0.0", "react-native-otp-entry": "^1.8.3", "react-native-pdf": "^6.7.6", "react-native-ratings": "^8.1.0", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "^3.15.4", "react-native-render-html": "^6.3.4", "react-native-responsive-dimensions": "^3.1.1", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.11.0", "react-native-screens": "^3.34.0", "react-native-select-dropdown": "^4.0.1", "react-native-svg": "^15.3.0", "react-native-switch": "^1.5.1", "react-native-toast-message": "^2.2.1", "react-native-video": "^6.8.2", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-webrtc": "^124.0.5", "react-native-webview": "^13.12.5", "react-redux": "^9.1.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.75.4", "@react-native/eslint-config": "0.75.4", "@react-native/metro-config": "0.75.4", "@react-native/typescript-config": "0.75.4", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-geolocation-service": "^5.3.1", "react-native-maps": "^1.20.1", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}