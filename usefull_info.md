# Taleem apk

### merge commands

```
git fetch origin dev_rajk_temp
git merge --no-ff origin/dev_rajk_temp -m 'murge code dev_rajk_temp to temp_dev_sameer'
```

suhail to raj
git fetch origin develop_M2
git merge --no-ff origin/develop_M2 -m 'murge code develop_M2 to dev_rajk_temp'

//tutor Profile Status÷
0 = Pending
1 = approved
2 = rejected
3 = submitted

  <!-- <Calendar
        // Customize the appearance of the calendar
        style={{
          // height: 200,
          marginHorizontal: 10,
          marginVertical: 10,
          borderRadius: fp(2.2),
          // padding: 20,

          paddingHorizontal: 40,
          paddingBottom: 20,
        }}
        // Specify the current date
        // current={}
        // Callback that gets called when the user selects a day
        onDayPress={day => {
          console.log('selected day', day);
        }}
        markingType={'period'}
        markedDates={markedDates}
        theme={{
          backgroundColor: '#ffffff',
          calendarBackground: '#ffffff',
          textSectionTitleColor: '#2d4150',
          textSectionTitleDisabledColor: '#d9e1e8',
          selectedDayBackgroundColor: '#00adf5',
          selectedDayTextColor: '#ffffff',
          todayTextColor: colors.themeBackgroundTwo,
          dayTextColor: '#2d4150',
          textDisabledColor: '#d9e1e8',
          dotColor: '#00adf5',
          selectedDotColor: '#ffffff',
          arrowColor: colors.themeBackgroundTwo,
          disabledArrowColor: '#d9e1e8',
          monthTextColor: colors.black,
          indicatorColor: 'blue',
          textDayFontFamily: Fonts.medium,
          textMonthFontFamily: Fonts.medium,
          textDayHeaderFontFamily: Fonts.medium,
          textDayFontSize: fp(1.6),
          textMonthFontSize: fp(1.6),
          textDayHeaderFontSize: fp(1.6),
          textSectionTitleDisabledColor: '#d9e1e8',
        }}
        hideExtraDays={true}
        enableSwipeMonths={true}
        headerStyle={{}}
        // customHeader={TaleemCalendarHeader}
        // Mark specific dates as marked
      /> -->

<!--
  const [markedDates, setMarkedDates] = useState({
    // '2025-05-15': {marked: true, dotColor: 'red'},
    // '2025-05-16': {marked: true, dotColor: '#50cebb'},
    '2025-05-21': {
      startingDay: true,
      color: activeColor,
      textColor: 'white',
      marked: true,
      dotColor: 'yellow',
    },
    '2025-05-22': {
      color: colors.themeBackground,
      textColor: 'white',
      marked: true,
      dotColor: 'yellow',
    },
    '2025-05-23': {
      color: colors.themeBackground,
      textColor: 'white',
      // marked: true,
      // dotColor: 'red',
    },
    '2025-05-24': {
      color: colors.themeBackground,
      textColor: 'white',
    },
    '2025-05-25': {
      endingDay: false,
      color: colors.themeBackground,
      textColor: 'white',
    },
    '2025-05-26': {color: colors.themeBackground, textColor: 'white'},
    '2025-05-27': {
      endingDay: true,
      color: activeColor,
      textColor: 'white',
      marked: true,
      dotColor: 'yellow',
    },
  }); -->
